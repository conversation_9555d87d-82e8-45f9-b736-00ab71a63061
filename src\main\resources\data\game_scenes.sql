-- 游戏场景初始化数据
-- 插入基础场景数据

INSERT INTO scene_info (scene_id, scene_name, scene_description, scene_type, level_limit, max_players, 
                       is_safe_zone, allow_pk, allow_teleport, background_music, background_image,
                       map_width, map_height, spawn_x, spawn_y, north_scene, south_scene, east_scene, west_scene,
                       monster_config, npc_config, teleport_config, weather_type, time_type, is_active) VALUES

-- 新手村及周边
('001', '新手村', '宁静祥和的小村庄，新冒险者的起点。这里有友善的村民和基础的商店。', 3, 1, 50, 1, 0, 1, 'village.mp3', 'village.jpg', 100, 100, 50, 50, '002', NULL, '003', NULL, 
 NULL, 
 '[{"npc_id":1,"name":"村长","x":45,"y":50,"type":"quest"},{"npc_id":2,"name":"武器商人","x":60,"y":40,"type":"shop"},{"npc_id":3,"name":"药剂师","x":40,"y":60,"type":"shop"}]',
 '[{"name":"传送阵","x":50,"y":45,"target":"101","cost":0}]', 1, 1, 1),

('002', '村外小径', '连接新手村和森林的小径，偶尔会遇到一些弱小的怪物。', 1, 1, 30, 0, 1, 1, 'path.mp3', 'path.jpg', 80, 120, 40, 100, '004', '001', NULL, NULL,
 '[{"monster_id":1,"name":"野兔","level":1,"spawn_rate":0.3,"max_count":3},{"monster_id":2,"name":"野狼","level":2,"spawn_rate":0.2,"max_count":2}]',
 NULL, NULL, 1, 1, 1),

('003', '村东农田', '新手村东边的农田，村民们在这里种植作物。', 1, 1, 20, 1, 0, 1, 'farm.mp3', 'farm.jpg', 120, 80, 20, 40, NULL, NULL, '005', '001',
 '[{"monster_id":3,"name":"田鼠","level":1,"spawn_rate":0.4,"max_count":5}]',
 '[{"npc_id":4,"name":"农夫老张","x":60,"y":40,"type":"quest"}]', NULL, 1, 1, 1),

-- 森林区域
('004', '幽暗森林入口', '通往幽暗森林的入口，空气中弥漫着神秘的气息。', 4, 3, 40, 0, 1, 1, 'forest_entrance.mp3', 'forest_entrance.jpg', 100, 100, 50, 90, '006', '002', '007', NULL,
 '[{"monster_id":4,"name":"森林狼","level":3,"spawn_rate":0.4,"max_count":4},{"monster_id":5,"name":"毒蜘蛛","level":4,"spawn_rate":0.3,"max_count":3}]',
 '[{"npc_id":5,"name":"森林守卫","x":50,"y":50,"type":"guard"}]', NULL, 2, 2, 1),

('005', '东部丘陵', '村庄东部的丘陵地带，地势起伏，视野开阔。', 4, 2, 35, 0, 1, 1, 'hills.mp3', 'hills.jpg', 150, 100, 30, 50, NULL, NULL, '008', '003',
 '[{"monster_id":6,"name":"山贼","level":4,"spawn_rate":0.2,"max_count":2},{"monster_id":7,"name":"野猪","level":3,"spawn_rate":0.3,"max_count":3}]',
 NULL, NULL, 1, 1, 1),

('006', '幽暗森林深处', '森林的深处，古树参天，光线昏暗。', 4, 5, 30, 0, 1, 1, 'deep_forest.mp3', 'deep_forest.jpg', 120, 120, 60, 60, '009', '004', NULL, NULL,
 '[{"monster_id":8,"name":"森林熊","level":6,"spawn_rate":0.2,"max_count":2},{"monster_id":9,"name":"树精","level":7,"spawn_rate":0.1,"max_count":1}]',
 '[{"npc_id":6,"name":"隐士","x":80,"y":80,"type":"quest"}]', NULL, 2, 3, 1),

('007', '森林东侧', '森林的东侧区域，有一条小溪流过。', 4, 4, 25, 0, 1, 1, 'forest_east.mp3', 'forest_east.jpg', 100, 80, 20, 40, NULL, NULL, '010', '004',
 '[{"monster_id":10,"name":"水元素","level":5,"spawn_rate":0.3,"max_count":3}]',
 NULL, NULL, 1, 1, 1),

-- 城镇区域
('101', '青云镇', '繁华的中级城镇，各种商店和设施齐全。', 3, 10, 100, 1, 0, 1, 'town.mp3', 'town.jpg', 200, 200, 100, 100, '102', '103', '104', '105',
 NULL,
 '[{"npc_id":11,"name":"镇长","x":100,"y":100,"type":"quest"},{"npc_id":12,"name":"铁匠","x":80,"y":120,"type":"shop"},{"npc_id":13,"name":"魔法师","x":120,"y":80,"type":"shop"},{"npc_id":14,"name":"旅店老板","x":90,"y":90,"type":"service"}]',
 '[{"name":"回新手村","x":100,"y":95,"target":"001","cost":50},{"name":"去王城","x":100,"y":105,"target":"201","cost":200}]', 1, 1, 1),

('102', '青云镇北门', '青云镇的北门，通往北方的荒野。', 1, 10, 40, 0, 1, 1, 'town_gate.mp3', 'town_gate.jpg', 80, 60, 40, 50, '106', '101', NULL, NULL,
 '[{"monster_id":11,"name":"盗贼","level":8,"spawn_rate":0.3,"max_count":3}]',
 '[{"npc_id":15,"name":"守门兵","x":40,"y":30,"type":"guard"}]', NULL, 1, 1, 1),

('103', '青云镇南门', '青云镇的南门，连接南方的道路。', 1, 10, 40, 0, 1, 1, 'town_gate.mp3', 'town_gate.jpg', 80, 60, 40, 10, '101', '107', NULL, NULL,
 '[{"monster_id":12,"name":"流浪者","level":7,"spawn_rate":0.2,"max_count":2}]',
 '[{"npc_id":16,"name":"守门兵","x":40,"y":50,"type":"guard"}]', NULL, 1, 1, 1),

-- 副本区域
('301', '废弃矿洞', '被遗弃的矿洞，里面充满了危险的怪物。', 2, 8, 5, 0, 0, 0, 'dungeon.mp3', 'mine.jpg', 60, 60, 30, 55, NULL, NULL, '302', NULL,
 '[{"monster_id":21,"name":"矿洞蝙蝠","level":8,"spawn_rate":0.5,"max_count":6},{"monster_id":22,"name":"骷髅矿工","level":10,"spawn_rate":0.3,"max_count":4}]',
 NULL, NULL, 3, 3, 1),

('302', '矿洞深层', '矿洞的深层，更加危险的区域。', 2, 12, 5, 0, 0, 0, 'dungeon_deep.mp3', 'mine_deep.jpg', 80, 80, 10, 40, NULL, NULL, '303', '301',
 '[{"monster_id":23,"name":"矿洞守护者","level":15,"spawn_rate":0.2,"max_count":2},{"monster_id":24,"name":"地底蠕虫","level":12,"spawn_rate":0.3,"max_count":3}]',
 NULL, NULL, 3, 3, 1),

('303', '矿洞宝库', '矿洞最深处的宝库，传说中有珍贵的宝藏。', 2, 15, 5, 0, 0, 0, 'treasure.mp3', 'treasure_room.jpg', 40, 40, 20, 20, NULL, NULL, NULL, '302',
 '[{"monster_id":25,"name":"宝库守护兽","level":18,"spawn_rate":0.1,"max_count":1}]',
 NULL, NULL, 3, 3, 1),

-- 高级区域
('201', '王城', '王国的首都，宏伟壮观的城市。', 3, 20, 200, 1, 0, 1, 'capital.mp3', 'capital.jpg', 300, 300, 150, 150, '202', '203', '204', '205',
 NULL,
 '[{"npc_id":31,"name":"国王","x":150,"y":150,"type":"quest"},{"npc_id":32,"name":"皇家铁匠","x":120,"y":180,"type":"shop"},{"npc_id":33,"name":"大法师","x":180,"y":120,"type":"shop"}]',
 '[{"name":"传送到各城镇","x":150,"y":145,"target":"multiple","cost":100}]', 1, 1, 1),

-- 特殊场景
('401', '神秘洞穴', '隐藏在山中的神秘洞穴，据说有古老的秘密。', 5, 25, 10, 0, 1, 0, 'mystery.mp3', 'cave.jpg', 100, 100, 50, 90, NULL, NULL, NULL, NULL,
 '[{"monster_id":41,"name":"洞穴守护者","level":25,"spawn_rate":0.1,"max_count":1},{"monster_id":42,"name":"暗影刺客","level":22,"spawn_rate":0.2,"max_count":2}]',
 '[{"npc_id":41,"name":"神秘老者","x":50,"y":20,"type":"special"}]', NULL, 4, 3, 1),

('402', '天空之城', '漂浮在云端的神秘城市，只有强者才能到达。', 5, 30, 20, 1, 1, 1, 'sky_city.mp3', 'sky_city.jpg', 150, 150, 75, 75, NULL, NULL, NULL, NULL,
 '[{"monster_id":51,"name":"天空守卫","level":30,"spawn_rate":0.2,"max_count":3}]',
 '[{"npc_id":51,"name":"天空之主","x":75,"y":75,"type":"boss"}]',
 '[{"name":"返回地面","x":75,"y":140,"target":"201","cost":0}]', 1, 1, 1);
