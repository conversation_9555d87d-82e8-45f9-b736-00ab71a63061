package com.honghuang.game.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honghuang.game.constant.RankingConstants;
import com.honghuang.game.dto.RankingDTO;
import com.honghuang.game.entity.Ranking;
import com.honghuang.game.mapper.RankingMapper;
import com.honghuang.game.service.RankingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 排行榜服务实现类
 */
@Slf4j
@Service
public class RankingServiceImpl extends ServiceImpl<RankingMapper, Ranking> implements RankingService {

    @Override
    public List<RankingDTO> getRankingList(String type, int limit) {
        List<RankingDTO> list = new ArrayList<>();
        
        try {
            switch (type) {
                case RankingConstants.LEVEL:
                    list = baseMapper.getLevelRanking(limit);
                    break;
                case RankingConstants.EXPERIENCE:
                    list = baseMapper.getExperienceRanking(limit);
                    break;
                case RankingConstants.COPPER:
                    list = baseMapper.getCopperRanking(limit);
                    break;
                case RankingConstants.ATTACK:
                    list = baseMapper.getAttackRanking(limit);
                    break;
                case RankingConstants.DEFENSE:
                    list = baseMapper.getDefenseRanking(limit);
                    break;
                case RankingConstants.AGILITY:
                    list = baseMapper.getAgilityRanking(limit);
                    break;
                case RankingConstants.KILL_MONSTER:
                    list = baseMapper.getKillMonsterRanking(limit);
                    break;
                case RankingConstants.PK_WIN:
                    list = baseMapper.getPkWinRanking(limit);
                    break;
                case RankingConstants.ONLINE_TIME:
                    list = baseMapper.getOnlineTimeRanking(limit);
                    break;
                case RankingConstants.PET_LEVEL:
                    list = baseMapper.getPetLevelRanking(limit);
                    break;
                case RankingConstants.PET_ATTACK:
                    list = baseMapper.getPetAttackRanking(limit);
                    break;
                case RankingConstants.EQUIP_SCORE:
                    list = baseMapper.getEquipScoreRanking(limit);
                    break;
                case RankingConstants.TOTAL_SCORE:
                    list = baseMapper.getTotalScoreRanking(limit);
                    break;
                default:
                    log.warn("未知的排行榜类型: {}", type);
                    return list;
            }
            
            // 设置排名和其他信息
            for (int i = 0; i < list.size(); i++) {
                RankingDTO dto = list.get(i);
                dto.setRank(i + 1);
                dto.setType(type);
                dto.setTypeName(RankingConstants.getRankName(type));
                dto.setUnit(RankingConstants.getRankUnit(type));
                dto.setIsFirst(i == 0);
                if (i == 0) {
                    dto.setFirstTitle(RankingConstants.getFirstTitle(type));
                }
            }
            
        } catch (Exception e) {
            log.error("获取排行榜数据失败, type: {}, limit: {}", type, limit, e);
        }
        
        return list;
    }

    @Override
    public Integer getPlayerRank(Integer roleId, String type) {
        if (roleId == null || type == null) {
            return 0;
        }
        
        try {
            String field = getFieldByType(type);
            if (field == null) {
                return 0;
            }
            
            Integer rank = baseMapper.getPlayerRank(roleId, field);
            return rank != null ? rank : 0;
        } catch (Exception e) {
            log.error("获取玩家排名失败, roleId: {}, type: {}", roleId, type, e);
            return 0;
        }
    }

    @Override
    public void updateRankingData(Integer roleId, String type, Long value) {
        if (roleId == null || type == null || value == null) {
            return;
        }
        
        try {
            // 查找或创建排行榜记录
            Ranking ranking = baseMapper.getByRoleId(roleId);
            if (ranking == null) {
                log.warn("排行榜记录不存在, roleId: {}", roleId);
                return;
            }
            
            LambdaUpdateWrapper<Ranking> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Ranking::getRoleId, roleId);
            
            LocalDateTime now = LocalDateTime.now();
            
            // 根据类型更新对应字段
            switch (type) {
                case RankingConstants.LEVEL:
                    updateWrapper.set(Ranking::getLevel, value.intValue());
                    break;
                case RankingConstants.EXPERIENCE:
                    updateWrapper.set(Ranking::getExperience, value)
                               .set(Ranking::getExperienceTime, now);
                    break;
                case RankingConstants.COPPER:
                    updateWrapper.set(Ranking::getCopper, value)
                               .set(Ranking::getCopperTime, now);
                    break;
                case RankingConstants.ATTACK:
                    updateWrapper.set(Ranking::getAttack, value.intValue())
                               .set(Ranking::getAttackTime, now);
                    break;
                case RankingConstants.DEFENSE:
                    updateWrapper.set(Ranking::getDefense, value.intValue())
                               .set(Ranking::getDefenseTime, now);
                    break;
                case RankingConstants.AGILITY:
                    updateWrapper.set(Ranking::getAgility, value.intValue())
                               .set(Ranking::getAgilityTime, now);
                    break;
                case RankingConstants.KILL_MONSTER:
                    updateWrapper.set(Ranking::getKillMonster, value.intValue())
                               .set(Ranking::getKillMonsterTime, now);
                    break;
                case RankingConstants.PK_WIN:
                    updateWrapper.set(Ranking::getPkWin, value.intValue())
                               .set(Ranking::getPkWinTime, now);
                    break;
                case RankingConstants.ONLINE_TIME:
                    updateWrapper.set(Ranking::getOnlineTime, value.intValue())
                               .set(Ranking::getOnlineTimeUpdate, now);
                    break;
                case RankingConstants.PET_LEVEL:
                    updateWrapper.set(Ranking::getPetLevel, value.intValue())
                               .set(Ranking::getPetLevelTime, now);
                    break;
                case RankingConstants.PET_ATTACK:
                    updateWrapper.set(Ranking::getPetAttack, value.intValue())
                               .set(Ranking::getPetAttackTime, now);
                    break;
                case RankingConstants.EQUIP_SCORE:
                    updateWrapper.set(Ranking::getEquipScore, value.intValue())
                               .set(Ranking::getEquipScoreTime, now);
                    break;
                case RankingConstants.TOTAL_SCORE:
                    updateWrapper.set(Ranking::getTotalScore, value.intValue())
                               .set(Ranking::getTotalScoreTime, now);
                    break;
                default:
                    log.warn("未知的排行榜类型: {}", type);
                    return;
            }
            
            updateWrapper.set(Ranking::getUpdateTime, now);
            update(updateWrapper);
            
        } catch (Exception e) {
            log.error("更新排行榜数据失败, roleId: {}, type: {}, value: {}", roleId, type, value, e);
        }
    }

    @Override
    public void initPlayerRanking(Integer roleId, String roleName, Integer race, Integer level) {
        if (roleId == null || roleName == null) {
            return;
        }
        
        try {
            // 检查是否已存在
            Ranking existing = baseMapper.getByRoleId(roleId);
            if (existing != null) {
                // 更新基本信息
                LambdaUpdateWrapper<Ranking> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Ranking::getRoleId, roleId)
                           .set(Ranking::getRoleName, roleName)
                           .set(Ranking::getRace, race)
                           .set(Ranking::getLevel, level)
                           .set(Ranking::getUpdateTime, LocalDateTime.now());
                update(updateWrapper);
                return;
            }
            
            // 创建新记录
            Ranking ranking = new Ranking();
            ranking.setRoleId(roleId);
            ranking.setRoleName(roleName);
            ranking.setRace(race);
            ranking.setLevel(level != null ? level : 1);
            ranking.setExperience(0L);
            ranking.setCopper(0L);
            ranking.setAttack(0);
            ranking.setDefense(0);
            ranking.setAgility(0);
            ranking.setKillMonster(0);
            ranking.setPkWin(0);
            ranking.setOnlineTime(0);
            ranking.setPetLevel(0);
            ranking.setPetAttack(0);
            ranking.setEquipScore(0);
            ranking.setTotalScore(0);
            ranking.setCreateTime(LocalDateTime.now());
            ranking.setUpdateTime(LocalDateTime.now());
            
            save(ranking);
            
        } catch (Exception e) {
            log.error("初始化玩家排行榜记录失败, roleId: {}, roleName: {}", roleId, roleName, e);
        }
    }

    @Override
    public Map<Integer, List<Map<String, Object>>> getAllRankingCategories() {
        Map<Integer, List<Map<String, Object>>> categories = new HashMap<>();

        // 个人榜
        List<Map<String, Object>> personalRanks = new ArrayList<>();
        addRankInfo(personalRanks, RankingConstants.LEVEL);
        addRankInfo(personalRanks, RankingConstants.EXPERIENCE);
        addRankInfo(personalRanks, RankingConstants.COPPER);
        addRankInfo(personalRanks, RankingConstants.ONLINE_TIME);
        addRankInfo(personalRanks, RankingConstants.TOTAL_SCORE);
        categories.put(RankingConstants.CATEGORY_PERSONAL, personalRanks);

        // 战斗榜
        List<Map<String, Object>> battleRanks = new ArrayList<>();
        addRankInfo(battleRanks, RankingConstants.ATTACK);
        addRankInfo(battleRanks, RankingConstants.DEFENSE);
        addRankInfo(battleRanks, RankingConstants.AGILITY);
        addRankInfo(battleRanks, RankingConstants.KILL_MONSTER);
        addRankInfo(battleRanks, RankingConstants.PK_WIN);
        categories.put(RankingConstants.CATEGORY_BATTLE, battleRanks);

        // 宠物榜
        List<Map<String, Object>> petRanks = new ArrayList<>();
        addRankInfo(petRanks, RankingConstants.PET_LEVEL);
        addRankInfo(petRanks, RankingConstants.PET_ATTACK);
        categories.put(RankingConstants.CATEGORY_PET, petRanks);

        // 装备榜
        List<Map<String, Object>> equipRanks = new ArrayList<>();
        addRankInfo(equipRanks, RankingConstants.EQUIP_SCORE);
        categories.put(RankingConstants.CATEGORY_EQUIPMENT, equipRanks);

        return categories;
    }

    @Override
    public void deletePlayerRanking(Integer roleId) {
        if (roleId == null) {
            return;
        }

        try {
            LambdaQueryWrapper<Ranking> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Ranking::getRoleId, roleId);
            remove(queryWrapper);
        } catch (Exception e) {
            log.error("删除玩家排行榜记录失败, roleId: {}", roleId, e);
        }
    }

    @Override
    public void batchUpdateRankingData(Integer roleId, Map<String, Long> dataMap) {
        if (roleId == null || dataMap == null || dataMap.isEmpty()) {
            return;
        }

        try {
            Ranking ranking = baseMapper.getByRoleId(roleId);
            if (ranking == null) {
                log.warn("排行榜记录不存在, roleId: {}", roleId);
                return;
            }

            LambdaUpdateWrapper<Ranking> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Ranking::getRoleId, roleId);

            LocalDateTime now = LocalDateTime.now();
            boolean hasUpdate = false;

            for (Map.Entry<String, Long> entry : dataMap.entrySet()) {
                String type = entry.getKey();
                Long value = entry.getValue();

                switch (type) {
                    case RankingConstants.LEVEL:
                        updateWrapper.set(Ranking::getLevel, value.intValue());
                        hasUpdate = true;
                        break;
                    case RankingConstants.EXPERIENCE:
                        updateWrapper.set(Ranking::getExperience, value)
                                   .set(Ranking::getExperienceTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.COPPER:
                        updateWrapper.set(Ranking::getCopper, value)
                                   .set(Ranking::getCopperTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.ATTACK:
                        updateWrapper.set(Ranking::getAttack, value.intValue())
                                   .set(Ranking::getAttackTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.DEFENSE:
                        updateWrapper.set(Ranking::getDefense, value.intValue())
                                   .set(Ranking::getDefenseTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.AGILITY:
                        updateWrapper.set(Ranking::getAgility, value.intValue())
                                   .set(Ranking::getAgilityTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.KILL_MONSTER:
                        updateWrapper.set(Ranking::getKillMonster, value.intValue())
                                   .set(Ranking::getKillMonsterTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.PK_WIN:
                        updateWrapper.set(Ranking::getPkWin, value.intValue())
                                   .set(Ranking::getPkWinTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.ONLINE_TIME:
                        updateWrapper.set(Ranking::getOnlineTime, value.intValue())
                                   .set(Ranking::getOnlineTimeUpdate, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.PET_LEVEL:
                        updateWrapper.set(Ranking::getPetLevel, value.intValue())
                                   .set(Ranking::getPetLevelTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.PET_ATTACK:
                        updateWrapper.set(Ranking::getPetAttack, value.intValue())
                                   .set(Ranking::getPetAttackTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.EQUIP_SCORE:
                        updateWrapper.set(Ranking::getEquipScore, value.intValue())
                                   .set(Ranking::getEquipScoreTime, now);
                        hasUpdate = true;
                        break;
                    case RankingConstants.TOTAL_SCORE:
                        updateWrapper.set(Ranking::getTotalScore, value.intValue())
                                   .set(Ranking::getTotalScoreTime, now);
                        hasUpdate = true;
                        break;
                }
            }

            if (hasUpdate) {
                updateWrapper.set(Ranking::getUpdateTime, now);
                update(updateWrapper);
            }

        } catch (Exception e) {
            log.error("批量更新排行榜数据失败, roleId: {}, dataMap: {}", roleId, dataMap, e);
        }
    }

    /**
     * 添加排行榜信息到列表
     */
    private void addRankInfo(List<Map<String, Object>> list, String type) {
        Map<String, Object> rankInfo = new HashMap<>();
        rankInfo.put("type", type);
        rankInfo.put("name", RankingConstants.getRankName(type));
        rankInfo.put("description", RankingConstants.getRankDescription(type));
        rankInfo.put("unit", RankingConstants.getRankUnit(type));
        list.add(rankInfo);
    }

    /**
     * 根据类型获取对应的数据库字段名
     */
    private String getFieldByType(String type) {
        switch (type) {
            case RankingConstants.LEVEL:
                return "level";
            case RankingConstants.EXPERIENCE:
                return "experience";
            case RankingConstants.COPPER:
                return "copper";
            case RankingConstants.ATTACK:
                return "attack";
            case RankingConstants.DEFENSE:
                return "defense";
            case RankingConstants.AGILITY:
                return "agility";
            case RankingConstants.KILL_MONSTER:
                return "kill_monster";
            case RankingConstants.PK_WIN:
                return "pk_win";
            case RankingConstants.ONLINE_TIME:
                return "online_time";
            case RankingConstants.PET_LEVEL:
                return "pet_level";
            case RankingConstants.PET_ATTACK:
                return "pet_attack";
            case RankingConstants.EQUIP_SCORE:
                return "equip_score";
            case RankingConstants.TOTAL_SCORE:
                return "total_score";
            default:
                return null;
        }
    }
}
