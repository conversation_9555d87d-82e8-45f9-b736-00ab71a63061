-- 洪荒游戏 MySQL 数据库初始化脚本
-- 基于当前Spring Boot项目的实体类结构

-- 创建数据库
CREATE DATABASE IF NOT EXISTS jygame DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE jygame;

-- 1. 用户信息表
CREATE TABLE IF NOT EXISTS user_info (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(20) NOT NULL UNIQUE COMMENT '用户登录名',
    password VARCHAR(60) NOT NULL COMMENT '用户登录密码',
    login_state INT DEFAULT 0 COMMENT '登录状态 1为登录 0为未登录',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_login_ip VARCHAR(20) COMMENT '最后登录IP地址',
    last_login_time DATETIME COMMENT '最后一次登录时间',
    yuanbao INT DEFAULT 0 COMMENT '元宝数量',
    jifen INT DEFAULT 0 COMMENT '商城积分数量',
    super_channel VARCHAR(255) COMMENT '超级渠道',
    channel VARCHAR(255) DEFAULT 'web' COMMENT '渠道来源',
    email VARCHAR(100) COMMENT '邮箱地址',
    phone VARCHAR(20) COMMENT '手机号码',
    status INT DEFAULT 1 COMMENT '账号状态 1正常 0禁用',
    deleted INT DEFAULT 0 COMMENT '是否删除 0否 1是',
    INDEX idx_username (username),
    INDEX idx_login_state (login_state),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 2. 玩家角色表
CREATE TABLE IF NOT EXISTS player_role (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '角色ID',
    user_id INT NOT NULL COMMENT '用户ID',
    name VARCHAR(20) NOT NULL UNIQUE COMMENT '角色名称',
    sex INT DEFAULT 1 COMMENT '性别 1男 2女',
    race INT DEFAULT 1 COMMENT '种族 1巫族 2妖族',
    level INT DEFAULT 1 COMMENT '等级',
    experience BIGINT DEFAULT 0 COMMENT '经验值',
    current_hp INT DEFAULT 100 COMMENT '当前生命值',
    max_hp INT DEFAULT 100 COMMENT '最大生命值',
    current_mp INT DEFAULT 50 COMMENT '当前魔法值',
    max_mp INT DEFAULT 50 COMMENT '最大魔法值',
    base_attack INT DEFAULT 10 COMMENT '基础攻击力',
    base_defense INT DEFAULT 5 COMMENT '基础防御力',
    base_agility INT DEFAULT 10 COMMENT '基础敏捷',
    base_intelligence INT DEFAULT 10 COMMENT '基础智力',
    base_constitution INT DEFAULT 10 COMMENT '基础体质',
    attribute_points INT DEFAULT 0 COMMENT '可分配属性点',
    copper BIGINT DEFAULT 1000 COMMENT '铜钱',
    silver BIGINT DEFAULT 0 COMMENT '银两',
    gold BIGINT DEFAULT 0 COMMENT '金币',
    scene_id INT DEFAULT 1 COMMENT '当前场景ID',
    scene_name VARCHAR(50) DEFAULT '新手村' COMMENT '当前场景名称',
    online_status INT DEFAULT 0 COMMENT '在线状态 0离线 1在线',
    last_login_time DATETIME COMMENT '最后登录时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted INT DEFAULT 0 COMMENT '是否删除 0否 1是',
    FOREIGN KEY (user_id) REFERENCES user_info(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_name (name),
    INDEX idx_level (level),
    INDEX idx_scene_id (scene_id),
    INDEX idx_online_status (online_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='玩家角色表';

-- 3. 物品表
CREATE TABLE IF NOT EXISTS item (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '物品ID',
    name VARCHAR(100) NOT NULL COMMENT '物品名称',
    type INT NOT NULL COMMENT '物品类型 1装备 2消耗品 3材料 4任务物品 5宝石 6书籍 7其他',
    sub_type INT COMMENT '物品子类型',
    quality INT DEFAULT 1 COMMENT '物品品质 1白色 2绿色 3蓝色 4紫色 5橙色 6红色',
    required_level INT DEFAULT 1 COMMENT '使用等级要求',
    required_race INT DEFAULT 0 COMMENT '种族要求 0无要求 1巫族 2妖族',
    required_sex INT DEFAULT 0 COMMENT '性别要求 0无要求 1男 2女',
    description TEXT COMMENT '物品描述',
    icon VARCHAR(100) COMMENT '物品图标',
    sell_price BIGINT DEFAULT 0 COMMENT '出售价格',
    buy_price BIGINT DEFAULT 0 COMMENT '购买价格',
    stackable INT DEFAULT 0 COMMENT '是否可堆叠 0否 1是',
    max_stack INT DEFAULT 1 COMMENT '最大堆叠数量',
    tradeable INT DEFAULT 1 COMMENT '是否可交易 0否 1是',
    droppable INT DEFAULT 1 COMMENT '是否可丢弃 0否 1是',
    effect_type INT DEFAULT 0 COMMENT '使用效果类型 0无效果 1恢复HP 2恢复MP 3增加经验 4学习技能 5其他',
    effect_value INT DEFAULT 0 COMMENT '使用效果值',
    cooldown INT DEFAULT 0 COMMENT '使用冷却时间（秒）',
    active INT DEFAULT 1 COMMENT '是否激活 0未激活 1已激活',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_type (type),
    INDEX idx_quality (quality),
    INDEX idx_required_level (required_level),
    INDEX idx_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物品表';

-- 4. 背包表
CREATE TABLE IF NOT EXISTS inventory (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '背包记录ID',
    role_id INT NOT NULL COMMENT '角色ID',
    item_id INT NOT NULL COMMENT '物品ID',
    slot_index INT NOT NULL COMMENT '背包位置索引',
    quantity INT DEFAULT 1 COMMENT '物品数量',
    bound INT DEFAULT 0 COMMENT '是否绑定 0否 1是',
    obtain_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    last_use_time DATETIME COMMENT '最后使用时间',
    FOREIGN KEY (role_id) REFERENCES player_role(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES item(id) ON DELETE CASCADE,
    UNIQUE KEY uk_role_slot (role_id, slot_index),
    INDEX idx_role_id (role_id),
    INDEX idx_item_id (item_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='背包表';

-- 5. 怪物信息表
CREATE TABLE IF NOT EXISTS npc_info (
    npc_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'NPC ID',
    npc_name VARCHAR(50) NOT NULL COMMENT 'NPC名称',
    npc_type INT DEFAULT 1 COMMENT 'NPC类型 1怪物 2商人 3任务NPC',
    npc_level INT DEFAULT 1 COMMENT 'NPC等级',
    npc_hp INT DEFAULT 100 COMMENT 'NPC生命值',
    npc_attack INT DEFAULT 10 COMMENT 'NPC攻击力',
    npc_defense INT DEFAULT 5 COMMENT 'NPC防御力',
    npc_agility INT DEFAULT 10 COMMENT 'NPC敏捷',
    exp_reward INT DEFAULT 0 COMMENT '击败获得经验',
    copper_reward INT DEFAULT 0 COMMENT '击败获得铜钱',
    npc_description TEXT COMMENT 'NPC描述',
    is_active INT DEFAULT 1 COMMENT '是否激活 0否 1是',
    INDEX idx_npc_type (npc_type),
    INDEX idx_npc_level (npc_level),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='NPC信息表';

-- 6. 场景信息表
CREATE TABLE IF NOT EXISTS scene_info (
    scene_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '场景ID',
    scene_name VARCHAR(50) NOT NULL COMMENT '场景名称',
    scene_description TEXT COMMENT '场景描述',
    scene_type INT DEFAULT 1 COMMENT '场景类型 1普通 2副本 3安全区',
    level_limit INT DEFAULT 1 COMMENT '等级限制',
    max_players INT DEFAULT 100 COMMENT '最大玩家数',
    is_safe_zone INT DEFAULT 0 COMMENT '是否安全区 0否 1是',
    allow_pk INT DEFAULT 1 COMMENT '是否允许PK 0否 1是',
    allow_teleport INT DEFAULT 1 COMMENT '是否允许传送 0否 1是',
    background_music VARCHAR(100) COMMENT '背景音乐',
    background_image VARCHAR(100) COMMENT '背景图片',
    map_width INT DEFAULT 100 COMMENT '地图宽度',
    map_height INT DEFAULT 100 COMMENT '地图高度',
    spawn_x INT DEFAULT 50 COMMENT '出生点X坐标',
    spawn_y INT DEFAULT 50 COMMENT '出生点Y坐标',
    north_scene VARCHAR(10) COMMENT '北方场景',
    south_scene VARCHAR(10) COMMENT '南方场景',
    east_scene VARCHAR(10) COMMENT '东方场景',
    west_scene VARCHAR(10) COMMENT '西方场景',
    monster_config TEXT COMMENT '怪物配置JSON',
    npc_config TEXT COMMENT 'NPC配置JSON',
    teleport_config TEXT COMMENT '传送点配置JSON',
    weather_type INT DEFAULT 1 COMMENT '天气类型',
    time_type INT DEFAULT 1 COMMENT '时间类型',
    is_active INT DEFAULT 1 COMMENT '是否激活 0否 1是',
    INDEX idx_scene_type (scene_type),
    INDEX idx_level_limit (level_limit),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='场景信息表';

-- 7. 商品信息表（兼容原系统）
CREATE TABLE IF NOT EXISTS goods_info (
    goods_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
    goods_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    goods_type INT NOT NULL COMMENT '商品类型',
    goods_sub_type INT COMMENT '商品子类型',
    goods_level INT DEFAULT 1 COMMENT '商品等级',
    goods_quality INT DEFAULT 1 COMMENT '商品品质',
    goods_description TEXT COMMENT '商品描述',
    is_stackable INT DEFAULT 0 COMMENT '是否可堆叠',
    max_stack INT DEFAULT 1 COMMENT '最大堆叠数量',
    is_tradeable INT DEFAULT 1 COMMENT '是否可交易',
    is_droppable INT DEFAULT 1 COMMENT '是否可丢弃',
    buy_price BIGINT DEFAULT 0 COMMENT '购买价格',
    sell_price BIGINT DEFAULT 0 COMMENT '出售价格',
    use_level_limit INT DEFAULT 1 COMMENT '使用等级限制',
    sex_limit INT DEFAULT 0 COMMENT '性别限制',
    race_limit INT DEFAULT 0 COMMENT '种族限制',
    attack_bonus INT DEFAULT 0 COMMENT '攻击力加成',
    defense_bonus INT DEFAULT 0 COMMENT '防御力加成',
    hp_bonus INT DEFAULT 0 COMMENT '生命值加成',
    mp_bonus INT DEFAULT 0 COMMENT '魔法值加成',
    force_bonus INT DEFAULT 0 COMMENT '力量加成',
    agile_bonus INT DEFAULT 0 COMMENT '敏捷加成',
    physique_bonus INT DEFAULT 0 COMMENT '体质加成',
    savvy_bonus INT DEFAULT 0 COMMENT '悟性加成',
    is_active INT DEFAULT 1 COMMENT '是否激活',
    INDEX idx_goods_type (goods_type),
    INDEX idx_goods_level (goods_level),
    INDEX idx_goods_quality (goods_quality),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品信息表';

-- 8. 用户背包表（兼容原系统）
CREATE TABLE IF NOT EXISTS u_part_goods (
    goods_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '背包物品ID',
    p_pk INT NOT NULL COMMENT '角色ID',
    goods_position INT NOT NULL COMMENT '物品位置',
    position_index INT NOT NULL COMMENT '位置索引',
    goods_type INT NOT NULL COMMENT '物品类型',
    goods_name VARCHAR(100) COMMENT '物品名称',
    goods_number INT DEFAULT 1 COMMENT '物品数量',
    goods_durability INT DEFAULT 100 COMMENT '物品耐久度',
    goods_bind INT DEFAULT 0 COMMENT '是否绑定',
    goods_protect INT DEFAULT 0 COMMENT '是否保护',
    goods_isReconfirm INT DEFAULT 0 COMMENT '是否需要确认',
    goods_yb_bind INT DEFAULT 0 COMMENT '元宝绑定',
    goods_sell_price BIGINT DEFAULT 0 COMMENT '出售价格',
    INDEX idx_p_pk (p_pk),
    INDEX idx_goods_position (goods_position),
    INDEX idx_position_index (position_index)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户背包表';

-- 9. 战斗记录表
CREATE TABLE IF NOT EXISTS battle_record (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '战斗记录ID',
    player_role_id INT NOT NULL COMMENT '玩家角色ID',
    monster_id INT COMMENT '怪物ID',
    battle_type INT DEFAULT 1 COMMENT '战斗类型 1PVE 2PVP',
    battle_result INT COMMENT '战斗结果 1胜利 2失败 3逃跑',
    exp_gained INT DEFAULT 0 COMMENT '获得经验',
    copper_gained BIGINT DEFAULT 0 COMMENT '获得铜钱',
    items_gained TEXT COMMENT '获得物品JSON',
    battle_duration INT DEFAULT 0 COMMENT '战斗持续时间（秒）',
    battle_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '战斗时间',
    FOREIGN KEY (player_role_id) REFERENCES player_role(id) ON DELETE CASCADE,
    INDEX idx_player_role_id (player_role_id),
    INDEX idx_battle_type (battle_type),
    INDEX idx_battle_time (battle_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='战斗记录表';

-- 10. 技能表
CREATE TABLE IF NOT EXISTS skill (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '技能ID',
    name VARCHAR(50) NOT NULL COMMENT '技能名称',
    type INT DEFAULT 1 COMMENT '技能类型 1攻击 2防御 3辅助 4被动',
    level INT DEFAULT 1 COMMENT '技能等级',
    max_level INT DEFAULT 10 COMMENT '最大等级',
    required_level INT DEFAULT 1 COMMENT '学习等级要求',
    required_race INT DEFAULT 0 COMMENT '种族要求 0无要求 1巫族 2妖族',
    required_sex INT DEFAULT 0 COMMENT '性别要求 0无要求 1男 2女',
    mp_cost INT DEFAULT 0 COMMENT '魔法消耗',
    cooldown INT DEFAULT 0 COMMENT '冷却时间（秒）',
    damage_min INT DEFAULT 0 COMMENT '最小伤害',
    damage_max INT DEFAULT 0 COMMENT '最大伤害',
    heal_min INT DEFAULT 0 COMMENT '最小治疗',
    heal_max INT DEFAULT 0 COMMENT '最大治疗',
    buff_type INT DEFAULT 0 COMMENT 'BUFF类型',
    buff_duration INT DEFAULT 0 COMMENT 'BUFF持续时间',
    description TEXT COMMENT '技能描述',
    icon VARCHAR(100) COMMENT '技能图标',
    is_active INT DEFAULT 1 COMMENT '是否激活 0否 1是',
    INDEX idx_type (type),
    INDEX idx_level (level),
    INDEX idx_required_level (required_level),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技能表';

-- 11. 玩家技能表
CREATE TABLE IF NOT EXISTS player_skill (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '玩家技能ID',
    role_id INT NOT NULL COMMENT '角色ID',
    skill_id INT NOT NULL COMMENT '技能ID',
    skill_level INT DEFAULT 1 COMMENT '技能等级',
    skill_exp INT DEFAULT 0 COMMENT '技能经验',
    learn_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '学习时间',
    last_use_time DATETIME COMMENT '最后使用时间',
    FOREIGN KEY (role_id) REFERENCES player_role(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skill(id) ON DELETE CASCADE,
    UNIQUE KEY uk_role_skill (role_id, skill_id),
    INDEX idx_role_id (role_id),
    INDEX idx_skill_id (skill_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='玩家技能表';

-- 12. 任务表
CREATE TABLE IF NOT EXISTS quest (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '任务ID',
    name VARCHAR(100) NOT NULL COMMENT '任务名称',
    type INT DEFAULT 1 COMMENT '任务类型 1主线 2支线 3日常 4活动',
    level_required INT DEFAULT 1 COMMENT '接取等级要求',
    race_required INT DEFAULT 0 COMMENT '种族要求 0无要求 1巫族 2妖族',
    sex_required INT DEFAULT 0 COMMENT '性别要求 0无要求 1男 2女',
    prerequisite_quest INT COMMENT '前置任务ID',
    description TEXT COMMENT '任务描述',
    objective TEXT COMMENT '任务目标',
    reward_exp INT DEFAULT 0 COMMENT '经验奖励',
    reward_copper BIGINT DEFAULT 0 COMMENT '铜钱奖励',
    reward_items TEXT COMMENT '物品奖励JSON',
    is_repeatable INT DEFAULT 0 COMMENT '是否可重复 0否 1是',
    is_active INT DEFAULT 1 COMMENT '是否激活 0否 1是',
    FOREIGN KEY (prerequisite_quest) REFERENCES quest(id) ON DELETE SET NULL,
    INDEX idx_type (type),
    INDEX idx_level_required (level_required),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 13. 玩家任务表
CREATE TABLE IF NOT EXISTS player_quest (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '玩家任务ID',
    role_id INT NOT NULL COMMENT '角色ID',
    quest_id INT NOT NULL COMMENT '任务ID',
    status INT DEFAULT 1 COMMENT '任务状态 1进行中 2已完成 3已放弃',
    progress TEXT COMMENT '任务进度JSON',
    accept_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '接取时间',
    complete_time DATETIME COMMENT '完成时间',
    FOREIGN KEY (role_id) REFERENCES player_role(id) ON DELETE CASCADE,
    FOREIGN KEY (quest_id) REFERENCES quest(id) ON DELETE CASCADE,
    INDEX idx_role_id (role_id),
    INDEX idx_quest_id (quest_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='玩家任务表';

-- 14. 好友关系表
CREATE TABLE IF NOT EXISTS friend (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '好友关系ID',
    role_id INT NOT NULL COMMENT '角色ID',
    friend_role_id INT NOT NULL COMMENT '好友角色ID',
    status INT DEFAULT 1 COMMENT '关系状态 1好友 2黑名单',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '建立时间',
    FOREIGN KEY (role_id) REFERENCES player_role(id) ON DELETE CASCADE,
    FOREIGN KEY (friend_role_id) REFERENCES player_role(id) ON DELETE CASCADE,
    UNIQUE KEY uk_role_friend (role_id, friend_role_id),
    INDEX idx_role_id (role_id),
    INDEX idx_friend_role_id (friend_role_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='好友关系表';

-- 15. 聊天消息表
CREATE TABLE IF NOT EXISTS chat_message (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
    sender_id INT NOT NULL COMMENT '发送者角色ID',
    receiver_id INT COMMENT '接收者角色ID（私聊时使用）',
    channel_type INT DEFAULT 1 COMMENT '频道类型 1世界 2私聊 3帮派 4队伍',
    message_type INT DEFAULT 1 COMMENT '消息类型 1文本 2表情 3系统',
    content TEXT NOT NULL COMMENT '消息内容',
    send_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    is_read INT DEFAULT 0 COMMENT '是否已读 0否 1是',
    FOREIGN KEY (sender_id) REFERENCES player_role(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES player_role(id) ON DELETE CASCADE,
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_channel_type (channel_type),
    INDEX idx_send_time (send_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 16. 商店表
CREATE TABLE IF NOT EXISTS shop (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商店ID',
    name VARCHAR(50) NOT NULL COMMENT '商店名称',
    type INT DEFAULT 1 COMMENT '商店类型 1普通商店 2特殊商店 3拍卖行',
    npc_id INT COMMENT '关联NPC ID',
    description TEXT COMMENT '商店描述',
    is_active INT DEFAULT 1 COMMENT '是否激活 0否 1是',
    INDEX idx_type (type),
    INDEX idx_npc_id (npc_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商店表';

-- 17. 商店商品表
CREATE TABLE IF NOT EXISTS shop_item (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商店商品ID',
    shop_id INT NOT NULL COMMENT '商店ID',
    item_id INT NOT NULL COMMENT '物品ID',
    price BIGINT NOT NULL COMMENT '价格',
    currency_type INT DEFAULT 1 COMMENT '货币类型 1铜钱 2银两 3金币 4元宝',
    stock INT DEFAULT -1 COMMENT '库存数量 -1表示无限',
    refresh_type INT DEFAULT 0 COMMENT '刷新类型 0不刷新 1每日刷新 2每周刷新',
    is_active INT DEFAULT 1 COMMENT '是否激活 0否 1是',
    FOREIGN KEY (shop_id) REFERENCES shop(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES item(id) ON DELETE CASCADE,
    INDEX idx_shop_id (shop_id),
    INDEX idx_item_id (item_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商店商品表';

-- 18. 装备表（兼容原系统）
CREATE TABLE IF NOT EXISTS accouter (
    acc_ID INT AUTO_INCREMENT PRIMARY KEY COMMENT '装备ID',
    acc_Name VARCHAR(50) COMMENT '装备名称',
    acc_sex INT COMMENT '性别要求',
    acc_Intro_ID INT COMMENT '品质',
    acc_durability INT COMMENT '耐久度',
    acc_Def_da INT COMMENT '防御力上限',
    acc_Def_xiao INT COMMENT '防御力下限',
    acc_display VARCHAR(150) COMMENT '说明',
    acc_ReLevel INT COMMENT '使用等级',
    acc_bonding INT COMMENT '绑定 1绑定 2拾取绑 3装备绑',
    acc_sell INT COMMENT '出售价格',
    acc_job VARCHAR(100) COMMENT '职业',
    acc_drop VARCHAR(100) COMMENT '掉落率',
    acc_class INT COMMENT '装备类型',
    acc_type INT COMMENT '装备子类型',
    acc_protect INT COMMENT '保护',
    acc_isReconfirm INT COMMENT '是否需要确认',
    acc_price INT COMMENT '价格',
    acc_HP INT COMMENT '生命值加成',
    acc_MP INT COMMENT '魔法值加成',
    acc_jin INT COMMENT '金属性',
    acc_mu INT COMMENT '木属性',
    acc_shui INT COMMENT '水属性',
    acc_huo INT COMMENT '火属性',
    acc_tu INT COMMENT '土属性',
    acc_gj INT COMMENT '攻击力',
    acc_fy INT COMMENT '防御力',
    acc_agile INT COMMENT '敏捷',
    acc_savvy INT COMMENT '悟性',
    acc_force INT COMMENT '力量',
    acc_physique INT COMMENT '体质',
    INDEX idx_acc_class (acc_class),
    INDEX idx_acc_ReLevel (acc_ReLevel)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='装备表';

-- ========================================
-- 基础数据插入
-- ========================================

-- 插入默认场景数据
INSERT IGNORE INTO scene_info (scene_id, scene_name, scene_description, scene_type, level_limit, is_safe_zone, allow_pk, spawn_x, spawn_y) VALUES
(1, '新手村', '新手玩家的起始地点，这里安全祥和，适合初学者熟悉游戏', 1, 1, 1, 0, 50, 50),
(2, '青草原', '绿草如茵的平原，有一些温和的小动物', 1, 5, 0, 1, 50, 50),
(3, '幽暗森林', '阴暗的森林，栖息着各种危险的野兽', 1, 10, 0, 1, 50, 50),
(4, '荒芜沙漠', '炎热干燥的沙漠，只有强者才能生存', 1, 20, 0, 1, 50, 50),
(5, '冰雪山峰', '终年积雪的高山，寒风刺骨', 1, 30, 0, 1, 50, 50);

-- 插入基础怪物数据
INSERT IGNORE INTO npc_info (npc_id, npc_name, npc_type, npc_level, npc_hp, npc_attack, npc_defense, npc_agility, exp_reward, copper_reward) VALUES
(1, '小野兔', 1, 1, 20, 3, 1, 8, 5, 10),
(2, '野狼', 1, 5, 80, 12, 5, 15, 25, 50),
(3, '森林熊', 1, 10, 200, 25, 12, 10, 60, 120),
(4, '沙漠蝎', 1, 15, 300, 35, 18, 20, 90, 180),
(5, '雪狼', 1, 20, 450, 50, 25, 25, 150, 300),
(6, '冰霜巨人', 1, 30, 800, 80, 40, 15, 300, 600);

-- 插入基础物品数据
INSERT IGNORE INTO item (id, name, type, sub_type, quality, required_level, description, sell_price, buy_price, stackable, max_stack, effect_type, effect_value) VALUES
(1, '新手剑', 1, 1, 1, 1, '新手专用的铁剑，虽然简陋但很实用', 50, 100, 0, 1, 0, 0),
(2, '布衣', 1, 2, 1, 1, '简单的布制衣服，提供基础防护', 30, 60, 0, 1, 0, 0),
(3, '生命药水', 2, 1, 1, 1, '恢复少量生命值的药水', 10, 20, 1, 99, 1, 50),
(4, '魔法药水', 2, 2, 1, 1, '恢复少量魔法值的药水', 15, 30, 1, 99, 2, 30),
(5, '面包', 2, 3, 1, 1, '简单的食物，可以恢复少量生命值', 5, 10, 1, 99, 1, 20),
(6, '铁剑', 1, 1, 2, 5, '锋利的铁制长剑，攻击力不错', 200, 400, 0, 1, 0, 0),
(7, '皮甲', 1, 2, 2, 5, '柔韧的皮制护甲，提供良好的防护', 150, 300, 0, 1, 0, 0),
(8, '高级生命药水', 2, 1, 2, 10, '恢复大量生命值的药水', 50, 100, 1, 99, 1, 150),
(9, '高级魔法药水', 2, 2, 2, 10, '恢复大量魔法值的药水', 75, 150, 1, 99, 2, 100),
(10, '经验卷轴', 2, 4, 3, 1, '使用后可以获得额外经验值', 100, 200, 1, 10, 3, 100);

-- 插入基础技能数据
INSERT IGNORE INTO skill (id, name, type, level, max_level, required_level, mp_cost, cooldown, damage_min, damage_max, description) VALUES
(1, '普通攻击', 1, 1, 1, 1, 0, 0, 5, 10, '最基础的攻击技能'),
(2, '重击', 1, 1, 5, 5, 10, 3, 15, 25, '消耗魔法值进行强力攻击'),
(3, '治疗术', 3, 1, 5, 3, 15, 5, 0, 0, '恢复自己或队友的生命值'),
(4, '火球术', 1, 1, 5, 8, 20, 2, 20, 35, '发射火球攻击敌人'),
(5, '防御姿态', 2, 1, 3, 10, 5, 0, 0, 0, '提高自身防御力'),
(6, '疾风步', 3, 1, 3, 12, 12, 8, 0, 0, '提高移动速度和闪避率');

-- 插入基础任务数据
INSERT IGNORE INTO quest (id, name, type, level_required, description, objective, reward_exp, reward_copper) VALUES
(1, '新手指引', 1, 1, '欢迎来到洪荒世界！让我们从基础开始学习', '击败3只小野兔', 50, 100),
(2, '装备升级', 1, 3, '是时候更换更好的装备了', '购买一把铁剑', 80, 150),
(3, '探索世界', 1, 5, '走出新手村，探索更广阔的世界', '前往青草原', 120, 200),
(4, '狼群威胁', 2, 8, '青草原的狼群威胁到了村民的安全', '击败5只野狼', 200, 400),
(5, '森林探险', 2, 12, '幽暗森林中隐藏着许多秘密', '在幽暗森林中探索30分钟', 300, 600);

-- 插入基础商店数据
INSERT IGNORE INTO shop (id, name, type, description) VALUES
(1, '新手商店', 1, '为新手玩家提供基础装备和物品'),
(2, '武器店', 1, '专门出售各种武器装备'),
(3, '药剂店', 1, '出售各种恢复药剂和魔法物品'),
(4, '杂货店', 1, '出售日常用品和材料');

-- 插入商店商品数据
INSERT IGNORE INTO shop_item (shop_id, item_id, price, currency_type, stock) VALUES
(1, 1, 100, 1, -1),  -- 新手剑
(1, 2, 60, 1, -1),   -- 布衣
(1, 3, 20, 1, -1),   -- 生命药水
(1, 4, 30, 1, -1),   -- 魔法药水
(1, 5, 10, 1, -1),   -- 面包
(2, 6, 400, 1, -1),  -- 铁剑
(2, 7, 300, 1, -1),  -- 皮甲
(3, 8, 100, 1, -1),  -- 高级生命药水
(3, 9, 150, 1, -1),  -- 高级魔法药水
(4, 10, 200, 1, 10); -- 经验卷轴

-- 创建测试用户（可选）
INSERT IGNORE INTO user_info (username, password, yuanbao, jifen) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tms6YdAyTqn3jXgC0Km', 10000, 5000),  -- 密码: admin123
('test', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tms6YdAyTqn3jXgC0Km', 1000, 500);    -- 密码: admin123

-- 为测试用户创建角色（可选）
INSERT IGNORE INTO player_role (user_id, name, sex, race, level, experience, current_hp, max_hp, current_mp, max_mp, copper, scene_id, scene_name) VALUES
(1, '管理员', 1, 1, 10, 1000, 200, 200, 100, 100, 10000, 1, '新手村'),
(2, '测试角色', 2, 2, 5, 300, 120, 120, 80, 80, 2000, 1, '新手村');

COMMIT;
