package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.PlayerPosition;
import com.honghuang.game.entity.Scene;
import com.honghuang.game.entity.TeleportPoint;
import com.honghuang.game.service.SceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 场景控制器
 */
@RestController
@RequestMapping("/api/scene")
public class SceneController {

    @Autowired
    private SceneService sceneService;

    /**
     * 获取所有激活的场景
     */
    @GetMapping("/list")
    public Result<List<Scene>> getAllActiveScenes() {
        try {
            List<Scene> scenes = sceneService.getAllActiveScenes();
            return Result.success("获取场景列表成功", scenes);
        } catch (Exception e) {
            return Result.error(500, "获取场景列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取场景
     */
    @GetMapping("/type/{type}")
    public Result<List<Scene>> getScenesByType(@PathVariable Integer type) {
        try {
            List<Scene> scenes = sceneService.getScenesByType(type);
            return Result.success("获取场景列表成功", scenes);
        } catch (Exception e) {
            return Result.error(500, "获取场景列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取适合角色等级的场景
     */
    @GetMapping("/suitable")
    public Result<List<Scene>> getSuitableScenes(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            // 这里简化处理，实际应该获取角色等级
            List<Scene> scenes = sceneService.getSuitableScenes(1);
            return Result.success("获取适合场景成功", scenes);
        } catch (Exception e) {
            return Result.error(500, "获取适合场景失败: " + e.getMessage());
        }
    }

    /**
     * 获取场景详情
     */
    @GetMapping("/{sceneId}")
    public Result<Scene> getSceneDetail(@PathVariable Integer sceneId) {
        try {
            Scene scene = sceneService.getSceneDetail(sceneId);
            return Result.success("获取场景详情成功", scene);
        } catch (Exception e) {
            return Result.error(500, "获取场景详情失败: " + e.getMessage());
        }
    }

    /**
     * 进入场景
     */
    @PostMapping("/{sceneId}/enter")
    public Result<Void> enterScene(@PathVariable Integer sceneId,
                                  @RequestBody Map<String, Integer> request,
                                  HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            Integer x = request.get("x");
            Integer y = request.get("y");

            boolean success = sceneService.enterScene(currentRoleId, sceneId, x, y);
            if (success) {
                return Result.success("进入场景成功");
            } else {
                return Result.error(400, "进入场景失败");
            }
        } catch (Exception e) {
            return Result.error(500, "进入场景失败: " + e.getMessage());
        }
    }

    /**
     * 玩家移动
     */
    @PostMapping("/move")
    public Result<Void> movePlayer(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            Integer x = request.get("x");
            Integer y = request.get("y");
            Integer direction = request.get("direction");

            if (x == null || y == null) {
                return Result.error(400, "坐标不能为空");
            }

            boolean success = sceneService.movePlayer(currentRoleId, x, y, direction);
            if (success) {
                return Result.success("移动成功");
            } else {
                return Result.error(400, "移动失败");
            }
        } catch (Exception e) {
            return Result.error(500, "移动失败: " + e.getMessage());
        }
    }

    /**
     * 使用传送点
     */
    @PostMapping("/teleport/{teleportPointId}")
    public Result<Void> useTeleportPoint(@PathVariable Integer teleportPointId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            boolean success = sceneService.useTeleportPoint(currentRoleId, teleportPointId);
            if (success) {
                return Result.success("传送成功");
            } else {
                return Result.error(400, "传送失败");
            }
        } catch (Exception e) {
            return Result.error(500, "传送失败: " + e.getMessage());
        }
    }

    /**
     * 获取玩家当前位置
     */
    @GetMapping("/position")
    public Result<PlayerPosition> getPlayerPosition(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            PlayerPosition position = sceneService.getPlayerPosition(currentRoleId);
            return Result.success("获取位置成功", position);
        } catch (Exception e) {
            return Result.error(500, "获取位置失败: " + e.getMessage());
        }
    }

    /**
     * 获取场景中的在线玩家
     */
    @GetMapping("/{sceneId}/players")
    public Result<List<PlayerPosition>> getOnlinePlayersInScene(@PathVariable Integer sceneId) {
        try {
            List<PlayerPosition> players = sceneService.getOnlinePlayersInScene(sceneId);
            return Result.success("获取在线玩家成功", players);
        } catch (Exception e) {
            return Result.error(500, "获取在线玩家失败: " + e.getMessage());
        }
    }

    /**
     * 获取附近的玩家
     */
    @GetMapping("/nearby/players")
    public Result<List<PlayerPosition>> getNearbyPlayers(@RequestParam(defaultValue = "50") Integer range,
                                                        HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            List<PlayerPosition> players = sceneService.getNearbyPlayers(currentRoleId, range);
            return Result.success("获取附近玩家成功", players);
        } catch (Exception e) {
            return Result.error(500, "获取附近玩家失败: " + e.getMessage());
        }
    }

    /**
     * 获取场景的传送点
     */
    @GetMapping("/{sceneId}/teleports")
    public Result<List<TeleportPoint>> getSceneTeleportPoints(@PathVariable Integer sceneId) {
        try {
            List<TeleportPoint> teleportPoints = sceneService.getSceneTeleportPoints(sceneId);
            return Result.success("获取传送点成功", teleportPoints);
        } catch (Exception e) {
            return Result.error(500, "获取传送点失败: " + e.getMessage());
        }
    }

    /**
     * 获取附近的传送点
     */
    @GetMapping("/nearby/teleports")
    public Result<List<TeleportPoint>> getNearbyTeleportPoints(@RequestParam(defaultValue = "10") Integer range,
                                                              HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            List<TeleportPoint> teleportPoints = sceneService.getNearbyTeleportPoints(currentRoleId, range);
            return Result.success("获取附近传送点成功", teleportPoints);
        } catch (Exception e) {
            return Result.error(500, "获取附近传送点失败: " + e.getMessage());
        }
    }

    /**
     * 回到上次位置
     */
    @PostMapping("/return")
    public Result<Void> returnToLastPosition(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            boolean success = sceneService.returnToLastPosition(currentRoleId);
            if (success) {
                return Result.success("回到上次位置成功");
            } else {
                return Result.error(400, "回到上次位置失败");
            }
        } catch (Exception e) {
            return Result.error(500, "回到上次位置失败: " + e.getMessage());
        }
    }

    /**
     * 随机传送
     */
    @PostMapping("/{sceneId}/random-teleport")
    public Result<Void> randomTeleport(@PathVariable Integer sceneId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            boolean success = sceneService.randomTeleport(currentRoleId, sceneId);
            if (success) {
                return Result.success("随机传送成功");
            } else {
                return Result.error(400, "随机传送失败");
            }
        } catch (Exception e) {
            return Result.error(500, "随机传送失败: " + e.getMessage());
        }
    }

    /**
     * 搜索场景
     */
    @GetMapping("/search")
    public Result<List<Scene>> searchScenes(@RequestParam String keyword) {
        try {
            List<Scene> scenes = sceneService.searchScenes(keyword);
            return Result.success("搜索场景成功", scenes);
        } catch (Exception e) {
            return Result.error(500, "搜索场景失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门场景
     */
    @GetMapping("/popular")
    public Result<List<Scene>> getPopularScenes() {
        try {
            List<Scene> scenes = sceneService.getPopularScenes();
            return Result.success("获取热门场景成功", scenes);
        } catch (Exception e) {
            return Result.error(500, "获取热门场景失败: " + e.getMessage());
        }
    }

    /**
     * 获取场景统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getSceneStatistics() {
        try {
            Map<String, Object> stats = sceneService.getSceneStatistics();
            return Result.success("获取场景统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取场景统计失败: " + e.getMessage());
        }
    }

    /**
     * 同步场景在线人数
     */
    @PostMapping("/sync-player-counts")
    public Result<Void> syncScenePlayerCounts() {
        try {
            sceneService.syncScenePlayerCounts();
            return Result.success("同步场景在线人数成功");
        } catch (Exception e) {
            return Result.error(500, "同步场景在线人数失败: " + e.getMessage());
        }
    }
}
