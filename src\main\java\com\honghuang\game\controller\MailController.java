package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.Mail;
import com.honghuang.game.entity.MailAttachment;
import com.honghuang.game.service.MailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 邮件控制器
 */
@RestController
@RequestMapping("/api/mail")
public class MailController {

    @Autowired
    private MailService mailService;

    /**
     * 发送邮件
     */
    @PostMapping("/send")
    public Result<Mail> sendMail(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String receiverName = (String) request.get("receiverName");
            String title = (String) request.get("title");
            String content = (String) request.get("content");
            Long gold = request.get("gold") != null ? ((Number) request.get("gold")).longValue() : null;
            Long silver = request.get("silver") != null ? ((Number) request.get("silver")).longValue() : null;
            Long copper = request.get("copper") != null ? ((Number) request.get("copper")).longValue() : null;
            @SuppressWarnings("unchecked")
            List<Map<String, Integer>> items = (List<Map<String, Integer>>) request.get("items");
            
            if (receiverName == null || receiverName.trim().isEmpty()) {
                return Result.error(400, "接收者不能为空");
            }
            
            if (title == null || title.trim().isEmpty()) {
                return Result.error(400, "邮件标题不能为空");
            }
            
            Mail mail = mailService.sendMail(currentRoleId, receiverName, title, content, gold, silver, copper, items);
            return Result.success("邮件发送成功", mail);
        } catch (Exception e) {
            return Result.error(500, "邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取收件箱
     */
    @GetMapping("/inbox")
    public Result<List<Mail>> getInboxMails(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Mail> mails = mailService.getInboxMails(currentRoleId);
            return Result.success("获取收件箱成功", mails);
        } catch (Exception e) {
            return Result.error(500, "获取收件箱失败: " + e.getMessage());
        }
    }

    /**
     * 获取发件箱
     */
    @GetMapping("/sent")
    public Result<List<Mail>> getSentMails(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Mail> mails = mailService.getSentMails(currentRoleId);
            return Result.success("获取发件箱成功", mails);
        } catch (Exception e) {
            return Result.error(500, "获取发件箱失败: " + e.getMessage());
        }
    }

    /**
     * 获取未读邮件
     */
    @GetMapping("/unread")
    public Result<List<Mail>> getUnreadMails(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Mail> mails = mailService.getUnreadMails(currentRoleId);
            return Result.success("获取未读邮件成功", mails);
        } catch (Exception e) {
            return Result.error(500, "获取未读邮件失败: " + e.getMessage());
        }
    }

    /**
     * 获取有附件的邮件
     */
    @GetMapping("/with-attachment")
    public Result<List<Mail>> getMailsWithAttachment(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Mail> mails = mailService.getMailsWithAttachment(currentRoleId);
            return Result.success("获取有附件邮件成功", mails);
        } catch (Exception e) {
            return Result.error(500, "获取有附件邮件失败: " + e.getMessage());
        }
    }

    /**
     * 阅读邮件
     */
    @PostMapping("/{mailId}/read")
    public Result<Mail> readMail(@PathVariable Integer mailId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Mail mail = mailService.readMail(currentRoleId, mailId);
            return Result.success("邮件阅读成功", mail);
        } catch (Exception e) {
            return Result.error(500, "邮件阅读失败: " + e.getMessage());
        }
    }

    /**
     * 领取邮件附件
     */
    @PostMapping("/{mailId}/claim")
    public Result<Void> claimMailAttachment(@PathVariable Integer mailId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = mailService.claimMailAttachment(currentRoleId, mailId);
            if (success) {
                return Result.success("附件领取成功");
            } else {
                return Result.error(400, "附件领取失败");
            }
        } catch (Exception e) {
            return Result.error(500, "附件领取失败: " + e.getMessage());
        }
    }

    /**
     * 删除邮件
     */
    @DeleteMapping("/{mailId}")
    public Result<Void> deleteMail(@PathVariable Integer mailId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = mailService.deleteMail(currentRoleId, mailId);
            if (success) {
                return Result.success("邮件删除成功");
            } else {
                return Result.error(400, "邮件删除失败");
            }
        } catch (Exception e) {
            return Result.error(500, "邮件删除失败: " + e.getMessage());
        }
    }

    /**
     * 标记所有邮件为已读
     */
    @PostMapping("/mark-all-read")
    public Result<Void> markAllAsRead(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = mailService.markAllAsRead(currentRoleId);
            if (success) {
                return Result.success("标记已读成功");
            } else {
                return Result.error(400, "标记已读失败");
            }
        } catch (Exception e) {
            return Result.error(500, "标记已读失败: " + e.getMessage());
        }
    }

    /**
     * 搜索邮件
     */
    @GetMapping("/search")
    public Result<List<Mail>> searchMails(@RequestParam String keyword, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Mail> mails = mailService.searchMails(currentRoleId, keyword);
            return Result.success("搜索邮件成功", mails);
        } catch (Exception e) {
            return Result.error(500, "搜索邮件失败: " + e.getMessage());
        }
    }

    /**
     * 获取邮件统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getMailStatistics(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, Object> stats = mailService.getMailStatistics(currentRoleId);
            return Result.success("获取邮件统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取邮件统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取邮件附件列表
     */
    @GetMapping("/{mailId}/attachments")
    public Result<List<MailAttachment>> getMailAttachments(@PathVariable Integer mailId) {
        try {
            List<MailAttachment> attachments = mailService.getMailAttachments(mailId);
            return Result.success("获取邮件附件成功", attachments);
        } catch (Exception e) {
            return Result.error(500, "获取邮件附件失败: " + e.getMessage());
        }
    }

    /**
     * 处理过期邮件
     */
    @PostMapping("/process-expired")
    public Result<Void> processExpiredMails() {
        try {
            mailService.processExpiredMails();
            return Result.success("处理过期邮件成功");
        } catch (Exception e) {
            return Result.error(500, "处理过期邮件失败: " + e.getMessage());
        }
    }

    /**
     * 清理已删除的邮件
     */
    @PostMapping("/cleanup")
    public Result<Void> cleanupDeletedMails() {
        try {
            mailService.cleanupDeletedMails();
            return Result.success("清理邮件成功");
        } catch (Exception e) {
            return Result.error(500, "清理邮件失败: " + e.getMessage());
        }
    }
}
