package com.honghuang.game.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 玩家物品实体类
 * 对应原表 u_part_goods
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "u_part_goods")
public class PlayerItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "pg_pk")
    private Integer id;

    /**
     * 玩家角色ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "p_pk")
    private PlayerRole playerRole;

    /**
     * 物品ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "goods_id")
    private GameItem gameItem;

    /**
     * 物品数量
     */
    @Column(name = "goods_num")
    private Integer quantity = 1;

    /**
     * 物品位置类型 1:背包 2:装备栏 3:仓库 4:临时背包
     */
    @Column(name = "goods_position")
    private Integer position = 1;

    /**
     * 具体位置索引（背包格子编号或装备部位）
     */
    @Column(name = "position_index")
    private Integer positionIndex;

    /**
     * 是否绑定 0:未绑定 1:绑定
     */
    @Column(name = "is_bound")
    private Integer isBound = 0;

    /**
     * 耐久度（当前）
     */
    @Column(name = "durability")
    private Integer durability;

    /**
     * 最大耐久度
     */
    @Column(name = "max_durability")
    private Integer maxDurability;

    /**
     * 强化等级
     */
    @Column(name = "enhance_level")
    private Integer enhanceLevel = 0;

    /**
     * 镶嵌宝石1
     */
    @Column(name = "gem1_id")
    private Integer gem1Id;

    /**
     * 镶嵌宝石2
     */
    @Column(name = "gem2_id")
    private Integer gem2Id;

    /**
     * 镶嵌宝石3
     */
    @Column(name = "gem3_id")
    private Integer gem3Id;

    /**
     * 镶嵌宝石4
     */
    @Column(name = "gem4_id")
    private Integer gem4Id;

    /**
     * 附加属性JSON
     */
    @Column(name = "extra_attributes", length = 1000)
    private String extraAttributes;

    /**
     * 获得时间
     */
    @CreationTimestamp
    @Column(name = "obtain_time")
    private LocalDateTime obtainTime;

    /**
     * 最后使用时间
     */
    @Column(name = "last_use_time")
    private LocalDateTime lastUseTime;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    // 构造函数
    public PlayerItem(PlayerRole playerRole, GameItem gameItem, Integer quantity) {
        this.playerRole = playerRole;
        this.gameItem = gameItem;
        this.quantity = quantity;
        this.position = 1; // 默认放入背包
        
        // 如果是装备，设置耐久度
        if (gameItem.isEquipment()) {
            this.durability = 100;
            this.maxDurability = 100;
        }
    }

    // 业务方法

    /**
     * 是否在背包中
     */
    public boolean isInBag() {
        return position != null && position == 1;
    }

    /**
     * 是否已装备
     */
    public boolean isEquipped() {
        return position != null && position == 2;
    }

    /**
     * 是否在仓库中
     */
    public boolean isInWarehouse() {
        return position != null && position == 3;
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return expireTime != null && expireTime.isBefore(LocalDateTime.now());
    }

    /**
     * 是否可以堆叠
     */
    public boolean canStack() {
        return gameItem != null && gameItem.getIsStackable() == 1;
    }

    /**
     * 是否可以与另一个物品堆叠
     */
    public boolean canStackWith(PlayerItem other) {
        if (other == null || !canStack() || !other.canStack()) {
            return false;
        }
        
        // 必须是同一种物品
        if (!gameItem.getId().equals(other.getGameItem().getId())) {
            return false;
        }
        
        // 必须在同一位置
        if (!position.equals(other.getPosition())) {
            return false;
        }
        
        // 绑定状态必须相同
        if (!isBound.equals(other.getIsBound())) {
            return false;
        }
        
        // 检查堆叠上限
        int totalQuantity = quantity + other.getQuantity();
        return totalQuantity <= gameItem.getMaxStack();
    }

    /**
     * 堆叠物品
     */
    public void stackWith(PlayerItem other) {
        if (canStackWith(other)) {
            this.quantity += other.getQuantity();
        }
    }

    /**
     * 分割物品
     */
    public PlayerItem split(int splitQuantity) {
        if (splitQuantity <= 0 || splitQuantity >= quantity) {
            return null;
        }
        
        PlayerItem newItem = new PlayerItem();
        newItem.setPlayerRole(this.playerRole);
        newItem.setGameItem(this.gameItem);
        newItem.setQuantity(splitQuantity);
        newItem.setPosition(this.position);
        newItem.setIsBound(this.isBound);
        newItem.setDurability(this.durability);
        newItem.setMaxDurability(this.maxDurability);
        newItem.setEnhanceLevel(this.enhanceLevel);
        newItem.setGem1Id(this.gem1Id);
        newItem.setGem2Id(this.gem2Id);
        newItem.setGem3Id(this.gem3Id);
        newItem.setGem4Id(this.gem4Id);
        newItem.setExtraAttributes(this.extraAttributes);
        newItem.setExpireTime(this.expireTime);
        
        this.quantity -= splitQuantity;
        
        return newItem;
    }

    /**
     * 获取总攻击力加成
     */
    public int getTotalAttackBonus() {
        int bonus = gameItem.getAttackBonus();
        // 强化等级加成
        bonus += enhanceLevel * 2;
        // TODO: 宝石加成
        return bonus;
    }

    /**
     * 获取总防御力加成
     */
    public int getTotalDefenseBonus() {
        int bonus = gameItem.getDefenseBonus();
        // 强化等级加成
        bonus += enhanceLevel * 1;
        // TODO: 宝石加成
        return bonus;
    }

    /**
     * 获取装备部位
     */
    public String getEquipSlot() {
        if (!gameItem.isEquipment()) {
            return null;
        }
        
        // 根据物品子类型确定装备部位
        switch (gameItem.getSubType()) {
            case 1: return "weapon"; // 武器
            case 2: return "helmet"; // 头盔
            case 3: return "armor"; // 盔甲
            case 4: return "pants"; // 裤子
            case 5: return "shoes"; // 鞋子
            case 6: return "gloves"; // 手套
            case 7: return "necklace"; // 项链
            case 8: return "ring"; // 戒指
            case 9: return "belt"; // 腰带
            default: return "other";
        }
    }
}
