package com.honghuang.game.repository;

import com.honghuang.game.entity.Friend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 好友数据访问接口
 */
@Repository
public interface FriendRepository extends JpaRepository<Friend, Long> {
    
    /**
     * 根据玩家ID查找好友列表
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.status = 1 ORDER BY f.isOnline DESC, f.addTime DESC")
    List<Friend> findFriendsByPlayerId(@Param("playerId") Integer playerId);
    
    /**
     * 查找玩家和好友的关系
     */
    Optional<Friend> findByPlayerIdAndFriendId(Integer playerId, Integer friendId);
    
    /**
     * 查找在线好友
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.status = 1 AND f.isOnline = 1 ORDER BY f.addTime DESC")
    List<Friend> findOnlineFriendsByPlayerId(@Param("playerId") Integer playerId);
    
    /**
     * 查找离线好友
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.status = 1 AND f.isOnline = 0 ORDER BY f.lastOnlineTime DESC")
    List<Friend> findOfflineFriendsByPlayerId(@Param("playerId") Integer playerId);
    
    /**
     * 查找待确认的好友申请
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.status = 3 ORDER BY f.addTime DESC")
    List<Friend> findPendingFriendsByPlayerId(@Param("playerId") Integer playerId);
    
    /**
     * 查找黑名单
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.status = 2 ORDER BY f.addTime DESC")
    List<Friend> findBlockedFriendsByPlayerId(@Param("playerId") Integer playerId);
    
    /**
     * 根据好友名称模糊查找
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.friendName LIKE %:friendName% AND f.status = 1")
    List<Friend> findFriendsByPlayerIdAndNameContaining(@Param("playerId") Integer playerId, @Param("friendName") String friendName);
    
    /**
     * 根据亲密度排序查找好友
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.status = 1 ORDER BY f.intimacy DESC")
    List<Friend> findFriendsByPlayerIdOrderByIntimacyDesc(@Param("playerId") Integer playerId);
    
    /**
     * 统计好友数量
     */
    @Query("SELECT COUNT(f) FROM Friend f WHERE f.playerId = :playerId AND f.status = 1")
    Long countFriendsByPlayerId(@Param("playerId") Integer playerId);
    
    /**
     * 统计在线好友数量
     */
    @Query("SELECT COUNT(f) FROM Friend f WHERE f.playerId = :playerId AND f.status = 1 AND f.isOnline = 1")
    Long countOnlineFriendsByPlayerId(@Param("playerId") Integer playerId);
    
    /**
     * 统计待确认申请数量
     */
    @Query("SELECT COUNT(f) FROM Friend f WHERE f.playerId = :playerId AND f.status = 3")
    Long countPendingFriendsByPlayerId(@Param("playerId") Integer playerId);
    
    /**
     * 检查是否已经是好友
     */
    @Query("SELECT COUNT(f) > 0 FROM Friend f WHERE f.playerId = :playerId AND f.friendId = :friendId AND f.status IN (1, 3)")
    boolean existsFriendship(@Param("playerId") Integer playerId, @Param("friendId") Integer friendId);

    // 为FriendService添加的方法别名

    /**
     * 查找正常好友列表
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.status = 1 ORDER BY f.isOnline DESC, f.addTime DESC")
    List<Friend> findNormalFriends(@Param("playerId") Integer playerId);

    /**
     * 查找在线好友列表
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.status = 1 AND f.isOnline = 1 ORDER BY f.addTime DESC")
    List<Friend> findOnlineFriends(@Param("playerId") Integer playerId);

    /**
     * 查找黑名单好友列表
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND f.status = 2 ORDER BY f.addTime DESC")
    List<Friend> findBlockedFriends(@Param("playerId") Integer playerId);

    /**
     * 根据好友ID查找好友列表
     */
    List<Friend> findByFriendId(Integer friendId);

    /**
     * 搜索好友
     */
    @Query("SELECT f FROM Friend f WHERE f.playerId = :playerId AND (f.friendName LIKE %:keyword% OR f.remark LIKE %:keyword%) AND f.status = 1 ORDER BY f.addTime DESC")
    List<Friend> searchFriends(@Param("playerId") Integer playerId, @Param("keyword") String keyword);

    /**
     * 统计正常好友数量
     */
    @Query("SELECT COUNT(f) FROM Friend f WHERE f.playerId = :playerId AND f.status = 1")
    long countNormalFriends(@Param("playerId") Integer playerId);

    /**
     * 统计在线好友数量
     */
    @Query("SELECT COUNT(f) FROM Friend f WHERE f.playerId = :playerId AND f.status = 1 AND f.isOnline = 1")
    long countOnlineFriends(@Param("playerId") Integer playerId);

    /**
     * 统计黑名单好友数量
     */
    @Query("SELECT COUNT(f) FROM Friend f WHERE f.playerId = :playerId AND f.status = 2")
    long countBlockedFriends(@Param("playerId") Integer playerId);
}
