package com.honghuang.game.repository;

import com.honghuang.game.entity.Faction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 帮派数据访问接口
 */
@Repository
public interface FactionRepository extends JpaRepository<Faction, Integer> {
    
    /**
     * 根据帮派名称查找
     */
    Optional<Faction> findByName(String name);
    
    /**
     * 根据帮主ID查找
     */
    Optional<Faction> findByLeaderId(Integer leaderId);
    
    /**
     * 查找活跃的帮派
     */
    @Query("SELECT f FROM Faction f WHERE f.isActive = 1 ORDER BY f.level DESC, f.prestige DESC")
    List<Faction> findActiveFactions();
    
    /**
     * 根据等级查找帮派
     */
    List<Faction> findByLevel(Integer level);
    
    /**
     * 根据等级范围查找帮派
     */
    @Query("SELECT f FROM Faction f WHERE f.level BETWEEN :minLevel AND :maxLevel AND f.isActive = 1")
    List<Faction> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);
    
    /**
     * 根据加入条件查找帮派
     */
    @Query("SELECT f FROM Faction f WHERE f.joinCondition = :joinCondition AND f.isActive = 1")
    List<Faction> findByJoinCondition(@Param("joinCondition") Integer joinCondition);
    
    /**
     * 查找可以自由加入的帮派
     */
    @Query("SELECT f FROM Faction f WHERE f.joinCondition = 1 AND f.memberCount < f.maxMemberCount AND f.isActive = 1")
    List<Faction> findOpenFactions();
    
    /**
     * 根据声望排序查找帮派
     */
    @Query("SELECT f FROM Faction f WHERE f.isActive = 1 ORDER BY f.prestige DESC")
    List<Faction> findByPrestigeDesc();
    
    /**
     * 根据成员数量排序查找帮派
     */
    @Query("SELECT f FROM Faction f WHERE f.isActive = 1 ORDER BY f.memberCount DESC")
    List<Faction> findByMemberCountDesc();
    
    /**
     * 查找有空位的帮派
     */
    @Query("SELECT f FROM Faction f WHERE f.memberCount < f.maxMemberCount AND f.isActive = 1")
    List<Faction> findFactionsWithSpace();
    
    /**
     * 根据名称模糊查找
     */
    @Query("SELECT f FROM Faction f WHERE f.name LIKE %:name% AND f.isActive = 1")
    List<Faction> findByNameContaining(@Param("name") String name);
    
    /**
     * 查找等级最高的帮派
     */
    @Query("SELECT f FROM Faction f WHERE f.isActive = 1 ORDER BY f.level DESC, f.exp DESC")
    List<Faction> findTopLevelFactions();
    
    /**
     * 统计活跃帮派数量
     */
    @Query("SELECT COUNT(f) FROM Faction f WHERE f.isActive = 1")
    Long countActiveFactions();
}
