package com.honghuang.game.service;

import com.honghuang.game.entity.AuctionBid;
import com.honghuang.game.entity.AuctionItem;
import com.honghuang.game.entity.Inventory;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.AuctionBidRepository;
import com.honghuang.game.repository.AuctionItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 拍卖服务类
 */
@Service
@Transactional
public class AuctionService {

    @Autowired
    private AuctionItemRepository auctionItemRepository;

    @Autowired
    private AuctionBidRepository auctionBidRepository;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private PlayerRoleService playerRoleService;

    private static final int MAX_AUCTION_DURATION_HOURS = 72; // 最长拍卖时间72小时
    private static final int MIN_AUCTION_DURATION_HOURS = 1; // 最短拍卖时间1小时
    private static final double AUCTION_FEE_RATE = 0.05; // 拍卖手续费5%

    /**
     * 创建拍卖
     */
    public AuctionItem createAuction(Integer sellerId, Integer inventorySlotIndex, Long startPrice, 
                                   Long buyoutPrice, Integer durationHours, Integer currencyType) {
        System.out.println("创建拍卖: sellerId=" + sellerId + ", slot=" + inventorySlotIndex + ", startPrice=" + startPrice);

        PlayerRole seller = playerRoleService.findById(sellerId);

        // 验证拍卖参数
        validateAuctionParams(startPrice, buyoutPrice, durationHours, currencyType);

        // 获取要拍卖的物品
        Inventory inventory = inventoryService.getInventorySlot(sellerId, inventorySlotIndex)
                .orElseThrow(() -> new BusinessException(404, "背包槽位为空"));

        if (inventory.isEmpty()) {
            throw new BusinessException(400, "背包槽位为空");
        }

        if (!inventory.canTrade()) {
            throw new BusinessException(400, "该物品不可交易");
        }

        // 创建拍卖物品
        AuctionItem auctionItem = new AuctionItem();
        auctionItem.setSellerId(sellerId);
        auctionItem.setSellerName(seller.getName());
        auctionItem.setItemId(inventory.getItemId());
        auctionItem.setQuantity(inventory.getQuantity());
        auctionItem.setStartPrice(startPrice);
        auctionItem.setCurrentPrice(startPrice);
        auctionItem.setBuyoutPrice(buyoutPrice);
        auctionItem.setCurrencyType(currencyType);
        auctionItem.setEndTime(LocalDateTime.now().plusHours(durationHours));

        auctionItem = auctionItemRepository.save(auctionItem);

        // 从背包移除物品
        inventoryService.removeItem(sellerId, inventory.getItemId(), inventory.getQuantity());

        System.out.println("拍卖创建成功: auctionId=" + auctionItem.getId());
        return auctionItem;
    }

    /**
     * 出价
     */
    public AuctionBid placeBid(Integer bidderId, Integer auctionItemId, Long bidPrice) {
        System.out.println("出价: bidderId=" + bidderId + ", auctionId=" + auctionItemId + ", price=" + bidPrice);

        PlayerRole bidder = playerRoleService.findById(bidderId);
        AuctionItem auctionItem = auctionItemRepository.findById(auctionItemId)
                .orElseThrow(() -> new BusinessException(404, "拍卖不存在"));

        // 验证出价条件
        if (!auctionItem.canBid(bidderId, bidPrice)) {
            throw new BusinessException(400, "无法出价");
        }

        if (bidPrice < auctionItem.getMinNextBid()) {
            throw new BusinessException(400, "出价过低，最低出价: " + auctionItem.getMinNextBid());
        }

        // 检查玩家资金
        if (!hasEnoughCurrency(bidder, auctionItem.getCurrencyType(), bidPrice)) {
            throw new BusinessException(400, "资金不足");
        }

        // 冻结资金
        freezeCurrency(bidder, auctionItem.getCurrencyType(), bidPrice);

        // 退还之前出价者的资金
        if (auctionItem.hasBidder()) {
            refundPreviousBidder(auctionItem);
        }

        // 更新所有出价记录的最高状态
        auctionBidRepository.clearHighestStatusByAuctionItemId(auctionItemId);

        // 创建出价记录
        AuctionBid bid = AuctionBid.createNormalBid(auctionItemId, bidderId, bidder.getName(), bidPrice);
        bid = auctionBidRepository.save(bid);

        // 更新拍卖物品
        auctionItem.bid(bidderId, bidder.getName(), bidPrice);
        auctionItemRepository.save(auctionItem);

        System.out.println("出价成功: bidId=" + bid.getId() + ", price=" + bidPrice);
        return bid;
    }

    /**
     * 一口价购买
     */
    public AuctionBid buyout(Integer buyerId, Integer auctionItemId) {
        System.out.println("一口价购买: buyerId=" + buyerId + ", auctionId=" + auctionItemId);

        PlayerRole buyer = playerRoleService.findById(buyerId);
        AuctionItem auctionItem = auctionItemRepository.findById(auctionItemId)
                .orElseThrow(() -> new BusinessException(404, "拍卖不存在"));

        if (!auctionItem.isActive() || !auctionItem.hasBuyoutPrice()) {
            throw new BusinessException(400, "无法一口价购买");
        }

        if (auctionItem.getSellerId().equals(buyerId)) {
            throw new BusinessException(400, "不能购买自己的拍卖");
        }

        // 检查玩家资金
        if (!hasEnoughCurrency(buyer, auctionItem.getCurrencyType(), auctionItem.getBuyoutPrice())) {
            throw new BusinessException(400, "资金不足");
        }

        // 扣除资金
        spendCurrency(buyer, auctionItem.getCurrencyType(), auctionItem.getBuyoutPrice());

        // 退还之前出价者的资金
        if (auctionItem.hasBidder()) {
            refundPreviousBidder(auctionItem);
        }

        // 更新所有出价记录的最高状态
        auctionBidRepository.clearHighestStatusByAuctionItemId(auctionItemId);

        // 创建一口价出价记录
        AuctionBid bid = AuctionBid.createBuyoutBid(auctionItemId, buyerId, buyer.getName(), auctionItem.getBuyoutPrice());
        bid = auctionBidRepository.save(bid);

        // 更新拍卖物品状态
        auctionItem.buyout(buyerId, buyer.getName());
        auctionItemRepository.save(auctionItem);

        // 处理成交
        processAuctionDeal(auctionItem);

        System.out.println("一口价购买成功: bidId=" + bid.getId());
        return bid;
    }

    /**
     * 取消拍卖
     */
    public boolean cancelAuction(Integer sellerId, Integer auctionItemId) {
        System.out.println("取消拍卖: sellerId=" + sellerId + ", auctionId=" + auctionItemId);

        AuctionItem auctionItem = auctionItemRepository.findById(auctionItemId)
                .orElseThrow(() -> new BusinessException(404, "拍卖不存在"));

        if (!auctionItem.getSellerId().equals(sellerId)) {
            throw new BusinessException(403, "只能取消自己的拍卖");
        }

        if (!auctionItem.cancel()) {
            throw new BusinessException(400, "无法取消拍卖");
        }

        auctionItemRepository.save(auctionItem);

        // 退还物品给卖家
        inventoryService.addItem(sellerId, auctionItem.getItemId(), auctionItem.getQuantity());

        System.out.println("拍卖取消成功: auctionId=" + auctionItemId);
        return true;
    }

    /**
     * 获取进行中的拍卖列表
     */
    @Transactional(readOnly = true)
    public List<AuctionItem> getActiveAuctions() {
        return auctionItemRepository.findActiveAuctions();
    }

    /**
     * 分页获取进行中的拍卖列表
     */
    @Transactional(readOnly = true)
    public Page<AuctionItem> getActiveAuctions(int page, int size) {
        return auctionItemRepository.findActiveAuctions(PageRequest.of(page, size));
    }

    /**
     * 根据物品ID搜索拍卖
     */
    @Transactional(readOnly = true)
    public List<AuctionItem> searchAuctionsByItemId(Integer itemId) {
        return auctionItemRepository.findActiveAuctionsByItemId(itemId);
    }

    /**
     * 根据关键词搜索拍卖
     */
    @Transactional(readOnly = true)
    public List<AuctionItem> searchAuctions(String keyword) {
        return auctionItemRepository.searchActiveAuctions(keyword);
    }

    /**
     * 获取卖家的拍卖列表
     */
    @Transactional(readOnly = true)
    public List<AuctionItem> getSellerAuctions(Integer sellerId) {
        return auctionItemRepository.findBySellerIdOrderByStartTimeDesc(sellerId);
    }

    /**
     * 获取买家的出价列表
     */
    @Transactional(readOnly = true)
    public List<AuctionBid> getBuyerBids(Integer buyerId) {
        return auctionBidRepository.findByBidderIdOrderByBidTimeDesc(buyerId);
    }

    /**
     * 获取拍卖的出价历史
     */
    @Transactional(readOnly = true)
    public List<AuctionBid> getAuctionBids(Integer auctionItemId) {
        return auctionBidRepository.findByAuctionItemIdOrderByBidTimeDesc(auctionItemId);
    }

    /**
     * 处理过期拍卖
     */
    @Transactional
    public void processExpiredAuctions() {
        System.out.println("处理过期拍卖");

        List<AuctionItem> expiredAuctions = auctionItemRepository.findExpiredAuctions();
        
        for (AuctionItem auction : expiredAuctions) {
            if (auction.hasBidder()) {
                // 有出价者，成交
                auction.setStatus(2);
                auction.setDealTime(LocalDateTime.now());
                processAuctionDeal(auction);
            } else {
                // 无出价者，流拍
                auction.fail();
                // 退还物品给卖家
                inventoryService.addItem(auction.getSellerId(), auction.getItemId(), auction.getQuantity());
            }
            auctionItemRepository.save(auction);
        }

        System.out.println("处理过期拍卖完成，共处理" + expiredAuctions.size() + "个拍卖");
    }

    /**
     * 获取拍卖统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getAuctionStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        long activeAuctions = auctionItemRepository.countActiveAuctions();
        long totalAuctions = auctionItemRepository.count();
        
        stats.put("activeAuctions", activeAuctions);
        stats.put("totalAuctions", totalAuctions);
        stats.put("completionRate", totalAuctions > 0 ? (double)(totalAuctions - activeAuctions) / totalAuctions * 100 : 0);
        
        return stats;
    }

    /**
     * 验证拍卖参数
     */
    private void validateAuctionParams(Long startPrice, Long buyoutPrice, Integer durationHours, Integer currencyType) {
        if (startPrice == null || startPrice <= 0) {
            throw new BusinessException(400, "起拍价必须大于0");
        }

        if (buyoutPrice != null && buyoutPrice <= startPrice) {
            throw new BusinessException(400, "一口价必须高于起拍价");
        }

        if (durationHours < MIN_AUCTION_DURATION_HOURS || durationHours > MAX_AUCTION_DURATION_HOURS) {
            throw new BusinessException(400, "拍卖时间必须在" + MIN_AUCTION_DURATION_HOURS + "-" + MAX_AUCTION_DURATION_HOURS + "小时之间");
        }

        if (currencyType == null || currencyType < 1 || currencyType > 3) {
            throw new BusinessException(400, "货币类型无效");
        }
    }

    /**
     * 检查玩家是否有足够的货币
     */
    private boolean hasEnoughCurrency(PlayerRole player, Integer currencyType, Long amount) {
        switch (currencyType) {
            case 1: return player.getCopper() >= amount;
            case 2: return player.getSilver() >= amount;
            case 3: return player.getGold() >= amount;
            default: return false;
        }
    }

    /**
     * 冻结货币
     */
    private void freezeCurrency(PlayerRole player, Integer currencyType, Long amount) {
        // 这里简化处理，直接扣除货币
        // 实际项目中应该有专门的冻结机制
        spendCurrency(player, currencyType, amount);
    }

    /**
     * 扣除货币
     */
    private void spendCurrency(PlayerRole player, Integer currencyType, Long amount) {
        switch (currencyType) {
            case 1: 
                if (!player.spendCopper(amount)) {
                    throw new BusinessException(400, "铜钱不足");
                }
                break;
            case 2: 
                if (!player.spendSilver(amount)) {
                    throw new BusinessException(400, "灵石不足");
                }
                break;
            case 3: 
                if (!player.spendGold(amount)) {
                    throw new BusinessException(400, "仙晶不足");
                }
                break;
        }
        playerRoleService.save(player);
    }

    /**
     * 退还之前出价者的资金
     */
    private void refundPreviousBidder(AuctionItem auctionItem) {
        if (!auctionItem.hasBidder()) return;

        PlayerRole previousBidder = playerRoleService.findById(auctionItem.getHighestBidderId());
        Long refundAmount = auctionItem.getCurrentPrice();

        switch (auctionItem.getCurrencyType()) {
            case 1: previousBidder.addCopper(refundAmount); break;
            case 2: previousBidder.addSilver(refundAmount); break;
            case 3: previousBidder.addGold(refundAmount); break;
        }
        playerRoleService.save(previousBidder);
    }

    /**
     * 处理拍卖成交
     */
    private void processAuctionDeal(AuctionItem auctionItem) {
        // 给买家发放物品
        inventoryService.addItem(auctionItem.getHighestBidderId(), auctionItem.getItemId(), auctionItem.getQuantity());

        // 给卖家发放资金（扣除手续费）
        PlayerRole seller = playerRoleService.findById(auctionItem.getSellerId());
        Long sellerIncome = auctionItem.calculateSellerIncome();

        switch (auctionItem.getCurrencyType()) {
            case 1: seller.addCopper(sellerIncome); break;
            case 2: seller.addSilver(sellerIncome); break;
            case 3: seller.addGold(sellerIncome); break;
        }
        playerRoleService.save(seller);
    }
}
