<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>战斗系统 - 洪荒Online</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            color: #666;
            margin-top: 0;
        }
        .monster-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        .monster-card {
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .monster-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .monster-stats {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .battle-btn {
            background-color: #ff4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
        }
        .battle-btn:hover {
            background-color: #cc3333;
        }
        .battle-log {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .battle-result {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .victory {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .defeat {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .stat-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗡️ 战斗系统</h1>
            <p>挑战怪物，获得经验和奖励！</p>
            <div style="margin-top: 15px;">
                <button class="btn" onclick="window.location.href='/honghuang-game/game.html'"
                        style="background-color: #3498db; margin-right: 10px;">
                    🏠 返回游戏
                </button>
                <button class="btn" onclick="window.location.reload()"
                        style="background-color: #95a5a6;">
                    🔄 刷新页面
                </button>
            </div>
        </div>

        <!-- 战斗统计 -->
        <div class="section">
            <h3>📊 战斗统计</h3>
            <div class="stats-grid" id="battleStats">
                <div class="stat-card">
                    <div class="stat-value" id="totalBattles">0</div>
                    <div class="stat-label">总战斗次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="victories">0</div>
                    <div class="stat-label">胜利次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="winRate">0%</div>
                    <div class="stat-label">胜率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalExp">0</div>
                    <div class="stat-label">总获得经验</div>
                </div>
            </div>
            <button class="btn" onclick="loadBattleStats()">刷新统计</button>
        </div>

        <!-- 怪物列表 -->
        <div class="section">
            <h3>👹 可挑战怪物</h3>
            <div class="monster-list" id="monsterList">
                <p>加载中...</p>
            </div>
            <button class="btn" onclick="loadMonsters()">刷新怪物列表</button>
        </div>

        <!-- 战斗结果 -->
        <div class="section" id="battleResultSection" style="display: none;">
            <h3>⚔️ 战斗结果</h3>
            <div id="battleResult"></div>
        </div>

        <!-- 战斗日志 -->
        <div class="section" id="battleLogSection" style="display: none;">
            <h3>📜 战斗日志</h3>
            <div class="battle-log" id="battleLog"></div>
        </div>

        <!-- 战斗记录 -->
        <div class="section">
            <h3>📋 最近战斗记录</h3>
            <div id="battleRecords">
                <p>暂无战斗记录</p>
            </div>
            <button class="btn" onclick="loadBattleRecords()">刷新记录</button>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="window.location.href='/honghuang-game/game.html'">返回游戏主界面</button>
        </div>
    </div>

    <script>
        const API_BASE = '/honghuang-game/api';

        // 加载怪物列表
        function loadMonsters() {
            fetch(`${API_BASE}/battle/monsters`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        displayMonsters(data.data);
                    } else {
                        document.getElementById('monsterList').innerHTML = '<p>加载怪物列表失败: ' + data.message + '</p>';
                    }
                })
                .catch(error => {
                    console.error('加载怪物列表失败:', error);
                    document.getElementById('monsterList').innerHTML = '<p>加载怪物列表失败</p>';
                });
        }

        // 显示怪物列表
        function displayMonsters(monsters) {
            const container = document.getElementById('monsterList');
            if (monsters.length === 0) {
                container.innerHTML = '<p>暂无可挑战的怪物</p>';
                return;
            }

            container.innerHTML = monsters.map(monster => `
                <div class="monster-card">
                    <h4>${monster.name} (Lv.${monster.level || 1})</h4>
                    <div class="monster-stats">
                        <div>生命值: ${monster.maxHp || 100}</div>
                        <div>攻击力: ${monster.attackMin || 10}-${monster.attackMax || 15}</div>
                        <div>防御力: ${monster.defenseMin || 5}-${monster.defenseMax || 8}</div>
                        <div>经验奖励: ${monster.exp || 10}</div>
                        <div>金钱奖励: ${monster.money || 5}</div>
                    </div>
                    <button class="battle-btn" onclick="startBattle(${monster.id})">挑战</button>
                </div>
            `).join('');
        }

        // 开始战斗
        function startBattle(monsterId, monsterName) {
            fetch(`${API_BASE}/battle/start`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ monsterId: monsterId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    displayBattleResult(data.data);
                } else {
                    alert('战斗失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('战斗失败:', error);
                alert('战斗失败');
            });
        }

        // 显示战斗结果
        function displayBattleResult(result) {
            const resultSection = document.getElementById('battleResultSection');
            const logSection = document.getElementById('battleLogSection');
            const resultDiv = document.getElementById('battleResult');
            const logDiv = document.getElementById('battleLog');

            // 显示战斗结果
            const resultClass = result.victory ? 'victory' : 'defeat';
            const resultText = result.victory ? '🎉 胜利！' : '💀 失败！';
            
            resultDiv.innerHTML = `
                <div class="battle-result ${resultClass}">
                    <h4>${resultText}</h4>
                    <p>对手: ${result.monsterName} (Lv.${result.monsterLevel})</p>
                    <p>战斗回合: ${result.rounds}</p>
                    ${result.victory ? `
                        <p>获得经验: ${result.expGained}</p>
                        <p>获得铜钱: ${result.copperGained}</p>
                        ${result.itemsDropped && result.itemsDropped.length > 0 ? 
                            `<p>掉落物品: ${result.itemsDropped.length}件</p>` : ''}
                    ` : ''}
                    <p>战斗后血量: ${result.playerHpAfter}</p>
                </div>
            `;

            // 显示战斗日志
            logDiv.innerHTML = result.battleLog.join('<br>');

            resultSection.style.display = 'block';
            logSection.style.display = 'block';

            // 刷新统计和记录
            loadBattleStats();
            loadBattleRecords();
        }

        // 加载战斗统计
        function loadBattleStats() {
            fetch(`${API_BASE}/battle/stats`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        const stats = data.data;
                        document.getElementById('totalBattles').textContent = stats.totalBattles || 0;
                        document.getElementById('victories').textContent = stats.victories || 0;
                        document.getElementById('winRate').textContent = (stats.winRate || 0) + '%';
                        document.getElementById('totalExp').textContent = stats.totalExpGained || 0;
                    } else if (data.code === 401) {
                        // 用户未登录，显示默认统计
                        document.getElementById('totalBattles').textContent = '请先登录';
                        document.getElementById('victories').textContent = '-';
                        document.getElementById('winRate').textContent = '-';
                        document.getElementById('totalExp').textContent = '-';
                    }
                })
                .catch(error => {
                    console.error('加载战斗统计失败:', error);
                });
        }

        // 加载战斗记录
        function loadBattleRecords() {
            fetch(`${API_BASE}/battle/records?limit=5`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        displayBattleRecords(data.data);
                    } else if (data.code === 401) {
                        // 用户未登录
                        const container = document.getElementById('battleRecords');
                        container.innerHTML = '<p style="color: #f39c12;">请先<a href="/honghuang-game/index.html" style="color: #3498db;">登录</a>后查看战斗记录</p>';
                    } else {
                        console.error('加载战斗记录失败:', data.message);
                        const container = document.getElementById('battleRecords');
                        container.innerHTML = '<p style="color: #e74c3c;">加载战斗记录失败</p>';
                    }
                })
                .catch(error => {
                    console.error('加载战斗记录失败:', error);
                    const container = document.getElementById('battleRecords');
                    container.innerHTML = '<p style="color: #e74c3c;">网络错误，无法加载战斗记录</p>';
                });
        }

        // 显示战斗记录
        function displayBattleRecords(records) {
            const container = document.getElementById('battleRecords');
            if (records.length === 0) {
                container.innerHTML = '<p>暂无战斗记录</p>';
                return;
            }

            container.innerHTML = records.map(record => `
                <div class="battle-result ${record.battleResult === 1 ? 'victory' : 'defeat'}">
                    <strong>${record.battleResult === 1 ? '胜利' : '失败'}</strong> vs ${record.monsterName}
                    (${record.rounds}回合, ${new Date(record.battleTime).toLocaleString()})
                    ${record.battleResult === 1 ? `- 经验+${record.expGained}, 铜钱+${record.copperGained}` : ''}
                </div>
            `).join('');
        }

        // 检查URL参数中是否有指定的怪物
        function checkForSpecificMonster() {
            const urlParams = new URLSearchParams(window.location.search);
            const monsterId = urlParams.get('monsterId');
            const monsterName = urlParams.get('monsterName');

            if (monsterId && monsterName) {
                // 显示特定怪物的战斗界面
                showSpecificMonsterBattle(monsterId, decodeURIComponent(monsterName));
                return true;
            }
            return false;
        }

        // 显示特定怪物的战斗界面
        function showSpecificMonsterBattle(monsterId, monsterName) {
            const monsterList = document.getElementById('monsterList');
            monsterList.innerHTML = `
                <div class="monster-card" style="border: 2px solid #e74c3c; background-color: #fdf2f2;">
                    <h4 style="color: #e74c3c;">🎯 遭遇战</h4>
                    <p><strong>怪物：</strong>${monsterName}</p>
                    <p><strong>状态：</strong>准备战斗</p>
                    <button class="btn" onclick="startBattleWithMonster(${monsterId}, '${monsterName}')"
                            style="background-color: #e74c3c; animation: pulse 1s infinite;">
                        ⚔️ 立即战斗
                    </button>
                    <button class="btn" onclick="loadMonsters()" style="background-color: #95a5a6; margin-left: 10px;">
                        🔄 查看其他怪物
                    </button>
                </div>
            `;

            // 添加脉冲动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
            `;
            document.head.appendChild(style);
        }

        // 与特定怪物战斗
        function startBattleWithMonster(monsterId, monsterName) {
            startBattle(monsterId, monsterName);
        }

        // 页面加载时初始化
        window.onload = function() {
            // 首先检查是否有特定怪物
            if (!checkForSpecificMonster()) {
                // 没有特定怪物，加载正常的怪物列表
                loadMonsters();
            }

            loadBattleStats();
            loadBattleRecords();
        };
    </script>
</body>
</html>
