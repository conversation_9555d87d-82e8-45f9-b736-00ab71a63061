package com.honghuang.game.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 排行榜常量类
 */
public class RankingConstants {
    
    /**
     * 排行榜类型
     */
    public static final String LEVEL = "level";              // 等级榜
    public static final String EXPERIENCE = "experience";    // 经验榜
    public static final String COPPER = "copper";           // 财富榜
    public static final String ATTACK = "attack";           // 攻击榜
    public static final String DEFENSE = "defense";         // 防御榜
    public static final String AGILITY = "agility";         // 敏捷榜
    public static final String KILL_MONSTER = "killMonster"; // 杀怪榜
    public static final String PK_WIN = "pkWin";            // PK榜
    public static final String ONLINE_TIME = "onlineTime";   // 在线榜
    public static final String PET_LEVEL = "petLevel";      // 宠物等级榜
    public static final String PET_ATTACK = "petAttack";    // 宠物攻击榜
    public static final String EQUIP_SCORE = "equipScore";  // 装备榜
    public static final String TOTAL_SCORE = "totalScore";  // 综合榜
    
    /**
     * 排行榜分类
     */
    public static final int CATEGORY_PERSONAL = 1;    // 个人榜
    public static final int CATEGORY_BATTLE = 2;      // 战斗榜
    public static final int CATEGORY_PET = 3;         // 宠物榜
    public static final int CATEGORY_EQUIPMENT = 4;   // 装备榜
    
    /**
     * 排行榜名称映射
     */
    public static final Map<String, String> RANK_NAMES = new HashMap<>();
    
    /**
     * 排行榜描述映射
     */
    public static final Map<String, String> RANK_DESCRIPTIONS = new HashMap<>();
    
    /**
     * 排行榜分类映射
     */
    public static final Map<String, Integer> RANK_CATEGORIES = new HashMap<>();
    
    /**
     * 排行榜单位映射
     */
    public static final Map<String, String> RANK_UNITS = new HashMap<>();
    
    /**
     * 第一名称号映射
     */
    public static final Map<String, String> FIRST_TITLES = new HashMap<>();
    
    static {
        // 初始化排行榜名称
        RANK_NAMES.put(LEVEL, "等级榜");
        RANK_NAMES.put(EXPERIENCE, "经验榜");
        RANK_NAMES.put(COPPER, "财富榜");
        RANK_NAMES.put(ATTACK, "攻击榜");
        RANK_NAMES.put(DEFENSE, "防御榜");
        RANK_NAMES.put(AGILITY, "敏捷榜");
        RANK_NAMES.put(KILL_MONSTER, "杀怪榜");
        RANK_NAMES.put(PK_WIN, "PK榜");
        RANK_NAMES.put(ONLINE_TIME, "在线榜");
        RANK_NAMES.put(PET_LEVEL, "宠物等级榜");
        RANK_NAMES.put(PET_ATTACK, "宠物攻击榜");
        RANK_NAMES.put(EQUIP_SCORE, "装备榜");
        RANK_NAMES.put(TOTAL_SCORE, "综合榜");
        
        // 初始化排行榜描述
        RANK_DESCRIPTIONS.put(LEVEL, "根据角色等级进行排行");
        RANK_DESCRIPTIONS.put(EXPERIENCE, "根据角色经验值进行排行");
        RANK_DESCRIPTIONS.put(COPPER, "根据角色铜钱数量进行排行");
        RANK_DESCRIPTIONS.put(ATTACK, "根据角色攻击力进行排行");
        RANK_DESCRIPTIONS.put(DEFENSE, "根据角色防御力进行排行");
        RANK_DESCRIPTIONS.put(AGILITY, "根据角色敏捷进行排行");
        RANK_DESCRIPTIONS.put(KILL_MONSTER, "根据杀怪数量进行排行");
        RANK_DESCRIPTIONS.put(PK_WIN, "根据PK胜利次数进行排行");
        RANK_DESCRIPTIONS.put(ONLINE_TIME, "根据在线时长进行排行");
        RANK_DESCRIPTIONS.put(PET_LEVEL, "根据宠物等级进行排行");
        RANK_DESCRIPTIONS.put(PET_ATTACK, "根据宠物攻击力进行排行");
        RANK_DESCRIPTIONS.put(EQUIP_SCORE, "根据装备评分进行排行");
        RANK_DESCRIPTIONS.put(TOTAL_SCORE, "根据综合实力进行排行");
        
        // 初始化排行榜分类
        RANK_CATEGORIES.put(LEVEL, CATEGORY_PERSONAL);
        RANK_CATEGORIES.put(EXPERIENCE, CATEGORY_PERSONAL);
        RANK_CATEGORIES.put(COPPER, CATEGORY_PERSONAL);
        RANK_CATEGORIES.put(ATTACK, CATEGORY_BATTLE);
        RANK_CATEGORIES.put(DEFENSE, CATEGORY_BATTLE);
        RANK_CATEGORIES.put(AGILITY, CATEGORY_BATTLE);
        RANK_CATEGORIES.put(KILL_MONSTER, CATEGORY_BATTLE);
        RANK_CATEGORIES.put(PK_WIN, CATEGORY_BATTLE);
        RANK_CATEGORIES.put(ONLINE_TIME, CATEGORY_PERSONAL);
        RANK_CATEGORIES.put(PET_LEVEL, CATEGORY_PET);
        RANK_CATEGORIES.put(PET_ATTACK, CATEGORY_PET);
        RANK_CATEGORIES.put(EQUIP_SCORE, CATEGORY_EQUIPMENT);
        RANK_CATEGORIES.put(TOTAL_SCORE, CATEGORY_PERSONAL);
        
        // 初始化排行榜单位
        RANK_UNITS.put(LEVEL, "级");
        RANK_UNITS.put(EXPERIENCE, "点");
        RANK_UNITS.put(COPPER, "铜钱");
        RANK_UNITS.put(ATTACK, "点");
        RANK_UNITS.put(DEFENSE, "点");
        RANK_UNITS.put(AGILITY, "点");
        RANK_UNITS.put(KILL_MONSTER, "只");
        RANK_UNITS.put(PK_WIN, "次");
        RANK_UNITS.put(ONLINE_TIME, "分钟");
        RANK_UNITS.put(PET_LEVEL, "级");
        RANK_UNITS.put(PET_ATTACK, "点");
        RANK_UNITS.put(EQUIP_SCORE, "分");
        RANK_UNITS.put(TOTAL_SCORE, "分");
        
        // 初始化第一名称号
        FIRST_TITLES.put(LEVEL, "★至尊★");
        FIRST_TITLES.put(EXPERIENCE, "★修仙大能★");
        FIRST_TITLES.put(COPPER, "★财神★");
        FIRST_TITLES.put(ATTACK, "★武林盟主★");
        FIRST_TITLES.put(DEFENSE, "★铁壁金刚★");
        FIRST_TITLES.put(AGILITY, "★疾风剑圣★");
        FIRST_TITLES.put(KILL_MONSTER, "★屠魔大师★");
        FIRST_TITLES.put(PK_WIN, "★战神★");
        FIRST_TITLES.put(ONLINE_TIME, "★修行者★");
        FIRST_TITLES.put(PET_LEVEL, "★御兽大师★");
        FIRST_TITLES.put(PET_ATTACK, "★神兽之主★");
        FIRST_TITLES.put(EQUIP_SCORE, "★神装大师★");
        FIRST_TITLES.put(TOTAL_SCORE, "★洪荒至尊★");
    }
    
    /**
     * 获取排行榜名称
     */
    public static String getRankName(String type) {
        return RANK_NAMES.getOrDefault(type, "未知榜");
    }
    
    /**
     * 获取排行榜描述
     */
    public static String getRankDescription(String type) {
        return RANK_DESCRIPTIONS.getOrDefault(type, "");
    }
    
    /**
     * 获取排行榜分类
     */
    public static Integer getRankCategory(String type) {
        return RANK_CATEGORIES.getOrDefault(type, CATEGORY_PERSONAL);
    }
    
    /**
     * 获取排行榜单位
     */
    public static String getRankUnit(String type) {
        return RANK_UNITS.getOrDefault(type, "");
    }
    
    /**
     * 获取第一名称号
     */
    public static String getFirstTitle(String type) {
        return FIRST_TITLES.getOrDefault(type, "★第一名★");
    }
}
