<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>洪荒Online</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #1a2332;
            color: #fff;
            font-size: 14px;
            line-height: 1.4;
        }

        .container {
            max-width: 480px;
            margin: 0 auto;
            background: #1a2332;
            min-height: 100vh;
        }

        .header {
            background: #2c3e50;
            padding: 10px;
            text-align: center;
            border-bottom: 2px solid #34495e;
        }

        .game-title {
            color: #3498db;
            font-size: 18px;
            font-weight: bold;
        }

        .main-content {
            display: flex;
            min-height: calc(100vh - 60px);
        }

        .left-panel {
            width: 140px;
            background: #2c3e50;
            border-right: 1px solid #34495e;
            padding: 10px 5px;
        }

        .center-panel {
            flex: 1;
            background: #34495e;
            padding: 10px;
        }

        .right-panel {
            width: 120px;
            background: #2c3e50;
            border-left: 1px solid #34495e;
            padding: 10px 5px;
        }

        .role-info {
            text-align: center;
            margin-bottom: 15px;
        }

        .role-avatar {
            width: 50px;
            height: 50px;
            background: #e74c3c;
            border-radius: 50%;
            margin: 0 auto 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .role-name {
            color: #f39c12;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .role-level {
            color: #3498db;
            font-size: 12px;
        }

        .stats-list {
            margin-bottom: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 3px 0;
            font-size: 12px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .stat-label {
            color: #bdc3c7;
        }

        .stat-value {
            color: #fff;
            font-weight: bold;
        }

        .hp-bar, .mp-bar {
            margin-bottom: 8px;
        }

        .bar-info {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            margin-bottom: 2px;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(0,0,0,0.3);
            border-radius: 6px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            transition: width 0.3s;
        }

        .hp-fill {
            background: #e74c3c;
        }

        .mp-fill {
            background: #3498db;
        }

        .game-area {
            background: rgba(0,0,0,0.2);
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            min-height: 200px;
        }

        .area-title {
            color: #f39c12;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            text-align: center;
        }

        .area-content {
            color: #bdc3c7;
            font-size: 12px;
            line-height: 1.5;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-bottom: 10px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 4px;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            display: block;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3px;
            margin-bottom: 10px;
        }

        .inventory-slot {
            aspect-ratio: 1;
            background: rgba(0,0,0,0.3);
            border: 1px solid #34495e;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            cursor: pointer;
        }

        .inventory-slot:hover {
            border-color: #3498db;
        }

        .menu-list {
            list-style: none;
        }

        .menu-item {
            margin-bottom: 5px;
        }

        .menu-link {
            display: block;
            background: #3498db;
            color: white;
            padding: 8px 6px;
            text-decoration: none;
            border-radius: 3px;
            font-size: 11px;
            text-align: center;
        }

        .menu-link:hover {
            background: #2980b9;
        }

        .chat-area {
            background: rgba(0,0,0,0.3);
            border-radius: 3px;
            padding: 8px;
            height: 120px;
            overflow-y: auto;
            margin-bottom: 8px;
            font-size: 11px;
        }

        .chat-input-area {
            display: flex;
            gap: 5px;
        }

        .chat-input {
            flex: 1;
            background: rgba(0,0,0,0.3);
            border: 1px solid #34495e;
            border-radius: 3px;
            padding: 5px;
            color: #fff;
            font-size: 11px;
        }

        .chat-send {
            background: #27ae60;
            color: white;
            border: none;
            padding: 5px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }

        .message {
            margin-bottom: 5px;
            padding: 3px;
            border-radius: 3px;
            font-size: 11px;
        }

        .system-message {
            color: #f39c12;
        }

        .error-message {
            color: #e74c3c;
        }

        .success-message {
            color: #27ae60;
        }

        /* 简单的模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background: #2c3e50;
            margin: 10% auto;
            padding: 15px;
            border-radius: 5px;
            width: 90%;
            max-width: 400px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #34495e;
        }

        .modal-title {
            color: #f39c12;
            font-size: 14px;
            font-weight: bold;
        }

        .close {
            color: #bdc3c7;
            font-size: 20px;
            cursor: pointer;
        }

        .close:hover {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="game-title">洪荒Online</div>
        </div>

        <div class="main-content">
            <!-- 左侧角色信息 -->
            <div class="left-panel">
                <div class="role-info">
                    <div class="role-avatar" id="roleAvatar">巫</div>
                    <div class="role-name" id="roleName">李逍遥</div>
                    <div class="role-level" id="roleLevel">等级: 1</div>
                </div>

                <div class="stats-list">
                    <div class="stat-item">
                        <span class="stat-label">生命</span>
                        <span class="stat-value" id="currentHp">200/200</span>
                    </div>
                    <div class="hp-bar">
                        <div class="progress-bar">
                            <div class="progress-fill hp-fill" id="hpBar" style="width: 100%"></div>
                        </div>
                    </div>

                    <div class="stat-item">
                        <span class="stat-label">法力</span>
                        <span class="stat-value" id="currentMp">100/100</span>
                    </div>
                    <div class="mp-bar">
                        <div class="progress-bar">
                            <div class="progress-fill mp-fill" id="mpBar" style="width: 100%"></div>
                        </div>
                    </div>

                    <div class="stat-item">
                        <span class="stat-label">攻击</span>
                        <span class="stat-value" id="attack">13</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">防御</span>
                        <span class="stat-value" id="defense">10</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">敏捷</span>
                        <span class="stat-value" id="agility">10</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">铜钱</span>
                        <span class="stat-value" id="copper">0</span>
                    </div>
                </div>
            </div>

            <!-- 中间游戏区域 -->
            <div class="center-panel">
                <div class="game-area">
                    <div class="area-title" id="sceneTitle">新手村</div>
                    <div class="area-content" id="gameContent">
                        欢迎来到洪荒世界！在这个充满神秘力量的世界里，你将踏上修仙之路。<br><br>
                        你可以通过战斗、完成任务、培养宠物来提升实力。选择你的道路，成为传说中的仙人！
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn" onclick="showBattle()">🗡 战斗</button>
                    <button class="btn btn-success" onclick="showPet()">🐾 宠物</button>
                    <button class="btn btn-warning" onclick="showEquipment()">⚔️ 装备</button>
                    <button class="btn" onclick="showShop()">🛍️ 商店</button>
                    <button class="btn" onclick="showQuest()">📋 任务</button>
                    <button class="btn" onclick="showSkill()">⚡ 技能</button>
                </div>
            </div>

            <!-- 右侧功能区 -->
            <div class="right-panel">
                <div class="inventory-grid" id="inventoryGrid">
                    <!-- 背包格子 -->
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                    <div class="inventory-slot"></div>
                </div>

                <ul class="menu-list">
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showChat()">💬 聊天</a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showGuild()">🏰 帮派</a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showMail()">📧 邮件</a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showAuction()">🎪 拍卖</a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link btn-danger" onclick="logout()">🚪 退出</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 聊天模态框 -->
    <div id="chatModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="modal-title">💬 聊天</span>
                <span class="close" onclick="closeModal('chatModal')">&times;</span>
            </div>
            <div class="chat-area" id="chatMessages">
                <div class="message system-message">[系统] 欢迎来到洪荒世界！</div>
                <div class="message system-message">[系统] 请遵守游戏规则，文明游戏。</div>
            </div>
            <div class="chat-input-area">
                <input type="text" class="chat-input" id="chatInput" placeholder="输入消息..." maxlength="50">
                <button class="chat-send" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <script>
        let currentRole = null;

        // 页面加载时初始化
        window.onload = function() {
            loadCurrentRole();
        };

        // 加载当前角色信息
        function loadCurrentRole() {
            fetch('/honghuang-game/api/role/current')
                .then(response => response.json())
                .then(result => {
                    if (result.success && result.data) {
                        currentRole = result.data;
                        updateRoleDisplay();
                    } else {
                        // 没有角色，跳转到角色选择页面
                        window.location.href = '/honghuang-game/role-select.html';
                    }
                })
                .catch(error => {
                    console.error('加载角色信息失败:', error);
                    addMessage('加载角色信息失败', 'error');
                });
        }

        // 更新角色显示
        function updateRoleDisplay() {
            if (!currentRole) return;

            document.getElementById('roleAvatar').textContent = currentRole.race === 1 ? '巫' : '妖';
            document.getElementById('roleName').textContent = currentRole.name;
            document.getElementById('roleLevel').textContent = `等级: ${currentRole.level}`;
            document.getElementById('currentHp').textContent = `${currentRole.currentHp}/${currentRole.maxHp}`;
            document.getElementById('currentMp').textContent = `${currentRole.currentMp}/${currentRole.maxMp}`;
            document.getElementById('attack').textContent = currentRole.attack;
            document.getElementById('defense').textContent = currentRole.defense;
            document.getElementById('agility').textContent = currentRole.agility;
            document.getElementById('copper').textContent = currentRole.copper;

            // 更新血条和蓝条
            const hpPercent = (currentRole.currentHp / currentRole.maxHp) * 100;
            const mpPercent = (currentRole.currentMp / currentRole.maxMp) * 100;
            document.getElementById('hpBar').style.width = hpPercent + '%';
            document.getElementById('mpBar').style.width = mpPercent + '%';
        }

        // 显示不同功能页面
        function showBattle() {
            window.location.href = '/honghuang-game/battle.html';
        }

        function showPet() {
            window.location.href = '/honghuang-game/pet.html';
        }

        function showEquipment() {
            addMessage('装备系统开发中...', 'system');
        }

        function showShop() {
            window.location.href = '/honghuang-game/shop.html';
        }

        function showQuest() {
            window.location.href = '/honghuang-game/quest.html';
        }

        function showSkill() {
            window.location.href = '/honghuang-game/skill.html';
        }

        function showChat() {
            document.getElementById('chatModal').style.display = 'block';
        }

        function showGuild() {
            window.location.href = '/honghuang-game/guild.html';
        }

        function showMail() {
            window.location.href = '/honghuang-game/mail.html';
        }

        function showAuction() {
            window.location.href = '/honghuang-game/auction.html';
        }

        function logout() {
            if (confirm('确定要退出游戏吗？')) {
                fetch('/honghuang-game/api/user/logout', { method: 'POST' })
                    .then(() => {
                        window.location.href = '/honghuang-game/';
                    });
            }
        }

        // 模态框控制
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 发送聊天消息
        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (message) {
                addChatMessage(`[${currentRole?.name || '玩家'}] ${message}`);
                input.value = '';
            }
        }

        function addChatMessage(message) {
            const chatArea = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.textContent = message;
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function addMessage(message, type = 'system') {
            const content = document.getElementById('gameContent');
            const messageClass = type + '-message';
            content.innerHTML += `<div class="message ${messageClass}">[${type.toUpperCase()}] ${message}</div>`;
        }

        // 回车发送消息
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && document.getElementById('chatModal').style.display === 'block') {
                sendMessage();
            }
        });

        // 定时更新角色信息
        setInterval(() => {
            if (currentRole) {
                loadCurrentRole();
            }
        }, 30000); // 每30秒更新一次
    </script>
</body>
</html>
