package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 玩家物品控制器
 */
@RestController
@RequestMapping("/api/player-item")
public class PlayerItemController {

    @GetMapping("/bag")
    public Result<List<Map<String, Object>>> getBagItems(HttpServletRequest request) {
        HttpSession session = request.getSession();
        Integer userId = (Integer) session.getAttribute("userId");
        
        if (userId != null) {
            // 模拟背包物品数据
            List<Map<String, Object>> bagItems = new ArrayList<>();
            
            Map<String, Object> item1 = new HashMap<>();
            item1.put("id", 1);
            item1.put("itemId", 1001);
            item1.put("itemName", "新手剑");
            item1.put("itemType", 1);
            item1.put("quantity", 1);
            item1.put("position", 1);
            item1.put("index", 0);
            bagItems.add(item1);
            
            Map<String, Object> item2 = new HashMap<>();
            item2.put("id", 2);
            item2.put("itemId", 2001);
            item2.put("itemName", "生命药水");
            item2.put("itemType", 2);
            item2.put("quantity", 5);
            item2.put("position", 1);
            item2.put("index", 1);
            bagItems.add(item2);
            
            return Result.success("获取背包物品成功", bagItems);
        } else {
            return Result.error(401, "用户未登录");
        }
    }
}
