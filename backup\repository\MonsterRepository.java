package com.honghuang.game.repository;

import com.honghuang.game.entity.Monster;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 怪物Repository
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface MonsterRepository extends JpaRepository<Monster, Integer> {

    /**
     * 根据名称查找怪物
     */
    Optional<Monster> findByName(String name);

    /**
     * 根据类型查找怪物
     */
    List<Monster> findByType(Integer type);

    /**
     * 根据等级范围查找怪物
     */
    List<Monster> findByLevelBetween(Integer minLevel, Integer maxLevel);

    /**
     * 根据是否激活查找怪物
     */
    List<Monster> findByIsActive(Integer isActive);

    /**
     * 查找激活的怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.isActive = 1")
    List<Monster> findActiveMonsters();

    /**
     * 根据名称模糊查找怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.name LIKE %:name% AND m.isActive = 1")
    List<Monster> findByNameContaining(@Param("name") String name);

    /**
     * 查找普通怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.type = 1 AND m.isActive = 1")
    List<Monster> findNormalMonsters();

    /**
     * 查找精英怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.type = 2 AND m.isActive = 1")
    List<Monster> findEliteMonsters();

    /**
     * 查找BOSS
     */
    @Query("SELECT m FROM Monster m WHERE m.type = 3 AND m.isActive = 1")
    List<Monster> findBosses();

    /**
     * 查找NPC
     */
    @Query("SELECT m FROM Monster m WHERE m.type = 4 AND m.isActive = 1")
    List<Monster> findNpcs();

    /**
     * 根据等级查找适合的怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.level BETWEEN :minLevel AND :maxLevel AND m.isActive = 1 AND m.type != 4")
    List<Monster> findSuitableMonsters(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 根据玩家等级推荐怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.level BETWEEN :playerLevel - 3 AND :playerLevel + 3 AND m.isActive = 1 AND m.type != 4")
    List<Monster> findRecommendedMonsters(@Param("playerLevel") Integer playerLevel);

    /**
     * 查找可攻击的怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.isActive = 1 AND m.type IN (1, 2, 3)")
    List<Monster> findAttackableMonsters();

    /**
     * 根据AI类型查找怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.aiType = :aiType AND m.isActive = 1")
    List<Monster> findByAiType(@Param("aiType") Integer aiType);

    /**
     * 查找主动攻击的怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.aiType = 2 AND m.isActive = 1")
    List<Monster> findAggressiveMonsters();

    /**
     * 查找被动怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.aiType = 1 AND m.isActive = 1")
    List<Monster> findPassiveMonsters();

    /**
     * 根据攻击力范围查找怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.attack BETWEEN :minAttack AND :maxAttack AND m.isActive = 1")
    List<Monster> findByAttackRange(@Param("minAttack") Integer minAttack, @Param("maxAttack") Integer maxAttack);

    /**
     * 根据生命值范围查找怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.maxHp BETWEEN :minHp AND :maxHp AND m.isActive = 1")
    List<Monster> findByHpRange(@Param("minHp") Integer minHp, @Param("maxHp") Integer maxHp);

    /**
     * 查找经验值奖励丰富的怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.expReward >= :minExp AND m.isActive = 1 ORDER BY m.expReward DESC")
    List<Monster> findHighExpMonsters(@Param("minExp") Long minExp);

    /**
     * 查找铜钱奖励丰富的怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.copperReward >= :minCopper AND m.isActive = 1 ORDER BY m.copperReward DESC")
    List<Monster> findHighCopperMonsters(@Param("minCopper") Long minCopper);

    /**
     * 分页查找怪物
     */
    @Query("SELECT m FROM Monster m WHERE " +
           "(:type IS NULL OR m.type = :type) AND " +
           "(:minLevel IS NULL OR m.level >= :minLevel) AND " +
           "(:maxLevel IS NULL OR m.level <= :maxLevel) AND " +
           "m.isActive = 1")
    Page<Monster> findByConditions(@Param("type") Integer type,
                                  @Param("minLevel") Integer minLevel,
                                  @Param("maxLevel") Integer maxLevel,
                                  Pageable pageable);

    /**
     * 统计各类型怪物数量
     */
    @Query("SELECT m.type, COUNT(m) FROM Monster m WHERE m.isActive = 1 GROUP BY m.type")
    List<Object[]> countByType();

    /**
     * 统计各等级段怪物数量
     */
    @Query("SELECT FLOOR(m.level / 10) * 10 as levelRange, COUNT(m) FROM Monster m WHERE m.isActive = 1 GROUP BY FLOOR(m.level / 10)")
    List<Object[]> countByLevelRange();

    /**
     * 查找有掉落物品的怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.dropItems IS NOT NULL AND m.dropItems != '' AND m.isActive = 1")
    List<Monster> findMonstersWithDrops();

    /**
     * 查找有技能的怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.skills IS NOT NULL AND m.skills != '' AND m.isActive = 1")
    List<Monster> findMonstersWithSkills();

    /**
     * 根据刷新时间查找怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.respawnTime <= :maxRespawnTime AND m.isActive = 1")
    List<Monster> findByMaxRespawnTime(@Param("maxRespawnTime") Integer maxRespawnTime);

    /**
     * 查找快速刷新的怪物（刷新时间少于5分钟）
     */
    @Query("SELECT m FROM Monster m WHERE m.respawnTime <= 300 AND m.isActive = 1")
    List<Monster> findFastRespawnMonsters();

    /**
     * 根据移动速度查找怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.moveSpeed >= :minSpeed AND m.isActive = 1")
    List<Monster> findByMinMoveSpeed(@Param("minSpeed") Integer minSpeed);

    /**
     * 查找远程攻击怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.attackRange > 1 AND m.isActive = 1")
    List<Monster> findRangedMonsters();

    /**
     * 查找近战怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.attackRange = 1 AND m.isActive = 1")
    List<Monster> findMeleeMonsters();

    /**
     * 根据视野范围查找怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.sightRange >= :minSight AND m.isActive = 1")
    List<Monster> findByMinSightRange(@Param("minSight") Integer minSight);

    /**
     * 根据激活状态统计怪物数量
     */
    List<Monster> countByIsActive(Integer isActive);
}
