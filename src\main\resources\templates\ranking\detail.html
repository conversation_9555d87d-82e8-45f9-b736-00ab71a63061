<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'洪荒世界 - ' + ${typeName}">洪荒世界 - 排行榜详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #fff;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.2em;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            color: #e0e0e0;
        }

        .player-rank {
            background: linear-gradient(145deg, rgba(255,215,0,0.1) 0%, rgba(255,215,0,0.05) 100%);
            border: 2px solid rgba(255,215,0,0.3);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            margin-bottom: 25px;
            backdrop-filter: blur(10px);
        }

        .player-rank.no-rank {
            border-color: rgba(255,255,255,0.3);
            background: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        }

        .ranking-list {
            background: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .ranking-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: linear-gradient(145deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%);
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }

        .ranking-item:hover {
            transform: translateX(5px);
            border-color: rgba(255,215,0,0.3);
        }

        .ranking-item.first {
            background: linear-gradient(145deg, rgba(255,215,0,0.2) 0%, rgba(255,215,0,0.1) 100%);
            border-color: #ffd700;
        }

        .ranking-item.second {
            background: linear-gradient(145deg, rgba(192,192,192,0.2) 0%, rgba(192,192,192,0.1) 100%);
            border-color: #c0c0c0;
        }

        .ranking-item.third {
            background: linear-gradient(145deg, rgba(205,127,50,0.2) 0%, rgba(205,127,50,0.1) 100%);
            border-color: #cd7f32;
        }

        .rank-number {
            font-size: 1.5em;
            font-weight: bold;
            width: 60px;
            text-align: center;
        }

        .rank-number.first { color: #ffd700; }
        .rank-number.second { color: #c0c0c0; }
        .rank-number.third { color: #cd7f32; }

        .player-info {
            flex: 1;
            margin-left: 15px;
        }

        .player-name {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .player-details {
            font-size: 0.9em;
            color: #ccc;
        }

        .race-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-right: 10px;
        }

        .race-wu { background: #ff6b6b; }
        .race-yao { background: #4ecdc4; }

        .rank-value {
            font-size: 1.3em;
            font-weight: bold;
            color: #ffd700;
            text-align: right;
            min-width: 100px;
        }

        .first-title {
            color: #ffd700;
            font-size: 0.9em;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,215,0,0.2);
            border: 2px solid #ffd700;
            color: #ffd700;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #ffd700;
            color: #1e3c72;
        }

        .empty-list {
            text-align: center;
            padding: 40px;
            color: #ccc;
            font-size: 1.1em;
        }

        .refresh-btn {
            background: rgba(255,215,0,0.2);
            border: 2px solid #ffd700;
            color: #ffd700;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
        }

        .refresh-btn:hover {
            background: #ffd700;
            color: #1e3c72;
        }

        /* 手机端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .ranking-item {
                padding: 12px;
            }

            .rank-number {
                font-size: 1.3em;
                width: 50px;
            }

            .player-info {
                margin-left: 10px;
            }

            .player-name {
                font-size: 1.1em;
            }

            .rank-value {
                font-size: 1.1em;
                min-width: 80px;
            }

            .back-btn {
                position: static;
                display: block;
                width: fit-content;
                margin: 0 auto 20px;
            }
        }
    </style>
</head>
<body>
    <a href="/honghuang-game/ranking/index" class="back-btn">← 返回排行榜</a>
    
    <div class="container">
        <div class="header">
            <h1 th:text="${typeName}">排行榜名称</h1>
            <p th:text="${description}">排行榜描述</p>
        </div>

        <!-- 玩家排名信息 -->
        <div class="player-rank" th:classappend="${playerRank > 0} ? '' : 'no-rank'">
            <div th:if="${playerRank > 0}">
                <strong>您的排名：第 <span th:text="${playerRank}" style="color: #ffd700;">0</span> 名</strong>
            </div>
            <div th:unless="${playerRank > 0}">
                <strong>您暂未上榜</strong>
                <br><small>努力提升实力，争取上榜吧！</small>
            </div>
        </div>

        <!-- 排行榜列表 -->
        <div class="ranking-list">
            <div th:if="${#lists.isEmpty(rankingList)}" class="empty-list">
                <p>暂无排行榜数据</p>
                <button class="refresh-btn" onclick="location.reload()">刷新</button>
            </div>
            
            <div th:unless="${#lists.isEmpty(rankingList)}">
                <div class="ranking-item" 
                     th:each="item : ${rankingList}"
                     th:classappend="${item.rank == 1} ? 'first' : (${item.rank == 2} ? 'second' : (${item.rank == 3} ? 'third' : ''))">
                    
                    <div class="rank-number" 
                         th:classappend="${item.rank == 1} ? 'first' : (${item.rank == 2} ? 'second' : (${item.rank == 3} ? 'third' : ''))">
                        <span th:if="${item.rank <= 3}">
                            <span th:if="${item.rank == 1}">🥇</span>
                            <span th:if="${item.rank == 2}">🥈</span>
                            <span th:if="${item.rank == 3}">🥉</span>
                        </span>
                        <span th:unless="${item.rank <= 3}" th:text="${item.rank}">4</span>
                    </div>
                    
                    <div class="player-info">
                        <div class="player-name">
                            <span th:text="${item.roleName}">玩家名称</span>
                            <span th:if="${item.isFirst}" class="first-title" th:text="${item.firstTitle}">★第一名★</span>
                        </div>
                        <div class="player-details">
                            <span class="race-badge" th:classappend="${item.race == 1} ? 'race-wu' : 'race-yao'" 
                                  th:text="${item.raceName}">种族</span>
                            <span th:text="'等级: ' + ${item.level}">等级: 1</span>
                        </div>
                    </div>
                    
                    <div class="rank-value">
                        <span th:text="${item.valueStr}">数值</span>
                        <span th:if="${item.unit}" th:text="${item.unit}">单位</span>
                    </div>
                </div>
            </div>
        </div>

        <button class="refresh-btn" onclick="location.reload()">刷新排行榜</button>
    </div>

    <script>
        // 自动刷新功能（可选）
        let autoRefresh = false;
        let refreshInterval;

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            if (autoRefresh) {
                refreshInterval = setInterval(() => {
                    location.reload();
                }, 30000); // 30秒刷新一次
                console.log('开启自动刷新');
            } else {
                clearInterval(refreshInterval);
                console.log('关闭自动刷新');
            }
        }

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'r' || e.key === 'R') {
                location.reload();
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('排行榜页面加载完成');
            
            // 添加排行榜项目点击效果
            document.querySelectorAll('.ranking-item').forEach(item => {
                item.addEventListener('click', function() {
                    // 可以在这里添加点击玩家的详细信息展示
                    const playerName = this.querySelector('.player-name').textContent;
                    console.log('点击玩家:', playerName);
                });
            });
        });
    </script>
</body>
</html>
