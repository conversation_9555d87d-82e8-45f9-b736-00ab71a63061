package com.honghuang.game.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 数据初始化配置
 * 在应用启动时执行数据库初始化脚本
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
// @Component  // 暂时禁用数据初始化，避免启动时卡住
public class DataInitializationConfig implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始执行数据库初始化...");
        
        try {
            // 1. 执行表结构更新
            executeScript("data/schema_updates.sql");
            
            // 2. 检查并初始化基础数据
            initializeItems();
            initializeGameItems();
            initializeGameScenes();
            initializeMonsters();
            
            log.info("数据库初始化完成");
        } catch (Exception e) {
            log.error("数据库初始化失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 初始化物品数据
     */
    private void initializeItems() {
        try {
            // 检查是否已有物品数据
            Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM item WHERE active = 1", Integer.class);

            if (count == null || count == 0) {
                log.info("初始化物品数据...");
                executeScript("data/items.sql");
                log.info("物品数据初始化完成");
            } else {
                log.info("物品数据已存在，跳过初始化");
            }
        } catch (Exception e) {
            log.error("初始化物品数据失败", e);
        }
    }

    /**
     * 初始化游戏物品数据
     */
    private void initializeGameItems() {
        try {
            // 检查是否已有物品数据
            Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM goods_info WHERE is_active = 1", Integer.class);

            if (count == null || count == 0) {
                log.info("初始化游戏物品数据...");
                executeScript("data/game_items.sql");
                log.info("游戏物品数据初始化完成");
            } else {
                log.info("游戏物品数据已存在，跳过初始化");
            }
        } catch (Exception e) {
            log.error("初始化游戏物品数据失败", e);
        }
    }

    /**
     * 初始化游戏场景数据
     */
    private void initializeGameScenes() {
        try {
            // 检查是否已有场景数据
            Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM scene_info WHERE is_active = 1", Integer.class);
            
            if (count == null || count == 0) {
                log.info("初始化游戏场景数据...");
                executeScript("data/game_scenes.sql");
                log.info("游戏场景数据初始化完成");
            } else {
                log.info("游戏场景数据已存在，跳过初始化");
            }
        } catch (Exception e) {
            log.error("初始化游戏场景数据失败", e);
        }
    }

    /**
     * 初始化怪物数据
     */
    private void initializeMonsters() {
        try {
            // 先清空现有怪物数据
            log.info("清空现有怪物数据...");
            jdbcTemplate.execute("DELETE FROM npc_info");

            // 重新初始化怪物数据
            log.info("初始化怪物数据...");
            executeScript("data/monsters.sql");
            log.info("怪物数据初始化完成");
        } catch (Exception e) {
            log.error("初始化怪物数据失败", e);
        }
    }

    /**
     * 执行SQL脚本
     */
    private void executeScript(String scriptPath) throws IOException {
        ClassPathResource resource = new ClassPathResource(scriptPath);
        if (!resource.exists()) {
            log.warn("SQL脚本文件不存在: {}", scriptPath);
            return;
        }

        byte[] scriptBytes = FileCopyUtils.copyToByteArray(resource.getInputStream());
        String script = new String(scriptBytes, StandardCharsets.UTF_8);
        
        // 分割SQL语句（以分号分隔）
        String[] statements = script.split(";");
        
        for (String statement : statements) {
            String trimmedStatement = statement.trim();
            if (!trimmedStatement.isEmpty() && !trimmedStatement.startsWith("--")) {
                try {
                    log.info("执行SQL: {}", trimmedStatement.substring(0, Math.min(100, trimmedStatement.length())));
                    jdbcTemplate.execute(trimmedStatement);
                    log.info("SQL执行成功");
                } catch (Exception e) {
                    log.error("执行SQL语句失败: {}", trimmedStatement.substring(0, Math.min(100, trimmedStatement.length())));
                    log.error("SQL执行错误详情", e);
                }
            }
        }
        
        log.info("SQL脚本执行完成: {}", scriptPath);
    }
}
