package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.service.PlayerRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 角色控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/role")
public class PlayerRoleController {

    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 获取当前角色信息
     */
    @GetMapping("/current")
    public Result<PlayerRole> getCurrentRole(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId == null) {
                return Result.error(401, "用户未登录");
            }

            // 获取用户的主角色
            PlayerRole role = playerRoleService.getMainRoleByUserId(userId);
            if (role != null) {
                return Result.success("获取角色信息成功", role);
            } else {
                return Result.error(404, "角色不存在");
            }
        } catch (Exception e) {
            return Result.error(500, "获取角色信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的所有角色
     */
    @GetMapping("/list")
    public Result<List<PlayerRole>> getUserRoles(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId == null) {
                return Result.error(401, "用户未登录");
            }

            List<PlayerRole> roles = playerRoleService.getRolesByUserId(userId);
            return Result.success("获取角色列表成功", roles);
        } catch (Exception e) {
            return Result.error(500, "获取角色列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建新角色
     */
    @PostMapping("/create")
    public Result<PlayerRole> createRole(@RequestBody Map<String, Object> roleData, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId == null) {
                return Result.error(401, "用户未登录");
            }

            String roleName = (String) roleData.get("roleName");
            Integer race = (Integer) roleData.get("race");
            Integer sex = (Integer) roleData.get("sex");

            if (roleName == null || roleName.trim().isEmpty()) {
                return Result.error(400, "角色名不能为空");
            }

            if (race == null || (race != 1 && race != 2)) {
                return Result.error(400, "请选择正确的种族（1-妖族，2-巫族）");
            }

            if (sex == null || (sex != 1 && sex != 2)) {
                return Result.error(400, "请选择正确的性别");
            }

            PlayerRole role = playerRoleService.createRole(userId, roleName, race, sex);
            return Result.success("创建角色成功", role);
        } catch (Exception e) {
            return Result.error(500, "创建角色失败: " + e.getMessage());
        }
    }

    /**
     * 选择角色
     */
    @PostMapping("/select/{roleId}")
    public Result<PlayerRole> selectRole(@PathVariable Integer roleId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId == null) {
                return Result.error(401, "用户未登录");
            }

            PlayerRole role = playerRoleService.selectRole(userId, roleId);

            // 在session中设置当前角色ID
            session.setAttribute("currentRoleId", roleId);
            log.info("设置当前角色ID到session: userId={}, roleId={}", userId, roleId);

            return Result.success("选择角色成功", role);
        } catch (Exception e) {
            return Result.error(500, "选择角色失败: " + e.getMessage());
        }
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{roleId}")
    public Result<Void> deleteRole(@PathVariable Integer roleId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId == null) {
                return Result.error(401, "用户未登录");
            }

            playerRoleService.deleteRole(userId, roleId);
            return Result.success("删除角色成功");
        } catch (Exception e) {
            return Result.error(500, "删除角色失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getRoleStatistics() {
        try {
            Map<String, Object> stats = playerRoleService.getRoleStatistics();
            return Result.success("获取角色统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取角色统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取等级排行榜
     */
    @GetMapping("/level-ranking")
    public Result<List<PlayerRole>> getLevelRanking(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<PlayerRole> ranking = playerRoleService.getLevelRanking(limit);
            return Result.success("获取等级排行榜成功", ranking);
        } catch (Exception e) {
            return Result.error(500, "获取等级排行榜失败: " + e.getMessage());
        }
    }

    /**
     * 获取财富排行榜
     */
    @GetMapping("/wealth-ranking")
    public Result<List<PlayerRole>> getWealthRanking(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<PlayerRole> ranking = playerRoleService.getGoldRanking(limit);
            return Result.success("获取财富排行榜成功", ranking);
        } catch (Exception e) {
            return Result.error(500, "获取财富排行榜失败: " + e.getMessage());
        }
    }

    /**
     * 角色升级
     */
    @PostMapping("/{roleId}/levelup")
    public Result<PlayerRole> levelUp(@PathVariable Integer roleId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId == null) {
                return Result.error(401, "用户未登录");
            }

            PlayerRole role = playerRoleService.levelUp(userId, roleId);
            return Result.success("角色升级成功", role);
        } catch (Exception e) {
            return Result.error(500, "角色升级失败: " + e.getMessage());
        }
    }

    /**
     * 恢复角色状态
     */
    @PostMapping("/{roleId}/recover")
    public Result<PlayerRole> recoverRole(@PathVariable Integer roleId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId == null) {
                return Result.error(401, "用户未登录");
            }

            PlayerRole role = playerRoleService.recoverRole(userId, roleId);
            return Result.success("角色状态恢复成功", role);
        } catch (Exception e) {
            return Result.error(500, "角色状态恢复失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色详细信息
     */
    @GetMapping("/{roleId}")
    public Result<PlayerRole> getRoleDetail(@PathVariable Integer roleId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId == null) {
                return Result.error(401, "用户未登录");
            }

            PlayerRole role = playerRoleService.getRoleDetail(userId, roleId);
            return Result.success("获取角色详情成功", role);
        } catch (Exception e) {
            return Result.error(500, "获取角色详情失败: " + e.getMessage());
        }
    }
}
