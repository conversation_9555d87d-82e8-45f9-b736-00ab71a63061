package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.PlayerSkill;
import com.honghuang.game.entity.Skill;
import com.honghuang.game.service.SkillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 技能控制器
 */
@RestController
@RequestMapping("/api/skill")
public class SkillController {

    @Autowired
    private SkillService skillService;

    /**
     * 获取所有可用技能
     */
    @GetMapping("/all")
    public Result<List<Skill>> getAllSkills() {
        try {
            List<Skill> skills = skillService.getAllSkills();
            return Result.success("获取技能列表成功", skills);
        } catch (Exception e) {
            return Result.error(500, "获取技能列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色可学习的技能
     */
    @GetMapping("/learnable")
    public Result<List<Skill>> getLearnableSkills(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Skill> skills = skillService.getLearnableSkills(currentRoleId);
            return Result.success("获取可学习技能成功", skills);
        } catch (Exception e) {
            return Result.error(500, "获取可学习技能失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色已学习的技能
     */
    @GetMapping("/learned")
    public Result<List<PlayerSkill>> getPlayerSkills(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<PlayerSkill> skills = skillService.getPlayerSkills(currentRoleId);
            return Result.success("获取已学习技能成功", skills);
        } catch (Exception e) {
            return Result.error(500, "获取已学习技能失败: " + e.getMessage());
        }
    }

    /**
     * 学习技能
     */
    @PostMapping("/learn/{skillId}")
    public Result<PlayerSkill> learnSkill(@PathVariable Integer skillId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            PlayerSkill playerSkill = skillService.learnSkill(currentRoleId, skillId);
            return Result.success("学习技能成功", playerSkill);
        } catch (Exception e) {
            return Result.error(500, "学习技能失败: " + e.getMessage());
        }
    }

    /**
     * 升级技能
     */
    @PostMapping("/upgrade/{skillId}")
    public Result<Void> upgradeSkill(@PathVariable Integer skillId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = skillService.upgradeSkill(currentRoleId, skillId);
            if (success) {
                return Result.success("升级技能成功");
            } else {
                return Result.error(400, "升级技能失败");
            }
        } catch (Exception e) {
            return Result.error(500, "升级技能失败: " + e.getMessage());
        }
    }

    /**
     * 使用技能
     */
    @PostMapping("/use/{skillId}")
    public Result<Void> useSkill(@PathVariable Integer skillId, 
                                @RequestParam(required = false) Integer targetId,
                                HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = skillService.useSkill(currentRoleId, skillId, targetId);
            if (success) {
                return Result.success("使用技能成功");
            } else {
                return Result.error(400, "使用技能失败");
            }
        } catch (Exception e) {
            return Result.error(500, "使用技能失败: " + e.getMessage());
        }
    }

    /**
     * 设置技能快捷键
     */
    @PostMapping("/shortcut/{skillId}")
    public Result<Void> setSkillShortcut(@PathVariable Integer skillId,
                                        @RequestBody Map<String, Integer> request,
                                        HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer shortcutKey = request.get("shortcutKey");
            if (shortcutKey == null) {
                return Result.error(400, "快捷键不能为空");
            }
            
            skillService.setSkillShortcut(currentRoleId, skillId, shortcutKey);
            return Result.success("设置快捷键成功");
        } catch (Exception e) {
            return Result.error(500, "设置快捷键失败: " + e.getMessage());
        }
    }

    /**
     * 获取快捷键技能
     */
    @GetMapping("/shortcuts")
    public Result<List<PlayerSkill>> getShortcutSkills(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<PlayerSkill> skills = skillService.getShortcutSkills(currentRoleId);
            return Result.success("获取快捷键技能成功", skills);
        } catch (Exception e) {
            return Result.error(500, "获取快捷键技能失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取技能
     */
    @GetMapping("/type/{type}")
    public Result<List<PlayerSkill>> getSkillsByType(@PathVariable Integer type, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<PlayerSkill> skills = skillService.getSkillsByType(currentRoleId, type);
            return Result.success("获取指定类型技能成功", skills);
        } catch (Exception e) {
            return Result.error(500, "获取指定类型技能失败: " + e.getMessage());
        }
    }
}
