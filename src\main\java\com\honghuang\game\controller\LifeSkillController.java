package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.LifeSkill;
import com.honghuang.game.entity.LifeSkillRecipe;
import com.honghuang.game.service.LifeSkillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 生活技能控制器
 */
@RestController
@RequestMapping("/api/life-skill")
public class LifeSkillController {

    @Autowired
    private LifeSkillService lifeSkillService;

    /**
     * 学习生活技能
     */
    @PostMapping("/learn")
    public Result<LifeSkill> learnSkill(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer skillType = request.get("skillType");
            
            if (skillType == null) {
                return Result.error(400, "技能类型不能为空");
            }
            
            LifeSkill skill = lifeSkillService.learnSkill(currentRoleId, skillType);
            return Result.success("学习技能成功", skill);
        } catch (Exception e) {
            return Result.error(500, "学习技能失败: " + e.getMessage());
        }
    }

    /**
     * 制作物品
     */
    @PostMapping("/craft")
    public Result<Void> craftItem(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer recipeId = request.get("recipeId");
            Integer quantity = request.get("quantity");
            
            if (recipeId == null || quantity == null) {
                return Result.error(400, "参数不完整");
            }
            
            boolean success = lifeSkillService.craftItem(currentRoleId, recipeId, quantity);
            if (success) {
                return Result.success("制作成功");
            } else {
                return Result.error(400, "制作失败");
            }
        } catch (Exception e) {
            return Result.error(500, "制作失败: " + e.getMessage());
        }
    }

    /**
     * 采集资源
     */
    @PostMapping("/gather")
    public Result<Void> gatherResource(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer resourceItemId = request.get("resourceItemId");
            Integer gatherTime = request.get("gatherTime");
            
            if (resourceItemId == null) {
                return Result.error(400, "资源物品ID不能为空");
            }
            
            if (gatherTime == null) {
                gatherTime = 5; // 默认5秒采集时间
            }
            
            boolean success = lifeSkillService.gatherResource(currentRoleId, resourceItemId, gatherTime);
            if (success) {
                return Result.success("采集成功");
            } else {
                return Result.error(400, "采集失败");
            }
        } catch (Exception e) {
            return Result.error(500, "采集失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色的生活技能列表
     */
    @GetMapping("/my-skills")
    public Result<List<LifeSkill>> getPlayerSkills(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<LifeSkill> skills = lifeSkillService.getPlayerSkills(currentRoleId);
            return Result.success("获取技能列表成功", skills);
        } catch (Exception e) {
            return Result.error(500, "获取技能列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定技能类型的配方列表
     */
    @GetMapping("/recipes/{skillType}")
    public Result<List<LifeSkillRecipe>> getRecipesBySkillType(@PathVariable Integer skillType) {
        try {
            List<LifeSkillRecipe> recipes = lifeSkillService.getRecipesBySkillType(skillType);
            return Result.success("获取配方列表成功", recipes);
        } catch (Exception e) {
            return Result.error(500, "获取配方列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色可制作的配方
     */
    @GetMapping("/craftable-recipes/{skillType}")
    public Result<List<LifeSkillRecipe>> getCraftableRecipes(@PathVariable Integer skillType, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<LifeSkillRecipe> recipes = lifeSkillService.getCraftableRecipes(currentRoleId, skillType);
            return Result.success("获取可制作配方成功", recipes);
        } catch (Exception e) {
            return Result.error(500, "获取可制作配方失败: " + e.getMessage());
        }
    }

    /**
     * 搜索配方
     */
    @GetMapping("/recipes/search")
    public Result<List<LifeSkillRecipe>> searchRecipes(@RequestParam String keyword) {
        try {
            List<LifeSkillRecipe> recipes = lifeSkillService.searchRecipes(keyword);
            return Result.success("搜索配方成功", recipes);
        } catch (Exception e) {
            return Result.error(500, "搜索配方失败: " + e.getMessage());
        }
    }

    /**
     * 获取配方详情
     */
    @GetMapping("/recipe/{recipeId}")
    public Result<LifeSkillRecipe> getRecipeDetail(@PathVariable Integer recipeId) {
        try {
            LifeSkillRecipe recipe = lifeSkillService.getRecipeDetail(recipeId);
            return Result.success("获取配方详情成功", recipe);
        } catch (Exception e) {
            return Result.error(500, "获取配方详情失败: " + e.getMessage());
        }
    }

    /**
     * 重置技能冷却
     */
    @PostMapping("/reset-cooldown")
    public Result<Void> resetSkillCooldown(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer skillType = request.get("skillType");
            
            if (skillType == null) {
                return Result.error(400, "技能类型不能为空");
            }
            
            boolean success = lifeSkillService.resetSkillCooldown(currentRoleId, skillType);
            if (success) {
                return Result.success("重置冷却成功");
            } else {
                return Result.error(400, "重置冷却失败");
            }
        } catch (Exception e) {
            return Result.error(500, "重置冷却失败: " + e.getMessage());
        }
    }

    /**
     * 获取技能统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getSkillStatistics(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, Object> stats = lifeSkillService.getSkillStatistics(currentRoleId);
            return Result.success("获取技能统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取技能统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取技能排行榜
     */
    @GetMapping("/ranking/{skillType}")
    public Result<List<LifeSkill>> getSkillRanking(@PathVariable Integer skillType) {
        try {
            List<LifeSkill> ranking = lifeSkillService.getSkillRanking(skillType);
            return Result.success("获取技能排行榜成功", ranking);
        } catch (Exception e) {
            return Result.error(500, "获取技能排行榜失败: " + e.getMessage());
        }
    }

    /**
     * 处理过期的技能冷却
     */
    @PostMapping("/process-expired-cooldowns")
    public Result<Void> processExpiredCooldowns() {
        try {
            lifeSkillService.processExpiredCooldowns();
            return Result.success("处理过期冷却成功");
        } catch (Exception e) {
            return Result.error(500, "处理过期冷却失败: " + e.getMessage());
        }
    }
}
