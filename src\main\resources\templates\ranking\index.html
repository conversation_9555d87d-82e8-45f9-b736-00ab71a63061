<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洪荒世界 - 排行榜</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #fff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            color: #e0e0e0;
        }

        .category-section {
            margin-bottom: 40px;
        }

        .category-title {
            font-size: 1.8em;
            color: #ffd700;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .ranking-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .ranking-card {
            background: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border: 2px solid rgba(255,215,0,0.3);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }

        .ranking-card:hover {
            transform: translateY(-5px);
            border-color: #ffd700;
            box-shadow: 0 10px 25px rgba(255,215,0,0.2);
        }

        .ranking-icon {
            font-size: 3em;
            margin-bottom: 15px;
            color: #ffd700;
        }

        .ranking-name {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
        }

        .ranking-desc {
            font-size: 0.9em;
            color: #ccc;
            line-height: 1.4;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,215,0,0.2);
            border: 2px solid #ffd700;
            color: #ffd700;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #ffd700;
            color: #1e3c72;
        }

        /* 手机端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .category-title {
                font-size: 1.5em;
            }

            .ranking-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
            }

            .ranking-card {
                padding: 15px;
            }

            .ranking-icon {
                font-size: 2.5em;
            }

            .ranking-name {
                font-size: 1.2em;
            }

            .back-btn {
                position: static;
                display: block;
                width: fit-content;
                margin: 0 auto 20px;
            }
        }

        /* 特殊图标样式 */
        .icon-level { color: #ff6b6b; }
        .icon-experience { color: #4ecdc4; }
        .icon-copper { color: #ffd93d; }
        .icon-attack { color: #ff4757; }
        .icon-defense { color: #5352ed; }
        .icon-agility { color: #26de81; }
        .icon-kill { color: #fd79a8; }
        .icon-pk { color: #e17055; }
        .icon-online { color: #74b9ff; }
        .icon-pet { color: #a29bfe; }
        .icon-equip { color: #fdcb6e; }
        .icon-total { color: #ffd700; }
    </style>
</head>
<body>
    <a href="/honghuang-game/" class="back-btn">← 返回游戏</a>
    
    <div class="container">
        <div class="header">
            <h1>洪荒世界排行榜</h1>
            <p>群雄逐鹿，谁与争锋</p>
        </div>

        <!-- 个人榜 -->
        <div class="category-section" th:if="${categories.containsKey(CATEGORY_PERSONAL)}">
            <h2 class="category-title">🏆 个人榜</h2>
            <div class="ranking-grid">
                <div class="ranking-card" th:each="rank : ${categories.get(CATEGORY_PERSONAL)}"
                     th:data-type="${rank.type}" onclick="navigateToRanking(this)">
                    <div class="ranking-icon" th:class="'icon-' + ${rank.type}">
                        <span th:switch="${rank.type}">
                            <span th:case="'level'">🎯</span>
                            <span th:case="'experience'">⭐</span>
                            <span th:case="'copper'">💰</span>
                            <span th:case="'onlineTime'">⏰</span>
                            <span th:case="'totalScore'">👑</span>
                            <span th:case="*">📊</span>
                        </span>
                    </div>
                    <div class="ranking-name" th:text="${rank.name}">排行榜名称</div>
                    <div class="ranking-desc" th:text="${rank.description}">排行榜描述</div>
                </div>
            </div>
        </div>

        <!-- 战斗榜 -->
        <div class="category-section" th:if="${categories.containsKey(CATEGORY_BATTLE)}">
            <h2 class="category-title">⚔️ 战斗榜</h2>
            <div class="ranking-grid">
                <div class="ranking-card" th:each="rank : ${categories.get(CATEGORY_BATTLE)}"
                     th:data-type="${rank.type}" onclick="navigateToRanking(this)">
                    <div class="ranking-icon" th:class="'icon-' + ${rank.type}">
                        <span th:switch="${rank.type}">
                            <span th:case="'attack'">⚔️</span>
                            <span th:case="'defense'">🛡️</span>
                            <span th:case="'agility'">💨</span>
                            <span th:case="'killMonster'">🗡️</span>
                            <span th:case="'pkWin'">🏆</span>
                            <span th:case="*">⚡</span>
                        </span>
                    </div>
                    <div class="ranking-name" th:text="${rank.name}">排行榜名称</div>
                    <div class="ranking-desc" th:text="${rank.description}">排行榜描述</div>
                </div>
            </div>
        </div>

        <!-- 宠物榜 -->
        <div class="category-section" th:if="${categories.containsKey(CATEGORY_PET)}">
            <h2 class="category-title">🐾 宠物榜</h2>
            <div class="ranking-grid">
                <div class="ranking-card" th:each="rank : ${categories.get(CATEGORY_PET)}"
                     th:data-type="${rank.type}" onclick="navigateToRanking(this)">
                    <div class="ranking-icon" th:class="'icon-' + ${rank.type}">
                        <span th:switch="${rank.type}">
                            <span th:case="'petLevel'">🐉</span>
                            <span th:case="'petAttack'">🦁</span>
                            <span th:case="*">🐾</span>
                        </span>
                    </div>
                    <div class="ranking-name" th:text="${rank.name}">排行榜名称</div>
                    <div class="ranking-desc" th:text="${rank.description}">排行榜描述</div>
                </div>
            </div>
        </div>

        <!-- 装备榜 -->
        <div class="category-section" th:if="${categories.containsKey(CATEGORY_EQUIPMENT)}">
            <h2 class="category-title">⚒️ 装备榜</h2>
            <div class="ranking-grid">
                <div class="ranking-card" th:each="rank : ${categories.get(CATEGORY_EQUIPMENT)}"
                     th:data-type="${rank.type}" onclick="navigateToRanking(this)">
                    <div class="ranking-icon" th:class="'icon-' + ${rank.type}">
                        <span th:switch="${rank.type}">
                            <span th:case="'equipScore'">⚒️</span>
                            <span th:case="*">🔧</span>
                        </span>
                    </div>
                    <div class="ranking-name" th:text="${rank.name}">排行榜名称</div>
                    <div class="ranking-desc" th:text="${rank.description}">排行榜描述</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导航到排行榜详情页
        function navigateToRanking(element) {
            const type = element.getAttribute('data-type');
            if (type) {
                window.location.href = '/honghuang-game/ranking/detail?type=' + type;
            }
        }

        // 添加点击音效（如果需要）
        document.querySelectorAll('.ranking-card').forEach(card => {
            card.addEventListener('click', function() {
                // 可以在这里添加点击音效
                console.log('点击排行榜:', this.querySelector('.ranking-name').textContent);
            });
        });
    </script>
</body>
</html>
