package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.Friend;
import com.honghuang.game.service.FriendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 好友控制器
 */
@RestController
@RequestMapping("/api/friend")
public class FriendController {

    @Autowired
    private FriendService friendService;

    /**
     * 添加好友
     */
    @PostMapping("/add")
    public Result<Friend> addFriend(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String friendName = request.get("friendName");
            
            if (friendName == null || friendName.trim().isEmpty()) {
                return Result.error(400, "好友名称不能为空");
            }
            
            Friend friend = friendService.addFriend(currentRoleId, friendName);
            return Result.success("好友添加成功", friend);
        } catch (Exception e) {
            return Result.error(500, "好友添加失败: " + e.getMessage());
        }
    }

    /**
     * 删除好友
     */
    @DeleteMapping("/{friendId}")
    public Result<Void> removeFriend(@PathVariable Integer friendId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = friendService.removeFriend(currentRoleId, friendId);
            if (success) {
                return Result.success("好友删除成功");
            } else {
                return Result.error(400, "好友删除失败");
            }
        } catch (Exception e) {
            return Result.error(500, "好友删除失败: " + e.getMessage());
        }
    }

    /**
     * 拉黑好友
     */
    @PostMapping("/{friendId}/block")
    public Result<Void> blockFriend(@PathVariable Integer friendId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = friendService.blockFriend(currentRoleId, friendId);
            if (success) {
                return Result.success("好友拉黑成功");
            } else {
                return Result.error(400, "好友拉黑失败");
            }
        } catch (Exception e) {
            return Result.error(500, "好友拉黑失败: " + e.getMessage());
        }
    }

    /**
     * 解除拉黑
     */
    @PostMapping("/{friendId}/unblock")
    public Result<Void> unblockFriend(@PathVariable Integer friendId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = friendService.unblockFriend(currentRoleId, friendId);
            if (success) {
                return Result.success("解除拉黑成功");
            } else {
                return Result.error(400, "解除拉黑失败");
            }
        } catch (Exception e) {
            return Result.error(500, "解除拉黑失败: " + e.getMessage());
        }
    }

    /**
     * 设置好友备注
     */
    @PostMapping("/{friendId}/remark")
    public Result<Void> setFriendRemark(@PathVariable Integer friendId,
                                       @RequestBody Map<String, String> request,
                                       HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String remark = request.get("remark");
            
            boolean success = friendService.setFriendRemark(currentRoleId, friendId, remark);
            if (success) {
                return Result.success("好友备注设置成功");
            } else {
                return Result.error(400, "好友备注设置失败");
            }
        } catch (Exception e) {
            return Result.error(500, "好友备注设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取好友列表
     */
    @GetMapping("/list")
    public Result<List<Friend>> getFriendList(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Friend> friends = friendService.getFriendList(currentRoleId);
            return Result.success("获取好友列表成功", friends);
        } catch (Exception e) {
            return Result.error(500, "获取好友列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取在线好友列表
     */
    @GetMapping("/online")
    public Result<List<Friend>> getOnlineFriends(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Friend> friends = friendService.getOnlineFriends(currentRoleId);
            return Result.success("获取在线好友成功", friends);
        } catch (Exception e) {
            return Result.error(500, "获取在线好友失败: " + e.getMessage());
        }
    }

    /**
     * 获取黑名单列表
     */
    @GetMapping("/blocked")
    public Result<List<Friend>> getBlockedFriends(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Friend> friends = friendService.getBlockedFriends(currentRoleId);
            return Result.success("获取黑名单成功", friends);
        } catch (Exception e) {
            return Result.error(500, "获取黑名单失败: " + e.getMessage());
        }
    }

    /**
     * 搜索好友
     */
    @GetMapping("/search")
    public Result<List<Friend>> searchFriends(@RequestParam String keyword, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            if (keyword == null || keyword.trim().isEmpty()) {
                return Result.error(400, "搜索关键词不能为空");
            }
            
            List<Friend> friends = friendService.searchFriends(currentRoleId, keyword);
            return Result.success("搜索好友成功", friends);
        } catch (Exception e) {
            return Result.error(500, "搜索好友失败: " + e.getMessage());
        }
    }

    /**
     * 获取好友详情
     */
    @GetMapping("/{friendId}")
    public Result<Friend> getFriendDetail(@PathVariable Integer friendId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Friend friend = friendService.getFriendDetail(currentRoleId, friendId);
            return Result.success("获取好友详情成功", friend);
        } catch (Exception e) {
            return Result.error(500, "获取好友详情失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否为好友
     */
    @GetMapping("/{friendId}/check")
    public Result<Map<String, Object>> checkFriendship(@PathVariable Integer friendId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean isFriend = friendService.isFriend(currentRoleId, friendId);
            boolean isBlocked = friendService.isBlocked(currentRoleId, friendId);
            
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("isFriend", isFriend);
            result.put("isBlocked", isBlocked);
            
            return Result.success("检查好友关系成功", result);
        } catch (Exception e) {
            return Result.error(500, "检查好友关系失败: " + e.getMessage());
        }
    }

    /**
     * 增加亲密度
     */
    @PostMapping("/{friendId}/intimacy")
    public Result<Void> addIntimacy(@PathVariable Integer friendId,
                                   @RequestBody Map<String, Integer> request,
                                   HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer intimacy = request.get("intimacy");
            
            if (intimacy == null || intimacy <= 0) {
                return Result.error(400, "亲密度必须大于0");
            }
            
            boolean success = friendService.addIntimacy(currentRoleId, friendId, intimacy);
            if (success) {
                return Result.success("亲密度增加成功");
            } else {
                return Result.error(400, "亲密度增加失败");
            }
        } catch (Exception e) {
            return Result.error(500, "亲密度增加失败: " + e.getMessage());
        }
    }

    /**
     * 获取好友统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getFriendStatistics(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, Object> stats = friendService.getFriendStatistics(currentRoleId);
            return Result.success("获取好友统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取好友统计失败: " + e.getMessage());
        }
    }

    /**
     * 更新好友在线状态
     */
    @PostMapping("/update-online-status")
    public Result<Void> updateOnlineStatus(@RequestBody Map<String, Boolean> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Boolean isOnline = request.get("isOnline");
            if (isOnline == null) {
                isOnline = true;
            }
            
            friendService.updateFriendOnlineStatus(currentRoleId, isOnline);
            return Result.success("在线状态更新成功");
        } catch (Exception e) {
            return Result.error(500, "在线状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新好友信息
     */
    @PostMapping("/batch-update")
    public Result<Void> batchUpdateFriendInfo() {
        try {
            friendService.batchUpdateFriendInfo();
            return Result.success("批量更新好友信息成功");
        } catch (Exception e) {
            return Result.error(500, "批量更新好友信息失败: " + e.getMessage());
        }
    }

    /**
     * 清理无效好友关系
     */
    @PostMapping("/cleanup")
    public Result<Void> cleanupInvalidFriends() {
        try {
            friendService.cleanupInvalidFriends();
            return Result.success("清理无效好友关系成功");
        } catch (Exception e) {
            return Result.error(500, "清理无效好友关系失败: " + e.getMessage());
        }
    }
}
