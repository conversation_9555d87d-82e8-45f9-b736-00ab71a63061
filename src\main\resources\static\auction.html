<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拍卖系统 - 洪荒游戏</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f8ff;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: #2c5aa0;
            border-bottom: 2px solid #66ccff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .auction-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            flex-wrap: wrap;
        }
        
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            margin-right: 5px;
        }
        
        .tab.active {
            color: #2c5aa0;
            border-bottom-color: #66ccff;
            background-color: #f8f9fa;
        }
        
        .tab:hover {
            background-color: #f8f9fa;
        }
        
        .search-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .search-btn {
            padding: 8px 16px;
            background-color: #2c5aa0;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .auction-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 15px;
            min-height: 300px;
        }
        
        .auction-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .auction-item:hover {
            background-color: #f0f8ff;
            border-color: #66ccff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .auction-item.expired {
            opacity: 0.6;
            background-color: #f8f8f8;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .item-name {
            font-weight: bold;
            color: #2c5aa0;
            font-size: 16px;
        }
        
        .item-quality {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            color: white;
        }
        
        .quality-1 { background-color: #6c757d; } /* 白色 */
        .quality-2 { background-color: #28a745; } /* 绿色 */
        .quality-3 { background-color: #007bff; } /* 蓝色 */
        .quality-4 { background-color: #6f42c1; } /* 紫色 */
        .quality-5 { background-color: #fd7e14; } /* 橙色 */
        .quality-6 { background-color: #dc3545; } /* 红色 */
        
        .auction-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .price-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .current-price {
            font-weight: bold;
            color: #dc3545;
            font-size: 16px;
        }
        
        .buyout-price {
            color: #28a745;
            font-size: 14px;
        }
        
        .time-left {
            color: #666;
            font-size: 12px;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .time-urgent {
            color: #dc3545;
            font-weight: bold;
        }
        
        .auction-actions {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #2c5aa0;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1e3d6f;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .seller-info {
            color: #666;
            font-size: 12px;
            text-align: center;
            margin-top: 5px;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal.show {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        
        .modal-title {
            color: #2c5aa0;
            font-weight: bold;
            font-size: 18px;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .bid-history {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .bid-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .bid-item:last-child {
            border-bottom: none;
        }
        
        .empty-message {
            text-align: center;
            color: #666;
            padding: 50px;
            font-style: italic;
            grid-column: 1 / -1;
        }
        
        .navigation {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .navigation a {
            color: #2c5aa0;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .navigation a:hover {
            text-decoration: underline;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .success {
            color: #155724;
            text-align: center;
            padding: 20px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .currency-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 3px;
        }
        
        .copper { background-color: #cd7f32; }
        .silver { background-color: #c0c0c0; }
        .gold { background-color: #ffd700; }
        
        .status-active { color: #28a745; }
        .status-deal { color: #007bff; }
        .status-failed { color: #dc3545; }
        .status-cancelled { color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>【拍卖系统】</h1>
        </div>
        
        <div id="message-area"></div>
        
        <div class="auction-tabs">
            <button class="tab active" onclick="switchTab('active')">进行中</button>
            <button class="tab" onclick="switchTab('my-auctions')">我的拍卖</button>
            <button class="tab" onclick="switchTab('my-bids')">我的出价</button>
            <button class="tab" onclick="switchTab('create')">创建拍卖</button>
        </div>
        
        <div class="search-bar" id="search-bar">
            <input type="text" id="search-input" class="search-input" placeholder="搜索物品名称..." onkeypress="handleSearchKeyPress(event)">
            <button class="search-btn" onclick="searchAuctions()">搜索</button>
            <button class="btn btn-warning" onclick="clearSearch()">清空</button>
        </div>
        
        <div id="auction-content">
            <div id="loading" class="loading">加载中...</div>
            <div id="auction-grid" class="auction-grid" style="display: none;"></div>
            <div id="create-auction-form" style="display: none;"></div>
        </div>
        
        <div class="navigation">
            <a href="game.html">返回游戏</a>
            <a href="javascript:void(0)" onclick="refreshAuctions()">刷新</a>
        </div>
    </div>

    <!-- 出价模态框 -->
    <div id="bid-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">竞价出价</div>
                <button class="close-btn" onclick="closeBidModal()">×</button>
            </div>
            <div>
                <div id="bid-item-info"></div>
                <div class="form-group">
                    <label class="form-label">出价金额：</label>
                    <input type="number" id="bid-price-input" class="form-input" placeholder="请输入出价金额" min="1">
                    <div style="font-size: 12px; color: #666; margin-top: 5px;" id="min-bid-hint"></div>
                </div>
                <div class="bid-history" id="bid-history"></div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-success" onclick="submitBid()">确认出价</button>
                <button class="btn btn-primary" onclick="closeBidModal()">取消</button>
            </div>
        </div>
    </div>

    <!-- 一口价购买模态框 -->
    <div id="buyout-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">一口价购买</div>
                <button class="close-btn" onclick="closeBuyoutModal()">×</button>
            </div>
            <div>
                <div id="buyout-item-info"></div>
                <div style="text-align: center; margin: 20px 0;">
                    <div style="font-size: 18px; font-weight: bold; color: #dc3545;">
                        确认以一口价购买此物品？
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-success" onclick="submitBuyout()">确认购买</button>
                <button class="btn btn-primary" onclick="closeBuyoutModal()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let currentTab = 'active';
        let currentAuctions = [];
        let selectedAuction = null;
        let searchKeyword = '';
        
        // 货币类型映射
        const currencyMap = {
            1: { name: '铜钱', icon: 'copper' },
            2: { name: '灵石', icon: 'silver' },
            3: { name: '仙晶', icon: 'gold' }
        };
        
        // 拍卖状态映射
        const statusMap = {
            1: { name: '进行中', class: 'status-active' },
            2: { name: '已成交', class: 'status-deal' },
            3: { name: '已流拍', class: 'status-failed' },
            4: { name: '已取消', class: 'status-cancelled' }
        };
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAuctions();
        });
        
        // 切换标签页
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            // 显示/隐藏搜索栏
            const searchBar = document.getElementById('search-bar');
            if (tab === 'active') {
                searchBar.style.display = 'flex';
            } else {
                searchBar.style.display = 'none';
            }
            
            // 隐藏所有内容
            document.getElementById('auction-grid').style.display = 'none';
            document.getElementById('create-auction-form').style.display = 'none';
            
            // 加载对应内容
            switch(tab) {
                case 'active':
                    loadAuctions();
                    break;
                case 'my-auctions':
                    loadMyAuctions();
                    break;
                case 'my-bids':
                    loadMyBids();
                    break;
                case 'create':
                    showCreateAuctionForm();
                    break;
            }
        }
        
        // 加载拍卖列表
        function loadAuctions() {
            showLoading();
            
            let url = '/honghuang-game/api/auction/active';
            if (searchKeyword) {
                url = `/honghuang-game/api/auction/search?keyword=${encodeURIComponent(searchKeyword)}`;
            }
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        currentAuctions = data.data;
                        displayAuctions();
                    } else {
                        showError(data.message || '加载拍卖列表失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示拍卖列表
        function displayAuctions() {
            const auctionGrid = document.getElementById('auction-grid');
            auctionGrid.style.display = 'grid';
            
            if (!currentAuctions || currentAuctions.length === 0) {
                auctionGrid.innerHTML = '<div class="empty-message">暂无拍卖</div>';
                return;
            }
            
            let html = '';
            currentAuctions.forEach(auction => {
                const currency = currencyMap[auction.currencyType] || currencyMap[1];
                const status = statusMap[auction.status] || statusMap[1];
                const timeLeft = getTimeLeft(auction.endTime);
                const isExpired = auction.isExpired || auction.status !== 1;
                
                html += `
                    <div class="auction-item ${isExpired ? 'expired' : ''}" onclick="showAuctionDetail(${auction.id})">
                        <div class="item-header">
                            <div class="item-name">${auction.item ? auction.item.name : '未知物品'}</div>
                            <div class="item-quality quality-${auction.item ? auction.item.quality : 1}">
                                ${getQualityName(auction.item ? auction.item.quality : 1)}
                            </div>
                        </div>
                        <div class="auction-info">数量: ${auction.quantity}</div>
                        <div class="auction-info ${status.class}">状态: ${status.name}</div>
                        <div class="price-info">
                            <div>
                                <div class="current-price">
                                    <span class="currency-icon ${currency.icon}"></span>
                                    ${auction.currentPrice} ${currency.name}
                                </div>
                                ${auction.hasBuyoutPrice() ? `
                                <div class="buyout-price">
                                    一口价: ${auction.buyoutPrice} ${currency.name}
                                </div>
                                ` : ''}
                            </div>
                        </div>
                        <div class="time-left ${timeLeft.urgent ? 'time-urgent' : ''}">
                            ${timeLeft.text}
                        </div>
                        <div class="auction-actions">
                            ${getAuctionActions(auction)}
                        </div>
                        <div class="seller-info">卖家: ${auction.sellerName}</div>
                    </div>
                `;
            });
            
            auctionGrid.innerHTML = html;
        }
        
        // 获取品质名称
        function getQualityName(quality) {
            const qualityNames = {
                1: '普通',
                2: '优秀', 
                3: '精良',
                4: '史诗',
                5: '传说',
                6: '神话'
            };
            return qualityNames[quality] || '普通';
        }
        
        // 获取剩余时间
        function getTimeLeft(endTimeString) {
            const endTime = new Date(endTimeString);
            const now = new Date();
            const diffMs = endTime - now;
            
            if (diffMs <= 0) {
                return { text: '已结束', urgent: true };
            }
            
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);
            
            if (diffDays > 0) {
                return { text: `${diffDays}天${diffHours % 24}小时`, urgent: false };
            } else if (diffHours > 0) {
                return { text: `${diffHours}小时${diffMins % 60}分钟`, urgent: diffHours < 2 };
            } else {
                return { text: `${diffMins}分钟`, urgent: true };
            }
        }
        
        // 获取拍卖操作按钮
        function getAuctionActions(auction) {
            if (auction.status !== 1 || auction.isExpired) {
                return '<span style="color: #666;">已结束</span>';
            }
            
            let actions = '';
            actions += `<button class="btn btn-primary" onclick="openBidModal(${auction.id}); event.stopPropagation();">出价</button>`;
            
            if (auction.hasBuyoutPrice && auction.hasBuyoutPrice()) {
                actions += `<button class="btn btn-success" onclick="openBuyoutModal(${auction.id}); event.stopPropagation();">一口价</button>`;
            }
            
            return actions;
        }
        
        // 搜索拍卖
        function searchAuctions() {
            searchKeyword = document.getElementById('search-input').value.trim();
            loadAuctions();
        }
        
        // 清空搜索
        function clearSearch() {
            document.getElementById('search-input').value = '';
            searchKeyword = '';
            loadAuctions();
        }
        
        // 处理搜索回车键
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter') {
                searchAuctions();
            }
        }
        
        // 打开出价模态框
        function openBidModal(auctionId) {
            selectedAuction = currentAuctions.find(a => a.id === auctionId);
            if (!selectedAuction) return;
            
            const currency = currencyMap[selectedAuction.currencyType] || currencyMap[1];
            const minBid = selectedAuction.getMinNextBid ? selectedAuction.getMinNextBid() : selectedAuction.currentPrice + 100;
            
            document.getElementById('bid-item-info').innerHTML = `
                <p><strong>物品:</strong> ${selectedAuction.item ? selectedAuction.item.name : '未知物品'}</p>
                <p><strong>数量:</strong> ${selectedAuction.quantity}</p>
                <p><strong>当前价格:</strong> ${selectedAuction.currentPrice} ${currency.name}</p>
                <p><strong>卖家:</strong> ${selectedAuction.sellerName}</p>
            `;
            
            document.getElementById('min-bid-hint').textContent = `最低出价: ${minBid} ${currency.name}`;
            document.getElementById('bid-price-input').value = minBid;
            document.getElementById('bid-price-input').min = minBid;
            
            // 加载出价历史
            loadBidHistory(auctionId);
            
            document.getElementById('bid-modal').classList.add('show');
        }
        
        // 关闭出价模态框
        function closeBidModal() {
            document.getElementById('bid-modal').classList.remove('show');
            selectedAuction = null;
        }
        
        // 提交出价
        function submitBid() {
            if (!selectedAuction) return;
            
            const bidPrice = parseInt(document.getElementById('bid-price-input').value);
            if (!bidPrice || bidPrice <= 0) {
                showError('请输入有效的出价金额');
                return;
            }
            
            fetch(`/honghuang-game/api/auction/${selectedAuction.id}/bid`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ bidPrice: bidPrice })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('出价成功！');
                    closeBidModal();
                    loadAuctions();
                } else {
                    showError(data.message || '出价失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 打开一口价购买模态框
        function openBuyoutModal(auctionId) {
            selectedAuction = currentAuctions.find(a => a.id === auctionId);
            if (!selectedAuction) return;
            
            const currency = currencyMap[selectedAuction.currencyType] || currencyMap[1];
            
            document.getElementById('buyout-item-info').innerHTML = `
                <div style="text-align: center;">
                    <p><strong>物品:</strong> ${selectedAuction.item ? selectedAuction.item.name : '未知物品'}</p>
                    <p><strong>数量:</strong> ${selectedAuction.quantity}</p>
                    <p><strong>一口价:</strong> <span style="color: #dc3545; font-size: 18px; font-weight: bold;">${selectedAuction.buyoutPrice} ${currency.name}</span></p>
                    <p><strong>卖家:</strong> ${selectedAuction.sellerName}</p>
                </div>
            `;
            
            document.getElementById('buyout-modal').classList.add('show');
        }
        
        // 关闭一口价购买模态框
        function closeBuyoutModal() {
            document.getElementById('buyout-modal').classList.remove('show');
            selectedAuction = null;
        }
        
        // 提交一口价购买
        function submitBuyout() {
            if (!selectedAuction) return;
            
            fetch(`/honghuang-game/api/auction/${selectedAuction.id}/buyout`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('一口价购买成功！');
                    closeBuyoutModal();
                    loadAuctions();
                } else {
                    showError(data.message || '购买失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 加载出价历史
        function loadBidHistory(auctionId) {
            fetch(`/honghuang-game/api/auction/${auctionId}/bids`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        displayBidHistory(data.data);
                    } else {
                        document.getElementById('bid-history').innerHTML = '<div style="text-align: center; color: #666;">暂无出价记录</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('bid-history').innerHTML = '<div style="text-align: center; color: #666;">加载出价记录失败</div>';
                });
        }
        
        // 显示出价历史
        function displayBidHistory(bids) {
            const historyDiv = document.getElementById('bid-history');
            
            if (!bids || bids.length === 0) {
                historyDiv.innerHTML = '<div style="text-align: center; color: #666;">暂无出价记录</div>';
                return;
            }
            
            let html = '<div style="font-weight: bold; margin-bottom: 10px;">出价历史:</div>';
            bids.forEach(bid => {
                const currency = currencyMap[bid.currencyType] || currencyMap[1];
                const time = new Date(bid.bidTime).toLocaleString();
                
                html += `
                    <div class="bid-item">
                        <div>${bid.bidderName}</div>
                        <div>${bid.bidPrice} ${currency.name}</div>
                        <div style="font-size: 11px; color: #666;">${time}</div>
                    </div>
                `;
            });
            
            historyDiv.innerHTML = html;
        }
        
        // 加载我的拍卖
        function loadMyAuctions() {
            showLoading();
            
            fetch('/honghuang-game/api/auction/my')
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        currentAuctions = data.data;
                        displayAuctions();
                    } else {
                        showError(data.message || '加载我的拍卖失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 加载我的出价
        function loadMyBids() {
            showLoading();
            
            fetch('/honghuang-game/api/auction/my-bids')
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        currentAuctions = data.data;
                        displayAuctions();
                    } else {
                        showError(data.message || '加载我的出价失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示创建拍卖表单
        function showCreateAuctionForm() {
            hideLoading();
            const formDiv = document.getElementById('create-auction-form');
            formDiv.style.display = 'block';
            
            formDiv.innerHTML = `
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
                    <h3 style="text-align: center; color: #2c5aa0;">创建拍卖</h3>
                    <div class="form-group">
                        <label class="form-label">背包槽位:</label>
                        <input type="number" id="slot-input" class="form-input" placeholder="请输入背包槽位编号" min="1">
                    </div>
                    <div class="form-group">
                        <label class="form-label">起拍价:</label>
                        <input type="number" id="start-price-input" class="form-input" placeholder="请输入起拍价" min="1">
                    </div>
                    <div class="form-group">
                        <label class="form-label">一口价 (可选):</label>
                        <input type="number" id="buyout-price-input" class="form-input" placeholder="请输入一口价，不填则无一口价" min="1">
                    </div>
                    <div class="form-group">
                        <label class="form-label">拍卖时长:</label>
                        <select id="duration-select" class="form-input">
                            <option value="24">24小时</option>
                            <option value="48">48小时</option>
                            <option value="72">72小时</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">货币类型:</label>
                        <select id="currency-select" class="form-input">
                            <option value="1">铜钱</option>
                            <option value="2">灵石</option>
                            <option value="3">仙晶</option>
                        </select>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-success" onclick="createAuction()">创建拍卖</button>
                        <button class="btn btn-primary" onclick="switchTab('active')">取消</button>
                    </div>
                </div>
            `;
        }
        
        // 创建拍卖
        function createAuction() {
            const slotIndex = parseInt(document.getElementById('slot-input').value);
            const startPrice = parseInt(document.getElementById('start-price-input').value);
            const buyoutPrice = parseInt(document.getElementById('buyout-price-input').value) || null;
            const duration = parseInt(document.getElementById('duration-select').value);
            const currencyType = parseInt(document.getElementById('currency-select').value);
            
            if (!slotIndex || !startPrice || !duration) {
                showError('请填写必要信息');
                return;
            }
            
            if (buyoutPrice && buyoutPrice <= startPrice) {
                showError('一口价必须高于起拍价');
                return;
            }
            
            fetch('/honghuang-game/api/auction/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inventorySlotIndex: slotIndex,
                    startPrice: startPrice,
                    buyoutPrice: buyoutPrice,
                    durationHours: duration,
                    currencyType: currencyType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('拍卖创建成功！');
                    switchTab('my-auctions');
                } else {
                    showError(data.message || '创建拍卖失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 刷新拍卖
        function refreshAuctions() {
            if (currentTab === 'active') {
                loadAuctions();
            } else if (currentTab === 'my-auctions') {
                loadMyAuctions();
            } else if (currentTab === 'my-bids') {
                loadMyBids();
            }
        }
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 显示错误消息
        function showError(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="error">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="success">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
