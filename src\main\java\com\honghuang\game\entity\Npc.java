package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * NPC实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "npc")
public class Npc {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * NPC名称
     */
    @Column(name = "name", length = 50)
    private String name;

    /**
     * NPC类型 1商店NPC 2任务NPC 3功能NPC 4装饰NPC 5战斗NPC
     */
    @Column(name = "type")
    private Integer type;

    /**
     * NPC子类型
     */
    @Column(name = "sub_type")
    private Integer subType;

    /**
     * 所在场景ID
     */
    @Column(name = "scene_id")
    private Integer sceneId;

    /**
     * 场景名称
     */
    @Column(name = "scene_name", length = 100)
    private String sceneName;

    /**
     * X坐标
     */
    @Column(name = "position_x")
    private Integer positionX;

    /**
     * Y坐标
     */
    @Column(name = "position_y")
    private Integer positionY;

    /**
     * NPC等级
     */
    @Column(name = "level")
    private Integer level = 1;

    /**
     * NPC描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * NPC头像
     */
    @Column(name = "avatar", length = 100)
    private String avatar;

    /**
     * 对话内容
     */
    @Column(name = "dialogue", length = 1000)
    private String dialogue;

    /**
     * 功能描述
     */
    @Column(name = "function_desc", length = 500)
    private String functionDesc;

    /**
     * 是否可交互 0不可 1可以
     */
    @Column(name = "interactive")
    private Integer interactive = 1;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    // 业务方法

    /**
     * 获取NPC类型名称
     */
    public String getTypeName() {
        switch (type) {
            case 1: return "商店NPC";
            case 2: return "任务NPC";
            case 3: return "功能NPC";
            case 4: return "装饰NPC";
            case 5: return "战斗NPC";
            default: return "未知";
        }
    }

    /**
     * 是否为商店NPC
     */
    public boolean isShopNpc() {
        return type == 1;
    }

    /**
     * 是否为任务NPC
     */
    public boolean isQuestNpc() {
        return type == 2;
    }

    /**
     * 是否为功能NPC
     */
    public boolean isFunctionNpc() {
        return type == 3;
    }

    /**
     * 是否为装饰NPC
     */
    public boolean isDecorationNpc() {
        return type == 4;
    }

    /**
     * 是否为战斗NPC
     */
    public boolean isBattleNpc() {
        return type == 5;
    }

    /**
     * 是否可交互
     */
    public boolean isInteractive() {
        return interactive == 1;
    }

    /**
     * 是否激活
     */
    public boolean isActive() {
        return active == 1;
    }

    /**
     * 获取位置描述
     */
    public String getPositionDescription() {
        return String.format("(%d, %d)", positionX, positionY);
    }

    /**
     * 获取完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(name).append(" (Lv.").append(level).append(") ");
        sb.append("[").append(getTypeName()).append("]");
        if (sceneName != null) {
            sb.append(" - ").append(sceneName);
        }
        return sb.toString();
    }

    /**
     * 检查是否在指定范围内
     */
    public boolean isInRange(Integer x, Integer y, Integer range) {
        if (positionX == null || positionY == null) {
            return false;
        }
        
        int distance = Math.abs(positionX - x) + Math.abs(positionY - y);
        return distance <= range;
    }

    /**
     * 计算与指定位置的距离
     */
    public Integer getDistanceTo(Integer x, Integer y) {
        if (positionX == null || positionY == null) {
            return Integer.MAX_VALUE;
        }
        
        return Math.abs(positionX - x) + Math.abs(positionY - y);
    }

    /**
     * 获取默认对话
     */
    public String getDefaultDialogue() {
        if (dialogue != null && !dialogue.trim().isEmpty()) {
            return dialogue;
        }
        
        switch (type) {
            case 1: return "欢迎光临我的商店！";
            case 2: return "有什么任务需要帮助吗？";
            case 3: return "我可以为你提供特殊服务。";
            case 4: return "你好，旅行者。";
            case 5: return "想要挑战我吗？";
            default: return "你好！";
        }
    }

    /**
     * 获取子类型名称
     */
    public String getSubTypeName() {
        if (subType == null) return "通用";
        
        switch (type) {
            case 1: // 商店NPC
                switch (subType) {
                    case 1: return "装备商";
                    case 2: return "药品商";
                    case 3: return "杂货商";
                    case 4: return "技能导师";
                    default: return "商人";
                }
            case 2: // 任务NPC
                switch (subType) {
                    case 1: return "任务发布者";
                    case 2: return "任务完成者";
                    case 3: return "任务引导者";
                    default: return "任务相关";
                }
            case 3: // 功能NPC
                switch (subType) {
                    case 1: return "传送师";
                    case 2: return "仓库管理员";
                    case 3: return "拍卖师";
                    case 4: return "邮件员";
                    default: return "功能服务";
                }
            default:
                return "通用";
        }
    }

    /**
     * 检查角色是否可以与此NPC交互
     */
    public boolean canInteractWith(PlayerRole role) {
        if (!isActive() || !isInteractive()) {
            return false;
        }
        
        // 这里可以添加更多的交互条件检查
        // 比如等级要求、任务状态等
        
        return true;
    }
}
