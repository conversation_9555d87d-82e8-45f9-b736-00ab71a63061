package com.honghuang.game.repository;

import com.honghuang.game.entity.Inventory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 背包数据访问层
 */
@Repository
public interface InventoryRepository extends JpaRepository<Inventory, Integer> {

    /**
     * 根据角色ID查找背包物品
     */
    List<Inventory> findByRoleIdOrderBySlotIndexAsc(Integer roleId);

    /**
     * 根据角色ID和槽位查找物品
     */
    Optional<Inventory> findByRoleIdAndSlotIndex(Integer roleId, Integer slotIndex);

    /**
     * 根据角色ID和物品ID查找物品
     */
    List<Inventory> findByRoleIdAndItemIdOrderBySlotIndexAsc(Integer roleId, Integer itemId);

    /**
     * 查找角色的空槽位
     */
    @Query("SELECT i.slotIndex FROM Inventory i WHERE i.roleId = :roleId AND (i.itemId IS NULL OR i.quantity <= 0) ORDER BY i.slotIndex ASC")
    List<Integer> findEmptySlots(@Param("roleId") Integer roleId);

    /**
     * 查找第一个空槽位
     */
    @Query("SELECT MIN(i.slotIndex) FROM Inventory i WHERE i.roleId = :roleId AND (i.itemId IS NULL OR i.quantity <= 0)")
    Optional<Integer> findFirstEmptySlot(@Param("roleId") Integer roleId);

    /**
     * 查找可堆叠的同类物品
     */
    @Query("SELECT i FROM Inventory i JOIN i.item item WHERE i.roleId = :roleId AND i.itemId = :itemId " +
           "AND item.stackable = 1 AND i.quantity < item.maxStack ORDER BY i.slotIndex ASC")
    List<Inventory> findStackableItems(@Param("roleId") Integer roleId, @Param("itemId") Integer itemId);

    /**
     * 统计背包使用槽位数
     */
    @Query("SELECT COUNT(i) FROM Inventory i WHERE i.roleId = :roleId AND i.itemId IS NOT NULL AND i.quantity > 0")
    long countUsedSlots(@Param("roleId") Integer roleId);

    /**
     * 统计背包空槽位数
     */
    @Query("SELECT COUNT(i) FROM Inventory i WHERE i.roleId = :roleId AND (i.itemId IS NULL OR i.quantity <= 0)")
    long countEmptySlots(@Param("roleId") Integer roleId);

    /**
     * 根据物品类型查找背包物品
     */
    @Query("SELECT i FROM Inventory i JOIN i.item item WHERE i.roleId = :roleId AND item.type = :itemType " +
           "AND i.quantity > 0 ORDER BY i.slotIndex ASC")
    List<Inventory> findByItemType(@Param("roleId") Integer roleId, @Param("itemType") Integer itemType);

    /**
     * 查找绑定物品
     */
    @Query("SELECT i FROM Inventory i WHERE i.roleId = :roleId AND i.bound = 1 AND i.quantity > 0 ORDER BY i.slotIndex ASC")
    List<Inventory> findBoundItems(@Param("roleId") Integer roleId);

    /**
     * 查找可交易物品
     */
    @Query("SELECT i FROM Inventory i JOIN i.item item WHERE i.roleId = :roleId AND item.tradeable = 1 " +
           "AND i.bound = 0 AND i.quantity > 0 ORDER BY i.slotIndex ASC")
    List<Inventory> findTradeableItems(@Param("roleId") Integer roleId);

    /**
     * 更新物品数量
     */
    @Modifying
    @Query("UPDATE Inventory i SET i.quantity = :quantity WHERE i.id = :inventoryId")
    int updateQuantity(@Param("inventoryId") Integer inventoryId, @Param("quantity") Integer quantity);

    /**
     * 更新物品位置
     */
    @Modifying
    @Query("UPDATE Inventory i SET i.slotIndex = :slotIndex WHERE i.id = :inventoryId")
    int updateSlotIndex(@Param("inventoryId") Integer inventoryId, @Param("slotIndex") Integer slotIndex);

    /**
     * 绑定物品
     */
    @Modifying
    @Query("UPDATE Inventory i SET i.bound = 1 WHERE i.id = :inventoryId")
    int bindItem(@Param("inventoryId") Integer inventoryId);

    /**
     * 清空槽位
     */
    @Modifying
    @Query("UPDATE Inventory i SET i.itemId = NULL, i.quantity = 0 WHERE i.id = :inventoryId")
    int clearSlot(@Param("inventoryId") Integer inventoryId);

    /**
     * 删除角色的所有背包物品
     */
    @Modifying
    @Query("DELETE FROM Inventory i WHERE i.roleId = :roleId")
    int deleteByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找过期物品（如果有过期机制）
     */
    @Query("SELECT i FROM Inventory i WHERE i.roleId = :roleId AND i.quantity > 0")
    List<Inventory> findExpiredItems(@Param("roleId") Integer roleId);

    /**
     * 查找新获得的物品
     */
    @Query("SELECT i FROM Inventory i WHERE i.roleId = :roleId AND i.obtainTime >= CURRENT_DATE AND i.quantity > 0 ORDER BY i.obtainTime DESC")
    List<Inventory> findNewItems(@Param("roleId") Integer roleId);

    /**
     * 统计指定物品数量
     */
    @Query("SELECT COALESCE(SUM(i.quantity), 0) FROM Inventory i WHERE i.roleId = :roleId AND i.itemId = :itemId")
    long countItemQuantity(@Param("roleId") Integer roleId, @Param("itemId") Integer itemId);

    /**
     * 查找背包价值最高的物品
     */
    @Query("SELECT i FROM Inventory i JOIN i.item item WHERE i.roleId = :roleId AND i.quantity > 0 " +
           "ORDER BY (item.sellPrice * i.quantity) DESC")
    List<Inventory> findMostValuableItems(@Param("roleId") Integer roleId);
}
