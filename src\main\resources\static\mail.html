<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件系统 - 洪荒游戏</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f8ff;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: #2c5aa0;
            border-bottom: 2px solid #66ccff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .mail-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            flex-wrap: wrap;
        }
        
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            margin-right: 5px;
            position: relative;
        }
        
        .tab.active {
            color: #2c5aa0;
            border-bottom-color: #66ccff;
            background-color: #f8f9fa;
        }
        
        .tab:hover {
            background-color: #f8f9fa;
        }
        
        .unread-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mail-list {
            min-height: 400px;
        }
        
        .mail-item {
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin-bottom: 10px;
            padding: 15px;
            background-color: #fafafa;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .mail-item:hover {
            background-color: #f0f8ff;
            border-color: #66ccff;
        }
        
        .mail-item.unread {
            border-left: 4px solid #dc3545;
            background-color: #fff5f5;
        }
        
        .mail-item.system {
            border-left: 4px solid #ffc107;
            background-color: #fffdf5;
        }
        
        .mail-item.has-attachment {
            border-right: 4px solid #28a745;
        }
        
        .mail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .mail-title {
            font-weight: bold;
            color: #2c5aa0;
            font-size: 16px;
        }
        
        .mail-time {
            color: #666;
            font-size: 12px;
        }
        
        .mail-sender {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .mail-preview {
            color: #888;
            font-size: 13px;
            line-height: 1.4;
            max-height: 40px;
            overflow: hidden;
        }
        
        .mail-status {
            display: flex;
            gap: 10px;
            margin-top: 8px;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            color: white;
        }
        
        .status-unread { background-color: #dc3545; }
        .status-read { background-color: #6c757d; }
        .status-system { background-color: #ffc107; color: #212529; }
        .status-attachment { background-color: #28a745; }
        
        .mail-actions {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }
        
        .btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #2c5aa0;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1e3d6f;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .compose-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #2c5aa0;
            color: white;
            border: none;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transition: all 0.3s;
        }
        
        .compose-btn:hover {
            background-color: #1e3d6f;
            transform: scale(1.1);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal.show {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        
        .modal-title {
            color: #2c5aa0;
            font-weight: bold;
            font-size: 18px;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
        }
        
        .attachment-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            background-color: #f8f9fa;
        }
        
        .attachment-title {
            font-weight: bold;
            color: #555;
            margin-bottom: 10px;
        }
        
        .attachment-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
        }
        
        .attachment-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .currency-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .copper { background-color: #cd7f32; }
        .silver { background-color: #c0c0c0; }
        .gold { background-color: #ffd700; }
        
        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .empty-message {
            text-align: center;
            color: #666;
            padding: 50px;
            font-style: italic;
        }
        
        .navigation {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .navigation a {
            color: #2c5aa0;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .navigation a:hover {
            text-decoration: underline;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .success {
            color: #155724;
            text-align: center;
            padding: 20px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .mail-detail {
            line-height: 1.6;
        }
        
        .mail-detail-header {
            border-bottom: 1px solid #ddd;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        
        .mail-detail-content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .pagination {
            text-align: center;
            margin-top: 20px;
        }
        
        .pagination button {
            margin: 0 5px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button.active {
            background-color: #2c5aa0;
            color: white;
            border-color: #2c5aa0;
        }
        
        .pagination button:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>【邮件系统】</h1>
        </div>
        
        <div id="message-area"></div>
        
        <div class="mail-tabs">
            <button class="tab active" onclick="switchTab('inbox')">
                收件箱
                <span id="unread-count" class="unread-badge" style="display: none;">0</span>
            </button>
            <button class="tab" onclick="switchTab('sent')">发件箱</button>
            <button class="tab" onclick="switchTab('attachment')">
                有附件
                <span id="attachment-count" class="unread-badge" style="display: none;">0</span>
            </button>
        </div>
        
        <div id="mail-content">
            <div id="loading" class="loading">加载中...</div>
            <div id="mail-list" class="mail-list" style="display: none;"></div>
            <div id="pagination" class="pagination" style="display: none;"></div>
        </div>
        
        <div class="navigation">
            <a href="game.html">返回游戏</a>
            <a href="javascript:void(0)" onclick="refreshMails()">刷新</a>
            <a href="javascript:void(0)" onclick="markAllAsRead()">全部已读</a>
        </div>
    </div>

    <!-- 写邮件按钮 -->
    <button class="compose-btn" onclick="openComposeModal()" title="写邮件">✉</button>

    <!-- 写邮件模态框 -->
    <div id="compose-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">写邮件</div>
                <button class="close-btn" onclick="closeComposeModal()">×</button>
            </div>
            <div>
                <div class="form-group">
                    <label class="form-label">收件人：</label>
                    <input type="text" id="recipient-input" class="form-input" placeholder="请输入收件人角色名" maxlength="50">
                </div>
                <div class="form-group">
                    <label class="form-label">邮件标题：</label>
                    <input type="text" id="title-input" class="form-input" placeholder="请输入邮件标题" maxlength="100">
                </div>
                <div class="form-group">
                    <label class="form-label">邮件内容：</label>
                    <textarea id="content-input" class="form-textarea" placeholder="请输入邮件内容" maxlength="2000"></textarea>
                </div>
                <div class="attachment-section">
                    <div class="attachment-title">附件 (可选)</div>
                    <div class="attachment-grid">
                        <div class="attachment-item">
                            <span class="currency-icon gold"></span>
                            <input type="number" id="gold-input" class="form-input" placeholder="金币" min="0">
                        </div>
                        <div class="attachment-item">
                            <span class="currency-icon silver"></span>
                            <input type="number" id="silver-input" class="form-input" placeholder="银两" min="0">
                        </div>
                        <div class="attachment-item">
                            <span class="currency-icon copper"></span>
                            <input type="number" id="copper-input" class="form-input" placeholder="铜钱" min="0">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-success" onclick="sendMail()">发送邮件</button>
                <button class="btn btn-primary" onclick="closeComposeModal()">取消</button>
            </div>
        </div>
    </div>

    <!-- 邮件详情模态框 -->
    <div id="mail-detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="detail-title">邮件详情</div>
                <button class="close-btn" onclick="closeMailDetailModal()">×</button>
            </div>
            <div id="mail-detail-content" class="mail-detail"></div>
            <div class="modal-actions" id="detail-actions"></div>
        </div>
    </div>

    <script>
        let currentTab = 'inbox';
        let currentMails = [];
        let currentPage = 1;
        let totalPages = 1;
        let selectedMail = null;
        let unreadCount = 0;
        let attachmentCount = 0;
        
        // 邮件类型映射
        const mailTypeMap = {
            1: '普通邮件',
            2: '系统邮件',
            3: '奖励邮件',
            4: '拍卖邮件'
        };
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadMails();
            loadMailCounts();
        });
        
        // 切换标签页
        function switchTab(tab) {
            currentTab = tab;
            currentPage = 1;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            loadMails();
        }
        
        // 加载邮件列表
        function loadMails() {
            showLoading();
            
            let url = '';
            switch(currentTab) {
                case 'inbox':
                    url = '/honghuang-game/api/mail/inbox';
                    break;
                case 'sent':
                    url = '/honghuang-game/api/mail/sent';
                    break;
                case 'attachment':
                    url = '/honghuang-game/api/mail/with-attachment';
                    break;
            }
            
            fetch(url + '?page=' + currentPage)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        currentMails = data.data;
                        displayMails();
                    } else {
                        showError(data.message || '加载邮件失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示邮件列表
        function displayMails() {
            const mailList = document.getElementById('mail-list');
            mailList.style.display = 'block';
            
            if (!currentMails || currentMails.length === 0) {
                mailList.innerHTML = '<div class="empty-message">暂无邮件</div>';
                return;
            }
            
            let html = '';
            currentMails.forEach(mail => {
                const isUnread = !mail.isRead();
                const isSystem = mail.mailType === 2;
                const hasAttachment = mail.hasAttachment();
                const timeAgo = getTimeAgo(mail.sendTime);
                
                let itemClass = 'mail-item';
                if (isUnread) itemClass += ' unread';
                if (isSystem) itemClass += ' system';
                if (hasAttachment) itemClass += ' has-attachment';
                
                html += `
                    <div class="${itemClass}" onclick="showMailDetail(${mail.id})">
                        <div class="mail-header">
                            <div class="mail-title">${mail.title || '无标题'}</div>
                            <div class="mail-time">${timeAgo}</div>
                        </div>
                        <div class="mail-sender">
                            ${currentTab === 'sent' ? '收件人: ' + mail.receiverName : '发件人: ' + (mail.senderName || '系统')}
                        </div>
                        <div class="mail-preview">${mail.content ? mail.content.substring(0, 100) + (mail.content.length > 100 ? '...' : '') : '无内容'}</div>
                        <div class="mail-status">
                            ${isUnread ? '<span class="status-badge status-unread">未读</span>' : '<span class="status-badge status-read">已读</span>'}
                            ${isSystem ? '<span class="status-badge status-system">系统</span>' : ''}
                            ${hasAttachment ? '<span class="status-badge status-attachment">附件</span>' : ''}
                        </div>
                        <div class="mail-actions" onclick="event.stopPropagation();">
                            ${getMailActions(mail)}
                        </div>
                    </div>
                `;
            });
            
            mailList.innerHTML = html;
        }
        
        // 获取邮件操作按钮
        function getMailActions(mail) {
            let actions = '';
            
            if (!mail.isRead()) {
                actions += `<button class="btn btn-primary" onclick="markAsRead(${mail.id})">标记已读</button>`;
            }
            
            if (mail.hasAttachment() && !mail.isAttachmentClaimed()) {
                actions += `<button class="btn btn-success" onclick="claimAttachment(${mail.id})">领取附件</button>`;
            }
            
            actions += `<button class="btn btn-danger" onclick="deleteMail(${mail.id})">删除</button>`;
            
            return actions;
        }
        
        // 显示邮件详情
        function showMailDetail(mailId) {
            fetch(`/honghuang-game/api/mail/${mailId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    selectedMail = data.data;
                    displayMailDetail();
                    loadMails(); // 刷新列表以更新已读状态
                    loadMailCounts(); // 更新计数
                } else {
                    showError(data.message || '打开邮件失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 显示邮件详情弹窗
        function displayMailDetail() {
            if (!selectedMail) return;
            
            const timeStr = new Date(selectedMail.sendTime).toLocaleString();
            
            document.getElementById('detail-title').textContent = selectedMail.title || '无标题';
            
            let attachmentHtml = '';
            if (selectedMail.hasAttachment()) {
                attachmentHtml = '<div class="attachment-section"><div class="attachment-title">邮件附件</div>';
                
                if (selectedMail.attachmentGold > 0 || selectedMail.attachmentSilver > 0 || selectedMail.attachmentCopper > 0) {
                    attachmentHtml += '<div class="attachment-grid">';
                    if (selectedMail.attachmentGold > 0) {
                        attachmentHtml += `<div class="attachment-item"><span class="currency-icon gold"></span> ${selectedMail.attachmentGold} 金币</div>`;
                    }
                    if (selectedMail.attachmentSilver > 0) {
                        attachmentHtml += `<div class="attachment-item"><span class="currency-icon silver"></span> ${selectedMail.attachmentSilver} 银两</div>`;
                    }
                    if (selectedMail.attachmentCopper > 0) {
                        attachmentHtml += `<div class="attachment-item"><span class="currency-icon copper"></span> ${selectedMail.attachmentCopper} 铜钱</div>`;
                    }
                    attachmentHtml += '</div>';
                }
                
                attachmentHtml += '</div>';
            }
            
            document.getElementById('mail-detail-content').innerHTML = `
                <div class="mail-detail-header">
                    <p><strong>发件人:</strong> ${selectedMail.senderName || '系统'}</p>
                    <p><strong>收件人:</strong> ${selectedMail.receiverName}</p>
                    <p><strong>发送时间:</strong> ${timeStr}</p>
                    <p><strong>邮件类型:</strong> ${mailTypeMap[selectedMail.mailType] || '普通邮件'}</p>
                </div>
                <div class="mail-detail-content">${selectedMail.content || '无内容'}</div>
                ${attachmentHtml}
            `;
            
            // 生成操作按钮
            let actions = '';
            if (selectedMail.hasAttachment() && !selectedMail.isAttachmentClaimed()) {
                actions += '<button class="btn btn-success" onclick="claimAttachment(' + selectedMail.id + ')">领取附件</button>';
            }
            actions += '<button class="btn btn-danger" onclick="deleteMail(' + selectedMail.id + ')">删除邮件</button>';
            actions += '<button class="btn btn-primary" onclick="closeMailDetailModal()">关闭</button>';
            
            document.getElementById('detail-actions').innerHTML = actions;
            
            document.getElementById('mail-detail-modal').classList.add('show');
        }
        
        // 关闭邮件详情弹窗
        function closeMailDetailModal() {
            document.getElementById('mail-detail-modal').classList.remove('show');
            selectedMail = null;
        }
        
        // 标记为已读
        function markAsRead(mailId) {
            fetch(`/honghuang-game/api/mail/${mailId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('邮件已标记为已读');
                    loadMails();
                    loadMailCounts();
                } else {
                    showError(data.message || '操作失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 领取附件
        function claimAttachment(mailId) {
            fetch(`/honghuang-game/api/mail/${mailId}/claim`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('附件领取成功！');
                    loadMails();
                    loadMailCounts();
                    if (selectedMail && selectedMail.id === mailId) {
                        closeMailDetailModal();
                    }
                } else {
                    showError(data.message || '领取附件失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 删除邮件
        function deleteMail(mailId) {
            if (!confirm('确定要删除这封邮件吗？')) {
                return;
            }
            
            fetch(`/honghuang-game/api/mail/${mailId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('邮件已删除');
                    loadMails();
                    loadMailCounts();
                    if (selectedMail && selectedMail.id === mailId) {
                        closeMailDetailModal();
                    }
                } else {
                    showError(data.message || '删除邮件失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 全部标记为已读
        function markAllAsRead() {
            if (!confirm('确定要将所有邮件标记为已读吗？')) {
                return;
            }
            
            fetch('/honghuang-game/api/mail/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('所有邮件已标记为已读');
                    loadMails();
                    loadMailCounts();
                } else {
                    showError(data.message || '操作失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 打开写邮件模态框
        function openComposeModal() {
            document.getElementById('compose-modal').classList.add('show');
            document.getElementById('recipient-input').focus();
        }
        
        // 关闭写邮件模态框
        function closeComposeModal() {
            document.getElementById('compose-modal').classList.remove('show');
            clearComposeForm();
        }
        
        // 清空写邮件表单
        function clearComposeForm() {
            document.getElementById('recipient-input').value = '';
            document.getElementById('title-input').value = '';
            document.getElementById('content-input').value = '';
            document.getElementById('gold-input').value = '';
            document.getElementById('silver-input').value = '';
            document.getElementById('copper-input').value = '';
        }
        
        // 发送邮件
        function sendMail() {
            const recipient = document.getElementById('recipient-input').value.trim();
            const title = document.getElementById('title-input').value.trim();
            const content = document.getElementById('content-input').value.trim();
            const gold = parseInt(document.getElementById('gold-input').value) || 0;
            const silver = parseInt(document.getElementById('silver-input').value) || 0;
            const copper = parseInt(document.getElementById('copper-input').value) || 0;
            
            if (!recipient) {
                showError('请输入收件人');
                return;
            }
            
            if (!title) {
                showError('请输入邮件标题');
                return;
            }
            
            if (!content) {
                showError('请输入邮件内容');
                return;
            }
            
            const mailData = {
                receiverName: recipient,
                title: title,
                content: content
            };
            
            if (gold > 0 || silver > 0 || copper > 0) {
                mailData.gold = gold;
                mailData.silver = silver;
                mailData.copper = copper;
            }
            
            fetch('/honghuang-game/api/mail/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(mailData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('邮件发送成功！');
                    closeComposeModal();
                    if (currentTab === 'sent') {
                        loadMails();
                    }
                } else {
                    showError(data.message || '邮件发送失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 加载邮件计数
        function loadMailCounts() {
            // 加载未读邮件数量
            fetch('/honghuang-game/api/mail/unread')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        unreadCount = data.data.length;
                        updateUnreadBadge();
                    }
                })
                .catch(error => {
                    console.error('加载未读邮件数量失败:', error);
                });
            
            // 加载有附件邮件数量
            fetch('/honghuang-game/api/mail/with-attachment')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        attachmentCount = data.data.length;
                        updateAttachmentBadge();
                    }
                })
                .catch(error => {
                    console.error('加载有附件邮件数量失败:', error);
                });
        }
        
        // 更新未读邮件徽章
        function updateUnreadBadge() {
            const badge = document.getElementById('unread-count');
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
        
        // 更新有附件邮件徽章
        function updateAttachmentBadge() {
            const badge = document.getElementById('attachment-count');
            if (attachmentCount > 0) {
                badge.textContent = attachmentCount > 99 ? '99+' : attachmentCount;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
        
        // 获取时间差描述
        function getTimeAgo(timeString) {
            const time = new Date(timeString);
            const now = new Date();
            const diffMs = now - time;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);
            
            if (diffMins < 1) return '刚刚';
            if (diffMins < 60) return `${diffMins}分钟前`;
            if (diffHours < 24) return `${diffHours}小时前`;
            if (diffDays < 7) return `${diffDays}天前`;
            return time.toLocaleDateString();
        }
        
        // 刷新邮件
        function refreshMails() {
            loadMails();
            loadMailCounts();
        }
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('mail-list').style.display = 'none';
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 显示错误消息
        function showError(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="error">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="success">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
