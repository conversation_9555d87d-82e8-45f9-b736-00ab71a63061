package com.honghuang.game.entity;

import javax.persistence.*;

/**
 * 宠物技能实体类
 */
@Entity
@Table(name = "pet_skill")
public class PetSkill {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "skill_id")
    private Integer id;
    
    @Column(name = "skill_name")
    private String name;
    
    @Column(name = "skill_type")
    private Integer type; // 技能类型：1-攻击 2-防御 3-辅助 4-治疗
    
    @Column(name = "skill_level")
    private Integer level;
    
    @Column(name = "skill_description")
    private String description;
    
    @Column(name = "skill_effect")
    private String effect; // 技能效果描述
    
    @Column(name = "skill_damage")
    private Integer damage; // 伤害值
    
    @Column(name = "skill_heal")
    private Integer heal; // 治疗值
    
    @Column(name = "skill_buff_type")
    private Integer buffType; // BUFF类型
    
    @Column(name = "skill_buff_value")
    private Integer buffValue; // BUFF数值
    
    @Column(name = "skill_buff_duration")
    private Integer buffDuration; // BUFF持续时间
    
    @Column(name = "skill_mp_cost")
    private Integer mpCost; // 消耗法力值
    
    @Column(name = "skill_cooldown")
    private Integer cooldown; // 冷却时间
    
    @Column(name = "skill_learn_level")
    private Integer learnLevel; // 学习等级要求
    
    @Column(name = "skill_learn_cost")
    private Integer learnCost; // 学习费用
    
    @Column(name = "skill_rarity")
    private Integer rarity; // 稀有度
    
    @Column(name = "skill_is_active")
    private Integer isActive; // 是否启用
    
    // 构造函数
    public PetSkill() {}
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    public Integer getLevel() {
        return level;
    }
    
    public void setLevel(Integer level) {
        this.level = level;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getEffect() {
        return effect;
    }
    
    public void setEffect(String effect) {
        this.effect = effect;
    }
    
    public Integer getDamage() {
        return damage;
    }
    
    public void setDamage(Integer damage) {
        this.damage = damage;
    }
    
    public Integer getHeal() {
        return heal;
    }
    
    public void setHeal(Integer heal) {
        this.heal = heal;
    }
    
    public Integer getBuffType() {
        return buffType;
    }
    
    public void setBuffType(Integer buffType) {
        this.buffType = buffType;
    }
    
    public Integer getBuffValue() {
        return buffValue;
    }
    
    public void setBuffValue(Integer buffValue) {
        this.buffValue = buffValue;
    }
    
    public Integer getBuffDuration() {
        return buffDuration;
    }
    
    public void setBuffDuration(Integer buffDuration) {
        this.buffDuration = buffDuration;
    }
    
    public Integer getMpCost() {
        return mpCost;
    }
    
    public void setMpCost(Integer mpCost) {
        this.mpCost = mpCost;
    }
    
    public Integer getCooldown() {
        return cooldown;
    }
    
    public void setCooldown(Integer cooldown) {
        this.cooldown = cooldown;
    }
    
    public Integer getLearnLevel() {
        return learnLevel;
    }
    
    public void setLearnLevel(Integer learnLevel) {
        this.learnLevel = learnLevel;
    }
    
    public Integer getLearnCost() {
        return learnCost;
    }
    
    public void setLearnCost(Integer learnCost) {
        this.learnCost = learnCost;
    }
    
    public Integer getRarity() {
        return rarity;
    }
    
    public void setRarity(Integer rarity) {
        this.rarity = rarity;
    }
    
    public Integer getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }
    
    // 业务方法
    public boolean isAttackSkill() {
        return type != null && type == 1;
    }
    
    public boolean isDefenseSkill() {
        return type != null && type == 2;
    }
    
    public boolean isHealSkill() {
        return type != null && type == 4;
    }
    
    public boolean isBuffSkill() {
        return type != null && type == 3;
    }
    
    public String getTypeName() {
        if (type == null) return "未知";
        switch (type) {
            case 1: return "攻击";
            case 2: return "防御";
            case 3: return "辅助";
            case 4: return "治疗";
            default: return "未知";
        }
    }
    
    public String getRarityName() {
        if (rarity == null) return "普通";
        switch (rarity) {
            case 1: return "普通";
            case 2: return "优秀";
            case 3: return "稀有";
            case 4: return "史诗";
            case 5: return "传说";
            default: return "普通";
        }
    }
}
