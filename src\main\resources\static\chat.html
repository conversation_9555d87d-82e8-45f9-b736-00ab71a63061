<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天系统 - 洪荒游戏</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f8ff;
            color: #333;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            height: 80vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            color: #2c5aa0;
            border-bottom: 2px solid #66ccff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .chat-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            color: #2c5aa0;
            border-bottom-color: #66ccff;
            background-color: #f8f9fa;
        }
        
        .tab:hover {
            background-color: #f8f9fa;
        }
        
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .messages-container {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background-color: #fafafa;
            max-height: 400px;
        }
        
        .message {
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            background-color: white;
            border-left: 3px solid #66ccff;
            animation: fadeIn 0.3s ease-in;
        }
        
        .message.system {
            border-left-color: #ffc107;
            background-color: #fff9e6;
        }
        
        .message.private {
            border-left-color: #dc3545;
            background-color: #ffeaea;
        }
        
        .message.guild {
            border-left-color: #28a745;
            background-color: #eafaf1;
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .sender-name {
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .message-time {
            font-size: 12px;
            color: #666;
        }
        
        .message-content {
            line-height: 1.4;
            word-wrap: break-word;
        }
        
        .input-area {
            padding: 15px;
            background-color: white;
            border-top: 1px solid #ddd;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .message-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            outline: none;
        }
        
        .message-input:focus {
            border-color: #66ccff;
            box-shadow: 0 0 5px rgba(102, 204, 255, 0.3);
        }
        
        .send-btn {
            padding: 10px 20px;
            background-color: #2c5aa0;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .send-btn:hover {
            background-color: #1e3d6f;
        }
        
        .send-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .channel-info {
            padding: 8px 12px;
            background-color: #e9ecef;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 14px;
            color: #666;
        }
        
        .online-users {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .online-title {
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .user-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .user-item {
            padding: 4px 8px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .user-item:hover {
            background-color: #e9ecef;
            border-color: #66ccff;
        }
        
        .user-item.online {
            border-color: #28a745;
            color: #28a745;
        }
        
        .navigation {
            text-align: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }
        
        .navigation a {
            color: #2c5aa0;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .navigation a:hover {
            text-decoration: underline;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 15px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .success {
            color: #155724;
            text-align: center;
            padding: 15px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .empty-message {
            text-align: center;
            color: #666;
            padding: 30px;
            font-style: italic;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .private-chat-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .private-chat-modal.show {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .private-chat-content {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 400px;
            width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        
        .modal-title {
            color: #2c5aa0;
            font-weight: bold;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }
        
        .recipient-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #2c5aa0;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1e3d6f;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>【聊天系统】</h1>
        </div>
        
        <div id="message-area"></div>
        
        <div class="chat-tabs">
            <button class="tab active" onclick="switchChannel('world')">世界</button>
            <button class="tab" onclick="switchChannel('scene')">当前场景</button>
            <button class="tab" onclick="switchChannel('guild')">帮派</button>
            <button class="tab" onclick="switchChannel('private')">私聊</button>
            <button class="tab" onclick="switchChannel('system')">系统</button>
        </div>
        
        <div class="chat-area">
            <div class="channel-info" id="channel-info">
                当前频道：世界聊天
            </div>
            
            <div class="messages-container" id="messages-container">
                <div id="loading" class="loading">加载聊天记录...</div>
                <div id="messages" style="display: none;"></div>
            </div>
            
            <div class="input-area">
                <div class="input-container">
                    <input type="text" id="message-input" class="message-input" 
                           placeholder="输入消息..." maxlength="200" 
                           onkeypress="handleKeyPress(event)">
                    <button class="send-btn" onclick="sendMessage()">发送</button>
                    <button class="btn btn-secondary" onclick="openPrivateChat()" id="private-btn" style="display: none;">私聊</button>
                </div>
            </div>
        </div>
        
        <div class="online-users" id="online-users" style="display: none;">
            <div class="online-title">在线玩家</div>
            <div class="user-list" id="user-list"></div>
        </div>
        
        <div class="navigation">
            <a href="game.html">返回游戏</a>
            <a href="javascript:void(0)" onclick="refreshChat()">刷新</a>
            <a href="javascript:void(0)" onclick="clearMessages()">清空</a>
        </div>
    </div>

    <!-- 私聊模态框 -->
    <div id="private-chat-modal" class="private-chat-modal">
        <div class="private-chat-content">
            <div class="modal-header">
                <div class="modal-title">发送私聊</div>
                <button class="close-btn" onclick="closePrivateChat()">×</button>
            </div>
            <div>
                <label>收件人：</label>
                <input type="text" id="recipient-input" class="recipient-input" placeholder="输入玩家名称">
            </div>
            <div>
                <label>消息：</label>
                <input type="text" id="private-message-input" class="recipient-input" placeholder="输入私聊内容" maxlength="200">
            </div>
            <div class="modal-actions">
                <button class="btn btn-primary" onclick="sendPrivateMessage()">发送</button>
                <button class="btn btn-secondary" onclick="closePrivateChat()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let currentChannel = 'world';
        let messages = [];
        let onlineUsers = [];
        let lastMessageTime = 0;
        let refreshInterval;
        let websocket = null;
        let currentRoleId = null;
        
        // 频道信息映射
        const channelInfo = {
            world: '世界聊天 - 所有玩家可见',
            scene: '当前场景 - 同场景玩家可见',
            guild: '帮派聊天 - 帮派成员可见',
            private: '私聊记录 - 私人消息',
            system: '系统消息 - 游戏通知'
        };
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            getCurrentRole();
            loadMessages();
            loadOnlineUsers();
            startAutoRefresh();
        });

        // 获取当前角色信息
        function getCurrentRole() {
            fetch('/honghuang-game/api/role/current')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        currentRoleId = data.data.id;
                        initWebSocket();
                    } else {
                        showError('获取角色信息失败');
                    }
                })
                .catch(error => {
                    console.error('获取角色信息失败:', error);
                });
        }

        // 初始化WebSocket连接
        function initWebSocket() {
            if (!currentRoleId) return;

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/honghuang-game/websocket/game/${currentRoleId}`;

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    console.log('WebSocket连接成功');
                    showSuccess('实时聊天已连接');
                };

                websocket.onmessage = function(event) {
                    handleWebSocketMessage(JSON.parse(event.data));
                };

                websocket.onclose = function(event) {
                    console.log('WebSocket连接关闭');
                    showError('实时聊天连接断开，尝试重连...');
                    // 5秒后尝试重连
                    setTimeout(initWebSocket, 5000);
                };

                websocket.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    showError('实时聊天连接错误');
                };
            } catch (error) {
                console.error('WebSocket初始化失败:', error);
                showError('无法建立实时聊天连接');
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            switch(message.type) {
                case 'chat':
                    // 实时聊天消息
                    if (message.channel === currentChannel) {
                        addRealtimeMessage(message);
                    }
                    break;
                case 'system':
                    // 系统消息
                    if (currentChannel === 'system') {
                        addRealtimeMessage(message);
                    }
                    break;
                case 'notification':
                    // 系统通知
                    showSuccess(message.content);
                    break;
                case 'heartbeat':
                    // 心跳响应，不需要处理
                    break;
                default:
                    console.log('未知WebSocket消息类型:', message.type);
                    break;
            }
        }

        // 添加实时消息到聊天区域
        function addRealtimeMessage(message) {
            const messageObj = {
                id: Date.now(),
                senderName: message.senderName || '系统',
                content: message.content,
                createTime: new Date().toISOString(),
                type: message.type === 'system' ? 1 : 0
            };

            messages.push(messageObj);

            // 只保留最近100条消息
            if (messages.length > 100) {
                messages = messages.slice(-100);
            }

            displayMessages();
        }
        
        // 切换频道
        function switchChannel(channel) {
            currentChannel = channel;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新频道信息
            document.getElementById('channel-info').textContent = `当前频道：${channelInfo[channel]}`;
            
            // 显示/隐藏私聊按钮
            const privateBtnElement = document.getElementById('private-btn');
            if (channel === 'private') {
                privateBtnElement.style.display = 'block';
            } else {
                privateBtnElement.style.display = 'none';
            }
            
            // 显示/隐藏在线用户
            const onlineUsersElement = document.getElementById('online-users');
            if (channel === 'world' || channel === 'scene') {
                onlineUsersElement.style.display = 'block';
                loadOnlineUsers();
            } else {
                onlineUsersElement.style.display = 'none';
            }
            
            loadMessages();
        }
        
        // 加载消息
        function loadMessages() {
            showLoading();
            
            fetch(`/honghuang-game/api/chat/messages?channel=${currentChannel}&since=${lastMessageTime}`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        if (lastMessageTime === 0) {
                            // 首次加载，替换所有消息
                            messages = data.data;
                        } else {
                            // 增量加载，追加新消息
                            messages = messages.concat(data.data);
                        }
                        displayMessages();
                        updateLastMessageTime();
                    } else {
                        showError(data.message || '加载消息失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示消息
        function displayMessages() {
            const messagesContainer = document.getElementById('messages');
            messagesContainer.style.display = 'block';
            
            if (!messages || messages.length === 0) {
                messagesContainer.innerHTML = '<div class="empty-message">暂无消息</div>';
                return;
            }
            
            let html = '';
            messages.forEach(message => {
                const messageClass = getMessageClass(message.type);
                const time = new Date(message.createTime).toLocaleTimeString();
                
                html += `
                    <div class="message ${messageClass}">
                        <div class="message-header">
                            <span class="sender-name">${message.senderName || '系统'}</span>
                            <span class="message-time">${time}</span>
                        </div>
                        <div class="message-content">${escapeHtml(message.content)}</div>
                    </div>
                `;
            });
            
            messagesContainer.innerHTML = html;
            
            // 滚动到底部
            const container = document.getElementById('messages-container');
            container.scrollTop = container.scrollHeight;
        }
        
        // 获取消息样式类
        function getMessageClass(type) {
            switch(type) {
                case 1: return 'system';
                case 2: return 'private';
                case 3: return 'guild';
                default: return '';
            }
        }
        
        // 转义HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // 更新最后消息时间
        function updateLastMessageTime() {
            if (messages.length > 0) {
                const lastMessage = messages[messages.length - 1];
                lastMessageTime = new Date(lastMessage.createTime).getTime();
            }
        }
        
        // 发送消息
        function sendMessage() {
            const input = document.getElementById('message-input');
            const content = input.value.trim();

            if (!content) {
                showError('请输入消息内容');
                return;
            }

            if (content.length > 200) {
                showError('消息长度不能超过200字符');
                return;
            }

            const sendBtn = document.querySelector('.send-btn');
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';

            // 优先使用WebSocket发送
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'chat',
                    content: content,
                    channel: currentChannel
                };

                try {
                    websocket.send(JSON.stringify(message));
                    input.value = '';
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送';
                } catch (error) {
                    console.error('WebSocket发送失败，使用HTTP发送:', error);
                    sendMessageHttp(content, sendBtn, input);
                }
            } else {
                // WebSocket不可用，使用HTTP发送
                sendMessageHttp(content, sendBtn, input);
            }
        }

        // HTTP方式发送消息（备用方案）
        function sendMessageHttp(content, sendBtn, input) {
            fetch('/honghuang-game/api/chat/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    channel: currentChannel,
                    content: content
                })
            })
            .then(response => response.json())
            .then(data => {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';

                if (data.code === 200) {
                    input.value = '';
                    loadMessages(); // 刷新消息列表
                } else {
                    showError(data.message || '发送失败');
                }
            })
            .catch(error => {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
                showError('网络错误: ' + error.message);
            });
        }
        
        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // 加载在线用户
        function loadOnlineUsers() {
            fetch('/honghuang-game/api/chat/online-users')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        onlineUsers = data.data;
                        displayOnlineUsers();
                    }
                })
                .catch(error => {
                    console.error('加载在线用户失败:', error);
                });
        }
        
        // 显示在线用户
        function displayOnlineUsers() {
            const userList = document.getElementById('user-list');
            
            if (!onlineUsers || onlineUsers.length === 0) {
                userList.innerHTML = '<div style="color: #666;">暂无在线用户</div>';
                return;
            }
            
            let html = '';
            onlineUsers.forEach(user => {
                html += `
                    <div class="user-item online" onclick="startPrivateChat('${user.name}')">
                        ${user.name} (${user.level}级)
                    </div>
                `;
            });
            
            userList.innerHTML = html;
        }
        
        // 开始私聊
        function startPrivateChat(userName) {
            document.getElementById('recipient-input').value = userName;
            openPrivateChat();
        }
        
        // 打开私聊模态框
        function openPrivateChat() {
            document.getElementById('private-chat-modal').classList.add('show');
            document.getElementById('recipient-input').focus();
        }
        
        // 关闭私聊模态框
        function closePrivateChat() {
            document.getElementById('private-chat-modal').classList.remove('show');
            document.getElementById('recipient-input').value = '';
            document.getElementById('private-message-input').value = '';
        }
        
        // 发送私聊消息
        function sendPrivateMessage() {
            const recipient = document.getElementById('recipient-input').value.trim();
            const content = document.getElementById('private-message-input').value.trim();
            
            if (!recipient) {
                showError('请输入收件人');
                return;
            }
            
            if (!content) {
                showError('请输入消息内容');
                return;
            }
            
            fetch('/honghuang-game/api/chat/private', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    recipient: recipient,
                    content: content
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('私聊发送成功');
                    closePrivateChat();
                    if (currentChannel === 'private') {
                        loadMessages();
                    }
                } else {
                    showError(data.message || '私聊发送失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 开始自动刷新
        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                loadMessages();
                if (currentChannel === 'world' || currentChannel === 'scene') {
                    loadOnlineUsers();
                }
            }, 5000); // 每5秒刷新一次
        }
        
        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }
        
        // 刷新聊天
        function refreshChat() {
            lastMessageTime = 0;
            messages = [];
            loadMessages();
            loadOnlineUsers();
        }
        
        // 清空消息
        function clearMessages() {
            if (confirm('确定要清空当前显示的消息吗？')) {
                messages = [];
                document.getElementById('messages').innerHTML = '<div class="empty-message">消息已清空</div>';
            }
        }
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('messages').style.display = 'none';
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 显示错误消息
        function showError(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="error">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="success">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }
        
        // 页面卸载时停止自动刷新和关闭WebSocket
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
            if (websocket) {
                websocket.close();
            }
        });
    </script>
</body>
</html>
