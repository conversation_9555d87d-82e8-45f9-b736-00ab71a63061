package com.honghuang.game.service;

import com.honghuang.game.entity.GameScene;
import com.honghuang.game.repository.GameSceneRepository;
import com.honghuang.game.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 游戏场景服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class GameSceneService {

    @Autowired
    private GameSceneRepository gameSceneRepository;

    /**
     * 根据ID查找场景
     */
    public GameScene findById(String id) {
        return gameSceneRepository.findById(id)
                .orElseThrow(() -> new BusinessException("场景不存在"));
    }

    /**
     * 根据名称查找场景
     */
    public GameScene findByName(String name) {
        return gameSceneRepository.findByName(name)
                .orElseThrow(() -> new BusinessException("场景不存在"));
    }

    /**
     * 查找所有激活的场景
     */
    public List<GameScene> findAllActive() {
        return gameSceneRepository.findActiveScenes();
    }

    /**
     * 根据类型查找场景
     */
    public List<GameScene> findByType(Integer type) {
        return gameSceneRepository.findByType(type);
    }

    /**
     * 根据等级限制查找适合的场景
     */
    public List<GameScene> findSuitableScenes(Integer level) {
        return gameSceneRepository.findSuitableScenes(level);
    }

    /**
     * 查找城镇场景
     */
    public List<GameScene> findTowns() {
        return gameSceneRepository.findTowns();
    }

    /**
     * 查找副本场景
     */
    public List<GameScene> findDungeons() {
        return gameSceneRepository.findDungeons();
    }

    /**
     * 查找野外场景
     */
    public List<GameScene> findWilderness() {
        return gameSceneRepository.findWilderness();
    }

    /**
     * 查找安全区域
     */
    public List<GameScene> findSafeZones() {
        return gameSceneRepository.findSafeZones();
    }

    /**
     * 查找允许PK的场景
     */
    public List<GameScene> findPkScenes() {
        return gameSceneRepository.findPkScenes();
    }

    /**
     * 查找连接到指定场景的场景
     */
    public List<GameScene> findConnectedScenes(String sceneId) {
        return gameSceneRepository.findConnectedScenes(sceneId);
    }

    /**
     * 根据场景名称模糊查找
     */
    public List<GameScene> findByNameContaining(String name) {
        return gameSceneRepository.findByNameContaining(name);
    }

    /**
     * 根据等级范围查找场景
     */
    public List<GameScene> findByLevelRange(Integer minLevel, Integer maxLevel) {
        return gameSceneRepository.findByLevelRange(minLevel, maxLevel);
    }

    /**
     * 查找有怪物的场景
     */
    public List<GameScene> findScenesWithMonsters() {
        return gameSceneRepository.findScenesWithMonsters();
    }

    /**
     * 查找有NPC的场景
     */
    public List<GameScene> findScenesWithNpcs() {
        return gameSceneRepository.findScenesWithNpcs();
    }

    /**
     * 查找有传送点的场景
     */
    public List<GameScene> findScenesWithTeleports() {
        return gameSceneRepository.findScenesWithTeleports();
    }

    /**
     * 查找有特殊事件的场景
     */
    public List<GameScene> findScenesWithEvents() {
        return gameSceneRepository.findScenesWithEvents();
    }

    /**
     * 根据天气类型查找场景
     */
    public List<GameScene> findByWeatherType(Integer weatherType) {
        return gameSceneRepository.findByWeatherType(weatherType);
    }

    /**
     * 根据时间类型查找场景
     */
    public List<GameScene> findByTimeType(Integer timeType) {
        return gameSceneRepository.findByTimeType(timeType);
    }

    /**
     * 查找新手场景
     */
    public List<GameScene> findNewbieScenes() {
        return gameSceneRepository.findNewbieScenes();
    }

    /**
     * 查找高级场景
     */
    public List<GameScene> findHighLevelScenes() {
        return gameSceneRepository.findHighLevelScenes();
    }

    /**
     * 创建新场景
     */
    public GameScene createScene(GameScene scene) {
        // 验证场景ID唯一性
        Optional<GameScene> existingScene = gameSceneRepository.findById(scene.getId());
        if (existingScene.isPresent()) {
            throw new BusinessException("场景ID已存在");
        }

        // 设置默认值
        if (scene.getIsActive() == null) {
            scene.setIsActive(1);
        }
        if (scene.getType() == null) {
            scene.setType(1);
        }
        if (scene.getLevelLimit() == null) {
            scene.setLevelLimit(1);
        }

        log.info("创建新场景: {}", scene.getName());
        return gameSceneRepository.save(scene);
    }

    /**
     * 更新场景信息
     */
    public GameScene updateScene(GameScene scene) {
        GameScene existingScene = findById(scene.getId());
        
        // 更新字段
        existingScene.setName(scene.getName());
        existingScene.setDescription(scene.getDescription());
        existingScene.setType(scene.getType());
        existingScene.setLevelLimit(scene.getLevelLimit());
        existingScene.setMaxPlayers(scene.getMaxPlayers());
        existingScene.setIsSafeZone(scene.getIsSafeZone());
        existingScene.setAllowPk(scene.getAllowPk());
        existingScene.setAllowTeleport(scene.getAllowTeleport());
        existingScene.setBackgroundMusic(scene.getBackgroundMusic());
        existingScene.setBackgroundImage(scene.getBackgroundImage());
        existingScene.setMapWidth(scene.getMapWidth());
        existingScene.setMapHeight(scene.getMapHeight());
        existingScene.setSpawnX(scene.getSpawnX());
        existingScene.setSpawnY(scene.getSpawnY());
        existingScene.setNorthScene(scene.getNorthScene());
        existingScene.setSouthScene(scene.getSouthScene());
        existingScene.setEastScene(scene.getEastScene());
        existingScene.setWestScene(scene.getWestScene());
        existingScene.setUpScene(scene.getUpScene());
        existingScene.setDownScene(scene.getDownScene());
        existingScene.setMonsterConfig(scene.getMonsterConfig());
        existingScene.setNpcConfig(scene.getNpcConfig());
        existingScene.setTeleportConfig(scene.getTeleportConfig());
        existingScene.setEventConfig(scene.getEventConfig());
        existingScene.setWeatherType(scene.getWeatherType());
        existingScene.setTimeType(scene.getTimeType());

        log.info("更新场景信息: {}", existingScene.getName());
        return gameSceneRepository.save(existingScene);
    }

    /**
     * 删除场景（软删除）
     */
    public void deleteScene(String id) {
        GameScene scene = findById(id);
        scene.setIsActive(0);
        gameSceneRepository.save(scene);
        log.info("删除场景: {}", scene.getName());
    }

    /**
     * 激活场景
     */
    public void activateScene(String id) {
        GameScene scene = findById(id);
        scene.setIsActive(1);
        gameSceneRepository.save(scene);
        log.info("激活场景: {}", scene.getName());
    }

    /**
     * 统计各类型场景数量
     */
    public List<Object[]> countByType() {
        return gameSceneRepository.countByType();
    }

    /**
     * 验证玩家是否可以进入场景
     */
    public boolean canPlayerEnter(GameScene scene, Integer playerLevel) {
        // 检查场景是否激活
        if (scene.getIsActive() != 1) {
            return false;
        }

        // 检查等级限制
        if (scene.getLevelLimit() != null && playerLevel < scene.getLevelLimit()) {
            return false;
        }

        return true;
    }

    /**
     * 验证坐标是否有效
     */
    public boolean isValidPosition(GameScene scene, int x, int y) {
        return scene.isValidPosition(x, y);
    }

    /**
     * 获取场景的随机出生点
     */
    public int[] getRandomSpawnPoint(GameScene scene) {
        return scene.getRandomSpawnPoint();
    }

    /**
     * 获取场景的连接方向
     */
    public String getConnectionDirection(String fromSceneId, String toSceneId) {
        GameScene fromScene = findById(fromSceneId);
        
        if (toSceneId.equals(fromScene.getNorthScene())) {
            return "北";
        } else if (toSceneId.equals(fromScene.getSouthScene())) {
            return "南";
        } else if (toSceneId.equals(fromScene.getEastScene())) {
            return "东";
        } else if (toSceneId.equals(fromScene.getWestScene())) {
            return "西";
        } else if (toSceneId.equals(fromScene.getUpScene())) {
            return "上";
        } else if (toSceneId.equals(fromScene.getDownScene())) {
            return "下";
        }
        
        return "未知";
    }

    /**
     * 获取场景推荐等级
     */
    public String getRecommendedLevel(GameScene scene) {
        if (scene.getLevelLimit() == null) {
            return "无限制";
        }
        
        int minLevel = scene.getLevelLimit();
        int maxLevel = minLevel + 10; // 假设适合等级范围为+10级
        
        return String.format("%d-%d级", minLevel, maxLevel);
    }

    /**
     * 获取场景难度评级
     */
    public String getDifficultyRating(GameScene scene) {
        if (scene.isTown() || scene.isSafe()) {
            return "安全";
        }
        
        if (scene.getLevelLimit() == null) {
            return "普通";
        }
        
        int levelLimit = scene.getLevelLimit();
        if (levelLimit <= 10) {
            return "简单";
        } else if (levelLimit <= 30) {
            return "普通";
        } else if (levelLimit <= 50) {
            return "困难";
        } else {
            return "极难";
        }
    }
}
