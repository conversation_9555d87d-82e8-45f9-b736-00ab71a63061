-- 任务数据初始化脚本

-- 插入测试任务数据
INSERT INTO quest (id, name, description, type, required_level, required_race, required_sex,
                  exp_reward, copper_reward, item_reward_id, item_reward_count, repeatable, active,
                  objective, objective_type, objective_id, objective_count) VALUES
(1, '新手试炼', '击败3只野狼，证明你的战斗能力', 1, 1, 0, 0, 100, 50, NULL, NULL, 0, 1, '击败野狼', 1, 1, 3),
(2, '采集草药', '收集10个草药，帮助村医制作药剂', 2, 1, 0, 0, 80, 30, 1, 2, 1, 1, '收集草药', 2, 4, 10),
(3, '清理洞穴', '清理洞穴中的5只蝙蝠', 1, 3, 0, 0, 200, 100, NULL, NULL, 0, 1, '击败蝙蝠', 1, 2, 5),
(4, '送信任务', '将信件送到隔壁村庄', 3, 2, 0, 0, 120, 60, NULL, NULL, 1, 1, '到达隔壁村庄', 3, 2, 1),
(5, '寻找宝物', '在古老的遗迹中寻找失落的宝物', 4, 5, 0, 0, 500, 300, 5, 1, 0, 1, '寻找古老宝物', 4, 1, 1),
(6, '巫族传承', '学习巫族的古老法术', 5, 3, 1, 0, 300, 0, 9, 1, 0, 1, '学习巫族法术', 5, 1, 1),
(7, '妖族血脉', '觉醒妖族的血脉力量', 5, 3, 2, 0, 300, 0, 10, 1, 0, 1, '觉醒妖族血脉', 5, 2, 1),
(8, '每日修炼', '完成每日的修炼任务', 6, 1, 0, 0, 50, 20, NULL, NULL, 1, 1, '完成修炼', 6, 1, 1),
(9, '帮派贡献', '为帮派做出贡献', 7, 5, 0, 0, 150, 80, NULL, NULL, 1, 1, '帮派任务', 7, 1, 1),
(10, '师父的考验', '通过师父的考验', 8, 10, 0, 0, 1000, 500, 11, 1, 0, 1, '师父考验', 8, 1, 1);

-- 任务目标已经包含在quest表中，不需要单独的quest_objective表

-- 给测试角色添加一个进行中的任务
INSERT INTO player_quest (role_id, quest_id, status, progress, accept_time) VALUES
(1, 1, 1, 0, NOW()),
(1, 8, 1, 0, NOW());
