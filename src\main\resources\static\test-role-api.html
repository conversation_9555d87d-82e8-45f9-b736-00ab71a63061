<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>角色API测试页面</h1>
        
        <div class="section">
            <h3>1. 用户登录</h3>
            <input type="text" id="username" placeholder="用户名" value="testuser2">
            <input type="password" id="password" placeholder="密码" value="123456">
            <button onclick="login()">登录</button>
            <div class="result" id="loginResult"></div>
        </div>

        <div class="section">
            <h3>2. 获取角色列表</h3>
            <button onclick="getRoleList()">获取角色列表</button>
            <div class="result" id="roleListResult"></div>
        </div>

        <div class="section">
            <h3>3. 创建角色</h3>
            <input type="text" id="roleName" placeholder="角色名称" value="测试角色2">
            <select id="race">
                <option value="1">巫族</option>
                <option value="2">妖族</option>
            </select>
            <select id="sex">
                <option value="1">男</option>
                <option value="2">女</option>
            </select>
            <button onclick="createRole()">创建角色</button>
            <div class="result" id="createRoleResult"></div>
        </div>

        <div class="section">
            <h3>4. 获取当前角色</h3>
            <button onclick="getCurrentRole()">获取当前角色</button>
            <div class="result" id="currentRoleResult"></div>
        </div>

        <div class="section">
            <h3>5. 选择角色</h3>
            <input type="number" id="selectRoleId" placeholder="角色ID" value="1">
            <button onclick="selectRole()">选择角色</button>
            <div class="result" id="selectRoleResult"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/honghuang-game/api';

        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            fetch(`${API_BASE}/user/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password }),
                credentials: 'include' // 重要：包含cookies
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loginResult').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('loginResult').textContent = 'Error: ' + error.message;
            });
        }

        function getRoleList() {
            fetch(`${API_BASE}/role/list`, {
                credentials: 'include' // 重要：包含cookies
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('roleListResult').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('roleListResult').textContent = 'Error: ' + error.message;
            });
        }

        function createRole() {
            const roleName = document.getElementById('roleName').value;
            const race = parseInt(document.getElementById('race').value);
            const sex = parseInt(document.getElementById('sex').value);
            
            fetch(`${API_BASE}/role/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ roleName, race, sex }),
                credentials: 'include' // 重要：包含cookies
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('createRoleResult').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('createRoleResult').textContent = 'Error: ' + error.message;
            });
        }

        function getCurrentRole() {
            fetch(`${API_BASE}/role/current`, {
                credentials: 'include' // 重要：包含cookies
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('currentRoleResult').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('currentRoleResult').textContent = 'Error: ' + error.message;
            });
        }

        function selectRole() {
            const roleId = document.getElementById('selectRoleId').value;
            
            fetch(`${API_BASE}/role/select/${roleId}`, {
                method: 'POST',
                credentials: 'include' // 重要：包含cookies
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('selectRoleResult').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('selectRoleResult').textContent = 'Error: ' + error.message;
            });
        }
    </script>
</body>
</html>
