package com.honghuang.game.controller;

import com.honghuang.game.websocket.GameWebSocketServer;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket测试控制器
 */
@RestController
@RequestMapping("/api/websocket")
public class WebSocketTestController {

    /**
     * 获取WebSocket状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getWebSocketStatus() {
        Map<String, Object> result = new HashMap<>();
        result.put("onlineCount", GameWebSocketServer.getOnlineCount());
        result.put("onlineUsers", GameWebSocketServer.getOnlineUsers());
        result.put("status", "WebSocket服务正常运行");
        return ResponseEntity.ok(result);
    }

    /**
     * 发送测试消息
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> sendTestMessage() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 广播测试消息
            Map<String, Object> message = new HashMap<>();
            message.put("type", "system");
            message.put("title", "系统测试");
            message.put("content", "这是一条WebSocket测试消息");
            message.put("timestamp", System.currentTimeMillis());
            
            GameWebSocketServer.broadcastMessage(message);
            
            result.put("success", true);
            result.put("message", "测试消息已发送");
            result.put("onlineCount", GameWebSocketServer.getOnlineCount());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "发送测试消息失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
}
