package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.MentorApplication;
import com.honghuang.game.entity.MentorRelation;
import com.honghuang.game.service.MentorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 师徒系统控制器
 */
@RestController
@RequestMapping("/api/mentor")
public class MentorController {

    @Autowired
    private MentorService mentorService;

    /**
     * 申请拜师
     */
    @PostMapping("/apply-mentor")
    public Result<MentorApplication> applyForMentor(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer mentorId = (Integer) request.get("mentorId");
            String message = (String) request.get("message");
            
            if (mentorId == null) {
                return Result.error(400, "师父ID不能为空");
            }
            
            MentorApplication application = mentorService.applyForMentor(currentRoleId, mentorId, message);
            return Result.success("拜师申请提交成功", application);
        } catch (Exception e) {
            return Result.error(500, "拜师申请失败: " + e.getMessage());
        }
    }

    /**
     * 申请收徒
     */
    @PostMapping("/apply-apprentice")
    public Result<MentorApplication> applyForApprentice(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer apprenticeId = (Integer) request.get("apprenticeId");
            String message = (String) request.get("message");
            
            if (apprenticeId == null) {
                return Result.error(400, "徒弟ID不能为空");
            }
            
            MentorApplication application = mentorService.applyForApprentice(currentRoleId, apprenticeId, message);
            return Result.success("收徒申请提交成功", application);
        } catch (Exception e) {
            return Result.error(500, "收徒申请失败: " + e.getMessage());
        }
    }

    /**
     * 同意师徒申请
     */
    @PostMapping("/applications/{applicationId}/approve")
    public Result<MentorRelation> approveApplication(@PathVariable Integer applicationId,
                                                    @RequestBody Map<String, String> request,
                                                    HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String note = request.get("note");
            
            MentorRelation relation = mentorService.approveApplication(currentRoleId, applicationId, note);
            return Result.success("师徒申请已同意", relation);
        } catch (Exception e) {
            return Result.error(500, "同意申请失败: " + e.getMessage());
        }
    }

    /**
     * 拒绝师徒申请
     */
    @PostMapping("/applications/{applicationId}/reject")
    public Result<Void> rejectApplication(@PathVariable Integer applicationId,
                                         @RequestBody Map<String, String> request,
                                         HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String note = request.get("note");
            
            boolean success = mentorService.rejectApplication(currentRoleId, applicationId, note);
            if (success) {
                return Result.success("师徒申请已拒绝");
            } else {
                return Result.error(400, "拒绝申请失败");
            }
        } catch (Exception e) {
            return Result.error(500, "拒绝申请失败: " + e.getMessage());
        }
    }

    /**
     * 解除师徒关系
     */
    @PostMapping("/relations/{relationId}/dissolve")
    public Result<Void> dissolveMentorRelation(@PathVariable Integer relationId,
                                              @RequestBody Map<String, String> request,
                                              HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String reason = request.get("reason");
            
            boolean success = mentorService.dissolveMentorRelation(currentRoleId, relationId, reason);
            if (success) {
                return Result.success("师徒关系已解除");
            } else {
                return Result.error(400, "解除师徒关系失败");
            }
        } catch (Exception e) {
            return Result.error(500, "解除师徒关系失败: " + e.getMessage());
        }
    }

    /**
     * 徒弟出师
     */
    @PostMapping("/relations/{relationId}/graduate")
    public Result<Void> graduateApprentice(@PathVariable Integer relationId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = mentorService.graduateApprentice(currentRoleId, relationId);
            if (success) {
                return Result.success("徒弟出师成功");
            } else {
                return Result.error(400, "徒弟出师失败");
            }
        } catch (Exception e) {
            return Result.error(500, "徒弟出师失败: " + e.getMessage());
        }
    }

    /**
     * 师父传功
     */
    @PostMapping("/teach")
    public Result<Void> teachApprentice(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer apprenticeId = (Integer) request.get("apprenticeId");
            Long experience = ((Number) request.get("experience")).longValue();
            
            if (apprenticeId == null || experience == null) {
                return Result.error(400, "参数不完整");
            }
            
            boolean success = mentorService.teachApprentice(currentRoleId, apprenticeId, experience);
            if (success) {
                return Result.success("师父传功成功");
            } else {
                return Result.error(400, "师父传功失败");
            }
        } catch (Exception e) {
            return Result.error(500, "师父传功失败: " + e.getMessage());
        }
    }

    /**
     * 获取徒弟列表
     */
    @GetMapping("/apprentices")
    public Result<List<MentorRelation>> getApprentices(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<MentorRelation> apprentices = mentorService.getApprentices(currentRoleId);
            return Result.success("获取徒弟列表成功", apprentices);
        } catch (Exception e) {
            return Result.error(500, "获取徒弟列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取师父信息
     */
    @GetMapping("/mentor")
    public Result<MentorRelation> getMentor(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Optional<MentorRelation> mentor = mentorService.getMentor(currentRoleId);
            if (mentor.isPresent()) {
                return Result.success("获取师父信息成功", mentor.get());
            } else {
                return Result.error(404, "没有师父");
            }
        } catch (Exception e) {
            return Result.error(500, "获取师父信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有师徒关系
     */
    @GetMapping("/relations")
    public Result<List<MentorRelation>> getAllMentorRelations(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<MentorRelation> relations = mentorService.getAllMentorRelations(currentRoleId);
            return Result.success("获取师徒关系成功", relations);
        } catch (Exception e) {
            return Result.error(500, "获取师徒关系失败: " + e.getMessage());
        }
    }

    /**
     * 获取待处理的申请列表
     */
    @GetMapping("/applications/pending")
    public Result<List<MentorApplication>> getPendingApplications(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<MentorApplication> applications = mentorService.getPendingApplications(currentRoleId);
            return Result.success("获取待处理申请成功", applications);
        } catch (Exception e) {
            return Result.error(500, "获取待处理申请失败: " + e.getMessage());
        }
    }

    /**
     * 获取申请历史
     */
    @GetMapping("/applications/history")
    public Result<List<MentorApplication>> getApplicationHistory(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<MentorApplication> applications = mentorService.getApplicationHistory(currentRoleId);
            return Result.success("获取申请历史成功", applications);
        } catch (Exception e) {
            return Result.error(500, "获取申请历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取师徒统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getMentorStatistics(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, Object> stats = mentorService.getMentorStatistics(currentRoleId);
            return Result.success("获取师徒统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取师徒统计失败: " + e.getMessage());
        }
    }
}
