package com.honghuang.game.repository;

import com.honghuang.game.entity.MentorRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 师徒关系数据访问层
 */
@Repository
public interface MentorRelationRepository extends JpaRepository<MentorRelation, Integer> {

    /**
     * 根据师父ID查找徒弟列表
     */
    List<MentorRelation> findByMentorIdAndStatusOrderByEstablishTimeDesc(Integer mentorId, Integer status);

    /**
     * 根据徒弟ID查找师父
     */
    Optional<MentorRelation> findByApprenticeIdAndStatus(Integer apprenticeId, Integer status);

    /**
     * 根据师父ID和徒弟ID查找关系
     */
    Optional<MentorRelation> findByMentorIdAndApprenticeIdAndStatus(Integer mentorId, Integer apprenticeId, Integer status);

    /**
     * 查找角色的所有师徒关系（作为师父或徒弟）
     */
    @Query("SELECT mr FROM MentorRelation mr WHERE (mr.mentorId = :roleId OR mr.apprenticeId = :roleId) AND mr.status = :status ORDER BY mr.establishTime DESC")
    List<MentorRelation> findByRoleIdAndStatus(@Param("roleId") Integer roleId, @Param("status") Integer status);

    /**
     * 统计师父的徒弟数量
     */
    @Query("SELECT COUNT(mr) FROM MentorRelation mr WHERE mr.mentorId = :mentorId AND mr.status = 1")
    long countApprenticesByMentorId(@Param("mentorId") Integer mentorId);

    /**
     * 查找活跃的师徒关系
     */
    List<MentorRelation> findByStatusOrderByLastInteractionTimeDesc(Integer status);

    /**
     * 查找长期未互动的师徒关系
     */
    @Query("SELECT mr FROM MentorRelation mr WHERE mr.status = 1 AND mr.lastInteractionTime <= :cutoffTime")
    List<MentorRelation> findInactiveRelations(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找可以出师的师徒关系
     */
    @Query("SELECT mr FROM MentorRelation mr WHERE mr.status = 1 " +
           "AND mr.apprenticeLevel >= (mr.mentorLevel * 0.8) " +
           "AND mr.establishTime <= :cutoffTime")
    List<MentorRelation> findGraduationCandidates(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 根据贡献度排序查找师父排行榜
     */
    @Query("SELECT mr FROM MentorRelation mr WHERE mr.status = 1 GROUP BY mr.mentorId ORDER BY SUM(mr.mentorContribution) DESC")
    List<MentorRelation> findMentorRanking();

    /**
     * 根据贡献度排序查找徒弟排行榜
     */
    @Query("SELECT mr FROM MentorRelation mr WHERE mr.status = 1 ORDER BY mr.apprenticeContribution DESC")
    List<MentorRelation> findApprenticeRanking();

    /**
     * 更新师父贡献度
     */
    @Modifying
    @Query("UPDATE MentorRelation mr SET mr.mentorContribution = mr.mentorContribution + :amount, mr.lastInteractionTime = CURRENT_TIMESTAMP WHERE mr.id = :relationId")
    int addMentorContribution(@Param("relationId") Integer relationId, @Param("amount") Long amount);

    /**
     * 更新徒弟贡献度
     */
    @Modifying
    @Query("UPDATE MentorRelation mr SET mr.apprenticeContribution = mr.apprenticeContribution + :amount, mr.lastInteractionTime = CURRENT_TIMESTAMP WHERE mr.id = :relationId")
    int addApprenticeContribution(@Param("relationId") Integer relationId, @Param("amount") Long amount);

    /**
     * 更新师父等级
     */
    @Modifying
    @Query("UPDATE MentorRelation mr SET mr.mentorLevel = :level WHERE mr.mentorId = :mentorId AND mr.status = 1")
    int updateMentorLevel(@Param("mentorId") Integer mentorId, @Param("level") Integer level);

    /**
     * 更新徒弟等级
     */
    @Modifying
    @Query("UPDATE MentorRelation mr SET mr.apprenticeLevel = :level WHERE mr.apprenticeId = :apprenticeId AND mr.status = 1")
    int updateApprenticeLevel(@Param("apprenticeId") Integer apprenticeId, @Param("level") Integer level);

    /**
     * 更新互动时间
     */
    @Modifying
    @Query("UPDATE MentorRelation mr SET mr.lastInteractionTime = CURRENT_TIMESTAMP WHERE mr.id = :relationId")
    int updateInteractionTime(@Param("relationId") Integer relationId);

    /**
     * 解除师徒关系
     */
    @Modifying
    @Query("UPDATE MentorRelation mr SET mr.status = :status, mr.dissolveTime = CURRENT_TIMESTAMP WHERE mr.id = :relationId")
    int dissolveRelation(@Param("relationId") Integer relationId, @Param("status") Integer status);

    /**
     * 查找指定时间范围内建立的师徒关系
     */
    @Query("SELECT mr FROM MentorRelation mr WHERE mr.establishTime BETWEEN :startTime AND :endTime ORDER BY mr.establishTime DESC")
    List<MentorRelation> findByEstablishTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计师徒关系总数
     */
    @Query("SELECT COUNT(mr) FROM MentorRelation mr WHERE mr.status = 1")
    long countActiveRelations();

    /**
     * 查找等级差距在指定范围内的师徒关系
     */
    @Query("SELECT mr FROM MentorRelation mr WHERE mr.status = 1 AND (mr.mentorLevel - mr.apprenticeLevel) BETWEEN :minGap AND :maxGap")
    List<MentorRelation> findByLevelGapRange(@Param("minGap") Integer minGap, @Param("maxGap") Integer maxGap);
}
