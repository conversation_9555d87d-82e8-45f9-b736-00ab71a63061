package com.honghuang.game.repository;

import com.honghuang.game.entity.Equipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 装备数据访问层
 */
@Repository
public interface EquipmentRepository extends JpaRepository<Equipment, Integer> {

    /**
     * 查找激活的装备
     */
    List<Equipment> findByActiveOrderByTypeAscQualityDesc(Integer active);

    /**
     * 根据类型查找装备
     */
    List<Equipment> findByTypeAndActiveOrderByQualityDesc(Integer type, Integer active);

    /**
     * 根据品质查找装备
     */
    List<Equipment> findByQualityAndActiveOrderByTypeAsc(Integer quality, Integer active);

    /**
     * 根据等级范围查找装备
     */
    @Query("SELECT e FROM Equipment e WHERE e.requiredLevel BETWEEN :minLevel AND :maxLevel AND e.active = 1 ORDER BY e.requiredLevel ASC")
    List<Equipment> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 根据种族查找装备
     */
    @Query("SELECT e FROM Equipment e WHERE (e.requiredRace = 0 OR e.requiredRace = :race) AND e.active = 1 ORDER BY e.type ASC, e.quality DESC")
    List<Equipment> findByRace(@Param("race") Integer race);

    /**
     * 根据性别查找装备
     */
    @Query("SELECT e FROM Equipment e WHERE (e.requiredSex = 0 OR e.requiredSex = :sex) AND e.active = 1 ORDER BY e.type ASC, e.quality DESC")
    List<Equipment> findBySex(@Param("sex") Integer sex);

    /**
     * 查找适合角色的装备
     */
    @Query("SELECT e FROM Equipment e WHERE e.requiredLevel <= :level " +
           "AND (e.requiredRace = 0 OR e.requiredRace = :race) " +
           "AND (e.requiredSex = 0 OR e.requiredSex = :sex) " +
           "AND e.active = 1 ORDER BY e.type ASC, e.quality DESC")
    List<Equipment> findSuitableEquipment(@Param("level") Integer level, @Param("race") Integer race, @Param("sex") Integer sex);

    /**
     * 根据套装ID查找装备
     */
    List<Equipment> findBySuitIdAndActiveOrderByTypeAsc(Integer suitId, Integer active);

    /**
     * 查找所有套装装备
     */
    @Query("SELECT e FROM Equipment e WHERE e.suitId > 0 AND e.active = 1 ORDER BY e.suitId ASC, e.type ASC")
    List<Equipment> findAllSuitEquipment();

    /**
     * 根据名称模糊查找装备
     */
    @Query("SELECT e FROM Equipment e WHERE e.name LIKE %:name% AND e.active = 1 ORDER BY e.quality DESC")
    List<Equipment> findByNameContaining(@Param("name") String name);

    /**
     * 查找价格范围内的装备
     */
    @Query("SELECT e FROM Equipment e WHERE e.buyPrice BETWEEN :minPrice AND :maxPrice AND e.active = 1 ORDER BY e.buyPrice ASC")
    List<Equipment> findByPriceRange(@Param("minPrice") Long minPrice, @Param("maxPrice") Long maxPrice);
}
