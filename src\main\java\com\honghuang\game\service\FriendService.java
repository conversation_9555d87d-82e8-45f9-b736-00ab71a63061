package com.honghuang.game.service;

import com.honghuang.game.entity.Friend;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.FriendRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 好友服务类
 */
@Service
@Transactional
public class FriendService {

    @Autowired
    private FriendRepository friendRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    @Autowired
    private ChatService chatService;

    /**
     * 添加好友
     */
    public Friend addFriend(Integer playerId, String friendName) {
        System.out.println("添加好友: playerId=" + playerId + ", friendName=" + friendName);

        PlayerRole player = playerRoleService.findById(playerId);
        PlayerRole friendRole = playerRoleService.findByName(friendName);

        if (friendRole == null) {
            throw new BusinessException(404, "玩家不存在");
        }

        if (playerId.equals(friendRole.getId())) {
            throw new BusinessException(400, "不能添加自己为好友");
        }

        // 检查是否已经是好友
        Optional<Friend> existingFriend = friendRepository.findByPlayerIdAndFriendId(playerId, friendRole.getId());
        if (existingFriend.isPresent()) {
            if (existingFriend.get().isNormal()) {
                throw new BusinessException(400, "已经是好友关系");
            } else if (existingFriend.get().isBlocked()) {
                throw new BusinessException(400, "该玩家在黑名单中");
            }
        }

        // 检查好友数量限制
        long friendCount = friendRepository.countNormalFriends(playerId);
        if (friendCount >= 50) { // 最多50个好友
            throw new BusinessException(400, "好友数量已达上限");
        }

        // 创建好友关系
        Friend friend = new Friend();
        friend.setPlayerId(playerId);
        friend.setFriendId(friendRole.getId());
        friend.setFriendName(friendRole.getName());
        friend.setFriendLevel(friendRole.getLevel());
        friend.setAddTime(LocalDateTime.now());
        friend.setStatus(1); // 正常状态

        friend = friendRepository.save(friend);

        // 发送系统消息通知
        chatService.sendSystemMessage("玩家 " + player.getName() + " 添加您为好友", 2, friendRole.getId());

        System.out.println("好友添加成功: friendId=" + friend.getId());
        return friend;
    }

    /**
     * 删除好友
     */
    public boolean removeFriend(Integer playerId, Integer friendId) {
        System.out.println("删除好友: playerId=" + playerId + ", friendId=" + friendId);

        Friend friend = friendRepository.findByPlayerIdAndFriendId(playerId, friendId)
                .orElseThrow(() -> new BusinessException(404, "好友关系不存在"));

        friendRepository.delete(friend);

        // 同时删除对方的好友关系（如果存在）
        Optional<Friend> reverseFriend = friendRepository.findByPlayerIdAndFriendId(friendId, playerId);
        if (reverseFriend.isPresent()) {
            friendRepository.delete(reverseFriend.get());
        }

        System.out.println("好友删除成功");
        return true;
    }

    /**
     * 拉黑好友
     */
    public boolean blockFriend(Integer playerId, Integer friendId) {
        System.out.println("拉黑好友: playerId=" + playerId + ", friendId=" + friendId);

        Friend friend = friendRepository.findByPlayerIdAndFriendId(playerId, friendId)
                .orElseThrow(() -> new BusinessException(404, "好友关系不存在"));

        friend.setStatus(2); // 设置为黑名单状态
        friendRepository.save(friend);

        System.out.println("好友拉黑成功");
        return true;
    }

    /**
     * 解除拉黑
     */
    public boolean unblockFriend(Integer playerId, Integer friendId) {
        System.out.println("解除拉黑: playerId=" + playerId + ", friendId=" + friendId);

        Friend friend = friendRepository.findByPlayerIdAndFriendId(playerId, friendId)
                .orElseThrow(() -> new BusinessException(404, "好友关系不存在"));

        if (!friend.isBlocked()) {
            throw new BusinessException(400, "该好友未被拉黑");
        }

        friend.setStatus(1); // 恢复正常状态
        friendRepository.save(friend);

        System.out.println("解除拉黑成功");
        return true;
    }

    /**
     * 设置好友备注
     */
    public boolean setFriendRemark(Integer playerId, Integer friendId, String remark) {
        System.out.println("设置好友备注: playerId=" + playerId + ", friendId=" + friendId);

        Friend friend = friendRepository.findByPlayerIdAndFriendId(playerId, friendId)
                .orElseThrow(() -> new BusinessException(404, "好友关系不存在"));

        if (remark != null && remark.length() > 20) {
            throw new BusinessException(400, "备注长度不能超过20字符");
        }

        friend.setRemark(remark);
        friendRepository.save(friend);

        System.out.println("好友备注设置成功");
        return true;
    }

    /**
     * 获取好友列表
     */
    @Transactional(readOnly = true)
    public List<Friend> getFriendList(Integer playerId) {
        return friendRepository.findNormalFriends(playerId);
    }

    /**
     * 获取在线好友列表
     */
    @Transactional(readOnly = true)
    public List<Friend> getOnlineFriends(Integer playerId) {
        return friendRepository.findOnlineFriends(playerId);
    }

    /**
     * 获取黑名单列表
     */
    @Transactional(readOnly = true)
    public List<Friend> getBlockedFriends(Integer playerId) {
        return friendRepository.findBlockedFriends(playerId);
    }

    /**
     * 更新好友在线状态
     */
    public void updateFriendOnlineStatus(Integer playerId, boolean isOnline) {
        System.out.println("更新好友在线状态: playerId=" + playerId + ", isOnline=" + isOnline);

        // 更新所有以该玩家为好友的记录
        List<Friend> friendRecords = friendRepository.findByFriendId(playerId);
        for (Friend friend : friendRecords) {
            friend.setIsOnline(isOnline ? 1 : 0);
            if (!isOnline) {
                friend.setLastOnlineTime(LocalDateTime.now());
            }
            friendRepository.save(friend);
        }

        System.out.println("好友在线状态更新完成");
    }

    /**
     * 增加亲密度
     */
    public boolean addIntimacy(Integer playerId, Integer friendId, Integer intimacy) {
        System.out.println("增加亲密度: playerId=" + playerId + ", friendId=" + friendId + ", intimacy=" + intimacy);

        if (intimacy <= 0) {
            return false;
        }

        Friend friend = friendRepository.findByPlayerIdAndFriendId(playerId, friendId)
                .orElse(null);

        if (friend != null && friend.isNormal()) {
            friend.setIntimacy(friend.getIntimacy() + intimacy);
            
            // 检查友好度升级
            checkFriendshipLevelUp(friend);
            
            friendRepository.save(friend);
            
            System.out.println("亲密度增加成功");
            return true;
        }

        return false;
    }

    /**
     * 更新聊天时间
     */
    public void updateLastChatTime(Integer playerId, Integer friendId) {
        Friend friend = friendRepository.findByPlayerIdAndFriendId(playerId, friendId)
                .orElse(null);

        if (friend != null) {
            friend.setLastChatTime(LocalDateTime.now());
            friendRepository.save(friend);
        }
    }

    /**
     * 搜索好友
     */
    @Transactional(readOnly = true)
    public List<Friend> searchFriends(Integer playerId, String keyword) {
        return friendRepository.searchFriends(playerId, keyword);
    }

    /**
     * 获取好友详情
     */
    @Transactional(readOnly = true)
    public Friend getFriendDetail(Integer playerId, Integer friendId) {
        return friendRepository.findByPlayerIdAndFriendId(playerId, friendId)
                .orElseThrow(() -> new BusinessException(404, "好友关系不存在"));
    }

    /**
     * 检查是否为好友
     */
    @Transactional(readOnly = true)
    public boolean isFriend(Integer playerId, Integer friendId) {
        Optional<Friend> friend = friendRepository.findByPlayerIdAndFriendId(playerId, friendId);
        return friend.isPresent() && friend.get().isNormal();
    }

    /**
     * 检查是否被拉黑
     */
    @Transactional(readOnly = true)
    public boolean isBlocked(Integer playerId, Integer friendId) {
        Optional<Friend> friend = friendRepository.findByPlayerIdAndFriendId(friendId, playerId);
        return friend.isPresent() && friend.get().isBlocked();
    }

    /**
     * 获取好友统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getFriendStatistics(Integer playerId) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        long totalFriends = friendRepository.countNormalFriends(playerId);
        long onlineFriends = friendRepository.countOnlineFriends(playerId);
        long blockedFriends = friendRepository.countBlockedFriends(playerId);
        
        stats.put("totalFriends", totalFriends);
        stats.put("onlineFriends", onlineFriends);
        stats.put("blockedFriends", blockedFriends);
        stats.put("maxFriends", 50);
        
        return stats;
    }

    /**
     * 批量更新好友信息
     */
    @Transactional
    public void batchUpdateFriendInfo() {
        System.out.println("批量更新好友信息");

        List<Friend> allFriends = friendRepository.findAll();
        for (Friend friend : allFriends) {
            try {
                PlayerRole friendRole = playerRoleService.findById(friend.getFriendId());
                if (friendRole != null) {
                    friend.setFriendName(friendRole.getName());
                    friend.setFriendLevel(friendRole.getLevel());
                    friendRepository.save(friend);
                }
            } catch (Exception e) {
                System.out.println("更新好友信息失败: friendId=" + friend.getFriendId() + ", error=" + e.getMessage());
            }
        }

        System.out.println("批量更新好友信息完成");
    }

    /**
     * 检查友好度升级
     */
    private void checkFriendshipLevelUp(Friend friend) {
        int currentLevel = friend.getFriendshipLevel();
        int requiredIntimacy = currentLevel * 100; // 每级需要100亲密度
        
        if (friend.getIntimacy() >= requiredIntimacy && currentLevel < 10) {
            friend.setFriendshipLevel(currentLevel + 1);
            friend.setIntimacy(friend.getIntimacy() - requiredIntimacy);
            
            // 发送升级通知
            chatService.sendSystemMessage("与好友 " + friend.getFriendName() + " 的友好度提升到 " + 
                    friend.getFriendshipLevel() + " 级", 2, friend.getPlayerId());
        }
    }

    /**
     * 清理无效好友关系
     */
    @Transactional
    public void cleanupInvalidFriends() {
        System.out.println("清理无效好友关系");

        List<Friend> allFriends = friendRepository.findAll();
        int cleanupCount = 0;

        for (Friend friend : allFriends) {
            try {
                PlayerRole friendRole = playerRoleService.findById(friend.getFriendId());
                if (friendRole == null) {
                    friendRepository.delete(friend);
                    cleanupCount++;
                }
            } catch (Exception e) {
                friendRepository.delete(friend);
                cleanupCount++;
            }
        }

        System.out.println("清理无效好友关系完成，共清理" + cleanupCount + "条记录");
    }
}
