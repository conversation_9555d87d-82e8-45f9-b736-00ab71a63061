server:
  port: 8080

spring:
  application:
    name: honghuang-game

  # H2内存数据库配置
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:

  # 禁用不必要的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # H2控制台
  h2:
    console:
      enabled: true
      path: /h2-console

  # Spring Security配置
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# 禁用Swagger
springfox:
  documentation:
    enabled: false

# 日志配置
logging:
  level:
    com.honghuang.game: info
    org.springframework: warn
    org.hibernate: warn
