package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 宠物实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pet")
public class Pet {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "pet_id")
    private Integer id;
    
    @Column(name = "pet_name")
    private String name;
    
    @Column(name = "pet_nickname")
    private String nickname;
    
    @Column(name = "pet_type")
    private Integer type; // 宠物类型
    
    @Column(name = "pet_level")
    private Integer level;
    
    @Column(name = "pet_exp")
    private Integer exp;
    
    @Column(name = "pet_max_exp")
    private Integer maxExp;
    
    @Column(name = "pet_hp")
    private Integer hp;
    
    @Column(name = "pet_max_hp")
    private Integer maxHp;
    
    @Column(name = "pet_mp")
    private Integer mp;
    
    @Column(name = "pet_max_mp")
    private Integer maxMp;
    
    @Column(name = "pet_attack_min")
    private Integer attackMin;
    
    @Column(name = "pet_attack_max")
    private Integer attackMax;
    
    @Column(name = "pet_defense")
    private Integer defense;
    
    @Column(name = "pet_agility")
    private Integer agility;
    
    @Column(name = "pet_intelligence")
    private Integer intelligence;
    
    @Column(name = "pet_growth_rate")
    private Double growthRate; // 成长率
    
    @Column(name = "pet_loyalty")
    private Integer loyalty; // 忠诚度
    
    @Column(name = "pet_fatigue")
    private Integer fatigue; // 疲劳度
    
    @Column(name = "pet_hunger")
    private Integer hunger; // 饥饿度
    
    @Column(name = "pet_skill1")
    private Integer skill1;
    
    @Column(name = "pet_skill2")
    private Integer skill2;
    
    @Column(name = "pet_skill3")
    private Integer skill3;
    
    @Column(name = "pet_skill4")
    private Integer skill4;
    
    @Column(name = "pet_skill5")
    private Integer skill5;
    
    @Column(name = "pet_is_active")
    private Integer isActive; // 是否出战
    
    @Column(name = "pet_is_follow")
    private Integer isFollow; // 是否跟随
    
    @Column(name = "owner_id")
    private Integer ownerId; // 主人ID
    
    @Column(name = "capture_time")
    private LocalDateTime captureTime; // 捕获时间
    
    @Column(name = "pet_picture")
    private String picture;
    
    @Column(name = "pet_description")
    private String description;
    
    @Column(name = "pet_rarity")
    private Integer rarity; // 稀有度 1-5
    
    @Column(name = "pet_sale_price")
    private Integer salePrice; // 出售价格
    


    // 业务方法

    /**
     * 获取宠物类型名称
     */
    public String getTypeName() {
        switch (type) {
            case 1: return "普通";
            case 2: return "稀有";
            case 3: return "史诗";
            case 4: return "传说";
            default: return "未知";
        }
    }

    /**
     * 是否出战中
     */
    public boolean isActiveStatus() {
        return isActive == 1;
    }

    /**
     * 是否存活
     */
    public boolean isAlive() {
        return hp > 0;
    }

    /**
     * 是否需要喂食
     */
    public boolean needsFeeding() {
        return hunger > 80;
    }

    /**
     * 是否需要休息
     */
    public boolean needsRest() {
        return fatigue > 80;
    }

    /**
     * 是否忠诚
     */
    public boolean isLoyal() {
        return loyalty > 60;
    }
    
    public void setMp(Integer mp) {
        this.mp = mp;
    }
    
    public Integer getMaxMp() {
        return maxMp;
    }
    
    public void setMaxMp(Integer maxMp) {
        this.maxMp = maxMp;
    }
    
    public Integer getAttackMin() {
        return attackMin;
    }
    
    public void setAttackMin(Integer attackMin) {
        this.attackMin = attackMin;
    }
    
    public Integer getAttackMax() {
        return attackMax;
    }
    
    public void setAttackMax(Integer attackMax) {
        this.attackMax = attackMax;
    }
    
    public Integer getDefense() {
        return defense;
    }
    
    public void setDefense(Integer defense) {
        this.defense = defense;
    }
    
    public Integer getAgility() {
        return agility;
    }
    
    public void setAgility(Integer agility) {
        this.agility = agility;
    }
    
    public Integer getIntelligence() {
        return intelligence;
    }
    
    public void setIntelligence(Integer intelligence) {
        this.intelligence = intelligence;
    }
    
    public Double getGrowthRate() {
        return growthRate;
    }
    
    public void setGrowthRate(Double growthRate) {
        this.growthRate = growthRate;
    }
    
    public Integer getLoyalty() {
        return loyalty;
    }
    
    public void setLoyalty(Integer loyalty) {
        this.loyalty = loyalty;
    }
    
    public Integer getFatigue() {
        return fatigue;
    }
    
    public void setFatigue(Integer fatigue) {
        this.fatigue = fatigue;
    }
    
    public Integer getHunger() {
        return hunger;
    }
    
    public void setHunger(Integer hunger) {
        this.hunger = hunger;
    }
    
    public Integer getSkill1() {
        return skill1;
    }
    
    public void setSkill1(Integer skill1) {
        this.skill1 = skill1;
    }
    
    public Integer getSkill2() {
        return skill2;
    }
    
    public void setSkill2(Integer skill2) {
        this.skill2 = skill2;
    }
    
    public Integer getSkill3() {
        return skill3;
    }
    
    public void setSkill3(Integer skill3) {
        this.skill3 = skill3;
    }
    
    public Integer getSkill4() {
        return skill4;
    }
    
    public void setSkill4(Integer skill4) {
        this.skill4 = skill4;
    }
    
    public Integer getSkill5() {
        return skill5;
    }
    
    public void setSkill5(Integer skill5) {
        this.skill5 = skill5;
    }
    
    public Integer getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }
    
    public Integer getIsFollow() {
        return isFollow;
    }
    
    public void setIsFollow(Integer isFollow) {
        this.isFollow = isFollow;
    }
    
    public Integer getOwnerId() {
        return ownerId;
    }
    
    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }
    
    public LocalDateTime getCaptureTime() {
        return captureTime;
    }
    
    public void setCaptureTime(LocalDateTime captureTime) {
        this.captureTime = captureTime;
    }
    
    public String getPicture() {
        return picture;
    }
    
    public void setPicture(String picture) {
        this.picture = picture;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getRarity() {
        return rarity;
    }
    
    public void setRarity(Integer rarity) {
        this.rarity = rarity;
    }
    
    public Integer getSalePrice() {
        return salePrice;
    }
    
    public void setSalePrice(Integer salePrice) {
        this.salePrice = salePrice;
    }
    
    // 业务方法
    public boolean isActive() {
        return isActive != null && isActive == 1;
    }
    
    public boolean isFollowing() {
        return isFollow != null && isFollow == 1;
    }
    
    public int getAttack() {
        if (attackMin == null || attackMax == null) {
            return 0;
        }
        return attackMin + (int)(Math.random() * (attackMax - attackMin + 1));
    }
    
    /**
     * 判断是否可以升级
     */
    public boolean canLevelUp() {
        return exp != null && maxExp != null && exp >= maxExp;
    }

    /**
     * 升级宠物
     */
    public boolean levelUp() {
        if (!canLevelUp()) {
            return false;
        }

        this.exp = 0;
        this.level++;
        this.maxExp = level * level * 100;

        // 升级时属性提升
        int hpIncrease = 15;
        int mpIncrease = 8;

        this.maxHp += hpIncrease;
        this.maxMp += mpIncrease;
        this.hp = this.maxHp; // 升级时恢复满血
        this.mp = this.maxMp; // 升级时恢复满蓝

        return true;
    }

    /**
     * 喂食宠物
     */
    public void feed() {
        this.hunger = Math.max(0, this.hunger - 30);
        this.loyalty = Math.min(100, this.loyalty + 5);
    }

    /**
     * 休息恢复
     */
    public void rest() {
        this.fatigue = Math.max(0, this.fatigue - 40);
    }

    /**
     * 获取宠物完整名称
     */
    public String getFullName() {
        return nickname + " (Lv." + level + ")";
    }
}
