package com.honghuang.game.repository;

import com.honghuang.game.entity.Mail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 邮件数据访问层
 */
@Repository
public interface MailRepository extends JpaRepository<Mail, Integer> {

    /**
     * 根据接收者ID查找邮件（收件箱）
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findInboxMails(@Param("receiverId") Integer receiverId);

    /**
     * 根据发送者ID查找邮件（发件箱）
     */
    @Query("SELECT m FROM Mail m WHERE m.senderId = :senderId AND m.status IN (1, 3) ORDER BY m.sendTime DESC")
    List<Mail> findSentMails(@Param("senderId") Integer senderId);

    /**
     * 查找未读邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND m.isRead = 0 AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findUnreadMails(@Param("receiverId") Integer receiverId);

    /**
     * 查找有附件的邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND m.hasAttachment = 1 AND m.attachmentClaimed = 0 AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findMailsWithUnclaimed(@Param("receiverId") Integer receiverId);

    /**
     * 根据邮件类型查找邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND m.mailType = :mailType AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findMailsByType(@Param("receiverId") Integer receiverId, @Param("mailType") Integer mailType);

    /**
     * 查找系统邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND m.mailType = 2 AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findSystemMails(@Param("receiverId") Integer receiverId);

    /**
     * 查找过期邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.expireTime <= CURRENT_TIMESTAMP AND m.status = 1")
    List<Mail> findExpiredMails();

    /**
     * 查找指定时间范围内的邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND m.sendTime BETWEEN :startTime AND :endTime AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findMailsByTimeRange(@Param("receiverId") Integer receiverId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据标题搜索邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND m.title LIKE %:keyword% AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findMailsByTitle(@Param("receiverId") Integer receiverId, @Param("keyword") String keyword);

    /**
     * 根据发送者搜索邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND m.senderName LIKE %:senderName% AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findMailsBySender(@Param("receiverId") Integer receiverId, @Param("senderName") String senderName);

    /**
     * 统计未读邮件数量
     */
    @Query("SELECT COUNT(m) FROM Mail m WHERE m.receiverId = :receiverId AND m.isRead = 0 AND m.status IN (1, 2)")
    long countUnreadMails(@Param("receiverId") Integer receiverId);

    /**
     * 统计有附件未领取的邮件数量
     */
    @Query("SELECT COUNT(m) FROM Mail m WHERE m.receiverId = :receiverId AND m.hasAttachment = 1 AND m.attachmentClaimed = 0 AND m.status IN (1, 2)")
    long countUnclaimedAttachmentMails(@Param("receiverId") Integer receiverId);

    /**
     * 统计指定类型邮件数量
     */
    @Query("SELECT COUNT(m) FROM Mail m WHERE m.receiverId = :receiverId AND m.mailType = :mailType AND m.status IN (1, 2)")
    long countMailsByType(@Param("receiverId") Integer receiverId, @Param("mailType") Integer mailType);

    /**
     * 标记邮件为已读
     */
    @Modifying
    @Query("UPDATE Mail m SET m.isRead = 1, m.readTime = CURRENT_TIMESTAMP WHERE m.id = :mailId")
    int markAsRead(@Param("mailId") Integer mailId);

    /**
     * 批量标记邮件为已读
     */
    @Modifying
    @Query("UPDATE Mail m SET m.isRead = 1, m.readTime = CURRENT_TIMESTAMP WHERE m.receiverId = :receiverId AND m.isRead = 0 AND m.status IN (1, 2)")
    int markAllAsRead(@Param("receiverId") Integer receiverId);

    /**
     * 标记附件为已领取
     */
    @Modifying
    @Query("UPDATE Mail m SET m.attachmentClaimed = 1 WHERE m.id = :mailId")
    int markAttachmentClaimed(@Param("mailId") Integer mailId);

    /**
     * 更新邮件状态
     */
    @Modifying
    @Query("UPDATE Mail m SET m.status = :status WHERE m.id = :mailId")
    int updateStatus(@Param("mailId") Integer mailId, @Param("status") Integer status);

    /**
     * 发送者删除邮件
     */
    @Modifying
    @Query("UPDATE Mail m SET m.status = CASE WHEN m.status = 3 THEN 4 ELSE 2 END WHERE m.id = :mailId AND m.senderId = :senderId")
    int deleteBySender(@Param("mailId") Integer mailId, @Param("senderId") Integer senderId);

    /**
     * 接收者删除邮件
     */
    @Modifying
    @Query("UPDATE Mail m SET m.status = CASE WHEN m.status = 2 THEN 4 ELSE 3 END WHERE m.id = :mailId AND m.receiverId = :receiverId")
    int deleteByReceiver(@Param("mailId") Integer mailId, @Param("receiverId") Integer receiverId);

    /**
     * 系统删除过期邮件
     */
    @Modifying
    @Query("UPDATE Mail m SET m.status = 4 WHERE m.expireTime <= CURRENT_TIMESTAMP AND m.status = 1")
    int deleteExpiredMails();

    /**
     * 物理删除已标记删除的邮件
     */
    @Modifying
    @Query("DELETE FROM Mail m WHERE m.status = 4")
    int physicalDeleteMarkedMails();

    /**
     * 删除角色的所有邮件
     */
    @Modifying
    @Query("UPDATE Mail m SET m.status = 4 WHERE m.senderId = :roleId OR m.receiverId = :roleId")
    int deleteByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找最近的邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findRecentMails(@Param("receiverId") Integer receiverId);

    /**
     * 查找今日邮件
     */
    @Query("SELECT m FROM Mail m WHERE m.receiverId = :receiverId AND DATE(m.sendTime) = CURRENT_DATE AND m.status IN (1, 2) ORDER BY m.sendTime DESC")
    List<Mail> findTodayMails(@Param("receiverId") Integer receiverId);
}
