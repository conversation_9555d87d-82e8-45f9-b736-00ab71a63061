package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 玩家位置实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "player_position")
public class PlayerPosition {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 角色ID
     */
    @Column(name = "role_id", unique = true)
    private Integer roleId;

    /**
     * 当前场景ID
     */
    @Column(name = "scene_id")
    private Integer sceneId;

    /**
     * 当前X坐标
     */
    @Column(name = "position_x")
    private Integer positionX;

    /**
     * 当前Y坐标
     */
    @Column(name = "position_y")
    private Integer positionY;

    /**
     * 朝向 0北 1东 2南 3西
     */
    @Column(name = "direction")
    private Integer direction = 2;

    /**
     * 上次场景ID（用于回城等功能）
     */
    @Column(name = "last_scene_id")
    private Integer lastSceneId;

    /**
     * 上次X坐标
     */
    @Column(name = "last_position_x")
    private Integer lastPositionX;

    /**
     * 上次Y坐标
     */
    @Column(name = "last_position_y")
    private Integer lastPositionY;

    /**
     * 是否在线 0离线 1在线
     */
    @Column(name = "online")
    private Integer online = 0;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 角色信息（关联查询）
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", insertable = false, updatable = false)
    private PlayerRole role;

    /**
     * 当前场景信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "scene_id", insertable = false, updatable = false)
    private Scene scene;

    /**
     * 上次场景信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "last_scene_id", insertable = false, updatable = false)
    private Scene lastScene;

    // 业务方法

    /**
     * 获取朝向名称
     */
    public String getDirectionName() {
        switch (direction) {
            case 0: return "北";
            case 1: return "东";
            case 2: return "南";
            case 3: return "西";
            default: return "未知";
        }
    }

    /**
     * 是否在线
     */
    public boolean isOnline() {
        return online == 1;
    }

    /**
     * 设置在线状态
     */
    public void setOnlineStatus(boolean isOnline) {
        this.online = isOnline ? 1 : 0;
    }

    /**
     * 移动到新位置
     */
    public void moveTo(Integer newX, Integer newY) {
        this.positionX = newX;
        this.positionY = newY;
    }

    /**
     * 移动到新场景
     */
    public void moveToScene(Integer newSceneId, Integer newX, Integer newY) {
        // 保存当前位置为上次位置
        this.lastSceneId = this.sceneId;
        this.lastPositionX = this.positionX;
        this.lastPositionY = this.positionY;
        
        // 设置新位置
        this.sceneId = newSceneId;
        this.positionX = newX;
        this.positionY = newY;
    }

    /**
     * 回到上次位置
     */
    public boolean returnToLastPosition() {
        if (lastSceneId == null) {
            return false;
        }
        
        moveToScene(lastSceneId, lastPositionX, lastPositionY);
        return true;
    }

    /**
     * 设置朝向
     */
    public void setDirection(Integer newDirection) {
        if (newDirection >= 0 && newDirection <= 3) {
            this.direction = newDirection;
        }
    }

    /**
     * 转向指定方向
     */
    public void turnTo(Integer targetX, Integer targetY) {
        if (targetX > positionX) {
            direction = 1; // 东
        } else if (targetX < positionX) {
            direction = 3; // 西
        } else if (targetY > positionY) {
            direction = 2; // 南
        } else if (targetY < positionY) {
            direction = 0; // 北
        }
    }

    /**
     * 计算与目标位置的距离
     */
    public double getDistanceTo(Integer targetX, Integer targetY) {
        return Math.sqrt(Math.pow(targetX - positionX, 2) + Math.pow(targetY - positionY, 2));
    }

    /**
     * 计算与目标位置的曼哈顿距离
     */
    public Integer getManhattanDistanceTo(Integer targetX, Integer targetY) {
        return Math.abs(targetX - positionX) + Math.abs(targetY - positionY);
    }

    /**
     * 检查是否在指定范围内
     */
    public boolean isInRange(Integer targetX, Integer targetY, Integer range) {
        return getManhattanDistanceTo(targetX, targetY) <= range;
    }

    /**
     * 检查是否在场景边界内
     */
    public boolean isInSceneBounds(Scene scene) {
        if (scene == null) return false;
        return positionX >= 0 && positionX <= scene.getMapWidth() && 
               positionY >= 0 && positionY <= scene.getMapHeight();
    }

    /**
     * 获取当前位置描述
     */
    public String getCurrentPositionDescription() {
        StringBuilder sb = new StringBuilder();
        if (scene != null) {
            sb.append(scene.getName()).append(" ");
        }
        sb.append("(").append(positionX).append(", ").append(positionY).append(")");
        sb.append(" 朝向").append(getDirectionName());
        return sb.toString();
    }

    /**
     * 获取上次位置描述
     */
    public String getLastPositionDescription() {
        if (lastSceneId == null) {
            return "无记录";
        }
        
        StringBuilder sb = new StringBuilder();
        if (lastScene != null) {
            sb.append(lastScene.getName()).append(" ");
        }
        sb.append("(").append(lastPositionX).append(", ").append(lastPositionY).append(")");
        return sb.toString();
    }

    /**
     * 获取移动距离
     */
    public double getMovedDistance(Integer newX, Integer newY) {
        return getDistanceTo(newX, newY);
    }

    /**
     * 检查移动是否有效
     */
    public boolean isValidMove(Integer newX, Integer newY, Scene targetScene) {
        if (targetScene == null) return false;
        
        // 检查目标位置是否在场景范围内
        if (!targetScene.isValidPosition(newX, newY)) {
            return false;
        }
        
        // 检查移动距离是否合理（防止瞬移）
        double distance = getDistanceTo(newX, newY);
        return distance <= 100; // 最大移动距离限制
    }

    /**
     * 获取周围位置
     */
    public int[][] getSurroundingPositions(Integer range) {
        int size = (range * 2 + 1) * (range * 2 + 1);
        int[][] positions = new int[size][2];
        int index = 0;
        
        for (int dx = -range; dx <= range; dx++) {
            for (int dy = -range; dy <= range; dy++) {
                positions[index][0] = positionX + dx;
                positions[index][1] = positionY + dy;
                index++;
            }
        }
        
        return positions;
    }

    /**
     * 获取前方位置
     */
    public int[] getFrontPosition() {
        int[] front = new int[2];
        front[0] = positionX;
        front[1] = positionY;
        
        switch (direction) {
            case 0: front[1]--; break; // 北
            case 1: front[0]++; break; // 东
            case 2: front[1]++; break; // 南
            case 3: front[0]--; break; // 西
        }
        
        return front;
    }

    /**
     * 随机移动
     */
    public void randomMove(Scene scene, Integer maxDistance) {
        if (scene == null) return;
        
        int attempts = 10; // 最多尝试10次
        while (attempts > 0) {
            int deltaX = (int) (Math.random() * maxDistance * 2) - maxDistance;
            int deltaY = (int) (Math.random() * maxDistance * 2) - maxDistance;
            
            int newX = positionX + deltaX;
            int newY = positionY + deltaY;
            
            if (scene.isValidPosition(newX, newY)) {
                moveTo(newX, newY);
                break;
            }
            
            attempts--;
        }
    }
}
