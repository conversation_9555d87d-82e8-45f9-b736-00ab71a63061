package com.honghuang.game.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 游戏配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "game.config")
public class GameConfig {

    /**
     * 游戏状态 1:开服 2:维护
     */
    private Integer state = 1;

    /**
     * 玩家人数上限
     */
    private Integer userLimit = 1800;

    /**
     * 公聊等级限制
     */
    private Integer chatGradeLimit = 5;

    /**
     * 玩家等级上限
     */
    private Integer gradeLimit = 100;

    /**
     * 经验倍数
     */
    private Integer expRate = 1;

    /**
     * 掉宝倍数
     */
    private Integer dropRate = 1;

    /**
     * 渠道ID
     */
    private Integer channelId = 8;

    /**
     * 分区ID
     */
    private Integer areaId = 1;

    /**
     * 游戏名称
     */
    private String name = "洪荒online";

    /**
     * 服务器URL
     */
    private String serverUrl = "http://localhost:8080";

    /**
     * 论坛地址
     */
    private String forumUrl = "http://localhost:8080/forum";

    // Getters and Setters
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getUserLimit() {
        return userLimit;
    }

    public void setUserLimit(Integer userLimit) {
        this.userLimit = userLimit;
    }

    public Integer getChatGradeLimit() {
        return chatGradeLimit;
    }

    public void setChatGradeLimit(Integer chatGradeLimit) {
        this.chatGradeLimit = chatGradeLimit;
    }

    public Integer getGradeLimit() {
        return gradeLimit;
    }

    public void setGradeLimit(Integer gradeLimit) {
        this.gradeLimit = gradeLimit;
    }

    public Integer getExpRate() {
        return expRate;
    }

    public void setExpRate(Integer expRate) {
        this.expRate = expRate;
    }

    public Integer getDropRate() {
        return dropRate;
    }

    public void setDropRate(Integer dropRate) {
        this.dropRate = dropRate;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getServerUrl() {
        return serverUrl;
    }

    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public String getForumUrl() {
        return forumUrl;
    }

    public void setForumUrl(String forumUrl) {
        this.forumUrl = forumUrl;
    }
}
