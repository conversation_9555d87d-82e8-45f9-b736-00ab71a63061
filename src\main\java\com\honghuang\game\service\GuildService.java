package com.honghuang.game.service;

import com.honghuang.game.entity.Guild;
import com.honghuang.game.entity.GuildApplication;
import com.honghuang.game.entity.GuildMember;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.GuildApplicationRepository;
import com.honghuang.game.repository.GuildMemberRepository;
import com.honghuang.game.repository.GuildRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 帮派服务类
 */
@Service
@Transactional
public class GuildService {

    @Autowired
    private GuildRepository guildRepository;

    @Autowired
    private GuildMemberRepository guildMemberRepository;

    @Autowired
    private GuildApplicationRepository guildApplicationRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 创建帮派
     */
    public Guild createGuild(Integer roleId, String guildName, String description) {
        System.out.println("创建帮派: roleId=" + roleId + ", guildName=" + guildName);

        PlayerRole role = playerRoleService.findById(roleId);

        // 检查角色是否已加入帮派
        if (guildMemberRepository.findByRoleId(roleId).isPresent()) {
            throw new BusinessException(400, "已加入帮派，无法创建新帮派");
        }

        // 检查帮派名是否已存在
        if (guildRepository.existsByNameAndStatus(guildName, 1)) {
            throw new BusinessException(400, "帮派名称已存在");
        }

        // 检查创建条件（等级、金钱等）
        if (role.getLevel() < 10) {
            throw new BusinessException(400, "等级不足，需要10级以上才能创建帮派");
        }

        Long createCost = 10000L; // 创建帮派费用
        if (!role.spendCopper(createCost)) {
            throw new BusinessException(400, "金钱不足，创建帮派需要" + createCost + "铜钱");
        }

        // 创建帮派
        Guild guild = new Guild();
        guild.setName(guildName);
        guild.setDescription(description);
        guild.setLeaderId(roleId);
        guild.setLeaderName(role.getName());
        guild.setLevel(1);
        guild.setExperience(0L);
        guild.setFunds(0L);
        guild.setMemberCount(1);
        guild.setMaxMembers(20);
        guild.setStatus(1);
        guild.setMinLevel(1);
        guild.setJoinType(2); // 默认需要审核
        guild.setLastActiveTime(LocalDateTime.now());

        guild = guildRepository.save(guild);

        // 创建帮主成员记录
        GuildMember leader = new GuildMember();
        leader.setGuildId(guild.getId());
        leader.setRoleId(roleId);
        leader.setRoleName(role.getName());
        leader.setRoleLevel(role.getLevel());
        leader.setPosition(1); // 帮主
        leader.setContribution(0L);
        leader.setLastOnlineTime(LocalDateTime.now());

        guildMemberRepository.save(leader);

        System.out.println("帮派创建成功: guildId=" + guild.getId() + ", name=" + guildName);
        return guild;
    }

    /**
     * 申请加入帮派
     */
    public GuildApplication applyToJoinGuild(Integer roleId, Integer guildId, String message) {
        System.out.println("申请加入帮派: roleId=" + roleId + ", guildId=" + guildId);

        PlayerRole role = playerRoleService.findById(roleId);
        Guild guild = guildRepository.findById(guildId)
                .orElseThrow(() -> new BusinessException(404, "帮派不存在"));

        // 检查角色是否已加入帮派
        if (guildMemberRepository.findByRoleId(roleId).isPresent()) {
            throw new BusinessException(400, "已加入其他帮派");
        }

        // 检查是否已有待处理的申请
        if (guildApplicationRepository.findByGuildIdAndApplicantIdAndStatus(guildId, roleId, 0).isPresent()) {
            throw new BusinessException(400, "已有待处理的申请");
        }

        // 检查帮派是否可以加入
        if (!guild.canJoin()) {
            throw new BusinessException(400, "帮派不接受新成员");
        }

        // 检查等级要求
        if (role.getLevel() < guild.getMinLevel()) {
            throw new BusinessException(400, "等级不足，需要" + guild.getMinLevel() + "级以上");
        }

        // 创建申请
        GuildApplication application = new GuildApplication();
        application.setGuildId(guildId);
        application.setApplicantId(roleId);
        application.setApplicantName(role.getName());
        application.setApplicantLevel(role.getLevel());
        application.setApplicationType(1); // 主动申请
        application.setStatus(0); // 待处理
        application.setMessage(message);

        application = guildApplicationRepository.save(application);

        // 如果是自由加入，直接通过
        if (guild.getJoinType() == 1) {
            approveApplication(guildId, application.getId(), roleId, "自动通过");
        }

        System.out.println("申请提交成功: applicationId=" + application.getId());
        return application;
    }

    /**
     * 审核申请
     */
    public boolean approveApplication(Integer guildId, Integer applicationId, Integer processorId, String note) {
        System.out.println("审核申请: guildId=" + guildId + ", applicationId=" + applicationId + ", approved=true");

        GuildApplication application = guildApplicationRepository.findById(applicationId)
                .orElseThrow(() -> new BusinessException(404, "申请不存在"));

        if (!application.isPending()) {
            throw new BusinessException(400, "申请已处理");
        }

        Guild guild = guildRepository.findById(guildId)
                .orElseThrow(() -> new BusinessException(404, "帮派不存在"));

        // 检查处理权限
        GuildMember processor = guildMemberRepository.findByGuildIdAndRoleId(guildId, processorId)
                .orElseThrow(() -> new BusinessException(403, "无权限处理申请"));

        if (!processor.canApproveApplication()) {
            throw new BusinessException(403, "职位权限不足");
        }

        // 检查帮派是否还有空位
        if (guild.isFull()) {
            throw new BusinessException(400, "帮派已满员");
        }

        // 通过申请
        PlayerRole applicant = playerRoleService.findById(application.getApplicantId());
        application.approve(processorId, processor.getRoleName(), note);
        guildApplicationRepository.save(application);

        // 添加成员
        GuildMember member = new GuildMember();
        member.setGuildId(guildId);
        member.setRoleId(application.getApplicantId());
        member.setRoleName(application.getApplicantName());
        member.setRoleLevel(application.getApplicantLevel());
        member.setPosition(6); // 普通成员
        member.setContribution(0L);
        member.setLastOnlineTime(LocalDateTime.now());

        guildMemberRepository.save(member);

        // 更新帮派成员数
        guild.addMember();
        guild.updateActiveTime();
        guildRepository.save(guild);

        System.out.println("申请审核通过: applicationId=" + applicationId);
        return true;
    }

    /**
     * 拒绝申请
     */
    public boolean rejectApplication(Integer guildId, Integer applicationId, Integer processorId, String note) {
        System.out.println("拒绝申请: guildId=" + guildId + ", applicationId=" + applicationId);

        GuildApplication application = guildApplicationRepository.findById(applicationId)
                .orElseThrow(() -> new BusinessException(404, "申请不存在"));

        if (!application.isPending()) {
            throw new BusinessException(400, "申请已处理");
        }

        // 检查处理权限
        GuildMember processor = guildMemberRepository.findByGuildIdAndRoleId(guildId, processorId)
                .orElseThrow(() -> new BusinessException(403, "无权限处理申请"));

        if (!processor.canApproveApplication()) {
            throw new BusinessException(403, "职位权限不足");
        }

        // 拒绝申请
        application.reject(processorId, processor.getRoleName(), note);
        guildApplicationRepository.save(application);

        System.out.println("申请已拒绝: applicationId=" + applicationId);
        return true;
    }

    /**
     * 踢出成员
     */
    public boolean kickMember(Integer guildId, Integer targetRoleId, Integer operatorId, String reason) {
        System.out.println("踢出成员: guildId=" + guildId + ", targetRoleId=" + targetRoleId + ", operatorId=" + operatorId);

        // 检查操作权限
        GuildMember operator = guildMemberRepository.findByGuildIdAndRoleId(guildId, operatorId)
                .orElseThrow(() -> new BusinessException(403, "无权限操作"));

        GuildMember target = guildMemberRepository.findByGuildIdAndRoleId(guildId, targetRoleId)
                .orElseThrow(() -> new BusinessException(404, "目标成员不存在"));

        if (!operator.canKickMember()) {
            throw new BusinessException(403, "职位权限不足");
        }

        // 不能踢出职位更高的成员
        if (target.getPosition() <= operator.getPosition()) {
            throw new BusinessException(403, "无法踢出职位更高的成员");
        }

        // 删除成员
        guildMemberRepository.delete(target);

        // 更新帮派成员数
        Guild guild = guildRepository.findById(guildId).orElse(null);
        if (guild != null) {
            guild.removeMember();
            guildRepository.save(guild);
        }

        System.out.println("成员踢出成功: targetRoleId=" + targetRoleId + ", reason=" + reason);
        return true;
    }

    /**
     * 主动退出帮派
     */
    public boolean leaveGuild(Integer roleId) {
        System.out.println("退出帮派: roleId=" + roleId);

        GuildMember member = guildMemberRepository.findByRoleId(roleId)
                .orElseThrow(() -> new BusinessException(404, "未加入任何帮派"));

        if (member.isLeader()) {
            throw new BusinessException(400, "帮主无法直接退出，请先转让帮主或解散帮派");
        }

        Guild guild = guildRepository.findById(member.getGuildId()).orElse(null);

        // 删除成员
        guildMemberRepository.delete(member);

        // 更新帮派成员数
        if (guild != null) {
            guild.removeMember();
            guildRepository.save(guild);
        }

        System.out.println("退出帮派成功: roleId=" + roleId);
        return true;
    }

    /**
     * 获取帮派列表
     */
    @Transactional(readOnly = true)
    public List<Guild> getGuildList() {
        return guildRepository.findByStatusOrderByLevelDescExperienceDesc(1);
    }

    /**
     * 获取帮派详情
     */
    @Transactional(readOnly = true)
    public Guild getGuildDetail(Integer guildId) {
        return guildRepository.findById(guildId)
                .orElseThrow(() -> new BusinessException(404, "帮派不存在"));
    }

    /**
     * 获取帮派成员列表
     */
    @Transactional(readOnly = true)
    public List<GuildMember> getGuildMembers(Integer guildId) {
        return guildMemberRepository.findByGuildIdOrderByPositionAscContributionDesc(guildId);
    }

    /**
     * 获取角色的帮派信息
     */
    @Transactional(readOnly = true)
    public Optional<GuildMember> getPlayerGuild(Integer roleId) {
        return guildMemberRepository.findByRoleId(roleId);
    }
}
