server:
  port: 8080
  servlet:
    context-path: /honghuang-game
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: honghuang-game
  
  # H2内存数据库配置（用于演示）
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:jygame;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password: 
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HonghuangGameHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # 使用create-drop自动创建表结构
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true

  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console

  # Redis配置（可选，如果没有Redis服务可以禁用）
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 6000ms
    database: 0
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # 缓存配置
  cache:
    type: simple  # 使用简单缓存而不是Redis

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# 日志配置
logging:
  level:
    com.honghuang.game: debug
    org.hibernate.SQL: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 游戏配置
game:
  config:
    # 游戏状态 1:开服 2:维护
    state: 1
    # 玩家人数上限
    user-limit: 1800
    # 公聊等级限制
    chat-grade-limit: 5
    # 玩家等级上限
    grade-limit: 100
    # 经验倍数
    exp-rate: 1
    # 掉宝倍数
    drop-rate: 1
    # 渠道ID
    channel-id: 8
    # 分区ID
    area-id: 1
    # 游戏名称
    name: 洪荒online
    # 服务器URL
    server-url: http://localhost:8080
    # 论坛地址
    forum-url: http://localhost:8080/forum
