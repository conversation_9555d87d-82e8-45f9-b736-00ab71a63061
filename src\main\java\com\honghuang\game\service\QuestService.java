package com.honghuang.game.service;

import com.honghuang.game.entity.PlayerQuest;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.Quest;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.PlayerQuestRepository;
import com.honghuang.game.repository.QuestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 任务服务类
 */
@Service
@Transactional
public class QuestService {

    @Autowired
    private QuestRepository questRepository;

    @Autowired
    private PlayerQuestRepository playerQuestRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 获取所有可用任务
     */
    @Transactional(readOnly = true)
    public List<Quest> getAllQuests() {
        return questRepository.findByActiveOrderByRequiredLevelAscTypeAsc(1);
    }

    /**
     * 根据角色获取可接取的任务
     */
    @Transactional(readOnly = true)
    public List<Quest> getAvailableQuests(Integer roleId) {
        PlayerRole role = playerRoleService.findById(roleId);
        List<Quest> suitableQuests = questRepository.findSuitableQuests(role.getLevel(), role.getRace(), role.getSex());
        
        // 过滤掉已接取的任务（非可重复任务）
        return suitableQuests.stream()
                .filter(quest -> {
                    Optional<PlayerQuest> existing = playerQuestRepository.findByRoleIdAndQuestId(roleId, quest.getId());
                    if (!existing.isPresent()) {
                        return true; // 未接取过
                    }
                    
                    PlayerQuest playerQuest = existing.get();
                    if (quest.getRepeatable() == 1 && playerQuest.canReaccept()) {
                        return true; // 可重复且可重新接取
                    }
                    
                    return false; // 已接取且不可重复
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取角色的任务列表
     */
    @Transactional(readOnly = true)
    public List<PlayerQuest> getPlayerQuests(Integer roleId) {
        return playerQuestRepository.findByRoleIdOrderByAcceptTimeDesc(roleId);
    }

    /**
     * 获取角色进行中的任务
     */
    @Transactional(readOnly = true)
    public List<PlayerQuest> getInProgressQuests(Integer roleId) {
        return playerQuestRepository.findInProgressQuestsByRoleId(roleId);
    }

    /**
     * 获取角色已完成的任务
     */
    @Transactional(readOnly = true)
    public List<PlayerQuest> getCompletedQuests(Integer roleId) {
        return playerQuestRepository.findCompletedQuestsByRoleId(roleId);
    }

    /**
     * 接取任务
     */
    public PlayerQuest acceptQuest(Integer roleId, Integer questId) {
        System.out.println("接取任务: roleId=" + roleId + ", questId=" + questId);

        PlayerRole role = playerRoleService.findById(roleId);
        Quest quest = questRepository.findById(questId)
                .orElseThrow(() -> new BusinessException(404, "任务不存在"));

        // 检查是否已接取
        Optional<PlayerQuest> existing = playerQuestRepository.findByRoleIdAndQuestId(roleId, questId);
        if (existing.isPresent()) {
            PlayerQuest playerQuest = existing.get();
            if (quest.getRepeatable() == 1 && playerQuest.canReaccept()) {
                // 重新接取可重复任务
                playerQuest.reset();
                playerQuest = playerQuestRepository.save(playerQuest);
                System.out.println("重新接取任务成功: roleId=" + roleId + ", questId=" + questId);
                return playerQuest;
            } else {
                throw new BusinessException(400, "已经接取过该任务");
            }
        }

        // 检查接取条件
        if (!quest.canAccept(role)) {
            throw new BusinessException(400, "不满足接取条件");
        }

        // 检查前置任务
        if (quest.getPrerequisiteQuestId() != null) {
            Optional<PlayerQuest> prerequisite = playerQuestRepository.findByRoleIdAndQuestId(
                    roleId, quest.getPrerequisiteQuestId());
            if (!prerequisite.isPresent() || !prerequisite.get().isCompleted()) {
                throw new BusinessException(400, "前置任务未完成");
            }
        }

        // 创建玩家任务
        PlayerQuest playerQuest = new PlayerQuest();
        playerQuest.setRoleId(roleId);
        playerQuest.setQuestId(questId);
        playerQuest.setStatus(1); // 进行中
        playerQuest.setProgress(0);

        playerQuest = playerQuestRepository.save(playerQuest);
        System.out.println("任务接取成功: roleId=" + roleId + ", questId=" + questId + ", questName=" + quest.getName());

        return playerQuest;
    }

    /**
     * 完成任务
     */
    public boolean completeQuest(Integer roleId, Integer questId) {
        System.out.println("完成任务: roleId=" + roleId + ", questId=" + questId);

        PlayerQuest playerQuest = playerQuestRepository.findByRoleIdAndQuestId(roleId, questId)
                .orElseThrow(() -> new BusinessException(404, "未接取该任务"));

        if (!playerQuest.canComplete()) {
            throw new BusinessException(400, "任务未达到完成条件");
        }

        // 完成任务
        playerQuest.complete();
        playerQuestRepository.save(playerQuest);

        // 发放奖励
        giveQuestRewards(roleId, playerQuest.getQuest());

        System.out.println("任务完成成功: roleId=" + roleId + ", questId=" + questId);
        return true;
    }

    /**
     * 放弃任务
     */
    public boolean abandonQuest(Integer roleId, Integer questId) {
        System.out.println("放弃任务: roleId=" + roleId + ", questId=" + questId);

        PlayerQuest playerQuest = playerQuestRepository.findByRoleIdAndQuestId(roleId, questId)
                .orElseThrow(() -> new BusinessException(404, "未接取该任务"));

        if (!playerQuest.isInProgress()) {
            throw new BusinessException(400, "只能放弃进行中的任务");
        }

        // 放弃任务
        playerQuest.abandon();
        playerQuestRepository.save(playerQuest);

        System.out.println("任务放弃成功: roleId=" + roleId + ", questId=" + questId);
        return true;
    }

    /**
     * 更新任务进度
     */
    public void updateQuestProgress(Integer roleId, Integer objectiveType, Integer objectiveId, Integer amount) {
        List<PlayerQuest> matchingQuests = playerQuestRepository.findMatchingQuests(roleId, objectiveType, objectiveId);
        
        for (PlayerQuest playerQuest : matchingQuests) {
            if (playerQuest.isInProgress() && !playerQuest.isObjectiveReached()) {
                playerQuest.addProgress(amount);
                playerQuestRepository.save(playerQuest);
                
                System.out.println("任务进度更新: roleId=" + roleId + ", questId=" + playerQuest.getQuestId() + 
                                 ", progress=" + playerQuest.getProgress() + "/" + playerQuest.getQuest().getObjectiveCount());
                
                // 检查是否可以自动完成（对话任务等）
                if (playerQuest.canComplete() && shouldAutoComplete(playerQuest.getQuest())) {
                    completeQuest(roleId, playerQuest.getQuestId());
                }
            }
        }
    }

    /**
     * 处理杀怪事件
     */
    public void handleMonsterKilled(Integer roleId, Integer monsterId) {
        updateQuestProgress(roleId, 1, monsterId, 1); // 目标类型1=杀怪
    }

    /**
     * 处理物品收集事件
     */
    public void handleItemCollected(Integer roleId, Integer itemId, Integer amount) {
        updateQuestProgress(roleId, 2, itemId, amount); // 目标类型2=收集物品
    }

    /**
     * 处理NPC对话事件
     */
    public void handleNpcTalk(Integer roleId, Integer npcId) {
        updateQuestProgress(roleId, 3, npcId, 1); // 目标类型3=对话NPC
    }

    /**
     * 处理场景探索事件
     */
    public void handleSceneExplored(Integer roleId, Integer sceneId) {
        updateQuestProgress(roleId, 4, sceneId, 1); // 目标类型4=到达地点
    }

    /**
     * 获取可完成的任务
     */
    @Transactional(readOnly = true)
    public List<PlayerQuest> getCompletableQuests(Integer roleId) {
        return playerQuestRepository.findCompletableQuestsByRoleId(roleId);
    }

    /**
     * 根据类型获取任务
     */
    @Transactional(readOnly = true)
    public List<PlayerQuest> getQuestsByType(Integer roleId, Integer type) {
        return playerQuestRepository.findByRoleIdAndQuestType(roleId, type);
    }

    /**
     * 发放任务奖励
     */
    private void giveQuestRewards(Integer roleId, Quest quest) {
        if (quest == null) return;

        PlayerRole role = playerRoleService.findById(roleId);

        // 发放经验奖励
        if (quest.getExpReward() > 0) {
            playerRoleService.addExperience(roleId, quest.getExpReward());
            System.out.println("任务奖励经验: roleId=" + roleId + ", exp=" + quest.getExpReward());
        }

        // 发放金钱奖励
        Long totalCopper = quest.getTotalCopperReward();
        if (totalCopper > 0) {
            role.addCopper(totalCopper);
            System.out.println("任务奖励金钱: roleId=" + roleId + ", copper=" + totalCopper);
        }

        // TODO: 发放物品奖励
        if (quest.hasItemReward()) {
            System.out.println("任务奖励物品: roleId=" + roleId + ", itemId=" + quest.getItemRewardId() + 
                             ", count=" + quest.getItemRewardCount());
            // 这里需要实现物品系统后才能发放物品奖励
        }
    }

    /**
     * 判断任务是否应该自动完成
     */
    private boolean shouldAutoComplete(Quest quest) {
        // 对话任务和探索任务可以自动完成
        return quest.isTalkQuest() || quest.isExploreQuest();
    }

    /**
     * 获取任务统计信息
     */
    @Transactional(readOnly = true)
    public java.util.Map<String, Object> getQuestStatistics(Integer roleId) {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        
        stats.put("totalQuests", playerQuestRepository.countByRoleId(roleId));
        stats.put("inProgressQuests", playerQuestRepository.countByRoleIdAndStatus(roleId, 1));
        stats.put("completedQuests", playerQuestRepository.countByRoleIdAndStatus(roleId, 2));
        stats.put("abandonedQuests", playerQuestRepository.countByRoleIdAndStatus(roleId, 3));
        
        return stats;
    }
}
