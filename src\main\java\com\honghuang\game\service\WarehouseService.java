package com.honghuang.game.service;

import com.honghuang.game.entity.Inventory;
import com.honghuang.game.entity.Item;
import com.honghuang.game.entity.Warehouse;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.InventoryRepository;
import com.honghuang.game.repository.ItemRepository;
import com.honghuang.game.repository.WarehouseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 仓库服务类
 */
@Service
@Transactional
public class WarehouseService {

    @Autowired
    private WarehouseRepository warehouseRepository;

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private ItemRepository itemRepository;

    private static final int SLOTS_PER_PAGE = 50; // 每页50格
    private static final int MAX_PAGES = 10; // 最多10页

    /**
     * 获取角色仓库物品列表
     */
    @Transactional(readOnly = true)
    public List<Warehouse> getWarehouse(Integer roleId) {
        return warehouseRepository.findByRoleIdOrderByPageNumberAscSlotIndexAsc(roleId);
    }

    /**
     * 获取指定页的仓库物品
     */
    @Transactional(readOnly = true)
    public List<Warehouse> getWarehousePage(Integer roleId, Integer pageNumber) {
        if (pageNumber < 1 || pageNumber > MAX_PAGES) {
            throw new BusinessException(400, "页数超出范围");
        }
        return warehouseRepository.findByRoleIdAndPageNumberOrderBySlotIndexAsc(roleId, pageNumber);
    }

    /**
     * 获取指定位置的仓库物品
     */
    @Transactional(readOnly = true)
    public Optional<Warehouse> getWarehouseSlot(Integer roleId, Integer pageNumber, Integer slotIndex) {
        return warehouseRepository.findByRoleIdAndPageNumberAndSlotIndex(roleId, pageNumber, slotIndex);
    }

    /**
     * 存入物品到仓库
     */
    public boolean depositItem(Integer roleId, Integer inventorySlotIndex, Integer quantity) {
        System.out.println("存入物品到仓库: roleId=" + roleId + ", slot=" + inventorySlotIndex + ", quantity=" + quantity);

        if (quantity <= 0) {
            throw new BusinessException(400, "存入数量必须大于0");
        }

        // 获取背包物品
        Inventory inventory = inventoryRepository.findByRoleIdAndSlotIndex(roleId, inventorySlotIndex)
                .orElseThrow(() -> new BusinessException(404, "背包槽位为空"));

        if (inventory.isEmpty() || inventory.getQuantity() < quantity) {
            throw new BusinessException(400, "背包物品数量不足");
        }

        Item item = inventory.getItem();
        if (item == null) {
            throw new BusinessException(400, "物品信息错误");
        }

        // 尝试堆叠到现有仓库物品
        if (item.isStackable()) {
            List<Warehouse> stackableItems = warehouseRepository.findStackableItems(roleId, item.getId());
            for (Warehouse warehouse : stackableItems) {
                int canAdd = item.getMaxStack() - warehouse.getQuantity();
                int toAdd = Math.min(canAdd, quantity);

                if (toAdd > 0) {
                    warehouse.addQuantity(toAdd);
                    warehouseRepository.save(warehouse);
                    
                    inventory.reduceQuantity(toAdd);
                    if (inventory.getQuantity() <= 0) {
                        inventory.clear();
                    }
                    inventoryRepository.save(inventory);
                    
                    quantity -= toAdd;
                    if (quantity <= 0) break;
                }
            }
        }

        // 如果还有剩余数量，创建新的仓库条目
        while (quantity > 0) {
            Optional<Warehouse> emptySlot = findEmptyWarehouseSlot(roleId);
            if (!emptySlot.isPresent()) {
                throw new BusinessException(400, "仓库已满");
            }

            int toAdd = item.isStackable() ? Math.min(quantity, item.getMaxStack()) : 1;

            Warehouse warehouse = emptySlot.get();
            warehouse.setItemId(item.getId());
            warehouse.setQuantity(toAdd);
            warehouse.setBound(inventory.getBound());
            warehouse.setStoreTime(LocalDateTime.now());
            warehouse.setLastAccessTime(LocalDateTime.now());

            warehouseRepository.save(warehouse);

            inventory.reduceQuantity(toAdd);
            if (inventory.getQuantity() <= 0) {
                inventory.clear();
            }
            inventoryRepository.save(inventory);

            quantity -= toAdd;
        }

        System.out.println("物品存入成功: roleId=" + roleId);
        return true;
    }

    /**
     * 从仓库取出物品
     */
    public boolean withdrawItem(Integer roleId, Integer pageNumber, Integer slotIndex, Integer quantity) {
        System.out.println("从仓库取出物品: roleId=" + roleId + ", page=" + pageNumber + ", slot=" + slotIndex + ", quantity=" + quantity);

        if (quantity <= 0) {
            throw new BusinessException(400, "取出数量必须大于0");
        }

        Warehouse warehouse = warehouseRepository.findByRoleIdAndPageNumberAndSlotIndex(roleId, pageNumber, slotIndex)
                .orElseThrow(() -> new BusinessException(404, "仓库槽位为空"));

        if (warehouse.isEmpty() || warehouse.getQuantity() < quantity) {
            throw new BusinessException(400, "仓库物品数量不足");
        }

        Item item = warehouse.getItem();
        if (item == null) {
            throw new BusinessException(400, "物品信息错误");
        }

        // 检查背包是否有空间
        if (!hasInventorySpace(roleId, item.getId(), quantity)) {
            throw new BusinessException(400, "背包空间不足");
        }

        // 添加到背包
        addToInventory(roleId, item.getId(), quantity, warehouse.getBound());

        // 从仓库移除
        warehouse.reduceQuantity(quantity);
        if (warehouse.getQuantity() <= 0) {
            warehouse.clear();
        }
        warehouseRepository.save(warehouse);

        System.out.println("物品取出成功: roleId=" + roleId);
        return true;
    }

    /**
     * 移动仓库物品位置
     */
    public boolean moveWarehouseItem(Integer roleId, Integer fromPage, Integer fromSlot, Integer toPage, Integer toSlot) {
        System.out.println("移动仓库物品: roleId=" + roleId + ", from=" + fromPage + ":" + fromSlot + ", to=" + toPage + ":" + toSlot);

        if (fromPage.equals(toPage) && fromSlot.equals(toSlot)) {
            return true; // 位置相同，无需移动
        }

        Optional<Warehouse> fromWarehouse = warehouseRepository.findByRoleIdAndPageNumberAndSlotIndex(roleId, fromPage, fromSlot);
        Optional<Warehouse> toWarehouse = warehouseRepository.findByRoleIdAndPageNumberAndSlotIndex(roleId, toPage, toSlot);

        if (!fromWarehouse.isPresent() || fromWarehouse.get().isEmpty()) {
            throw new BusinessException(400, "源槽位为空");
        }

        Warehouse from = fromWarehouse.get();

        if (!toWarehouse.isPresent() || toWarehouse.get().isEmpty()) {
            // 目标槽位为空，直接移动
            from.moveTo(toPage, toSlot);
            warehouseRepository.save(from);
        } else {
            // 目标槽位有物品，尝试合并或交换
            Warehouse to = toWarehouse.get();
            
            if (from.getItemId().equals(to.getItemId()) && from.canStack()) {
                if (to.canAddQuantity(from.getQuantity())) {
                    // 可以合并
                    to.addQuantity(from.getQuantity());
                    from.clear();
                    warehouseRepository.save(from);
                    warehouseRepository.save(to);
                } else {
                    throw new BusinessException(400, "无法合并，超出堆叠上限");
                }
            } else {
                // 交换位置
                Integer tempPage = from.getPageNumber();
                Integer tempSlot = from.getSlotIndex();
                from.moveTo(to.getPageNumber(), to.getSlotIndex());
                to.moveTo(tempPage, tempSlot);
                warehouseRepository.save(from);
                warehouseRepository.save(to);
            }
        }

        System.out.println("仓库物品移动成功: roleId=" + roleId);
        return true;
    }

    /**
     * 整理仓库
     */
    public boolean organizeWarehouse(Integer roleId) {
        System.out.println("整理仓库: roleId=" + roleId);

        // 先合并可堆叠的物品
        mergeStackableWarehouseItems(roleId);

        // 重新排列物品，按类型和品质排序
        List<Warehouse> items = warehouseRepository.findByRoleIdOrderByPageNumberAscSlotIndexAsc(roleId);
        
        // 清空所有物品位置
        for (Warehouse warehouse : items) {
            if (!warehouse.isEmpty()) {
                warehouse.clear();
                warehouseRepository.save(warehouse);
            }
        }

        // 按类型和品质重新排列
        items.sort((a, b) -> {
            if (a.isEmpty() && b.isEmpty()) return 0;
            if (a.isEmpty()) return 1;
            if (b.isEmpty()) return -1;
            
            Item itemA = a.getItem();
            Item itemB = b.getItem();
            
            int typeCompare = itemA.getType().compareTo(itemB.getType());
            if (typeCompare != 0) return typeCompare;
            
            return itemB.getQuality().compareTo(itemA.getQuality());
        });

        int currentPage = 1;
        int currentSlot = 0;

        for (Warehouse warehouse : items) {
            if (warehouse.isEmpty()) continue;

            if (currentSlot >= SLOTS_PER_PAGE) {
                currentPage++;
                currentSlot = 0;
            }

            warehouse.moveTo(currentPage, currentSlot);
            warehouseRepository.save(warehouse);
            currentSlot++;
        }

        System.out.println("仓库整理完成: roleId=" + roleId);
        return true;
    }

    /**
     * 获取仓库统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getWarehouseStatistics(Integer roleId) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        long usedSlots = warehouseRepository.countUsedSlots(roleId);
        long openedPages = warehouseRepository.countOpenedPages(roleId);
        long totalSlots = openedPages * SLOTS_PER_PAGE;
        
        stats.put("openedPages", openedPages);
        stats.put("maxPages", MAX_PAGES);
        stats.put("slotsPerPage", SLOTS_PER_PAGE);
        stats.put("totalSlots", totalSlots);
        stats.put("usedSlots", usedSlots);
        stats.put("emptySlots", totalSlots - usedSlots);
        stats.put("usageRate", totalSlots > 0 ? (double) usedSlots / totalSlots * 100 : 0);
        
        return stats;
    }

    /**
     * 查找空的仓库槽位
     */
    private Optional<Warehouse> findEmptyWarehouseSlot(Integer roleId) {
        // 先查找现有的空槽位
        Optional<Warehouse> emptySlot = warehouseRepository.findFirstEmptySlot(roleId);
        if (emptySlot.isPresent()) {
            return emptySlot;
        }

        // 如果没有空槽位，创建新的槽位
        for (int page = 1; page <= MAX_PAGES; page++) {
            for (int slot = 0; slot < SLOTS_PER_PAGE; slot++) {
                Optional<Warehouse> existing = warehouseRepository.findByRoleIdAndPageNumberAndSlotIndex(roleId, page, slot);
                if (!existing.isPresent()) {
                    // 创建新的空槽位
                    Warehouse newSlot = new Warehouse();
                    newSlot.setRoleId(roleId);
                    newSlot.setPageNumber(page);
                    newSlot.setSlotIndex(slot);
                    return Optional.of(warehouseRepository.save(newSlot));
                }
            }
        }

        return Optional.empty();
    }

    /**
     * 检查背包是否有足够空间
     */
    private boolean hasInventorySpace(Integer roleId, Integer itemId, Integer quantity) {
        Item item = itemRepository.findById(itemId).orElse(null);
        if (item == null) return false;

        if (item.isStackable()) {
            // 检查现有可堆叠物品的空间
            List<Inventory> stackableItems = inventoryRepository.findStackableItems(roleId, itemId);
            int availableSpace = 0;
            for (Inventory inventory : stackableItems) {
                availableSpace += item.getMaxStack() - inventory.getQuantity();
            }
            
            if (availableSpace >= quantity) {
                return true;
            }
            
            quantity -= availableSpace;
        }

        // 检查空槽位数量
        long emptySlots = inventoryRepository.countEmptySlots(roleId);
        int slotsNeeded = item.isStackable() ? 
            (int) Math.ceil((double) quantity / item.getMaxStack()) : quantity;
        
        return emptySlots >= slotsNeeded;
    }

    /**
     * 添加物品到背包
     */
    private void addToInventory(Integer roleId, Integer itemId, Integer quantity, Integer bound) {
        Item item = itemRepository.findById(itemId).orElse(null);
        if (item == null) return;

        int remainingQuantity = quantity;

        // 如果物品可堆叠，先尝试堆叠到现有物品上
        if (item.isStackable()) {
            List<Inventory> stackableItems = inventoryRepository.findStackableItems(roleId, itemId);
            for (Inventory inventory : stackableItems) {
                if (remainingQuantity <= 0) break;

                int canAdd = item.getMaxStack() - inventory.getQuantity();
                int toAdd = Math.min(canAdd, remainingQuantity);

                if (toAdd > 0) {
                    inventory.addQuantity(toAdd);
                    inventoryRepository.save(inventory);
                    remainingQuantity -= toAdd;
                }
            }
        }

        // 创建新的背包条目
        while (remainingQuantity > 0) {
            for (int i = 0; i < 100; i++) { // 背包100格
                Optional<Inventory> slot = inventoryRepository.findByRoleIdAndSlotIndex(roleId, i);
                if (!slot.isPresent() || slot.get().isEmpty()) {
                    int toAdd = item.isStackable() ? Math.min(remainingQuantity, item.getMaxStack()) : 1;

                    Inventory inventory = slot.orElse(new Inventory());
                    inventory.setRoleId(roleId);
                    inventory.setItemId(itemId);
                    inventory.setSlotIndex(i);
                    inventory.setQuantity(toAdd);
                    inventory.setBound(bound);
                    inventory.setObtainTime(LocalDateTime.now());

                    inventoryRepository.save(inventory);
                    remainingQuantity -= toAdd;
                    break;
                }
            }
            
            if (remainingQuantity > 0) {
                throw new BusinessException(400, "背包空间不足");
            }
        }
    }

    /**
     * 合并可堆叠的仓库物品
     */
    private void mergeStackableWarehouseItems(Integer roleId) {
        List<Warehouse> items = warehouseRepository.findByRoleIdOrderByPageNumberAscSlotIndexAsc(roleId);
        
        for (int i = 0; i < items.size(); i++) {
            Warehouse current = items.get(i);
            if (current.isEmpty() || !current.canStack()) continue;
            
            for (int j = i + 1; j < items.size(); j++) {
                Warehouse other = items.get(j);
                if (other.isEmpty() || !current.getItemId().equals(other.getItemId())) continue;
                
                if (current.canAddQuantity(other.getQuantity())) {
                    current.addQuantity(other.getQuantity());
                    other.clear();
                    warehouseRepository.save(current);
                    warehouseRepository.save(other);
                }
            }
        }
    }
}
