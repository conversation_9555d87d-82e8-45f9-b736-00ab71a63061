package com.honghuang.game.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.honghuang.game.entity.Ranking;
import com.honghuang.game.dto.RankingDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 排行榜Mapper
 */
@Mapper
public interface RankingMapper extends BaseMapper<Ranking> {
    
    /**
     * 获取等级排行榜
     */
    @Select("SELECT role_id, role_name, race, level, level as value " +
            "FROM ranking WHERE level > 0 " +
            "ORDER BY level DESC, experience_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getLevelRanking(@Param("limit") int limit);
    
    /**
     * 获取经验排行榜
     */
    @Select("SELECT role_id, role_name, race, level, experience as value " +
            "FROM ranking WHERE experience > 0 " +
            "ORDER BY experience DESC, experience_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getExperienceRanking(@Param("limit") int limit);
    
    /**
     * 获取财富排行榜
     */
    @Select("SELECT role_id, role_name, race, level, copper as value " +
            "FROM ranking WHERE copper > 0 " +
            "ORDER BY copper DESC, copper_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getCopperRanking(@Param("limit") int limit);
    
    /**
     * 获取攻击力排行榜
     */
    @Select("SELECT role_id, role_name, race, level, attack as value " +
            "FROM ranking WHERE attack > 0 " +
            "ORDER BY attack DESC, attack_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getAttackRanking(@Param("limit") int limit);
    
    /**
     * 获取防御力排行榜
     */
    @Select("SELECT role_id, role_name, race, level, defense as value " +
            "FROM ranking WHERE defense > 0 " +
            "ORDER BY defense DESC, defense_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getDefenseRanking(@Param("limit") int limit);
    
    /**
     * 获取敏捷排行榜
     */
    @Select("SELECT role_id, role_name, race, level, agility as value " +
            "FROM ranking WHERE agility > 0 " +
            "ORDER BY agility DESC, agility_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getAgilityRanking(@Param("limit") int limit);
    
    /**
     * 获取杀怪排行榜
     */
    @Select("SELECT role_id, role_name, race, level, kill_monster as value " +
            "FROM ranking WHERE kill_monster > 0 " +
            "ORDER BY kill_monster DESC, kill_monster_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getKillMonsterRanking(@Param("limit") int limit);
    
    /**
     * 获取PK排行榜
     */
    @Select("SELECT role_id, role_name, race, level, pk_win as value " +
            "FROM ranking WHERE pk_win > 0 " +
            "ORDER BY pk_win DESC, pk_win_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getPkWinRanking(@Param("limit") int limit);
    
    /**
     * 获取在线时长排行榜
     */
    @Select("SELECT role_id, role_name, race, level, online_time as value " +
            "FROM ranking WHERE online_time > 0 " +
            "ORDER BY online_time DESC, online_time_update ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getOnlineTimeRanking(@Param("limit") int limit);
    
    /**
     * 获取宠物等级排行榜
     */
    @Select("SELECT role_id, role_name, race, level, pet_level as value " +
            "FROM ranking WHERE pet_level > 0 " +
            "ORDER BY pet_level DESC, pet_level_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getPetLevelRanking(@Param("limit") int limit);
    
    /**
     * 获取宠物攻击力排行榜
     */
    @Select("SELECT role_id, role_name, race, level, pet_attack as value " +
            "FROM ranking WHERE pet_attack > 0 " +
            "ORDER BY pet_attack DESC, pet_attack_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getPetAttackRanking(@Param("limit") int limit);
    
    /**
     * 获取装备评分排行榜
     */
    @Select("SELECT role_id, role_name, race, level, equip_score as value " +
            "FROM ranking WHERE equip_score > 0 " +
            "ORDER BY equip_score DESC, equip_score_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getEquipScoreRanking(@Param("limit") int limit);
    
    /**
     * 获取综合实力排行榜
     */
    @Select("SELECT role_id, role_name, race, level, total_score as value " +
            "FROM ranking WHERE total_score > 0 " +
            "ORDER BY total_score DESC, total_score_time ASC " +
            "LIMIT #{limit}")
    List<RankingDTO> getTotalScoreRanking(@Param("limit") int limit);
    
    /**
     * 获取玩家在指定排行榜中的排名
     */
    @Select("SELECT COUNT(*) + 1 FROM ranking " +
            "WHERE ${field} > (SELECT ${field} FROM ranking WHERE role_id = #{roleId}) " +
            "AND ${field} > 0")
    Integer getPlayerRank(@Param("roleId") Integer roleId, @Param("field") String field);
    
    /**
     * 根据角色ID获取排行榜记录
     */
    @Select("SELECT * FROM ranking WHERE role_id = #{roleId}")
    Ranking getByRoleId(@Param("roleId") Integer roleId);
}
