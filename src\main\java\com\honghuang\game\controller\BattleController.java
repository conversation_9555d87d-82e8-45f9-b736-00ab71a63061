package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.dto.BattleResultDTO;
import com.honghuang.game.entity.BattleRecord;
import com.honghuang.game.entity.Monster;
import com.honghuang.game.service.BattleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 战斗控制器
 */
@RestController
@RequestMapping("/api/battle")
public class BattleController {
    
    @Autowired
    private BattleService battleService;
    
    /**
     * 开始战斗
     */
    @PostMapping("/start")
    public Result<BattleResultDTO> startBattle(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer userId = (Integer) session.getAttribute("userId");
            
            if (userId == null) {
                return Result.error(401, "用户未登录");
            }
            
            Integer monsterId = (Integer) request.get("monsterId");
            if (monsterId == null) {
                return Result.error(400, "怪物ID不能为空");
            }
            
            // 使用用户ID作为角色ID（简化处理）
            BattleResultDTO result = battleService.startBattle(userId, monsterId);
            
            return Result.success("战斗完成", result);
            
        } catch (Exception e) {
            return Result.error(500, "战斗失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取可攻击的怪物列表
     */
    @GetMapping("/monsters")
    public Result<List<Monster>> getAttackableMonsters() {
        try {
            List<Monster> monsters = battleService.getAttackableMonsters();
            return Result.success("获取怪物列表成功", monsters);
        } catch (Exception e) {
            return Result.error(500, "获取怪物列表失败: " + e.getMessage());
        }
    }

    /**
     * 调试：获取所有怪物数据
     */
    @GetMapping("/monsters/all")
    public Result<List<Monster>> getAllMonsters() {
        try {
            List<Monster> monsters = battleService.getAllMonsters();
            return Result.success("获取所有怪物成功", monsters);
        } catch (Exception e) {
            return Result.error(500, "获取所有怪物失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据场景获取怪物
     */
    @GetMapping("/monsters/scene/{sceneId}")
    public Result<List<Monster>> getMonstersByScene(@PathVariable Integer sceneId) {
        try {
            List<Monster> monsters = battleService.getMonstersByScene(sceneId);
            return Result.success("获取场景怪物成功", monsters);
        } catch (Exception e) {
            return Result.error(500, "获取场景怪物失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取战斗记录
     */
    @GetMapping("/records")
    public Result<List<BattleRecord>> getBattleRecords(HttpServletRequest request,
                                                       @RequestParam(defaultValue = "10") int limit) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");
            
            if (userId == null) {
                return Result.error(401, "用户未登录");
            }
            
            List<BattleRecord> records = battleService.getPlayerBattleRecords(userId, limit);
            return Result.success("获取战斗记录成功", records);
            
        } catch (Exception e) {
            return Result.error(500, "获取战斗记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取战斗统计信息
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getBattleStats(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");
            
            if (userId == null) {
                return Result.error(401, "用户未登录");
            }
            
            // 获取最近的战斗记录进行统计
            List<BattleRecord> records = battleService.getPlayerBattleRecords(userId, 100);
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalBattles", records.size());
            
            long victories = records.stream().mapToLong(r -> r.isVictory() ? 1 : 0).sum();
            stats.put("victories", victories);
            stats.put("defeats", records.size() - victories);
            
            if (records.size() > 0) {
                double winRate = (double) victories / records.size() * 100;
                stats.put("winRate", Math.round(winRate * 100.0) / 100.0);
                
                int totalExp = records.stream().mapToInt(r -> r.getExpGained() != null ? r.getExpGained() : 0).sum();
                stats.put("totalExpGained", totalExp);
                
                int totalCopper = records.stream().mapToInt(r -> r.getCopperGained() != null ? r.getCopperGained() : 0).sum();
                stats.put("totalCopperGained", totalCopper);
            } else {
                stats.put("winRate", 0.0);
                stats.put("totalExpGained", 0);
                stats.put("totalCopperGained", 0);
            }
            
            return Result.success("获取战斗统计成功", stats);
            
        } catch (Exception e) {
            return Result.error(500, "获取战斗统计失败: " + e.getMessage());
        }
    }
}
