#!/bin/bash

# 修复jygame项目中文编码问题的脚本
# 将GBK/ISO-8859编码的文件转换为UTF-8编码

echo "开始修复jygame项目中文编码问题..."

# 创建备份目录
BACKUP_DIR="encoding_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
echo "创建备份目录: $BACKUP_DIR"

# 计数器
total_files=0
converted_files=0
failed_files=0

# 查找需要转换的文件
echo "正在查找需要转换编码的文件..."

# 查找所有可能包含中文的文件类型，排除UTF-8编码的文件
find . -type f \( -name "*.java" -o -name "*.sql" -o -name "*.properties" -o -name "*.xml" -o -name "*.jsp" -o -name "*.js" -o -name "*.html" -o -name "*.txt" \) \
    -not -path "./target/*" \
    -not -path "./.git/*" \
    -not -path "./logs/*" \
    -not -path "./data/*" \
    -not -path "./$BACKUP_DIR/*" \
    -exec file {} \; | grep -v "UTF-8" | while IFS=':' read -r filepath fileinfo; do
    
    # 跳过二进制文件和已经是UTF-8的文件
    if echo "$fileinfo" | grep -q "binary\|UTF-8"; then
        continue
    fi
    
    total_files=$((total_files + 1))
    echo "处理文件: $filepath"
    
    # 创建备份
    backup_path="$BACKUP_DIR$filepath"
    backup_dir=$(dirname "$backup_path")
    mkdir -p "$backup_dir"
    cp "$filepath" "$backup_path"
    
    # 尝试不同的编码转换
    converted=false
    
    # 尝试从GBK转换
    if iconv -f GBK -t UTF-8 "$filepath" > "${filepath}.tmp" 2>/dev/null; then
        # 检查转换后的文件是否有效
        if [ -s "${filepath}.tmp" ]; then
            mv "${filepath}.tmp" "$filepath"
            echo "  ✓ 成功从GBK转换为UTF-8"
            converted=true
            converted_files=$((converted_files + 1))
        else
            rm -f "${filepath}.tmp"
        fi
    fi
    
    # 如果GBK转换失败，尝试从GB2312转换
    if [ "$converted" = false ]; then
        if iconv -f GB2312 -t UTF-8 "$filepath" > "${filepath}.tmp" 2>/dev/null; then
            if [ -s "${filepath}.tmp" ]; then
                mv "${filepath}.tmp" "$filepath"
                echo "  ✓ 成功从GB2312转换为UTF-8"
                converted=true
                converted_files=$((converted_files + 1))
            else
                rm -f "${filepath}.tmp"
            fi
        fi
    fi
    
    # 如果还是失败，尝试从ISO-8859-1转换（可能是错误检测的GBK）
    if [ "$converted" = false ]; then
        if iconv -f ISO-8859-1 -t UTF-8 "$filepath" > "${filepath}.tmp" 2>/dev/null; then
            if [ -s "${filepath}.tmp" ]; then
                mv "${filepath}.tmp" "$filepath"
                echo "  ✓ 成功从ISO-8859-1转换为UTF-8"
                converted=true
                converted_files=$((converted_files + 1))
            else
                rm -f "${filepath}.tmp"
            fi
        fi
    fi
    
    if [ "$converted" = false ]; then
        echo "  ✗ 转换失败: $filepath"
        failed_files=$((failed_files + 1))
        # 恢复原文件
        cp "$backup_path" "$filepath"
    fi
    
    # 清理临时文件
    rm -f "${filepath}.tmp"
done

echo ""
echo "编码转换完成！"
echo "总文件数: $total_files"
echo "成功转换: $converted_files"
echo "转换失败: $failed_files"
echo "备份目录: $BACKUP_DIR"
echo ""

# 验证转换结果
echo "验证转换结果..."
echo "检查还有哪些文件不是UTF-8编码："
find . -type f \( -name "*.java" -o -name "*.sql" -o -name "*.properties" -o -name "*.xml" -o -name "*.jsp" -o -name "*.js" -o -name "*.html" -o -name "*.txt" \) \
    -not -path "./target/*" \
    -not -path "./.git/*" \
    -not -path "./logs/*" \
    -not -path "./data/*" \
    -not -path "./$BACKUP_DIR/*" \
    -exec file {} \; | grep -v "UTF-8" | head -10

echo ""
echo "如果转换结果满意，可以删除备份目录: rm -rf $BACKUP_DIR"
echo "如果需要恢复，可以从备份目录恢复文件"
