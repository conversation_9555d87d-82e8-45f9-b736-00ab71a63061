<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session测试 - 洪荒游戏</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #fff;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #3498db;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
            border: 1px solid #34495e;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.3);
        }
        .success { color: #2ecc71; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Session和API测试</h1>
        
        <div class="test-section">
            <h3>1. 用户登录测试</h3>
            <button class="btn" onclick="testLogin()">测试登录</button>
            <div id="loginResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 战斗记录API测试</h3>
            <button class="btn" onclick="testBattleRecords()">测试战斗记录</button>
            <div id="recordsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 战斗统计API测试</h3>
            <button class="btn" onclick="testBattleStats()">测试战斗统计</button>
            <div id="statsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 进行一次战斗测试</h3>
            <button class="btn" onclick="testBattle()">测试战斗</button>
            <div id="battleResult" class="result"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="window.location.href='/honghuang-game/game.html'">返回游戏主界面</button>
            <button class="btn" onclick="window.location.href='/honghuang-game/battle.html'">打开战斗页面</button>
        </div>
    </div>

    <script>
        const API_BASE = '/honghuang-game/api';

        // 测试登录
        function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '正在测试登录...';

            fetch(`${API_BASE}/user/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: 'test',
                    password: '123456'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.innerHTML = `<span class="success">✅ 登录成功！用户: ${data.data.username}</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 登录失败: ${data.message}</span>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            });
        }

        // 测试战斗记录
        function testBattleRecords() {
            const resultDiv = document.getElementById('recordsResult');
            resultDiv.innerHTML = '正在获取战斗记录...';

            fetch(`${API_BASE}/battle/records?limit=5`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.innerHTML = `<span class="success">✅ 获取成功！共${data.data.length}条记录</span>
                        <br><pre>${JSON.stringify(data.data, null, 2)}</pre>`;
                } else if (data.code === 401) {
                    resultDiv.innerHTML = `<span class="warning">⚠️ 用户未登录</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 获取失败: ${data.message}</span>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            });
        }

        // 测试战斗统计
        function testBattleStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.innerHTML = '正在获取战斗统计...';

            fetch(`${API_BASE}/battle/stats`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.innerHTML = `<span class="success">✅ 获取成功！</span>
                        <br><pre>${JSON.stringify(data.data, null, 2)}</pre>`;
                } else if (data.code === 401) {
                    resultDiv.innerHTML = `<span class="warning">⚠️ 用户未登录</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 获取失败: ${data.message}</span>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            });
        }

        // 测试战斗
        function testBattle() {
            const resultDiv = document.getElementById('battleResult');
            resultDiv.innerHTML = '正在进行战斗...';

            fetch(`${API_BASE}/battle/start`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    monsterId: 2
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    const result = data.data;
                    resultDiv.innerHTML = `<span class="success">✅ 战斗完成！</span>
                        <br>结果: ${result.victory ? '胜利' : '失败'}
                        <br>回合数: ${result.rounds}
                        <br>经验: +${result.expGained}
                        <br>铜钱: +${result.copperGained}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 战斗失败: ${data.message}</span>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            });
        }

        // 页面加载时自动测试
        window.onload = function() {
            // 自动测试战斗记录，看看是否已登录
            testBattleRecords();
        };
    </script>
</body>
</html>
