package com.honghuang.game.service;

import com.honghuang.game.entity.Mail;
import com.honghuang.game.entity.MailAttachment;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.MailAttachmentRepository;
import com.honghuang.game.repository.MailRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 邮件服务类
 */
@Service
@Transactional
public class MailService {

    @Autowired
    private MailRepository mailRepository;

    @Autowired
    private MailAttachmentRepository mailAttachmentRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    @Autowired
    private InventoryService inventoryService;

    /**
     * 发送邮件
     */
    public Mail sendMail(Integer senderId, String receiverName, String title, String content, 
                        Long gold, Long silver, Long copper, List<Map<String, Integer>> items) {
        System.out.println("发送邮件: senderId=" + senderId + ", receiverName=" + receiverName);

        PlayerRole sender = null;
        if (senderId != null) {
            sender = playerRoleService.findById(senderId);
        }

        // 查找接收者
        PlayerRole receiver = playerRoleService.findByName(receiverName);
        if (receiver == null) {
            throw new BusinessException(404, "接收者不存在");
        }

        // 检查是否给自己发邮件
        if (senderId != null && senderId.equals(receiver.getId())) {
            throw new BusinessException(400, "不能给自己发邮件");
        }

        // 创建邮件
        Mail mail = new Mail();
        mail.setSenderId(senderId);
        mail.setSenderName(sender != null ? sender.getName() : "系统");
        mail.setReceiverId(receiver.getId());
        mail.setReceiverName(receiver.getName());
        mail.setTitle(title);
        mail.setContent(content);
        mail.setMailType(senderId == null ? 2 : 1); // 系统邮件或普通邮件
        mail.setDefaultExpireTime();

        // 设置附件金币
        if (gold != null || silver != null || copper != null) {
            mail.setAttachmentMoney(gold, silver, copper);
        }

        // 检查是否有物品附件
        if (items != null && !items.isEmpty()) {
            mail.setHasAttachment(1);
            
            // 检查发送者是否有足够的物品
            if (senderId != null) {
                for (Map<String, Integer> item : items) {
                    Integer itemId = item.get("itemId");
                    Integer quantity = item.get("quantity");
                    
                    if (!inventoryService.hasItem(senderId, itemId, quantity)) {
                        throw new BusinessException(400, "物品数量不足");
                    }
                }
            }
        }

        mail = mailRepository.save(mail);

        // 扣除发送者的金币和物品
        if (senderId != null) {
            if (mail.hasAttachment()) {
                // 扣除金币
                if (gold != null && gold > 0) {
                    if (!sender.spendGold(gold)) {
                        throw new BusinessException(400, "仙晶不足");
                    }
                }
                if (silver != null && silver > 0) {
                    if (!sender.spendSilver(silver)) {
                        throw new BusinessException(400, "灵石不足");
                    }
                }
                if (copper != null && copper > 0) {
                    if (!sender.spendCopper(copper)) {
                        throw new BusinessException(400, "铜钱不足");
                    }
                }
                playerRoleService.save(sender);

                // 扣除物品并创建附件
                if (items != null && !items.isEmpty()) {
                    for (Map<String, Integer> item : items) {
                        Integer itemId = item.get("itemId");
                        Integer quantity = item.get("quantity");
                        
                        inventoryService.removeItem(senderId, itemId, quantity);
                        
                        MailAttachment attachment = MailAttachment.create(mail.getId(), itemId, quantity);
                        mailAttachmentRepository.save(attachment);
                    }
                }
            }
        } else {
            // 系统邮件直接创建附件
            if (items != null && !items.isEmpty()) {
                for (Map<String, Integer> item : items) {
                    Integer itemId = item.get("itemId");
                    Integer quantity = item.get("quantity");
                    
                    MailAttachment attachment = MailAttachment.create(mail.getId(), itemId, quantity);
                    mailAttachmentRepository.save(attachment);
                }
            }
        }

        System.out.println("邮件发送成功: mailId=" + mail.getId());
        return mail;
    }

    /**
     * 发送系统邮件
     */
    public Mail sendSystemMail(String receiverName, String title, String content, 
                              Long gold, Long silver, Long copper, List<Map<String, Integer>> items) {
        return sendMail(null, receiverName, title, content, gold, silver, copper, items);
    }

    /**
     * 获取收件箱
     */
    @Transactional(readOnly = true)
    public List<Mail> getInboxMails(Integer roleId) {
        return mailRepository.findInboxMails(roleId);
    }

    /**
     * 获取发件箱
     */
    @Transactional(readOnly = true)
    public List<Mail> getSentMails(Integer roleId) {
        return mailRepository.findSentMails(roleId);
    }

    /**
     * 获取未读邮件
     */
    @Transactional(readOnly = true)
    public List<Mail> getUnreadMails(Integer roleId) {
        return mailRepository.findUnreadMails(roleId);
    }

    /**
     * 获取有附件的邮件
     */
    @Transactional(readOnly = true)
    public List<Mail> getMailsWithAttachment(Integer roleId) {
        return mailRepository.findMailsWithUnclaimed(roleId);
    }

    /**
     * 阅读邮件
     */
    public Mail readMail(Integer roleId, Integer mailId) {
        System.out.println("阅读邮件: roleId=" + roleId + ", mailId=" + mailId);

        Mail mail = mailRepository.findById(mailId)
                .orElseThrow(() -> new BusinessException(404, "邮件不存在"));

        if (!mail.getReceiverId().equals(roleId)) {
            throw new BusinessException(403, "无权限阅读此邮件");
        }

        if (!mail.isRead()) {
            mail.markAsRead();
            mailRepository.save(mail);
        }

        System.out.println("邮件阅读成功: mailId=" + mailId);
        return mail;
    }

    /**
     * 领取邮件附件
     */
    public boolean claimMailAttachment(Integer roleId, Integer mailId) {
        System.out.println("领取邮件附件: roleId=" + roleId + ", mailId=" + mailId);

        Mail mail = mailRepository.findById(mailId)
                .orElseThrow(() -> new BusinessException(404, "邮件不存在"));

        if (!mail.getReceiverId().equals(roleId)) {
            throw new BusinessException(403, "无权限操作此邮件");
        }

        if (!mail.hasAttachment()) {
            throw new BusinessException(400, "邮件没有附件");
        }

        if (mail.isAttachmentClaimed()) {
            throw new BusinessException(400, "附件已领取");
        }

        if (mail.isExpired()) {
            throw new BusinessException(400, "邮件已过期");
        }

        PlayerRole receiver = playerRoleService.findById(roleId);

        // 领取金币附件
        if (mail.getAttachmentGold() > 0) {
            receiver.addGold(mail.getAttachmentGold());
        }
        if (mail.getAttachmentSilver() > 0) {
            receiver.addSilver(mail.getAttachmentSilver());
        }
        if (mail.getAttachmentCopper() > 0) {
            receiver.addCopper(mail.getAttachmentCopper());
        }

        // 领取物品附件
        List<MailAttachment> attachments = mailAttachmentRepository.findUnclaimedByMailId(mailId);
        for (MailAttachment attachment : attachments) {
            inventoryService.addItem(roleId, attachment.getItemId(), attachment.getQuantity());
            attachment.claim();
            mailAttachmentRepository.save(attachment);
        }

        // 标记邮件附件为已领取
        mail.claimAttachment();
        mailRepository.save(mail);
        playerRoleService.save(receiver);

        System.out.println("邮件附件领取成功: mailId=" + mailId);
        return true;
    }

    /**
     * 删除邮件
     */
    public boolean deleteMail(Integer roleId, Integer mailId) {
        System.out.println("删除邮件: roleId=" + roleId + ", mailId=" + mailId);

        Mail mail = mailRepository.findById(mailId)
                .orElseThrow(() -> new BusinessException(404, "邮件不存在"));

        if (!mail.canDelete(roleId)) {
            throw new BusinessException(400, "无法删除此邮件");
        }

        if (mail.getSenderId() != null && mail.getSenderId().equals(roleId)) {
            mail.deleteBySender();
        } else if (mail.getReceiverId().equals(roleId)) {
            mail.deleteByReceiver();
        } else {
            throw new BusinessException(403, "无权限删除此邮件");
        }

        mailRepository.save(mail);

        System.out.println("邮件删除成功: mailId=" + mailId);
        return true;
    }

    /**
     * 标记所有邮件为已读
     */
    public boolean markAllAsRead(Integer roleId) {
        System.out.println("标记所有邮件为已读: roleId=" + roleId);

        int count = mailRepository.markAllAsRead(roleId);

        System.out.println("标记邮件为已读完成: 共" + count + "封邮件");
        return true;
    }

    /**
     * 搜索邮件
     */
    @Transactional(readOnly = true)
    public List<Mail> searchMails(Integer roleId, String keyword) {
        return mailRepository.findMailsByTitle(roleId, keyword);
    }

    /**
     * 获取邮件统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getMailStatistics(Integer roleId) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        long unreadCount = mailRepository.countUnreadMails(roleId);
        long attachmentCount = mailRepository.countUnclaimedAttachmentMails(roleId);
        long systemMailCount = mailRepository.countMailsByType(roleId, 2);
        long normalMailCount = mailRepository.countMailsByType(roleId, 1);
        
        stats.put("unreadCount", unreadCount);
        stats.put("attachmentCount", attachmentCount);
        stats.put("systemMailCount", systemMailCount);
        stats.put("normalMailCount", normalMailCount);
        
        return stats;
    }

    /**
     * 获取邮件附件列表
     */
    @Transactional(readOnly = true)
    public List<MailAttachment> getMailAttachments(Integer mailId) {
        return mailAttachmentRepository.findByMailIdOrderByCreateTimeAsc(mailId);
    }

    /**
     * 处理过期邮件
     */
    @Transactional
    public void processExpiredMails() {
        System.out.println("处理过期邮件");

        int count = mailRepository.deleteExpiredMails();

        System.out.println("过期邮件处理完成，共处理" + count + "封邮件");
    }

    /**
     * 清理已删除的邮件
     */
    @Transactional
    public void cleanupDeletedMails() {
        System.out.println("清理已删除的邮件");

        // 先删除附件
        List<Mail> deletedMails = mailRepository.findAll().stream()
                .filter(mail -> mail.getStatus() == 4)
                .collect(java.util.stream.Collectors.toList());
        
        if (!deletedMails.isEmpty()) {
            List<Integer> mailIds = deletedMails.stream()
                    .map(Mail::getId)
                    .collect(java.util.stream.Collectors.toList());
            
            mailAttachmentRepository.deleteByMailIds(mailIds);
        }

        // 删除邮件
        int count = mailRepository.physicalDeleteMarkedMails();

        System.out.println("邮件清理完成，共清理" + count + "封邮件");
    }
}
