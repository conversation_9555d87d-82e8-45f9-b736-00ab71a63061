# 洪荒Online - 网页游戏系统

## 项目简介

洪荒Online是一个基于Spring Boot开发的网页游戏系统，采用现代化的Web技术栈，提供完整的游戏功能包括用户注册登录、角色管理、物品系统、战斗系统、场景系统等。

## 技术栈

- **后端框架**: Spring Boot 2.7.x
- **数据库**: MySQL 8.0
- **ORM框架**: Spring Data JPA + Hibernate
- **前端技术**: HTML5 + CSS3 + JavaScript (原生)
- **API文档**: Swagger/OpenAPI 3
- **构建工具**: Maven
- **Java版本**: JDK 8+

## 功能特性

### 🎮 核心游戏功能

1. **用户系统**
   - 用户注册、登录、登出
   - 密码加密存储
   - 会话管理

2. **角色系统**
   - 角色创建、删除
   - 多角色支持
   - 角色属性管理（等级、经验、属性点等）
   - 角色状态管理（生命值、法力值等）

3. **物品系统**
   - 丰富的物品类型（武器、防具、消耗品、材料等）
   - 物品品质系统
   - 背包管理
   - 装备系统
   - 物品使用和交易

4. **战斗系统**
   - PVE战斗
   - 回合制战斗机制
   - 战斗记录和统计
   - 经验值和掉落奖励

5. **场景系统**
   - 多样化的游戏场景
   - 场景连接和移动
   - 安全区域和PK区域
   - 场景怪物配置

6. **怪物系统**
   - 多种类型怪物（普通、精英、BOSS、NPC）
   - 怪物AI系统
   - 掉落物品配置
   - 技能系统

### 🛠 管理功能

1. **数据管理**
   - 物品管理（增删改查）
   - 怪物管理
   - 场景管理
   - 玩家数据管理

2. **服务器监控**
   - 服务器状态监控
   - 内存使用情况
   - 游戏数据统计
   - 健康检查

## 🚀 快速开始

### 环境要求
- JDK 8 或更高版本
- Maven 3.6 或更高版本
- MySQL 8.0 或更高版本

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd honghuang-game-springboot
   ```

2. **配置数据库**
   - 创建MySQL数据库：`honghuang_game`
   - 修改 `src/main/resources/application.yml` 中的数据库连接配置

3. **编译运行**
   ```bash
   mvn clean compile
   mvn spring-boot:run
   ```

4. **访问应用**
   - 游戏首页：http://localhost:8080/honghuang-game/
   - API文档：http://localhost:8080/honghuang-game/swagger-ui.html

### 数据库初始化

应用启动时会自动执行以下操作：
1. 创建/更新数据库表结构
2. 初始化基础游戏数据（物品、场景、怪物）
3. 如果数据已存在，则跳过初始化

## API接口

### 用户相关
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `POST /api/user/logout` - 用户登出
- `GET /api/user/current` - 获取当前用户信息

### 角色相关
- `GET /api/role/list` - 获取角色列表
- `POST /api/role/create` - 创建角色
- `POST /api/role/select/{id}` - 选择角色
- `DELETE /api/role/{id}` - 删除角色

### 物品相关
- `GET /api/item/active` - 获取所有激活物品
- `GET /api/item/{id}` - 获取物品详情
- `GET /api/item/type/{type}` - 根据类型获取物品

### 玩家物品相关
- `GET /api/player-item/bag` - 获取背包物品
- `GET /api/player-item/equipped` - 获取装备物品
- `POST /api/player-item/use` - 使用物品
- `POST /api/player-item/equip` - 装备物品

### 战斗相关
- `POST /api/battle/start` - 开始战斗
- `GET /api/battle/monsters/recommended` - 获取推荐怪物
- `GET /api/battle/records` - 获取战斗记录
- `GET /api/battle/stats` - 获取战斗统计

### 场景相关
- `GET /api/scene/active` - 获取所有激活场景
- `GET /api/scene/{id}` - 获取场景详情
- `POST /api/scene/move` - 移动到场景

### 服务器状态
- `GET /api/server/status` - 获取服务器状态
- `GET /api/server/health` - 健康检查
- `GET /api/server/game-stats` - 获取游戏统计

## 游戏玩法

1. **注册账号**: 在首页注册新账号
2. **创建角色**: 登录后创建游戏角色
3. **探索世界**: 在不同场景间移动探索
4. **战斗升级**: 与怪物战斗获得经验和物品
5. **装备管理**: 使用获得的装备提升角色实力
6. **持续成长**: 通过战斗和任务不断提升角色等级

## 项目结构

```
honghuang-game-springboot/
├── src/
│   ├── main/
│   │   ├── java/com/honghuang/game/
│   │   │   ├── HonghuangGameApplication.java     # 主启动类
│   │   │   ├── config/                           # 配置类
│   │   │   │   ├── GameConfig.java              # 游戏配置
│   │   │   │   ├── SecurityConfig.java          # 安全配置
│   │   │   │   ├── RedisConfig.java             # Redis配置
│   │   │   │   ├── SimpleCacheConfig.java       # 简单缓存配置
│   │   │   │   ├── SwaggerConfig.java           # Swagger配置
│   │   │   │   └── WebConfig.java               # Web配置
│   │   │   ├── controller/                       # 控制层
│   │   │   │   ├── UserController.java          # 用户控制器
│   │   │   │   ├── PlayerRoleController.java    # 角色控制器
│   │   │   │   └── GameController.java          # 游戏控制器
│   │   │   ├── service/                          # 服务层
│   │   │   │   ├── UserService.java             # 用户服务
│   │   │   │   └── PlayerRoleService.java       # 角色服务
│   │   │   ├── repository/                       # 数据访问层
│   │   │   │   ├── UserRepository.java          # 用户仓库
│   │   │   │   └── PlayerRoleRepository.java    # 角色仓库
│   │   │   ├── entity/                           # 实体类
│   │   │   │   ├── User.java                    # 用户实体
│   │   │   │   └── PlayerRole.java              # 角色实体
│   │   │   ├── dto/                              # 数据传输对象
│   │   │   │   ├── UserLoginDTO.java            # 用户登录DTO
│   │   │   │   ├── UserRegisterDTO.java         # 用户注册DTO
│   │   │   │   └── PlayerRoleCreateDTO.java     # 角色创建DTO
│   │   │   ├── vo/                               # 视图对象
│   │   │   │   ├── UserVO.java                  # 用户VO
│   │   │   │   └── PlayerRoleVO.java            # 角色VO
│   │   │   ├── common/                           # 通用类
│   │   │   │   ├── Result.java                  # 统一返回结果
│   │   │   │   └── ResultCode.java              # 返回状态码
│   │   │   ├── exception/                        # 异常处理
│   │   │   │   ├── BusinessException.java       # 业务异常
│   │   │   │   └── GlobalExceptionHandler.java  # 全局异常处理
│   │   │   └── util/                             # 工具类
│   │   │       └── PasswordUtil.java            # 密码工具
│   │   └── resources/
│   │       ├── application.yml                   # 应用配置
│   │       ├── application-simple.yml            # 简化配置
│   │       ├── application-demo.yml              # 演示配置
│   │       └── static/
│   │           └── index.html                    # 首页
│   └── test/                                     # 测试代码
│       └── java/com/honghuang/game/
│           ├── service/
│           │   └── UserServiceTest.java          # 用户服务测试
│           └── controller/
│               └── UserControllerTest.java       # 用户控制器测试
├── pom.xml                                       # Maven配置
└── README.md                                     # 项目说明
```

## 🔧 多环境配置

### 简化模式 (simple)
使用H2内存数据库，无需额外配置，适合快速体验：
```bash
java -jar target/honghuang-game-1.0.0.jar --spring.profiles.active=simple
```

### 演示模式 (demo)
使用H2内存数据库，启用Swagger文档：
```bash
java -jar target/honghuang-game-1.0.0.jar --spring.profiles.active=demo
```

### 生产模式 (prod)
使用MySQL数据库和Redis缓存：

1. 创建数据库：
```sql
CREATE DATABASE jygame CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE jygame_user CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改 `application.yml` 中的数据库连接信息：
```yaml
spring:
  datasource:
    url: ************************************************************************************************************************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

3. 启动应用：
```bash
java -jar target/honghuang-game-1.0.0.jar --spring.profiles.active=prod
```

## 📚 API接口

### 用户管理
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `POST /api/user/logout` - 用户登出
- `GET /api/user/current` - 获取当前用户信息
- `GET /api/user/{userId}` - 根据ID获取用户信息
- `GET /api/user/online-count` - 获取在线用户数量

### 角色管理
- `POST /api/role/create` - 创建角色
- `GET /api/role/{roleId}` - 获取角色信息
- `GET /api/role/my-roles` - 获取当前用户角色列表
- `POST /api/role/{roleId}/login` - 角色登录
- `POST /api/role/{roleId}/logout` - 角色登出
- `GET /api/role/ranking/grade` - 等级排行榜

### 游戏信息
- `GET /api/game/info` - 获取游戏基本信息
- `GET /api/game/status` - 获取服务器状态
- `GET /api/game/health` - 健康检查

## ✨ 主要改进

### 1. 架构现代化
- 从Struts 1.x升级到Spring Boot 2.7
- 采用RESTful API设计
- 使用Spring Data JPA替代传统DAO
- 集成Redis缓存提升性能

### 2. 代码质量
- 统一的异常处理机制
- 完善的参数校验
- 标准化的返回结果格式
- 完整的单元测试覆盖

### 3. 开发体验
- 自动生成API文档
- 热部署支持
- 配置外部化
- 多环境配置支持

### 4. 运维友好
- 健康检查接口
- 应用监控指标
- 结构化日志输出
- Docker容器化支持（可扩展）

## 🚀 部署说明

### 开发环境
```bash
mvn spring-boot:run
```

### 生产环境
```bash
# 打包
mvn clean package -Dmaven.test.skip=true

# 运行
java -jar target/honghuang-game-1.0.0.jar --spring.profiles.active=prod
```

## 🧪 测试

### 运行所有测试
```bash
mvn test
```

### 运行特定测试
```bash
mvn test -Dtest=UserServiceTest
```

## 🔧 已解决的问题

### 1. 注册403错误问题
- **问题**: 用户注册时返回403 Forbidden错误
- **原因**: Spring Security默认启用CSRF保护
- **解决**: 创建SecurityConfig配置类，禁用CSRF保护，允许注册和登录接口无需认证

### 2. 密码字段长度问题
- **问题**: BCrypt加密后的密码长度超过数据库字段限制
- **原因**: 数据库密码字段长度设置为50，但BCrypt加密后为60字符
- **解决**: 修改User实体类密码字段长度为60

### 3. 多环境配置支持
- **问题**: 缺少不同环境的配置文件
- **解决**: 添加application-simple.yml、application-demo.yml等配置文件

## 📝 更新日志

### v1.0.0 (2025-01-26)
- ✅ 完成Struts到Spring Boot的重构
- ✅ 实现用户注册、登录功能
- ✅ 实现角色创建、管理功能
- ✅ 添加Spring Security配置，解决CSRF 403问题
- ✅ 修复密码字段长度问题，支持BCrypt加密
- ✅ 添加H2内存数据库支持，便于快速体验
- ✅ 添加多环境配置支持(simple/demo/prod)
- ✅ 添加完整的单元测试
- ✅ 集成Swagger API文档
- ✅ 添加性能监控和缓存配置

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitLab Issues](http://192.168.11.10/root/honghuang-game-springboot/-/issues)
- 邮箱: <EMAIL>
