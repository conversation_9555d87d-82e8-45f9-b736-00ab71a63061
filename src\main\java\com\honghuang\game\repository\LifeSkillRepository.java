package com.honghuang.game.repository;

import com.honghuang.game.entity.LifeSkill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 生活技能数据访问层
 */
@Repository
public interface LifeSkillRepository extends JpaRepository<LifeSkill, Integer> {

    /**
     * 根据角色ID查找技能列表
     */
    List<LifeSkill> findByRoleIdOrderBySkillTypeAsc(Integer roleId);

    /**
     * 根据角色ID和技能类型查找技能
     */
    Optional<LifeSkill> findByRoleIdAndSkillType(Integer roleId, Integer skillType);

    /**
     * 根据技能类型查找所有技能
     */
    List<LifeSkill> findBySkillTypeOrderBySkillLevelDesc(Integer skillType);

    /**
     * 根据技能等级范围查找技能
     */
    @Query("SELECT ls FROM LifeSkill ls WHERE ls.skillLevel BETWEEN :minLevel AND :maxLevel ORDER BY ls.skillLevel DESC")
    List<LifeSkill> findBySkillLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 查找高等级技能
     */
    @Query("SELECT ls FROM LifeSkill ls WHERE ls.skillLevel >= :minLevel ORDER BY ls.skillLevel DESC, ls.experience DESC")
    List<LifeSkill> findHighLevelSkills(@Param("minLevel") Integer minLevel);

    /**
     * 查找正常状态的技能
     */
    @Query("SELECT ls FROM LifeSkill ls WHERE ls.roleId = :roleId AND ls.status = 1 ORDER BY ls.skillType ASC")
    List<LifeSkill> findAvailableSkills(@Param("roleId") Integer roleId);

    /**
     * 查找冷却中的技能
     */
    @Query("SELECT ls FROM LifeSkill ls WHERE ls.roleId = :roleId AND ls.status = 2 ORDER BY ls.lastUseTime DESC")
    List<LifeSkill> findCooldownSkills(@Param("roleId") Integer roleId);

    /**
     * 查找需要重置冷却的技能
     */
    @Query("SELECT ls FROM LifeSkill ls WHERE ls.status = 2 AND ls.lastUseTime <= :cutoffTime")
    List<LifeSkill> findSkillsToResetCooldown(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 更新技能经验
     */
    @Modifying
    @Query("UPDATE LifeSkill ls SET ls.experience = ls.experience + :exp WHERE ls.id = :skillId")
    int addExperience(@Param("skillId") Integer skillId, @Param("exp") Long exp);

    /**
     * 更新技能等级
     */
    @Modifying
    @Query("UPDATE LifeSkill ls SET ls.skillLevel = :level, ls.experience = :exp WHERE ls.id = :skillId")
    int updateSkillLevel(@Param("skillId") Integer skillId, @Param("level") Integer level, @Param("exp") Long exp);

    /**
     * 更新熟练度
     */
    @Modifying
    @Query("UPDATE LifeSkill ls SET ls.proficiency = ls.proficiency + :proficiency WHERE ls.id = :skillId AND ls.proficiency < ls.maxProficiency")
    int addProficiency(@Param("skillId") Integer skillId, @Param("proficiency") Integer proficiency);

    /**
     * 更新技能状态
     */
    @Modifying
    @Query("UPDATE LifeSkill ls SET ls.status = :status, ls.lastUseTime = :useTime WHERE ls.id = :skillId")
    int updateSkillStatus(@Param("skillId") Integer skillId, @Param("status") Integer status, @Param("useTime") LocalDateTime useTime);

    /**
     * 重置技能冷却
     */
    @Modifying
    @Query("UPDATE LifeSkill ls SET ls.status = 1, ls.lastUseTime = NULL WHERE ls.id = :skillId")
    int resetCooldown(@Param("skillId") Integer skillId);

    /**
     * 批量重置过期冷却
     */
    @Modifying
    @Query("UPDATE LifeSkill ls SET ls.status = 1 WHERE ls.status = 2 AND ls.lastUseTime <= :cutoffTime")
    int resetExpiredCooldowns(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计角色技能数量
     */
    @Query("SELECT COUNT(ls) FROM LifeSkill ls WHERE ls.roleId = :roleId")
    long countByRoleId(@Param("roleId") Integer roleId);

    /**
     * 统计指定类型技能数量
     */
    @Query("SELECT COUNT(ls) FROM LifeSkill ls WHERE ls.skillType = :skillType")
    long countBySkillType(@Param("skillType") Integer skillType);

    /**
     * 查找技能排行榜
     */
    @Query("SELECT ls FROM LifeSkill ls WHERE ls.skillType = :skillType ORDER BY ls.skillLevel DESC, ls.experience DESC")
    List<LifeSkill> findSkillRanking(@Param("skillType") Integer skillType);

    /**
     * 查找角色的最高技能等级
     */
    @Query("SELECT MAX(ls.skillLevel) FROM LifeSkill ls WHERE ls.roleId = :roleId")
    Integer findMaxSkillLevel(@Param("roleId") Integer roleId);

    /**
     * 查找角色的平均技能等级
     */
    @Query("SELECT AVG(ls.skillLevel) FROM LifeSkill ls WHERE ls.roleId = :roleId")
    Double findAverageSkillLevel(@Param("roleId") Integer roleId);

    /**
     * 删除角色的所有技能
     */
    @Modifying
    @Query("DELETE FROM LifeSkill ls WHERE ls.roleId = :roleId")
    int deleteByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找满级技能
     */
    @Query("SELECT ls FROM LifeSkill ls WHERE ls.skillLevel = 100 ORDER BY ls.roleId ASC")
    List<LifeSkill> findMaxLevelSkills();

    /**
     * 查找活跃玩家的技能
     */
    @Query("SELECT ls FROM LifeSkill ls WHERE ls.lastUseTime >= :cutoffTime ORDER BY ls.lastUseTime DESC")
    List<LifeSkill> findActivePlayerSkills(@Param("cutoffTime") LocalDateTime cutoffTime);
}
