<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洪荒游戏 - API测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 洪荒游戏 API 测试工具</h1>
        
        <div class="test-section">
            <h3>🔧 系统状态测试</h3>
            <button onclick="testGameStatus()">获取游戏状态</button>
            <button onclick="testHealthCheck()">健康检查</button>
            <button onclick="testGameInfo()">游戏信息</button>
            <div id="systemResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>👤 用户系统测试</h3>
            <div>
                <input type="text" id="regUsername" placeholder="用户名" value="testuser">
                <input type="password" id="regPassword" placeholder="密码" value="123456">
                <button onclick="testRegister()">注册用户</button>
            </div>
            <div>
                <input type="text" id="loginUsername" placeholder="用户名" value="testuser">
                <input type="password" id="loginPassword" placeholder="密码" value="123456">
                <button onclick="testLogin()">用户登录</button>
            </div>
            <button onclick="testCurrentUser()">获取当前用户</button>
            <button onclick="testLogout()">用户登出</button>
            <div id="userResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 数据库测试</h3>
            <button onclick="testUserStats()">用户统计</button>
            <button onclick="testUserSearch()">搜索用户</button>
            <div id="dataResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔗 快速链接</h3>
            <button onclick="window.open('/honghuang-game/h2-console', '_blank')">H2数据库控制台</button>
            <button onclick="window.open('/honghuang-game/', '_blank')">游戏首页</button>
            <button onclick="window.open('/honghuang-game/game.html', '_blank')">游戏界面</button>
        </div>
    </div>

    <script>
        const API_BASE = '/honghuang-game/api';

        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    credentials: 'include',
                    ...options
                });
                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, data: { error: error.message } };
            }
        }

        // 系统状态测试
        async function testGameStatus() {
            const result = await apiCall('/game/status');
            showResult('systemResult', result.data, result.success);
        }

        async function testHealthCheck() {
            const result = await apiCall('/game/health');
            showResult('systemResult', result.data, result.success);
        }

        async function testGameInfo() {
            const result = await apiCall('/game/info');
            showResult('systemResult', result.data, result.success);
        }

        // 用户系统测试
        async function testRegister() {
            const username = document.getElementById('regUsername').value;
            const password = document.getElementById('regPassword').value;
            
            const result = await apiCall('/user/register', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username,
                    password,
                    confirmPassword: password,
                    channel: 'web'
                })
            });
            showResult('userResult', result.data, result.success);
        }

        async function testLogin() {
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            const result = await apiCall('/user/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password, channel: 'web' })
            });
            showResult('userResult', result.data, result.success);
        }

        async function testCurrentUser() {
            const result = await apiCall('/user/current');
            showResult('userResult', result.data, result.success);
        }

        async function testLogout() {
            const result = await apiCall('/user/logout', { method: 'POST' });
            showResult('userResult', result.data, result.success);
        }

        // 数据库测试
        async function testUserStats() {
            const result = await apiCall('/user/statistics');
            showResult('dataResult', result.data, result.success);
        }

        async function testUserSearch() {
            const result = await apiCall('/user/search?keyword=test');
            showResult('dataResult', result.data, result.success);
        }

        // 页面加载时自动测试系统状态
        window.onload = function() {
            testGameStatus();
        };
    </script>
</body>
</html>
