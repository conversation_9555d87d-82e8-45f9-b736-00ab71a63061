server:
  port: 8080
  servlet:
    context-path: /honghuang-game
    encoding:
      charset: UTF-8
      enabled: true
      force: true
      force-request: true
      force-response: true
  # Tomcat性能优化
  tomcat:
    threads:
      max: 200              # 最大工作线程数
      min-spare: 20         # 最小空闲线程数
    max-connections: 8192   # 最大连接数
    accept-count: 100       # 等待队列长度
    connection-timeout: 20000  # 连接超时(ms)

spring:
  application:
    name: honghuang-game
  
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************
    username: root
    password: root
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HonghuangGameHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        use_sql_comments: false

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password:
    timeout: 6000ms
    database: 0
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null



  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

# 日志配置
logging:
  level:
    com.honghuang.game: debug
    org.springframework.security: debug
    org.springframework.web.socket: debug
    org.hibernate.SQL: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/honghuang-game.log

# 游戏配置
game:
  config:
    # 游戏状态 1:开服 2:维护
    state: 1
    # 玩家人数上限
    user-limit: 1800
    # 公聊等级限制
    chat-grade-limit: 5
    # 玩家等级上限
    grade-limit: 100
    # 经验倍数
    exp-rate: 1
    # 掉宝倍数
    drop-rate: 1
    # 渠道ID
    channel-id: 8
    # 分区ID
    area-id: 1
    # 游戏名称
    name: 洪荒online
    # 服务器URL
    server-url: http://localhost:8080
    # 论坛地址
    forum-url: http://localhost:8080/forum

# JWT配置
jwt:
  secret: honghuang-game-secret-key-2024
  expiration: 86400000 # 24小时
  header: Authorization
  prefix: Bearer

# 排行榜配置
ranking:
  default-limit: 10
  max-limit: 100
  cache-enabled: true
  cache-expire-seconds: 300
  real-time-update: true
  batch-update-interval: 60
  reward-enabled: true
  reward-cron: "0 0 0 * * ?"
  first-reward-copper: 100000
  top-three-reward-copper: 50000
  top-ten-reward-copper: 10000

# Swagger配置（暂时禁用）
springfox:
  documentation:
    swagger-ui:
      enabled: false

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************
    username: root
    password: root
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
    defer-datasource-initialization: false
  redis:
    host: localhost
    port: 6379

logging:
  level:
    root: info
    com.honghuang.game: debug

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: **************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
  jpa:
    show-sql: false
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}

logging:
  level:
    root: warn
    com.honghuang.game: info
