package com.honghuang.game.repository;

import com.honghuang.game.entity.PlayerRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 玩家角色数据访问层
 */
@Repository
public interface PlayerRoleRepository extends JpaRepository<PlayerRole, Integer> {

    /**
     * 根据用户ID查找角色列表
     */
    List<PlayerRole> findByUserIdAndDeleted(Integer userId, Integer deleted);

    /**
     * 根据用户ID查找角色列表（包含已删除）
     */
    List<PlayerRole> findByUserId(Integer userId);

    /**
     * 根据用户ID和删除状态查找角色列表
     */
    List<PlayerRole> findByUserIdAndDeletedOrderByCreateTimeAsc(Integer userId, Integer deleted);

    /**
     * 统计未删除的角色数量
     */
    long countByDeleted(Integer deleted);



    /**
     * 获取等级排行榜前10名
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.deleted = :deleted ORDER BY pr.level DESC, pr.createTime ASC")
    List<PlayerRole> findTop10ByDeletedOrderByLevelDescCreateTimeAsc(@Param("deleted") Integer deleted);

    /**
     * 获取财富排行榜前10名
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.deleted = :deleted ORDER BY pr.gold DESC, pr.createTime ASC")
    List<PlayerRole> findTop10ByDeletedOrderByGoldDescCreateTimeAsc(@Param("deleted") Integer deleted);

    /**
     * 根据删除状态查找角色列表
     */
    List<PlayerRole> findByDeleted(Integer deleted);

    /**
     * 根据角色名称查找角色
     */
    Optional<PlayerRole> findByNameAndDeleted(String name, Integer deleted);

    /**
     * 检查角色名是否存在
     */
    boolean existsByNameAndDeleted(String name, Integer deleted);

    /**
     * 根据用户ID和角色ID查找角色
     */
    Optional<PlayerRole> findByIdAndUserIdAndDeleted(Integer id, Integer userId, Integer deleted);

    /**
     * 更新角色在线状态
     */
    @Modifying
    @Query("UPDATE PlayerRole r SET r.onlineStatus = :status, r.lastLoginTime = :loginTime WHERE r.id = :roleId")
    int updateOnlineStatus(@Param("roleId") Integer roleId, @Param("status") Integer status, @Param("loginTime") LocalDateTime loginTime);

    /**
     * 更新角色位置
     */
    @Modifying
    @Query("UPDATE PlayerRole r SET r.sceneId = :sceneId, r.sceneName = :sceneName WHERE r.id = :roleId")
    int updateLocation(@Param("roleId") Integer roleId, @Param("sceneId") Integer sceneId, @Param("sceneName") String sceneName);

    /**
     * 更新角色等级和经验
     */
    @Modifying
    @Query("UPDATE PlayerRole r SET r.level = :level, r.experience = :experience WHERE r.id = :roleId")
    int updateLevelAndExp(@Param("roleId") Integer roleId, @Param("level") Integer level, @Param("experience") Long experience);

    /**
     * 更新角色HP和MP
     */
    @Modifying
    @Query("UPDATE PlayerRole r SET r.currentHp = :hp, r.currentMp = :mp WHERE r.id = :roleId")
    int updateHpAndMp(@Param("roleId") Integer roleId, @Param("hp") Integer hp, @Param("mp") Integer mp);

    /**
     * 更新角色金钱
     */
    @Modifying
    @Query("UPDATE PlayerRole r SET r.copper = :copper, r.silver = :silver, r.gold = :gold WHERE r.id = :roleId")
    int updateMoney(@Param("roleId") Integer roleId, @Param("copper") Long copper, @Param("silver") Long silver, @Param("gold") Long gold);

    /**
     * 更新角色属性
     */
    @Modifying
    @Query("UPDATE PlayerRole r SET r.baseAttack = :attack, r.baseDefense = :defense, r.baseAgility = :agility, " +
           "r.baseIntelligence = :intelligence, r.baseConstitution = :constitution, r.attributePoints = :attributePoints WHERE r.id = :roleId")
    int updateAttributes(@Param("roleId") Integer roleId, @Param("attack") Integer attack, @Param("defense") Integer defense,
                        @Param("agility") Integer agility, @Param("intelligence") Integer intelligence, 
                        @Param("constitution") Integer constitution, @Param("attributePoints") Integer attributePoints);

    /**
     * 软删除角色
     */
    @Modifying
    @Query("UPDATE PlayerRole r SET r.deleted = 1 WHERE r.id = :roleId")
    int softDeleteRole(@Param("roleId") Integer roleId);

    /**
     * 恢复删除的角色
     */
    @Modifying
    @Query("UPDATE PlayerRole r SET r.deleted = 0 WHERE r.id = :roleId")
    int restoreRole(@Param("roleId") Integer roleId);

    /**
     * 获取等级排行榜
     */
    @Query("SELECT r FROM PlayerRole r WHERE r.deleted = 0 ORDER BY r.level DESC, r.experience DESC")
    Page<PlayerRole> findLevelRanking(Pageable pageable);

    /**
     * 获取在线角色列表
     */
    @Query("SELECT r FROM PlayerRole r WHERE r.onlineStatus = 1 AND r.deleted = 0")
    List<PlayerRole> findOnlineRoles();

    /**
     * 根据场景ID查找角色
     */
    @Query("SELECT r FROM PlayerRole r WHERE r.sceneId = :sceneId AND r.onlineStatus = 1 AND r.deleted = 0")
    List<PlayerRole> findBySceneId(@Param("sceneId") Integer sceneId);

    /**
     * 统计用户角色数量
     */
    @Query("SELECT COUNT(r) FROM PlayerRole r WHERE r.userId = :userId AND r.deleted = 0")
    long countByUserId(@Param("userId") Integer userId);

    /**
     * 查找用户的主角色（第一个创建的角色）
     */
    @Query("SELECT r FROM PlayerRole r WHERE r.userId = :userId AND r.deleted = 0 ORDER BY r.createTime ASC")
    List<PlayerRole> findMainRole(@Param("userId") Integer userId);
}
