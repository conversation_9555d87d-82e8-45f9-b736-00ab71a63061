package com.honghuang.game.service;

import com.honghuang.game.entity.Npc;
import com.honghuang.game.entity.NpcDialogue;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.NpcDialogueRepository;
import com.honghuang.game.repository.NpcRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * NPC服务类
 */
@Service
@Transactional
public class NpcService {

    @Autowired
    private NpcRepository npcRepository;

    @Autowired
    private NpcDialogueRepository npcDialogueRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 获取所有激活的NPC
     */
    @Transactional(readOnly = true)
    public List<Npc> getAllActiveNpcs() {
        return npcRepository.findByActiveOrderBySceneIdAscTypeAsc(1);
    }

    /**
     * 根据场景ID获取NPC列表
     */
    @Transactional(readOnly = true)
    public List<Npc> getNpcsByScene(Integer sceneId) {
        return npcRepository.findBySceneIdAndActiveOrderByTypeAsc(sceneId, 1);
    }

    /**
     * 根据类型获取NPC列表
     */
    @Transactional(readOnly = true)
    public List<Npc> getNpcsByType(Integer type) {
        return npcRepository.findByTypeAndActiveOrderBySceneIdAsc(type, 1);
    }

    /**
     * 获取NPC详情
     */
    @Transactional(readOnly = true)
    public Npc getNpcDetail(Integer npcId) {
        return npcRepository.findById(npcId)
                .orElseThrow(() -> new BusinessException(404, "NPC不存在"));
    }

    /**
     * 查找范围内的NPC
     */
    @Transactional(readOnly = true)
    public List<Npc> getNpcsInRange(Integer sceneId, Integer x, Integer y, Integer range) {
        return npcRepository.findNpcsInRange(sceneId, x, y, range);
    }

    /**
     * 查找最近的NPC
     */
    @Transactional(readOnly = true)
    public List<Npc> getNearestNpcs(Integer sceneId, Integer x, Integer y) {
        return npcRepository.findNearestNpcs(sceneId, x, y);
    }

    /**
     * 与NPC对话
     */
    public NpcDialogue talkToNpc(Integer roleId, Integer npcId, Integer dialogueType, String message) {
        System.out.println("与NPC对话: roleId=" + roleId + ", npcId=" + npcId + ", type=" + dialogueType);

        PlayerRole role = playerRoleService.findById(roleId);
        Npc npc = getNpcDetail(npcId);

        // 检查是否可以与NPC交互
        if (!npc.canInteractWith(role)) {
            throw new BusinessException(400, "无法与此NPC交互");
        }

        // 生成对话内容
        String dialogueContent = generateDialogueContent(npc, role, dialogueType, message);
        String[] options = generateDialogueOptions(npc, role, dialogueType);

        // 创建对话记录
        NpcDialogue dialogue = new NpcDialogue();
        dialogue.setNpcId(npcId);
        dialogue.setRoleId(roleId);
        dialogue.setDialogueType(dialogueType);
        dialogue.setContent(dialogueContent);
        
        if (options.length > 0) {
            dialogue.setDialogueOptions(options);
        }

        dialogue = npcDialogueRepository.save(dialogue);

        System.out.println("对话记录创建成功: dialogueId=" + dialogue.getId());
        return dialogue;
    }

    /**
     * 选择对话选项
     */
    public boolean selectDialogueOption(Integer roleId, Integer dialogueId, Integer optionIndex) {
        System.out.println("选择对话选项: roleId=" + roleId + ", dialogueId=" + dialogueId + ", option=" + optionIndex);

        NpcDialogue dialogue = npcDialogueRepository.findById(dialogueId)
                .orElseThrow(() -> new BusinessException(404, "对话记录不存在"));

        if (!dialogue.getRoleId().equals(roleId)) {
            throw new BusinessException(403, "无权限操作此对话");
        }

        if (!dialogue.hasOptions()) {
            throw new BusinessException(400, "此对话没有选项");
        }

        if (dialogue.hasSelectedOption()) {
            throw new BusinessException(400, "已选择过选项");
        }

        String[] options = dialogue.getDialogueOptions();
        if (optionIndex < 0 || optionIndex >= options.length) {
            throw new BusinessException(400, "选项索引无效");
        }

        // 处理选项结果
        String result = processDialogueOption(dialogue, optionIndex);
        dialogue.selectOption(optionIndex, result);
        npcDialogueRepository.save(dialogue);

        System.out.println("对话选项处理完成: option=" + optionIndex + ", result=" + result);
        return true;
    }

    /**
     * 获取角色的对话历史
     */
    @Transactional(readOnly = true)
    public List<NpcDialogue> getDialogueHistory(Integer roleId) {
        return npcDialogueRepository.findByRoleIdOrderByDialogueTimeDesc(roleId);
    }

    /**
     * 获取角色与指定NPC的对话历史
     */
    @Transactional(readOnly = true)
    public List<NpcDialogue> getDialogueHistoryWithNpc(Integer roleId, Integer npcId) {
        return npcDialogueRepository.findByRoleIdAndNpcIdOrderByDialogueTimeDesc(roleId, npcId);
    }

    /**
     * 获取最近的对话记录
     */
    @Transactional(readOnly = true)
    public List<NpcDialogue> getRecentDialogues(Integer roleId, Integer hours) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(hours);
        return npcDialogueRepository.findRecentDialogues(roleId, cutoffTime);
    }

    /**
     * 获取商店NPC列表
     */
    @Transactional(readOnly = true)
    public List<Npc> getShopNpcs() {
        return npcRepository.findShopNpcs();
    }

    /**
     * 获取任务NPC列表
     */
    @Transactional(readOnly = true)
    public List<Npc> getQuestNpcs() {
        return npcRepository.findQuestNpcs();
    }

    /**
     * 获取功能NPC列表
     */
    @Transactional(readOnly = true)
    public List<Npc> getFunctionNpcs() {
        return npcRepository.findFunctionNpcs();
    }

    /**
     * 搜索NPC
     */
    @Transactional(readOnly = true)
    public List<Npc> searchNpcs(String keyword) {
        return npcRepository.findByNameContaining(keyword);
    }

    /**
     * 获取NPC统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getNpcStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        long totalNpcs = npcRepository.countByType(null);
        long shopNpcs = npcRepository.countByType(1);
        long questNpcs = npcRepository.countByType(2);
        long functionNpcs = npcRepository.countByType(3);
        long decorationNpcs = npcRepository.countByType(4);
        long battleNpcs = npcRepository.countByType(5);
        
        stats.put("totalNpcs", totalNpcs);
        stats.put("shopNpcs", shopNpcs);
        stats.put("questNpcs", questNpcs);
        stats.put("functionNpcs", functionNpcs);
        stats.put("decorationNpcs", decorationNpcs);
        stats.put("battleNpcs", battleNpcs);
        
        return stats;
    }

    /**
     * 生成对话内容
     */
    private String generateDialogueContent(Npc npc, PlayerRole role, Integer dialogueType, String message) {
        StringBuilder content = new StringBuilder();
        
        // 根据对话类型生成不同的内容
        switch (dialogueType) {
            case 1: // 普通对话
                content.append(npc.getDefaultDialogue());
                if (message != null && !message.trim().isEmpty()) {
                    content.append("\n\n你说：").append(message);
                    content.append("\n\n").append(npc.getName()).append("：很高兴与你交谈，").append(role.getName()).append("。");
                }
                break;
                
            case 2: // 任务对话
                if (npc.isQuestNpc()) {
                    content.append("我这里有一些任务需要你的帮助，").append(role.getName()).append("。");
                } else {
                    content.append("我这里暂时没有任务给你。");
                }
                break;
                
            case 3: // 商店对话
                if (npc.isShopNpc()) {
                    content.append("欢迎来到我的商店！看看有什么需要的吗？");
                } else {
                    content.append("我这里不是商店哦。");
                }
                break;
                
            case 4: // 功能对话
                if (npc.isFunctionNpc()) {
                    content.append("我可以为你提供").append(npc.getSubTypeName()).append("服务。");
                } else {
                    content.append("我这里没有特殊功能服务。");
                }
                break;
                
            default:
                content.append(npc.getDefaultDialogue());
        }
        
        return content.toString();
    }

    /**
     * 生成对话选项
     */
    private String[] generateDialogueOptions(Npc npc, PlayerRole role, Integer dialogueType) {
        switch (dialogueType) {
            case 1: // 普通对话
                return new String[]{"再见", "了解更多"};
                
            case 2: // 任务对话
                if (npc.isQuestNpc()) {
                    return new String[]{"查看任务", "接受任务", "完成任务", "离开"};
                } else {
                    return new String[]{"离开"};
                }
                
            case 3: // 商店对话
                if (npc.isShopNpc()) {
                    return new String[]{"查看商品", "购买物品", "出售物品", "离开"};
                } else {
                    return new String[]{"离开"};
                }
                
            case 4: // 功能对话
                if (npc.isFunctionNpc()) {
                    switch (npc.getSubType()) {
                        case 1: // 传送师
                            return new String[]{"传送", "查看传送点", "离开"};
                        case 2: // 仓库管理员
                            return new String[]{"打开仓库", "扩展仓库", "离开"};
                        case 3: // 拍卖师
                            return new String[]{"拍卖物品", "查看拍卖", "离开"};
                        case 4: // 邮件员
                            return new String[]{"查看邮件", "发送邮件", "离开"};
                        default:
                            return new String[]{"使用功能", "离开"};
                    }
                } else {
                    return new String[]{"离开"};
                }
                
            default:
                return new String[]{"离开"};
        }
    }

    /**
     * 处理对话选项
     */
    private String processDialogueOption(NpcDialogue dialogue, Integer optionIndex) {
        String[] options = dialogue.getDialogueOptions();
        String selectedOption = options[optionIndex];
        
        // 根据选择的选项执行相应的逻辑
        switch (selectedOption) {
            case "再见":
            case "离开":
                return "对话结束";
                
            case "了解更多":
                return "获得了更多信息";
                
            case "查看任务":
                return "显示可用任务列表";
                
            case "接受任务":
                return "任务已接受";
                
            case "完成任务":
                return "任务已完成";
                
            case "查看商品":
                return "显示商店商品列表";
                
            case "购买物品":
                return "进入购买界面";
                
            case "出售物品":
                return "进入出售界面";
                
            case "传送":
                return "选择传送目的地";
                
            case "打开仓库":
                return "仓库已打开";
                
            case "拍卖物品":
                return "进入拍卖界面";
                
            case "查看邮件":
                return "显示邮件列表";
                
            default:
                return "选项已处理：" + selectedOption;
        }
    }
}
