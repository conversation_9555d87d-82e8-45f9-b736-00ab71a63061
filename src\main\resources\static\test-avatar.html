<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头像测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #2c3e50;
            color: white;
        }
        .avatar-test {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #3498db;
            border-radius: 10px;
        }
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin: 10px;
        }
        .avatar-container {
            display: flex;
            align-items: center;
            gap: 20px;
        }
    </style>
</head>
<body>
    <h1>🎭 头像测试页面</h1>
    
    <div class="avatar-test">
        <h2>妖族头像测试</h2>
        <div class="avatar-container">
            <img src="/honghuang-game/images/role/yao.png" alt="妖族" class="avatar" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <span style="display: none; color: red;">❌ 妖族头像加载失败</span>
            <div>
                <p>种族：妖族 (race = 1)</p>
                <p>图片路径：/honghuang-game/images/role/yao.png</p>
            </div>
        </div>
    </div>
    
    <div class="avatar-test">
        <h2>巫族头像测试</h2>
        <div class="avatar-container">
            <img src="/honghuang-game/images/role/wu.png" alt="巫族" class="avatar" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <span style="display: none; color: red;">❌ 巫族头像加载失败</span>
            <div>
                <p>种族：巫族 (race = 2)</p>
                <p>图片路径：/honghuang-game/images/role/wu.png</p>
            </div>
        </div>
    </div>
    
    <div class="avatar-test">
        <h2>模拟游戏中的头像显示</h2>
        <div class="avatar-container">
            <div id="testAvatar" style="width: 80px; height: 80px; border-radius: 50%; background: #e74c3c; display: flex; align-items: center; justify-content: center; font-size: 24px;">
                ⚔
            </div>
            <div>
                <button onclick="testRace(1)">测试妖族 (race=1)</button>
                <button onclick="testRace(2)">测试巫族 (race=2)</button>
                <button onclick="testRace(0)">测试默认</button>
            </div>
        </div>
    </div>
    
    <script>
        function testRace(race) {
            const avatarElement = document.getElementById('testAvatar');
            console.log('测试种族:', race);
            
            if (race === 1) {
                // 种族1：妖族
                avatarElement.innerHTML = '<img src="/honghuang-game/images/role/yao.png" alt="妖族" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;" onerror="console.log(\'妖族头像加载失败\'); this.style.display=\'none\'; this.parentElement.textContent=\'妖\';">';
            } else if (race === 2) {
                // 种族2：巫族
                avatarElement.innerHTML = '<img src="/honghuang-game/images/role/wu.png" alt="巫族" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;" onerror="console.log(\'巫族头像加载失败\'); this.style.display=\'none\'; this.parentElement.textContent=\'巫\';">';
            } else {
                // 默认图标
                avatarElement.textContent = '⚔';
            }
        }
        
        // 页面加载时测试图片
        window.onload = function() {
            console.log('头像测试页面加载完成');
            
            // 测试图片是否可以直接访问
            const testImg = new Image();
            testImg.onload = function() {
                console.log('✅ 妖族头像可以正常加载');
            };
            testImg.onerror = function() {
                console.log('❌ 妖族头像加载失败');
            };
            testImg.src = '/honghuang-game/images/role/yao.png';
            
            const testImg2 = new Image();
            testImg2.onload = function() {
                console.log('✅ 巫族头像可以正常加载');
            };
            testImg2.onerror = function() {
                console.log('❌ 巫族头像加载失败');
            };
            testImg2.src = '/honghuang-game/images/role/wu.png';
        };
    </script>
</body>
</html>
