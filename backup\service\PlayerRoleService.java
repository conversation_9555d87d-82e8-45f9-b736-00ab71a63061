package com.honghuang.game.service;

import com.honghuang.game.common.ResultCode;
import com.honghuang.game.dto.PlayerRoleCreateDTO;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.User;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.PlayerRoleRepository;
import com.honghuang.game.repository.UserRepository;
import com.honghuang.game.vo.PlayerRoleVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 玩家角色服务类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
public class PlayerRoleService {

    private static final Logger logger = LoggerFactory.getLogger(PlayerRoleService.class);

    @Autowired
    private PlayerRoleRepository playerRoleRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 创建角色
     */
    public PlayerRoleVO createRole(Integer userId, PlayerRoleCreateDTO createDTO) {
        logger.info("创建角色: userId={}, roleName={}", userId, createDTO.getName());

        // 检查用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 检查角色名是否已存在
        if (playerRoleRepository.existsByName(createDTO.getName())) {
            throw new BusinessException(ResultCode.ROLE_NAME_DUPLICATE);
        }

        // 检查用户角色数量限制（假设最多3个角色）
        List<PlayerRole> existingRoles = playerRoleRepository.findByUserId(userId);
        if (existingRoles.size() >= 3) {
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "每个用户最多只能创建3个角色");
        }

        // 创建角色
        PlayerRole role = new PlayerRole();
        BeanUtils.copyProperties(createDTO, role);
        role.setUser(userOpt.get());
        
        // 设置初始属性
        initializeRoleAttributes(role);

        role = playerRoleRepository.save(role);
        logger.info("角色创建成功: roleId={}, roleName={}", role.getId(), role.getName());

        return convertToVO(role);
    }

    /**
     * 根据ID查找角色
     */
    @Transactional(readOnly = true)
    public PlayerRoleVO findById(Integer roleId) {
        Optional<PlayerRole> roleOpt = playerRoleRepository.findById(roleId);
        if (!roleOpt.isPresent()) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
        return convertToVO(roleOpt.get());
    }

    /**
     * 根据角色名查找角色
     */
    @Transactional(readOnly = true)
    public PlayerRoleVO findByName(String roleName) {
        Optional<PlayerRole> roleOpt = playerRoleRepository.findByName(roleName);
        if (!roleOpt.isPresent()) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
        return convertToVO(roleOpt.get());
    }

    /**
     * 根据用户ID查找角色列表
     */
    @Transactional(readOnly = true)
    public List<PlayerRoleVO> findByUserId(Integer userId) {
        List<PlayerRole> roles = playerRoleRepository.findByUserId(userId);
        return roles.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 角色登录
     */
    public void roleLogin(Integer roleId) {
        logger.info("角色登录: roleId={}", roleId);

        int updated = playerRoleRepository.updateLoginStatus(roleId, 1);
        if (updated == 0) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
    }

    /**
     * 角色登出
     */
    public void roleLogout(Integer roleId) {
        logger.info("角色登出: roleId={}", roleId);

        int updated = playerRoleRepository.updateLoginStatus(roleId, 0);
        if (updated == 0) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
    }

    /**
     * 更新角色位置
     */
    public void updateLocation(Integer roleId, String map) {
        logger.info("更新角色位置: roleId={}, map={}", roleId, map);

        int updated = playerRoleRepository.updateLocation(roleId, map);
        if (updated == 0) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
    }

    /**
     * 更新角色等级和经验
     */
    public void updateGradeAndExp(Integer roleId, Integer grade, String experience) {
        logger.info("更新角色等级和经验: roleId={}, grade={}, exp={}", roleId, grade, experience);

        int updated = playerRoleRepository.updateGradeAndExp(roleId, grade, experience);
        if (updated == 0) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
    }

    /**
     * 更新角色HP和MP
     */
    public void updateHpAndMp(Integer roleId, Integer hp, Integer mp) {
        logger.info("更新角色HP和MP: roleId={}, hp={}, mp={}", roleId, hp, mp);

        int updated = playerRoleRepository.updateHpAndMp(roleId, hp, mp);
        if (updated == 0) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
    }

    /**
     * 更新角色金钱
     */
    public void updateMoney(Integer roleId, String silver, String copper) {
        logger.info("更新角色金钱: roleId={}, silver={}, copper={}", roleId, silver, copper);

        int updated = playerRoleRepository.updateMoney(roleId, silver, copper);
        if (updated == 0) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
    }

    /**
     * 软删除角色
     */
    public void deleteRole(Integer roleId) {
        logger.info("删除角色: roleId={}", roleId);

        int updated = playerRoleRepository.softDeleteRole(roleId, LocalDateTime.now());
        if (updated == 0) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
        logger.info("角色删除成功: roleId={}", roleId);
    }

    /**
     * 恢复删除的角色
     */
    public void restoreRole(Integer roleId) {
        logger.info("恢复角色: roleId={}", roleId);

        int updated = playerRoleRepository.restoreRole(roleId);
        if (updated == 0) {
            throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
        }
        logger.info("角色恢复成功: roleId={}", roleId);
    }

    /**
     * 获取等级排行榜
     */
    @Transactional(readOnly = true)
    public List<PlayerRoleVO> getGradeRanking(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<PlayerRole> rolePage = playerRoleRepository.findGradeRanking(pageable);
        return rolePage.getContent().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取PK排行榜
     */
    @Transactional(readOnly = true)
    public List<PlayerRoleVO> getPkRanking(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<PlayerRole> rolePage = playerRoleRepository.findPkRanking(pageable);
        return rolePage.getContent().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取杀人数排行榜
     */
    @Transactional(readOnly = true)
    public List<PlayerRoleVO> getKillRanking(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<PlayerRole> rolePage = playerRoleRepository.findKillRanking(pageable);
        return rolePage.getContent().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取在线角色数量
     */
    @Transactional(readOnly = true)
    public long getOnlineRoleCount() {
        return playerRoleRepository.countOnlineRoles();
    }

    /**
     * 获取今日创建的角色数量
     */
    @Transactional(readOnly = true)
    public long getTodayCreatedRoleCount() {
        return playerRoleRepository.countTodayCreatedRoles();
    }

    /**
     * 根据场景查找在线角色
     */
    @Transactional(readOnly = true)
    public List<PlayerRoleVO> findOnlineRolesByMap(String map) {
        List<PlayerRole> roles = playerRoleRepository.findOnlineRolesByMap(map);
        return roles.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 初始化角色属性
     */
    private void initializeRoleAttributes(PlayerRole role) {
        // 根据性别和种族设置初始属性
        if (role.getSex() == 1) { // 男性
            role.setForce(role.getForce() + 2);
            role.setPhysique(role.getPhysique() + 1);
        } else { // 女性
            role.setAgile(role.getAgile() + 2);
            role.setSavvy(role.getSavvy() + 1);
        }

        // 根据种族设置属性
        if (role.getRace() == 2) { // 妖族
            role.setSavvy(role.getSavvy() + 2);
            role.setAgile(role.getAgile() + 1);
        }

        // 设置初始场景
        if (role.getMap() == null) {
            role.setMap("1001"); // 默认新手村
        }

        // 设置门派名称
        if (role.getSchool() != null) {
            switch (role.getSchool()) {
                case "少林":
                    role.setSchoolName("少林寺");
                    break;
                case "武当":
                    role.setSchoolName("武当派");
                    break;
                case "峨眉":
                    role.setSchoolName("峨眉派");
                    break;
                default:
                    role.setSchoolName(role.getSchool());
                    break;
            }
        }

        // 设置新手状态
        role.setPlayerStateByNew(1);
    }

    /**
     * 转换为VO对象
     */
    private PlayerRoleVO convertToVO(PlayerRole role) {
        PlayerRoleVO roleVO = new PlayerRoleVO();
        BeanUtils.copyProperties(role, roleVO);
        return roleVO;
    }

    /**
     * 更新角色信息
     */
    public PlayerRole updateRole(PlayerRole role) {
        return playerRoleRepository.save(role);
    }

    /**
     * 根据ID查找角色实体
     */
    public PlayerRole findEntityById(Integer roleId) {
        return playerRoleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException(ResultCode.ROLE_NOT_FOUND));
    }
}
