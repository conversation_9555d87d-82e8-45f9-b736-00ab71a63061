package com.honghuang.game.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * 排行榜配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ranking")
public class RankingConfig {
    
    /**
     * 排行榜默认显示数量
     */
    private int defaultLimit = 10;
    
    /**
     * 排行榜最大显示数量
     */
    private int maxLimit = 100;
    
    /**
     * 是否启用排行榜缓存
     */
    private boolean cacheEnabled = true;
    
    /**
     * 排行榜缓存过期时间（秒）
     */
    private int cacheExpireSeconds = 300;
    
    /**
     * 是否启用实时更新
     */
    private boolean realTimeUpdate = true;
    
    /**
     * 批量更新间隔（秒）
     */
    private int batchUpdateInterval = 60;
    
    /**
     * 是否启用排行榜奖励
     */
    private boolean rewardEnabled = true;
    
    /**
     * 排行榜奖励发放时间（cron表达式）
     */
    private String rewardCron = "0 0 0 * * ?";
    
    /**
     * 第一名奖励铜钱
     */
    private long firstRewardCopper = 100000;
    
    /**
     * 前三名奖励铜钱
     */
    private long topThreeRewardCopper = 50000;
    
    /**
     * 前十名奖励铜钱
     */
    private long topTenRewardCopper = 10000;
}
