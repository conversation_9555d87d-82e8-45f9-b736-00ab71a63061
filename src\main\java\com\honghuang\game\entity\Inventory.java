package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 背包物品实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "inventory")
public class Inventory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 角色ID
     */
    @Column(name = "role_id")
    private Integer roleId;

    /**
     * 物品ID
     */
    @Column(name = "item_id")
    private Integer itemId;

    /**
     * 背包位置索引 0-99（背包100格）
     */
    @Column(name = "slot_index")
    private Integer slotIndex;

    /**
     * 物品数量
     */
    @Column(name = "quantity")
    private Integer quantity = 1;

    /**
     * 是否绑定 0未绑定 1已绑定
     */
    @Column(name = "bound")
    private Integer bound = 0;

    /**
     * 获得时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "obtain_time")
    private LocalDateTime obtainTime;

    /**
     * 最后使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_use_time")
    private LocalDateTime lastUseTime;

    /**
     * 物品信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "item_id", insertable = false, updatable = false)
    private Item item;

    // 业务方法

    /**
     * 是否绑定
     */
    public boolean isBound() {
        return bound == 1;
    }

    /**
     * 绑定物品
     */
    public void bind() {
        this.bound = 1;
    }

    /**
     * 是否可以堆叠
     */
    public boolean canStack() {
        return item != null && item.isStackable();
    }

    /**
     * 是否可以增加数量
     */
    public boolean canAddQuantity(Integer amount) {
        if (!canStack()) {
            return false;
        }
        return quantity + amount <= item.getMaxStack();
    }

    /**
     * 增加数量
     */
    public boolean addQuantity(Integer amount) {
        if (!canAddQuantity(amount)) {
            return false;
        }
        this.quantity += amount;
        return true;
    }

    /**
     * 减少数量
     */
    public boolean reduceQuantity(Integer amount) {
        if (quantity < amount) {
            return false;
        }
        this.quantity -= amount;
        return true;
    }

    /**
     * 是否可以交易
     */
    public boolean canTrade() {
        return item != null && item.isTradeable() && !isBound();
    }

    /**
     * 是否可以丢弃
     */
    public boolean canDrop() {
        return item != null && item.isDroppable();
    }

    /**
     * 是否可以使用
     */
    public boolean canUse(PlayerRole role) {
        if (item == null || !item.hasEffect()) {
            return false;
        }
        return item.canUse(role);
    }

    /**
     * 使用物品
     */
    public void use() {
        this.lastUseTime = LocalDateTime.now();
        if (item != null && item.isConsumable()) {
            reduceQuantity(1);
        }
    }

    /**
     * 获取物品完整名称
     */
    public String getFullName() {
        if (item == null) {
            return "未知物品";
        }
        String name = item.getFullName();
        if (quantity > 1) {
            name += " x" + quantity;
        }
        if (isBound()) {
            name += " (绑定)";
        }
        return name;
    }

    /**
     * 获取物品总价值
     */
    public Long getTotalValue() {
        if (item == null) {
            return 0L;
        }
        return item.getSellPrice() * quantity;
    }

    /**
     * 是否为空槽位
     */
    public boolean isEmpty() {
        return item == null || quantity <= 0;
    }

    /**
     * 清空槽位
     */
    public void clear() {
        this.itemId = null;
        this.quantity = 0;
        this.item = null;
    }

    /**
     * 移动到新位置
     */
    public void moveTo(Integer newSlotIndex) {
        this.slotIndex = newSlotIndex;
    }

    /**
     * 分割物品
     */
    public Inventory split(Integer splitQuantity) {
        if (!canStack() || quantity <= splitQuantity) {
            return null;
        }

        Inventory newInventory = new Inventory();
        newInventory.setRoleId(this.roleId);
        newInventory.setItemId(this.itemId);
        newInventory.setQuantity(splitQuantity);
        newInventory.setBound(this.bound);
        newInventory.setObtainTime(LocalDateTime.now());

        this.quantity -= splitQuantity;

        return newInventory;
    }

    /**
     * 合并物品
     */
    public boolean merge(Inventory other) {
        if (other == null || !this.itemId.equals(other.itemId) || !canStack()) {
            return false;
        }

        if (!canAddQuantity(other.quantity)) {
            return false;
        }

        this.quantity += other.quantity;
        return true;
    }

    /**
     * 获取物品获得天数
     */
    public Long getObtainDays() {
        if (obtainTime == null) {
            return 0L;
        }
        return java.time.Duration.between(obtainTime, LocalDateTime.now()).toDays();
    }

    /**
     * 获取上次使用天数
     */
    public Long getLastUseDays() {
        if (lastUseTime == null) {
            return null;
        }
        return java.time.Duration.between(lastUseTime, LocalDateTime.now()).toDays();
    }

    /**
     * 是否为新获得的物品
     */
    public boolean isNewItem() {
        return getObtainDays() <= 1; // 1天内获得的算新物品
    }

    /**
     * 检查物品是否过期（如果有过期机制）
     */
    public boolean isExpired() {
        // 这里可以根据物品类型实现过期逻辑
        // 目前返回false，表示物品不会过期
        return false;
    }
}
