package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.Warehouse;
import com.honghuang.game.service.WarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 仓库控制器
 */
@RestController
@RequestMapping("/api/warehouse")
public class WarehouseController {

    @Autowired
    private WarehouseService warehouseService;

    /**
     * 获取仓库物品列表
     */
    @GetMapping("/list")
    public Result<List<Warehouse>> getWarehouse(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Warehouse> warehouse = warehouseService.getWarehouse(currentRoleId);
            return Result.success("获取仓库列表成功", warehouse);
        } catch (Exception e) {
            return Result.error(500, "获取仓库列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定页的仓库物品
     */
    @GetMapping("/page/{pageNumber}")
    public Result<List<Warehouse>> getWarehousePage(@PathVariable Integer pageNumber, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Warehouse> warehouse = warehouseService.getWarehousePage(currentRoleId, pageNumber);
            return Result.success("获取仓库页面成功", warehouse);
        } catch (Exception e) {
            return Result.error(500, "获取仓库页面失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定位置的仓库物品
     */
    @GetMapping("/slot/{pageNumber}/{slotIndex}")
    public Result<Warehouse> getWarehouseSlot(@PathVariable Integer pageNumber, @PathVariable Integer slotIndex, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            return warehouseService.getWarehouseSlot(currentRoleId, pageNumber, slotIndex)
                    .map(warehouse -> Result.success("获取仓库槽位成功", warehouse))
                    .orElse(Result.error(404, "槽位为空"));
        } catch (Exception e) {
            return Result.error(500, "获取仓库槽位失败: " + e.getMessage());
        }
    }

    /**
     * 存入物品到仓库
     */
    @PostMapping("/deposit")
    public Result<Void> depositItem(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer inventorySlotIndex = request.get("inventorySlotIndex");
            Integer quantity = request.get("quantity");
            
            if (inventorySlotIndex == null || quantity == null) {
                return Result.error(400, "参数不完整");
            }
            
            boolean success = warehouseService.depositItem(currentRoleId, inventorySlotIndex, quantity);
            if (success) {
                return Result.success("存入物品成功");
            } else {
                return Result.error(400, "存入物品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "存入物品失败: " + e.getMessage());
        }
    }

    /**
     * 从仓库取出物品
     */
    @PostMapping("/withdraw")
    public Result<Void> withdrawItem(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer pageNumber = request.get("pageNumber");
            Integer slotIndex = request.get("slotIndex");
            Integer quantity = request.get("quantity");
            
            if (pageNumber == null || slotIndex == null || quantity == null) {
                return Result.error(400, "参数不完整");
            }
            
            boolean success = warehouseService.withdrawItem(currentRoleId, pageNumber, slotIndex, quantity);
            if (success) {
                return Result.success("取出物品成功");
            } else {
                return Result.error(400, "取出物品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "取出物品失败: " + e.getMessage());
        }
    }

    /**
     * 移动仓库物品位置
     */
    @PostMapping("/move")
    public Result<Void> moveWarehouseItem(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer fromPage = request.get("fromPage");
            Integer fromSlot = request.get("fromSlot");
            Integer toPage = request.get("toPage");
            Integer toSlot = request.get("toSlot");
            
            if (fromPage == null || fromSlot == null || toPage == null || toSlot == null) {
                return Result.error(400, "参数不完整");
            }
            
            boolean success = warehouseService.moveWarehouseItem(currentRoleId, fromPage, fromSlot, toPage, toSlot);
            if (success) {
                return Result.success("移动仓库物品成功");
            } else {
                return Result.error(400, "移动仓库物品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "移动仓库物品失败: " + e.getMessage());
        }
    }

    /**
     * 整理仓库
     */
    @PostMapping("/organize")
    public Result<Void> organizeWarehouse(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = warehouseService.organizeWarehouse(currentRoleId);
            if (success) {
                return Result.success("整理仓库成功");
            } else {
                return Result.error(400, "整理仓库失败");
            }
        } catch (Exception e) {
            return Result.error(500, "整理仓库失败: " + e.getMessage());
        }
    }

    /**
     * 获取仓库统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getWarehouseStatistics(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, Object> stats = warehouseService.getWarehouseStatistics(currentRoleId);
            return Result.success("获取仓库统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取仓库统计失败: " + e.getMessage());
        }
    }
}
