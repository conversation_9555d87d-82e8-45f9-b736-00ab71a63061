package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 生活技能配方实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "life_skill_recipe")
public class LifeSkillRecipe {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 配方名称
     */
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 技能类型 1采集 2制造 3炼药 4锻造 5烹饪 6附魔
     */
    @Column(name = "skill_type")
    private Integer skillType;

    /**
     * 需要技能等级
     */
    @Column(name = "required_skill_level")
    private Integer requiredSkillLevel;

    /**
     * 产出物品ID
     */
    @Column(name = "output_item_id")
    private Integer outputItemId;

    /**
     * 产出数量
     */
    @Column(name = "output_quantity")
    private Integer outputQuantity = 1;

    /**
     * 材料1 ID
     */
    @Column(name = "material1_id")
    private Integer material1Id;

    /**
     * 材料1 数量
     */
    @Column(name = "material1_quantity")
    private Integer material1Quantity;

    /**
     * 材料2 ID
     */
    @Column(name = "material2_id")
    private Integer material2Id;

    /**
     * 材料2 数量
     */
    @Column(name = "material2_quantity")
    private Integer material2Quantity;

    /**
     * 材料3 ID
     */
    @Column(name = "material3_id")
    private Integer material3Id;

    /**
     * 材料3 数量
     */
    @Column(name = "material3_quantity")
    private Integer material3Quantity;

    /**
     * 材料4 ID
     */
    @Column(name = "material4_id")
    private Integer material4Id;

    /**
     * 材料4 数量
     */
    @Column(name = "material4_quantity")
    private Integer material4Quantity;

    /**
     * 制作时间（秒）
     */
    @Column(name = "craft_time")
    private Integer craftTime = 10;

    /**
     * 基础成功率
     */
    @Column(name = "base_success_rate")
    private Double baseSuccessRate = 80.0;

    /**
     * 经验奖励
     */
    @Column(name = "exp_reward")
    private Long expReward = 10L;

    /**
     * 熟练度奖励
     */
    @Column(name = "proficiency_reward")
    private Integer proficiencyReward = 1;

    /**
     * 配方描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 产出物品信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "output_item_id", insertable = false, updatable = false)
    private Item outputItem;

    /**
     * 材料1信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material1_id", insertable = false, updatable = false)
    private Item material1;

    /**
     * 材料2信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material2_id", insertable = false, updatable = false)
    private Item material2;

    /**
     * 材料3信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material3_id", insertable = false, updatable = false)
    private Item material3;

    /**
     * 材料4信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material4_id", insertable = false, updatable = false)
    private Item material4;

    // 业务方法

    /**
     * 获取技能类型名称
     */
    public String getSkillTypeName() {
        switch (skillType) {
            case 1: return "采集";
            case 2: return "制造";
            case 3: return "炼药";
            case 4: return "锻造";
            case 5: return "烹饪";
            case 6: return "附魔";
            default: return "未知";
        }
    }

    /**
     * 是否激活
     */
    public boolean isActive() {
        return active == 1;
    }

    /**
     * 获取所有材料需求
     */
    public java.util.Map<Integer, Integer> getMaterialRequirements() {
        java.util.Map<Integer, Integer> materials = new java.util.HashMap<>();
        
        if (material1Id != null && material1Quantity != null && material1Quantity > 0) {
            materials.put(material1Id, material1Quantity);
        }
        if (material2Id != null && material2Quantity != null && material2Quantity > 0) {
            materials.put(material2Id, material2Quantity);
        }
        if (material3Id != null && material3Quantity != null && material3Quantity > 0) {
            materials.put(material3Id, material3Quantity);
        }
        if (material4Id != null && material4Quantity != null && material4Quantity > 0) {
            materials.put(material4Id, material4Quantity);
        }
        
        return materials;
    }

    /**
     * 检查角色是否满足制作条件
     */
    public boolean canCraft(LifeSkill skill) {
        if (!isActive()) return false;
        if (!skill.getSkillType().equals(this.skillType)) return false;
        if (skill.getSkillLevel() < requiredSkillLevel) return false;
        if (!skill.isAvailable()) return false;
        if (!skill.isCooldownFinished()) return false;
        
        return true;
    }

    /**
     * 计算实际成功率
     */
    public Double calculateSuccessRate(LifeSkill skill) {
        if (!canCraft(skill)) return 0.0;
        
        double rate = baseSuccessRate;
        
        // 技能等级加成
        int levelDiff = skill.getSkillLevel() - requiredSkillLevel;
        rate += levelDiff * 2.0; // 每高1级增加2%成功率
        
        // 熟练度加成
        double proficiencyBonus = (double) skill.getProficiency() / skill.getMaxProficiency() * 20.0;
        rate += proficiencyBonus;
        
        return Math.min(95.0, Math.max(5.0, rate));
    }

    /**
     * 计算制作时间
     */
    public Integer calculateCraftTime(LifeSkill skill) {
        double time = craftTime;
        
        // 技能等级减少制作时间
        int levelDiff = skill.getSkillLevel() - requiredSkillLevel;
        time *= Math.max(0.5, 1.0 - levelDiff * 0.05); // 每高1级减少5%时间
        
        return Math.max(1, (int) time);
    }

    /**
     * 计算经验奖励
     */
    public Long calculateExpReward(LifeSkill skill) {
        long exp = expReward;
        
        // 技能等级影响经验获得
        if (skill.getSkillLevel() > requiredSkillLevel + 10) {
            exp = exp / 2; // 等级过高时经验减半
        }
        
        return Math.max(1L, exp);
    }

    /**
     * 获取配方完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(name).append(" [").append(getSkillTypeName()).append("]");
        sb.append(" 需要等级: ").append(requiredSkillLevel);
        
        if (outputItem != null) {
            sb.append(" 产出: ").append(outputItem.getName());
            if (outputQuantity > 1) {
                sb.append(" x").append(outputQuantity);
            }
        }
        
        sb.append(" 成功率: ").append(String.format("%.1f", baseSuccessRate)).append("%");
        sb.append(" 制作时间: ").append(craftTime).append("秒");
        
        return sb.toString();
    }

    /**
     * 获取材料列表描述
     */
    public String getMaterialsDescription() {
        StringBuilder sb = new StringBuilder();
        java.util.Map<Integer, Integer> materials = getMaterialRequirements();
        
        boolean first = true;
        for (java.util.Map.Entry<Integer, Integer> entry : materials.entrySet()) {
            if (!first) sb.append(", ");
            
            // 这里简化处理，实际应该查询物品名称
            sb.append("物品").append(entry.getKey()).append(" x").append(entry.getValue());
            first = false;
        }
        
        return sb.toString();
    }

    /**
     * 检查是否为高级配方
     */
    public boolean isAdvancedRecipe() {
        return requiredSkillLevel >= 60;
    }

    /**
     * 检查是否为专家配方
     */
    public boolean isMasterRecipe() {
        return requiredSkillLevel >= 80;
    }

    /**
     * 获取配方难度等级
     */
    public String getDifficultyLevel() {
        if (requiredSkillLevel >= 80) return "专家";
        if (requiredSkillLevel >= 60) return "高级";
        if (requiredSkillLevel >= 40) return "中级";
        if (requiredSkillLevel >= 20) return "初级";
        return "新手";
    }

    /**
     * 计算材料成本（基于材料数量）
     */
    public Integer calculateMaterialCost() {
        int cost = 0;
        java.util.Map<Integer, Integer> materials = getMaterialRequirements();
        
        for (Integer quantity : materials.values()) {
            cost += quantity * 10; // 简化计算，每个材料基础价值10
        }
        
        return cost;
    }
}
