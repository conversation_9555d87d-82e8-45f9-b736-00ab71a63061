2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE player_role ADD INDEX [*]IF NOT EXISTS idx_current_scene (current_scene)"; expected "identifier"; SQL statement:
ALTER TABLE player_role ADD INDEX IF NOT EXISTS idx_current_scene (current_scene) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE player_role ADD INDEX [*]IF NOT EXISTS idx_is_online (is_online)"; expected "identifier"; SQL statement:
ALTER TABLE player_role ADD INDEX IF NOT EXISTS idx_is_online (is_online) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE goods_info ADD INDEX [*]IF NOT EXISTS idx_goods_type (goods_type)"; expected "identifier"; SQL statement:
ALTER TABLE goods_info ADD INDEX IF NOT EXISTS idx_goods_type (goods_type) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE goods_info ADD INDEX [*]IF NOT EXISTS idx_goods_level (goods_level)"; expected "identifier"; SQL statement:
ALTER TABLE goods_info ADD INDEX IF NOT EXISTS idx_goods_level (goods_level) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE goods_info ADD INDEX [*]IF NOT EXISTS idx_goods_quality (goods_quality)"; expected "identifier"; SQL statement:
ALTER TABLE goods_info ADD INDEX IF NOT EXISTS idx_goods_quality (goods_quality) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE goods_info ADD INDEX [*]IF NOT EXISTS idx_is_active (is_active)"; expected "identifier"; SQL statement:
ALTER TABLE goods_info ADD INDEX IF NOT EXISTS idx_is_active (is_active) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE u_part_goods ADD INDEX [*]IF NOT EXISTS idx_goods_position (goods_position)"; expected "identifier"; SQL statement:
ALTER TABLE u_part_goods ADD INDEX IF NOT EXISTS idx_goods_position (goods_position) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE u_part_goods ADD INDEX [*]IF NOT EXISTS idx_position_index (position_index)"; expected "identifier"; SQL statement:
ALTER TABLE u_part_goods ADD INDEX IF NOT EXISTS idx_position_index (position_index) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE scene_info ADD INDEX [*]IF NOT EXISTS idx_scene_type (scene_type)"; expected "identifier"; SQL statement:
ALTER TABLE scene_info ADD INDEX IF NOT EXISTS idx_scene_type (scene_type) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE scene_info ADD INDEX [*]IF NOT EXISTS idx_level_limit (level_limit)"; expected "identifier"; SQL statement:
ALTER TABLE scene_info ADD INDEX IF NOT EXISTS idx_level_limit (level_limit) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE scene_info ADD INDEX [*]IF NOT EXISTS idx_is_active (is_active)"; expected "identifier"; SQL statement:
ALTER TABLE scene_info ADD INDEX IF NOT EXISTS idx_is_active (is_active) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE npc_info ADD INDEX [*]IF NOT EXISTS idx_npc_type (npc_type)"; expected "identifier"; SQL statement:
ALTER TABLE npc_info ADD INDEX IF NOT EXISTS idx_npc_type (npc_type) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE npc_info ADD INDEX [*]IF NOT EXISTS idx_npc_level (npc_level)"; expected "identifier"; SQL statement:
ALTER TABLE npc_info ADD INDEX IF NOT EXISTS idx_npc_level (npc_level) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "ALTER TABLE npc_info ADD INDEX [*]IF NOT EXISTS idx_is_active (is_active)"; expected "identifier"; SQL statement:
ALTER TABLE npc_info ADD INDEX IF NOT EXISTS idx_is_active (is_active) [42001-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "goods_info" not found; SQL statement:
SELECT COUNT(*) FROM goods_info WHERE is_active = 1 [42102-214]
2025-07-27 17:07:10 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "scene_info" not found; SQL statement:
SELECT COUNT(*) FROM scene_info WHERE is_active = 1 [42102-214]
