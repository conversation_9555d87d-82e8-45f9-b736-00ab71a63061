package com.honghuang.game.service;

import com.honghuang.game.dto.BattleResultDTO;
import com.honghuang.game.entity.BattleRecord;
import com.honghuang.game.entity.Monster;
import com.honghuang.game.repository.BattleRecordRepository;
import com.honghuang.game.repository.MonsterRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 战斗服务类
 */
@Service
@Transactional
public class BattleService {
    
    @Autowired
    private MonsterRepository monsterRepository;
    
    @Autowired
    private BattleRecordRepository battleRecordRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    @Autowired
    private QuestService questService;

    @Autowired
    private PetService petService;
    
    /**
     * 开始战斗
     */
    public BattleResultDTO startBattle(Integer playerRoleId, Integer monsterId) {
        // 获取怪物信息
        Monster monster = monsterRepository.findById(monsterId)
                .orElseThrow(() -> new RuntimeException("怪物不存在"));
        
        if (!monster.canAttack()) {
            throw new RuntimeException("该怪物无法攻击");
        }
        
        // 模拟玩家数据（实际应该从数据库获取）
        Map<String, Object> playerData = getPlayerData(playerRoleId);
        
        // 执行战斗逻辑
        BattleResultDTO result = executeBattle(playerData, monster, playerRoleId);
        
        // 保存战斗记录
        try {
            saveBattleRecord(playerRoleId, monster, result, playerData);
        } catch (Exception e) {
            // 如果保存失败，记录日志但不影响战斗结果
            System.err.println("保存战斗记录失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 执行战斗逻辑
     */
    private BattleResultDTO executeBattle(Map<String, Object> playerData, Monster monster, Integer playerRoleId) {
        BattleResultDTO result = new BattleResultDTO();
        List<String> battleLog = new ArrayList<>();
        
        // 玩家属性
        int playerHp = (Integer) playerData.get("hp");
        int playerMp = (Integer) playerData.get("mp");
        int playerAttack = (Integer) playerData.get("attack");
        int playerDefense = (Integer) playerData.get("defense");
        int playerAgility = (Integer) playerData.get("agility");
        int playerLevel = (Integer) playerData.get("level");
        
        // 怪物属性
        int monsterHp = monster.getMaxHp();
        int monsterAttack = monster.getAttack();
        int monsterDefense = monster.getDefense();
        int monsterAgility = monster.getAgility() != null ? monster.getAgility() : 10;
        
        int rounds = 0;
        int originalPlayerHp = playerHp;
        
        battleLog.add(String.format("战斗开始！%s(Lv.%d) VS %s(Lv.%d)", 
                playerData.get("name"), playerLevel, monster.getName(), monster.getLevel()));
        
        // 战斗循环
        while (playerHp > 0 && monsterHp > 0 && rounds < 50) {
            rounds++;
            battleLog.add(String.format("=== 第%d回合 ===", rounds));
            
            // 计算先手（敏捷高的先攻击）
            boolean playerFirst = playerAgility >= monsterAgility;
            
            if (playerFirst) {
                // 玩家攻击
                int damage = calculateDamage(playerAttack, monsterDefense);
                monsterHp = Math.max(0, monsterHp - damage);
                battleLog.add(String.format("%s 攻击 %s，造成 %d 点伤害", 
                        playerData.get("name"), monster.getName(), damage));
                
                if (monsterHp <= 0) {
                    battleLog.add(String.format("%s 被击败了！", monster.getName()));
                    break;
                }
                
                // 怪物反击
                int monsterDamage = calculateDamage(monsterAttack, playerDefense);
                playerHp = Math.max(0, playerHp - monsterDamage);
                battleLog.add(String.format("%s 攻击 %s，造成 %d 点伤害", 
                        monster.getName(), playerData.get("name"), monsterDamage));
                
                if (playerHp <= 0) {
                    battleLog.add(String.format("%s 被击败了！", playerData.get("name")));
                    break;
                }
            } else {
                // 怪物先攻击
                int monsterDamage = calculateDamage(monsterAttack, playerDefense);
                playerHp = Math.max(0, playerHp - monsterDamage);
                battleLog.add(String.format("%s 攻击 %s，造成 %d 点伤害", 
                        monster.getName(), playerData.get("name"), monsterDamage));
                
                if (playerHp <= 0) {
                    battleLog.add(String.format("%s 被击败了！", playerData.get("name")));
                    break;
                }
                
                // 玩家反击
                int damage = calculateDamage(playerAttack, monsterDefense);
                monsterHp = Math.max(0, monsterHp - damage);
                battleLog.add(String.format("%s 攻击 %s，造成 %d 点伤害", 
                        playerData.get("name"), monster.getName(), damage));
                
                if (monsterHp <= 0) {
                    battleLog.add(String.format("%s 被击败了！", monster.getName()));
                    break;
                }
            }
        }
        
        // 设置战斗结果
        boolean victory = monsterHp <= 0;
        result.setVictory(victory);
        result.setRounds(rounds);
        result.setBattleLog(battleLog);
        result.setPlayerHpAfter(playerHp);
        result.setPlayerMpAfter(playerMp);
        result.setMonsterName(monster.getName());
        result.setMonsterLevel(monster.getLevel());
        
        if (victory) {
            // 计算奖励
            int expGained = calculateExpReward(playerLevel, monster.getLevel(), monster.getExpReward().intValue());
            int copperGained = calculateCopperReward(monster.getCopperReward().intValue());
            List<Map<String, Object>> itemsDropped = calculateItemDrops(monster);
            
            result.setExpGained(expGained);
            result.setCopperGained(copperGained);
            result.setItemsDropped(itemsDropped);

            // 给角色增加经验值（这会自动处理升级）
            if (expGained > 0) {
                try {
                    playerRoleService.addExperience(playerRoleId, (long) expGained);
                    System.out.println("角色获得经验值: playerRoleId=" + playerRoleId + ", exp=" + expGained);
                } catch (Exception e) {
                    System.out.println("增加经验值失败: playerRoleId=" + playerRoleId + ", exp=" + expGained + ", error=" + e.getMessage());
                }
            }

            // 更新杀怪任务进度
            try {
                questService.handleMonsterKilled(playerRoleId, monster.getId());
                System.out.println("更新杀怪任务进度: playerRoleId=" + playerRoleId + ", monsterId=" + monster.getId());
            } catch (Exception e) {
                System.out.println("更新杀怪任务进度失败: " + e.getMessage());
            }

            // 给出战宠物增加经验
            try {
                petService.addPetExperience(playerRoleId, (long) (expGained / 2)); // 宠物获得一半经验
                System.out.println("宠物获得经验值: playerRoleId=" + playerRoleId + ", exp=" + (expGained / 2));
            } catch (Exception e) {
                System.out.println("宠物增加经验失败: " + e.getMessage());
            }

            battleLog.add(String.format("获得经验: %d", expGained));
            battleLog.add(String.format("获得铜钱: %d", copperGained));
            
            if (!itemsDropped.isEmpty()) {
                battleLog.add("掉落物品: " + itemsDropped.toString());
            }
        }
        
        return result;
    }
    
    /**
     * 计算伤害
     */
    private int calculateDamage(int attack, int defense) {
        int baseDamage = Math.max(1, attack - defense);
        // 添加随机因子 (80%-120%)
        double randomFactor = 0.8 + Math.random() * 0.4;
        return (int) (baseDamage * randomFactor);
    }
    
    /**
     * 计算经验奖励
     */
    private int calculateExpReward(int playerLevel, int monsterLevel, Integer baseExp) {
        if (baseExp == null) baseExp = 10;
        
        // 根据等级差调整经验
        int levelDiff = monsterLevel - playerLevel;
        double multiplier = 1.0;
        
        if (levelDiff > 5) {
            multiplier = 1.5; // 挑战高等级怪物额外奖励
        } else if (levelDiff < -5) {
            multiplier = 0.5; // 挑战低等级怪物减少奖励
        }
        
        return (int) (baseExp * multiplier);
    }
    
    /**
     * 计算铜钱奖励
     */
    private int calculateCopperReward(Integer baseMoney) {
        if (baseMoney == null) baseMoney = 5;
        
        // 添加随机因子 (80%-120%)
        double randomFactor = 0.8 + Math.random() * 0.4;
        return (int) (baseMoney * randomFactor);
    }
    
    /**
     * 计算物品掉落
     */
    private List<Map<String, Object>> calculateItemDrops(Monster monster) {
        List<Map<String, Object>> drops = new ArrayList<>();

        // TODO: 暂时返回空列表，因为Monster实体类中没有掉落物品相关字段
        // 可以根据怪物等级和类型随机生成一些基础掉落物品

        return drops;
    }
    
    /**
     * 检查单个掉落
     */
    private void checkDrop(List<Map<String, Object>> drops, Integer itemId, Integer probability) {
        if (itemId != null && probability != null && probability > 0) {
            int random = (int) (Math.random() * 100) + 1;
            if (random <= probability) {
                Map<String, Object> drop = new HashMap<>();
                drop.put("itemId", itemId);
                drop.put("quantity", 1);
                drops.add(drop);
            }
        }
    }
    
    /**
     * 获取玩家数据（模拟）
     */
    private Map<String, Object> getPlayerData(Integer playerRoleId) {
        Map<String, Object> playerData = new HashMap<>();
        playerData.put("id", playerRoleId);
        playerData.put("name", "测试角色");
        playerData.put("level", 1);
        playerData.put("hp", 100);
        playerData.put("mp", 50);
        playerData.put("attack", 15);
        playerData.put("defense", 8);
        playerData.put("agility", 10);
        return playerData;
    }
    
    /**
     * 保存战斗记录
     */
    private void saveBattleRecord(Integer playerRoleId, Monster monster, BattleResultDTO result, Map<String, Object> playerData) {
        BattleRecord record = new BattleRecord();
        record.setPlayerRoleId(playerRoleId);
        record.setMonsterId(monster.getId());
        record.setMonsterName(monster.getName());
        record.setBattleResult(result.isVictory() ? 1 : 0);
        record.setRounds(result.getRounds());
        record.setExpGained(result.getExpGained());
        record.setCopperGained(result.getCopperGained());
        record.setBattleLog(String.join("\n", result.getBattleLog()));
        record.setPlayerHpBefore((Integer) playerData.get("hp"));
        record.setPlayerHpAfter(result.getPlayerHpAfter());
        record.setPlayerLevel((Integer) playerData.get("level"));
        record.setMonsterLevel(monster.getLevel());
        
        battleRecordRepository.save(record);
    }
    
    /**
     * 获取玩家战斗记录
     */
    public List<BattleRecord> getPlayerBattleRecords(Integer playerRoleId, int limit) {
        try {
            List<BattleRecord> records = battleRecordRepository.findByPlayerRoleIdOrderByBattleTimeDesc(playerRoleId);
            return records.size() > limit ? records.subList(0, limit) : records;
        } catch (Exception e) {
            // 如果查询失败，返回空列表
            System.err.println("查询战斗记录失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取可攻击的怪物列表
     */
    public List<Monster> getAttackableMonsters() {
        return monsterRepository.findAttackableMonsters();
    }

    /**
     * 获取所有怪物（调试用）
     */
    public List<Monster> getAllMonsters() {
        return monsterRepository.findAll();
    }
    
    /**
     * 根据场景获取怪物
     */
    public List<Monster> getMonstersByScene(Integer sceneId) {
        return monsterRepository.findBySceneId(sceneId);
    }
}
