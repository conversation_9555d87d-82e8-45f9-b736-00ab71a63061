package com.honghuang.game.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 战斗记录实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "battle_record")
public class BattleRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 玩家角色ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_role_id")
    private PlayerRole playerRole;

    /**
     * 怪物ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "monster_id")
    private Monster monster;

    /**
     * 战斗类型 1:PVE 2:PVP
     */
    @Column(name = "battle_type")
    private Integer battleType = 1;

    /**
     * 战斗结果 1:胜利 2:失败 3:逃跑
     */
    @Column(name = "battle_result")
    private Integer battleResult;

    /**
     * 战斗回合数
     */
    @Column(name = "battle_rounds")
    private Integer battleRounds = 0;

    /**
     * 玩家初始生命值
     */
    @Column(name = "player_initial_hp")
    private Integer playerInitialHp;

    /**
     * 玩家剩余生命值
     */
    @Column(name = "player_remaining_hp")
    private Integer playerRemainingHp;

    /**
     * 怪物初始生命值
     */
    @Column(name = "monster_initial_hp")
    private Integer monsterInitialHp;

    /**
     * 怪物剩余生命值
     */
    @Column(name = "monster_remaining_hp")
    private Integer monsterRemainingHp;

    /**
     * 获得经验值
     */
    @Column(name = "exp_gained")
    private Long expGained = 0L;

    /**
     * 获得铜钱
     */
    @Column(name = "copper_gained")
    private Long copperGained = 0L;

    /**
     * 掉落物品JSON
     */
    @Column(name = "dropped_items", length = 1000)
    private String droppedItems;

    /**
     * 战斗详情JSON
     */
    @Column(name = "battle_details", length = 2000)
    private String battleDetails;

    /**
     * 战斗时长（秒）
     */
    @Column(name = "battle_duration")
    private Integer battleDuration;

    /**
     * 战斗开始时间
     */
    @CreationTimestamp
    @Column(name = "battle_time")
    private LocalDateTime battleTime;

    // 业务方法

    /**
     * 是否胜利
     */
    public boolean isVictory() {
        return battleResult != null && battleResult == 1;
    }

    /**
     * 是否失败
     */
    public boolean isDefeat() {
        return battleResult != null && battleResult == 2;
    }

    /**
     * 是否逃跑
     */
    public boolean isEscape() {
        return battleResult != null && battleResult == 3;
    }

    /**
     * 获取战斗结果名称
     */
    public String getBattleResultName() {
        if (battleResult == null) return "未知";
        switch (battleResult) {
            case 1: return "胜利";
            case 2: return "失败";
            case 3: return "逃跑";
            default: return "未知";
        }
    }

    /**
     * 获取战斗类型名称
     */
    public String getBattleTypeName() {
        if (battleType == null) return "PVE";
        switch (battleType) {
            case 1: return "PVE";
            case 2: return "PVP";
            default: return "PVE";
        }
    }

    /**
     * 计算玩家生命值损失百分比
     */
    public double getPlayerHpLossPercent() {
        if (playerInitialHp == null || playerInitialHp == 0) return 0.0;
        int hpLoss = playerInitialHp - (playerRemainingHp != null ? playerRemainingHp : 0);
        return (double) hpLoss / playerInitialHp * 100;
    }

    /**
     * 计算怪物生命值损失百分比
     */
    public double getMonsterHpLossPercent() {
        if (monsterInitialHp == null || monsterInitialHp == 0) return 0.0;
        int hpLoss = monsterInitialHp - (monsterRemainingHp != null ? monsterRemainingHp : 0);
        return (double) hpLoss / monsterInitialHp * 100;
    }

    /**
     * 计算平均每回合伤害
     */
    public double getAverageDamagePerRound() {
        if (battleRounds == null || battleRounds == 0) return 0.0;
        int totalDamage = (playerInitialHp - (playerRemainingHp != null ? playerRemainingHp : 0)) +
                         (monsterInitialHp - (monsterRemainingHp != null ? monsterRemainingHp : 0));
        return (double) totalDamage / battleRounds;
    }

    /**
     * 是否为快速战斗（少于3回合）
     */
    public boolean isQuickBattle() {
        return battleRounds != null && battleRounds < 3;
    }

    /**
     * 是否为长时间战斗（超过10回合）
     */
    public boolean isLongBattle() {
        return battleRounds != null && battleRounds > 10;
    }

    /**
     * 获取战斗评级
     */
    public String getBattleRating() {
        if (!isVictory()) return "失败";
        
        if (isQuickBattle() && getPlayerHpLossPercent() < 20) {
            return "完美";
        } else if (getPlayerHpLossPercent() < 50) {
            return "优秀";
        } else if (getPlayerHpLossPercent() < 80) {
            return "良好";
        } else {
            return "惊险";
        }
    }
}
