package com.honghuang.game.repository;

import com.honghuang.game.entity.AuctionItem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 拍卖物品数据访问层
 */
@Repository
public interface AuctionItemRepository extends JpaRepository<AuctionItem, Integer> {

    /**
     * 查找进行中的拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP ORDER BY ai.endTime ASC")
    List<AuctionItem> findActiveAuctions();

    /**
     * 分页查找进行中的拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP ORDER BY ai.endTime ASC")
    Page<AuctionItem> findActiveAuctions(Pageable pageable);

    /**
     * 根据卖家ID查找拍卖
     */
    List<AuctionItem> findBySellerIdOrderByStartTimeDesc(Integer sellerId);

    /**
     * 根据最高出价者ID查找拍卖
     */
    List<AuctionItem> findByHighestBidderIdOrderByStartTimeDesc(Integer highestBidderId);

    /**
     * 根据状态查找拍卖
     */
    List<AuctionItem> findByStatusOrderByStartTimeDesc(Integer status);

    /**
     * 根据物品ID查找拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.itemId = :itemId AND ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP ORDER BY ai.currentPrice ASC")
    List<AuctionItem> findActiveAuctionsByItemId(@Param("itemId") Integer itemId);

    /**
     * 根据货币类型查找拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.currencyType = :currencyType AND ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP ORDER BY ai.endTime ASC")
    List<AuctionItem> findActiveAuctionsByCurrency(@Param("currencyType") Integer currencyType);

    /**
     * 根据价格范围查找拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP " +
           "AND ai.currentPrice BETWEEN :minPrice AND :maxPrice ORDER BY ai.currentPrice ASC")
    List<AuctionItem> findActiveAuctionsByPriceRange(@Param("minPrice") Long minPrice, @Param("maxPrice") Long maxPrice);

    /**
     * 查找即将结束的拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime BETWEEN CURRENT_TIMESTAMP AND :cutoffTime ORDER BY ai.endTime ASC")
    List<AuctionItem> findEndingSoonAuctions(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找已过期但未处理的拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime <= CURRENT_TIMESTAMP")
    List<AuctionItem> findExpiredAuctions();

    /**
     * 查找有一口价的拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP " +
           "AND ai.buyoutPrice IS NOT NULL AND ai.buyoutPrice > 0 ORDER BY ai.buyoutPrice ASC")
    List<AuctionItem> findAuctionsWithBuyout();

    /**
     * 查找无人出价的拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP " +
           "AND ai.highestBidderId IS NULL ORDER BY ai.endTime ASC")
    List<AuctionItem> findAuctionsWithoutBids();

    /**
     * 根据物品类型查找拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai JOIN ai.item i WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP " +
           "AND i.type = :itemType ORDER BY ai.endTime ASC")
    List<AuctionItem> findActiveAuctionsByItemType(@Param("itemType") Integer itemType);

    /**
     * 根据物品品质查找拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai JOIN ai.item i WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP " +
           "AND i.quality = :quality ORDER BY ai.currentPrice ASC")
    List<AuctionItem> findActiveAuctionsByQuality(@Param("quality") Integer quality);

    /**
     * 搜索拍卖物品
     */
    @Query("SELECT ai FROM AuctionItem ai JOIN ai.item i WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP " +
           "AND i.name LIKE %:keyword% ORDER BY ai.endTime ASC")
    List<AuctionItem> searchActiveAuctions(@Param("keyword") String keyword);

    /**
     * 更新拍卖状态
     */
    @Modifying
    @Query("UPDATE AuctionItem ai SET ai.status = :status WHERE ai.id = :auctionId")
    int updateStatus(@Param("auctionId") Integer auctionId, @Param("status") Integer status);

    /**
     * 更新最高出价信息
     */
    @Modifying
    @Query("UPDATE AuctionItem ai SET ai.currentPrice = :price, ai.highestBidderId = :bidderId, ai.highestBidderName = :bidderName WHERE ai.id = :auctionId")
    int updateHighestBid(@Param("auctionId") Integer auctionId, @Param("price") Long price, @Param("bidderId") Integer bidderId, @Param("bidderName") String bidderName);

    /**
     * 批量处理过期拍卖
     */
    @Modifying
    @Query("UPDATE AuctionItem ai SET ai.status = 3 WHERE ai.status = 1 AND ai.endTime <= CURRENT_TIMESTAMP AND ai.highestBidderId IS NULL")
    int markExpiredAuctionsAsFailed();

    /**
     * 批量处理过期有出价的拍卖
     */
    @Modifying
    @Query("UPDATE AuctionItem ai SET ai.status = 2, ai.dealTime = CURRENT_TIMESTAMP WHERE ai.status = 1 AND ai.endTime <= CURRENT_TIMESTAMP AND ai.highestBidderId IS NOT NULL")
    int markExpiredAuctionsAsDealt();

    /**
     * 统计卖家的拍卖数量
     */
    @Query("SELECT COUNT(ai) FROM AuctionItem ai WHERE ai.sellerId = :sellerId AND ai.status = :status")
    long countBySellerIdAndStatus(@Param("sellerId") Integer sellerId, @Param("status") Integer status);

    /**
     * 统计进行中的拍卖数量
     */
    @Query("SELECT COUNT(ai) FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP")
    long countActiveAuctions();

    /**
     * 查找热门拍卖（按当前价格排序）
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP ORDER BY ai.currentPrice DESC")
    List<AuctionItem> findPopularAuctions();

    /**
     * 查找最新拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP ORDER BY ai.startTime DESC")
    List<AuctionItem> findLatestAuctions();

    /**
     * 查找价格最低的拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.status = 1 AND ai.endTime > CURRENT_TIMESTAMP ORDER BY ai.currentPrice ASC")
    List<AuctionItem> findCheapestAuctions();

    /**
     * 查找指定时间范围内的拍卖
     */
    @Query("SELECT ai FROM AuctionItem ai WHERE ai.startTime BETWEEN :startTime AND :endTime ORDER BY ai.startTime DESC")
    List<AuctionItem> findAuctionsByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
