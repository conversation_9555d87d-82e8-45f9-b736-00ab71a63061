package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.Inventory;
import com.honghuang.game.entity.Item;
import com.honghuang.game.repository.ItemRepository;
import com.honghuang.game.service.InventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 背包控制器
 */
@RestController
@RequestMapping("/api/inventory")
public class InventoryController {

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private ItemRepository itemRepository;

    /**
     * 测试API - 获取所有物品
     */
    @GetMapping("/test/items")
    public Result<List<Item>> getAllItems() {
        try {
            List<Item> items = itemRepository.findAll();
            return Result.success("获取物品列表成功", items);
        } catch (Exception e) {
            return Result.error(500, "获取物品列表失败: " + e.getMessage());
        }
    }

    /**
     * 测试API - 创建基础物品
     */
    @PostMapping("/test/create-items")
    public Result<String> createTestItems() {
        try {
            // 创建一些基础物品
            Item sword = new Item();
            sword.setName("新手木剑");
            sword.setType(1); // 装备
            sword.setSubType(1); // 武器
            sword.setQuality(1); // 白色品质
            sword.setRequiredLevel(1);
            sword.setDescription("新手专用的木制长剑");
            sword.setIcon("⚔️");
            sword.setSellPrice(50L);
            sword.setBuyPrice(100L);
            sword.setStackable(0);
            sword.setMaxStack(1);
            sword.setTradeable(1);
            sword.setDroppable(1);
            sword.setActive(1);
            itemRepository.save(sword);

            Item potion = new Item();
            potion.setName("小血瓶");
            potion.setType(2); // 消耗品
            potion.setSubType(1); // 药水
            potion.setQuality(1); // 白色品质
            potion.setRequiredLevel(1);
            potion.setDescription("恢复少量生命值的药水");
            potion.setIcon("🧪");
            potion.setSellPrice(10L);
            potion.setBuyPrice(20L);
            potion.setStackable(1);
            potion.setMaxStack(99);
            potion.setTradeable(1);
            potion.setDroppable(1);
            potion.setEffectType(1); // 恢复HP
            potion.setEffectValue(30);
            potion.setActive(1);
            itemRepository.save(potion);

            Item armor = new Item();
            armor.setName("布衣");
            armor.setType(1); // 装备
            armor.setSubType(3); // 防具
            armor.setQuality(1); // 白色品质
            armor.setRequiredLevel(1);
            armor.setDescription("普通的布制衣服");
            armor.setIcon("👕");
            armor.setSellPrice(40L);
            armor.setBuyPrice(80L);
            armor.setStackable(0);
            armor.setMaxStack(1);
            armor.setTradeable(1);
            armor.setDroppable(1);
            armor.setActive(1);
            itemRepository.save(armor);

            return Result.success("测试物品创建成功", "已创建3个基础物品");
        } catch (Exception e) {
            return Result.error(500, "创建测试物品失败: " + e.getMessage());
        }
    }

    /**
     * 获取背包物品列表
     */
    @GetMapping("/list")
    public Result<List<Inventory>> getInventory(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");

            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }

            List<Inventory> inventory = inventoryService.getInventory(currentRoleId);
            return Result.success("获取背包列表成功", inventory);
        } catch (Exception e) {
            return Result.error(500, "获取背包列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定槽位的物品
     */
    @GetMapping("/slot/{slotIndex}")
    public Result<Inventory> getInventorySlot(@PathVariable Integer slotIndex, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            return inventoryService.getInventorySlot(currentRoleId, slotIndex)
                    .map(inventory -> Result.success("获取槽位物品成功", inventory))
                    .orElse(Result.error(404, "槽位为空"));
        } catch (Exception e) {
            return Result.error(500, "获取槽位物品失败: " + e.getMessage());
        }
    }

    /**
     * 添加物品到背包
     */
    @PostMapping("/add")
    public Result<Void> addItem(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer itemId = request.get("itemId");
            Integer quantity = request.get("quantity");
            
            if (itemId == null || quantity == null) {
                return Result.error(400, "参数不完整");
            }
            
            boolean success = inventoryService.addItem(currentRoleId, itemId, quantity);
            if (success) {
                return Result.success("添加物品成功");
            } else {
                return Result.error(400, "添加物品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "添加物品失败: " + e.getMessage());
        }
    }

    /**
     * 移除背包物品
     */
    @PostMapping("/remove")
    public Result<Void> removeItem(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer itemId = request.get("itemId");
            Integer quantity = request.get("quantity");
            
            if (itemId == null || quantity == null) {
                return Result.error(400, "参数不完整");
            }
            
            boolean success = inventoryService.removeItem(currentRoleId, itemId, quantity);
            if (success) {
                return Result.success("移除物品成功");
            } else {
                return Result.error(400, "移除物品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "移除物品失败: " + e.getMessage());
        }
    }

    /**
     * 使用物品
     */
    @PostMapping("/use/{slotIndex}")
    public Result<Void> useItem(@PathVariable Integer slotIndex, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = inventoryService.useItem(currentRoleId, slotIndex);
            if (success) {
                return Result.success("使用物品成功");
            } else {
                return Result.error(400, "使用物品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "使用物品失败: " + e.getMessage());
        }
    }

    /**
     * 移动物品位置
     */
    @PostMapping("/move")
    public Result<Void> moveItem(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer fromSlot = request.get("fromSlot");
            Integer toSlot = request.get("toSlot");
            
            if (fromSlot == null || toSlot == null) {
                return Result.error(400, "参数不完整");
            }
            
            boolean success = inventoryService.moveItem(currentRoleId, fromSlot, toSlot);
            if (success) {
                return Result.success("移动物品成功");
            } else {
                return Result.error(400, "移动物品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "移动物品失败: " + e.getMessage());
        }
    }

    /**
     * 分割物品
     */
    @PostMapping("/split")
    public Result<Void> splitItem(@RequestBody Map<String, Integer> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer slotIndex = request.get("slotIndex");
            Integer splitQuantity = request.get("splitQuantity");
            
            if (slotIndex == null || splitQuantity == null) {
                return Result.error(400, "参数不完整");
            }
            
            boolean success = inventoryService.splitItem(currentRoleId, slotIndex, splitQuantity);
            if (success) {
                return Result.success("分割物品成功");
            } else {
                return Result.error(400, "分割物品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "分割物品失败: " + e.getMessage());
        }
    }

    /**
     * 整理背包
     */
    @PostMapping("/organize")
    public Result<Void> organizeInventory(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = inventoryService.organizeInventory(currentRoleId);
            if (success) {
                return Result.success("整理背包成功");
            } else {
                return Result.error(400, "整理背包失败");
            }
        } catch (Exception e) {
            return Result.error(500, "整理背包失败: " + e.getMessage());
        }
    }

    /**
     * 获取背包统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getInventoryStatistics(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, Object> stats = inventoryService.getInventoryStatistics(currentRoleId);
            return Result.success("获取背包统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取背包统计失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否有指定物品
     */
    @GetMapping("/has/{itemId}/{quantity}")
    public Result<Boolean> hasItem(@PathVariable Integer itemId, @PathVariable Integer quantity, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean hasItem = inventoryService.hasItem(currentRoleId, itemId, quantity);
            return Result.success("检查物品成功", hasItem);
        } catch (Exception e) {
            return Result.error(500, "检查物品失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定物品的数量
     */
    @GetMapping("/quantity/{itemId}")
    public Result<Long> getItemQuantity(@PathVariable Integer itemId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            long quantity = inventoryService.getItemQuantity(currentRoleId, itemId);
            return Result.success("获取物品数量成功", quantity);
        } catch (Exception e) {
            return Result.error(500, "获取物品数量失败: " + e.getMessage());
        }
    }
}
