package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.User;
import com.honghuang.game.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginData, HttpServletRequest request) {
        try {
            String username = loginData.get("username");
            String password = loginData.get("password");

            if (username == null || username.trim().isEmpty()) {
                return Result.error(400, "用户名不能为空");
            }

            if (password == null || password.trim().isEmpty()) {
                return Result.error(400, "密码不能为空");
            }

            User user = userService.login(username, password, request);

            HttpSession session = request.getSession();
            session.setAttribute("userId", user.getId());
            session.setAttribute("username", user.getUsername());

            Map<String, Object> userData = new java.util.HashMap<>();
            userData.put("id", user.getId());
            userData.put("username", user.getUsername());
            userData.put("loginState", user.getLoginState());
            userData.put("yuanbao", user.getYuanbao());
            userData.put("jifen", user.getJifen());

            return Result.success("登录成功", userData);
        } catch (Exception e) {
            return Result.error(500, "登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<User> register(@RequestBody Map<String, String> registerData) {
        try {
            String username = registerData.get("username");
            String password = registerData.get("password");
            String confirmPassword = registerData.get("confirmPassword");
            String channel = registerData.get("channel");

            if (username == null || username.trim().isEmpty()) {
                return Result.error(400, "用户名不能为空");
            }

            if (password == null || password.trim().isEmpty()) {
                return Result.error(400, "密码不能为空");
            }

            User user = userService.register(username, password, confirmPassword, channel);
            return Result.success("注册成功", user);
        } catch (Exception e) {
            return Result.error(500, "注册失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/current")
    public Result<User> getCurrentUser(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId == null) {
                return Result.error(401, "用户未登录");
            }

            User user = userService.findById(userId);
            return Result.success("获取用户信息成功", user);
        } catch (Exception e) {
            return Result.error(500, "获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");

            if (userId != null) {
                userService.logout(userId);
            }

            session.invalidate();
            return Result.success("登出成功");
        } catch (Exception e) {
            return Result.error(500, "登出失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getUserStatistics() {
        try {
            Map<String, Object> stats = userService.getUserStatistics();
            return Result.success("获取用户统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取用户统计失败: " + e.getMessage());
        }
    }

    /**
     * 搜索用户
     */
    @GetMapping("/search")
    public Result<List<User>> searchUsers(@RequestParam String keyword) {
        try {
            List<User> users = userService.searchUsers(keyword);
            return Result.success("搜索用户成功", users);
        } catch (Exception e) {
            return Result.error(500, "搜索用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取元宝排行榜
     */
    @GetMapping("/yuanbao-ranking")
    public Result<List<User>> getYuanbaoRanking(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<User> users = userService.getYuanbaoRanking(limit);
            return Result.success("获取元宝排行榜成功", users);
        } catch (Exception e) {
            return Result.error(500, "获取元宝排行榜失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分排行榜
     */
    @GetMapping("/jifen-ranking")
    public Result<List<User>> getJifenRanking(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<User> users = userService.getJifenRanking(limit);
            return Result.success("获取积分排行榜成功", users);
        } catch (Exception e) {
            return Result.error(500, "获取积分排行榜失败: " + e.getMessage());
        }
    }
}
