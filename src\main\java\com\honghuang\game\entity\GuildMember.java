package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 帮派成员实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "guild_member")
public class GuildMember {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 帮派ID
     */
    @Column(name = "guild_id")
    private Integer guildId;

    /**
     * 角色ID
     */
    @Column(name = "role_id")
    private Integer roleId;

    /**
     * 角色名称
     */
    @Column(name = "role_name", length = 50)
    private String roleName;

    /**
     * 角色等级
     */
    @Column(name = "role_level")
    private Integer roleLevel;

    /**
     * 职位 1帮主 2副帮主 3长老 4堂主 5精英 6普通成员
     */
    @Column(name = "position")
    private Integer position = 6;

    /**
     * 贡献度
     */
    @Column(name = "contribution")
    private Long contribution = 0L;

    /**
     * 加入时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "join_time")
    private LocalDateTime joinTime;

    /**
     * 最后在线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_online_time")
    private LocalDateTime lastOnlineTime;

    /**
     * 帮派信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "guild_id", insertable = false, updatable = false)
    private Guild guild;

    /**
     * 角色信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", insertable = false, updatable = false)
    private PlayerRole role;

    // 业务方法

    /**
     * 获取职位名称
     */
    public String getPositionName() {
        switch (position) {
            case 1: return "帮主";
            case 2: return "副帮主";
            case 3: return "长老";
            case 4: return "堂主";
            case 5: return "精英";
            case 6: return "普通成员";
            default: return "未知";
        }
    }

    /**
     * 是否为帮主
     */
    public boolean isLeader() {
        return position == 1;
    }

    /**
     * 是否为副帮主
     */
    public boolean isViceLeader() {
        return position == 2;
    }

    /**
     * 是否为长老
     */
    public boolean isElder() {
        return position == 3;
    }

    /**
     * 是否为管理层（帮主、副帮主、长老）
     */
    public boolean isManager() {
        return position <= 3;
    }

    /**
     * 是否有踢人权限
     */
    public boolean canKickMember() {
        return position <= 3; // 帮主、副帮主、长老可以踢人
    }

    /**
     * 是否有邀请权限
     */
    public boolean canInviteMember() {
        return position <= 4; // 堂主及以上可以邀请
    }

    /**
     * 是否有审核权限
     */
    public boolean canApproveApplication() {
        return position <= 3; // 长老及以上可以审核
    }

    /**
     * 是否可以提升到指定职位
     */
    public boolean canPromoteTo(Integer targetPosition) {
        // 帮主可以任命任何职位
        if (isLeader()) {
            return targetPosition > 1; // 不能任命其他帮主
        }
        
        // 副帮主可以任命长老以下职位
        if (isViceLeader()) {
            return targetPosition > 2;
        }
        
        // 长老可以任命堂主以下职位
        if (isElder()) {
            return targetPosition > 3;
        }
        
        return false;
    }

    /**
     * 增加贡献度
     */
    public void addContribution(Long amount) {
        this.contribution += amount;
    }

    /**
     * 扣除贡献度
     */
    public boolean spendContribution(Long amount) {
        if (this.contribution >= amount) {
            this.contribution -= amount;
            return true;
        }
        return false;
    }

    /**
     * 更新在线时间
     */
    public void updateOnlineTime() {
        this.lastOnlineTime = LocalDateTime.now();
    }

    /**
     * 提升职位
     */
    public void promote(Integer newPosition) {
        if (newPosition < this.position) {
            this.position = newPosition;
        }
    }

    /**
     * 降低职位
     */
    public void demote(Integer newPosition) {
        if (newPosition > this.position) {
            this.position = newPosition;
        }
    }

    /**
     * 获取在帮天数
     */
    public Long getDaysInGuild() {
        if (joinTime == null) {
            return 0L;
        }
        return java.time.Duration.between(joinTime, LocalDateTime.now()).toDays();
    }

    /**
     * 获取离线天数
     */
    public Long getOfflineDays() {
        if (lastOnlineTime == null) {
            return 0L;
        }
        return java.time.Duration.between(lastOnlineTime, LocalDateTime.now()).toDays();
    }

    /**
     * 是否长期离线
     */
    public boolean isLongTimeOffline() {
        return getOfflineDays() > 7; // 7天未上线
    }

    /**
     * 获取成员完整信息
     */
    public String getFullInfo() {
        return String.format("%s (Lv.%d) [%s] 贡献:%d", 
                roleName, roleLevel, getPositionName(), contribution);
    }

    /**
     * 计算贡献度排名权重
     */
    public Double getContributionWeight() {
        // 职位越高，权重越大
        double positionWeight = (7 - position) * 0.1;
        return contribution.doubleValue() + positionWeight * 1000;
    }
}
