package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 帮派申请实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "guild_application")
public class GuildApplication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 帮派ID
     */
    @Column(name = "guild_id")
    private Integer guildId;

    /**
     * 申请者角色ID
     */
    @Column(name = "applicant_id")
    private Integer applicantId;

    /**
     * 申请者角色名称
     */
    @Column(name = "applicant_name", length = 50)
    private String applicantName;

    /**
     * 申请者等级
     */
    @Column(name = "applicant_level")
    private Integer applicantLevel;

    /**
     * 申请类型 1主动申请 2被邀请
     */
    @Column(name = "application_type")
    private Integer applicationType = 1;

    /**
     * 申请状态 0待处理 1已同意 2已拒绝 3已过期
     */
    @Column(name = "status")
    private Integer status = 0;

    /**
     * 申请消息
     */
    @Column(name = "message", length = 200)
    private String message;

    /**
     * 申请时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "apply_time")
    private LocalDateTime applyTime;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "process_time")
    private LocalDateTime processTime;

    /**
     * 处理者角色ID
     */
    @Column(name = "processor_id")
    private Integer processorId;

    /**
     * 处理者角色名称
     */
    @Column(name = "processor_name", length = 50)
    private String processorName;

    /**
     * 处理备注
     */
    @Column(name = "process_note", length = 200)
    private String processNote;

    /**
     * 帮派信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "guild_id", insertable = false, updatable = false)
    private Guild guild;

    /**
     * 申请者信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "applicant_id", insertable = false, updatable = false)
    private PlayerRole applicant;

    // 业务方法

    /**
     * 获取申请类型名称
     */
    public String getApplicationTypeName() {
        switch (applicationType) {
            case 1: return "主动申请";
            case 2: return "被邀请";
            default: return "未知";
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        switch (status) {
            case 0: return "待处理";
            case 1: return "已同意";
            case 2: return "已拒绝";
            case 3: return "已过期";
            default: return "未知";
        }
    }

    /**
     * 是否待处理
     */
    public boolean isPending() {
        return status == 0;
    }

    /**
     * 是否已同意
     */
    public boolean isApproved() {
        return status == 1;
    }

    /**
     * 是否已拒绝
     */
    public boolean isRejected() {
        return status == 2;
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return status == 3;
    }

    /**
     * 是否为主动申请
     */
    public boolean isActiveApplication() {
        return applicationType == 1;
    }

    /**
     * 是否为邀请
     */
    public boolean isInvitation() {
        return applicationType == 2;
    }

    /**
     * 同意申请
     */
    public void approve(Integer processorId, String processorName, String note) {
        this.status = 1;
        this.processTime = LocalDateTime.now();
        this.processorId = processorId;
        this.processorName = processorName;
        this.processNote = note;
    }

    /**
     * 拒绝申请
     */
    public void reject(Integer processorId, String processorName, String note) {
        this.status = 2;
        this.processTime = LocalDateTime.now();
        this.processorId = processorId;
        this.processorName = processorName;
        this.processNote = note;
    }

    /**
     * 设置为过期
     */
    public void expire() {
        this.status = 3;
        this.processTime = LocalDateTime.now();
    }

    /**
     * 检查是否应该过期
     */
    public boolean shouldExpire() {
        if (!isPending()) {
            return false;
        }
        
        // 申请7天后自动过期
        LocalDateTime expireTime = applyTime.plusDays(7);
        return LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 获取申请等待时间（小时）
     */
    public Long getPendingHours() {
        if (!isPending() || applyTime == null) {
            return null;
        }
        return java.time.Duration.between(applyTime, LocalDateTime.now()).toHours();
    }

    /**
     * 获取处理用时（小时）
     */
    public Long getProcessHours() {
        if (applyTime == null || processTime == null) {
            return null;
        }
        return java.time.Duration.between(applyTime, processTime).toHours();
    }

    /**
     * 获取申请完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(applicantName).append("(Lv.").append(applicantLevel).append(") ");
        sb.append(getApplicationTypeName()).append(" - ").append(getStatusName());
        
        if (message != null && !message.trim().isEmpty()) {
            sb.append(" [").append(message).append("]");
        }
        
        return sb.toString();
    }

    /**
     * 获取剩余有效时间（小时）
     */
    public Long getRemainingValidHours() {
        if (!isPending()) {
            return 0L;
        }
        
        LocalDateTime expireTime = applyTime.plusDays(7);
        LocalDateTime now = LocalDateTime.now();
        
        if (now.isAfter(expireTime)) {
            return 0L;
        }
        
        return java.time.Duration.between(now, expireTime).toHours();
    }
}
