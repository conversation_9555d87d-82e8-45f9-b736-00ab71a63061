-- 洪荒游戏数据库修复脚本
-- 解决实体类与数据库表名不匹配的问题

-- 使用数据库
USE jygame;

-- 1. 创建 scene 表（实体类使用的表名）
CREATE TABLE IF NOT EXISTS scene (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '场景ID',
    name VARCHAR(100) NOT NULL COMMENT '场景名称',
    type INT DEFAULT 1 COMMENT '场景类型 1新手村 2城镇 3野外 4副本 5特殊场景',
    required_level INT DEFAULT 1 COMMENT '场景等级要求',
    description VARCHAR(1000) COMMENT '场景描述',
    background_image VARCHAR(200) COMMENT '场景背景图',
    background_music VARCHAR(200) COMMENT '场景音乐',
    map_width INT DEFAULT 1000 COMMENT '地图宽度',
    map_height INT DEFAULT 1000 COMMENT '地图高度',
    spawn_x INT DEFAULT 500 COMMENT '默认出生点X坐标',
    spawn_y INT DEFAULT 500 COMMENT '默认出生点Y坐标',
    safe_zone INT DEFAULT 0 COMMENT '是否安全区域 0否 1是',
    allow_pk INT DEFAULT 1 COMMENT '是否允许PK 0不允许 1允许',
    allow_teleport INT DEFAULT 1 COMMENT '是否允许传送 0不允许 1允许',
    max_players INT DEFAULT 100 COMMENT '最大在线人数',
    current_players INT DEFAULT 0 COMMENT '当前在线人数',
    active INT DEFAULT 1 COMMENT '是否激活 0未激活 1已激活',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_type (type),
    INDEX idx_required_level (required_level),
    INDEX idx_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='场景表';

-- 2. 创建 npc 表（实体类使用的表名）
CREATE TABLE IF NOT EXISTS npc (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'NPC ID',
    name VARCHAR(50) NOT NULL COMMENT 'NPC名称',
    type INT DEFAULT 1 COMMENT 'NPC类型 1商店NPC 2任务NPC 3功能NPC 4装饰NPC 5战斗NPC',
    sub_type INT DEFAULT 1 COMMENT 'NPC子类型',
    scene_id INT COMMENT '所在场景ID',
    scene_name VARCHAR(100) COMMENT '场景名称',
    position_x INT DEFAULT 0 COMMENT 'X坐标',
    position_y INT DEFAULT 0 COMMENT 'Y坐标',
    level INT DEFAULT 1 COMMENT 'NPC等级',
    max_hp INT DEFAULT 100 COMMENT 'NPC最大生命值',
    current_hp INT DEFAULT 100 COMMENT 'NPC当前生命值',
    attack INT DEFAULT 10 COMMENT 'NPC攻击力',
    defense INT DEFAULT 5 COMMENT 'NPC防御力',
    agility INT DEFAULT 10 COMMENT 'NPC敏捷',
    intelligence INT DEFAULT 10 COMMENT 'NPC智力',
    constitution INT DEFAULT 10 COMMENT 'NPC体质',
    exp_reward BIGINT DEFAULT 10 COMMENT '击败获得经验',
    copper_reward BIGINT DEFAULT 10 COMMENT '击败获得铜钱',
    description TEXT COMMENT 'NPC描述',
    dialogue TEXT COMMENT 'NPC对话内容',
    shop_id INT COMMENT '关联商店ID',
    quest_id INT COMMENT '关联任务ID',
    avatar VARCHAR(200) COMMENT 'NPC头像',
    model VARCHAR(200) COMMENT 'NPC模型',
    active INT DEFAULT 1 COMMENT '是否激活 1激活 0未激活',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_type (type),
    INDEX idx_scene_id (scene_id),
    INDEX idx_level (level),
    INDEX idx_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='NPC表';

-- 3. skill 表已存在，跳过创建

-- 4. 插入基础场景数据
INSERT IGNORE INTO scene (id, name, type, required_level, description, spawn_x, spawn_y, safe_zone, active) VALUES
(1, '新手村', 1, 1, '初来乍到的新手村，这里是所有冒险者的起点。', 500, 500, 1, 1),
(2, '洪荒城', 2, 5, '洪荒大陆的主城，繁华热闹，各种商店和NPC聚集于此。', 300, 400, 1, 1),
(3, '青草原', 3, 1, '新手村外的青草原，有一些低级怪物适合新手练级。', 200, 300, 0, 1),
(4, '迷雾森林', 3, 10, '充满迷雾的神秘森林，危险与机遇并存。', 150, 250, 0, 1),
(5, '烈火山谷', 3, 20, '炎热的山谷，火属性怪物的聚集地。', 400, 200, 0, 1);

-- 5. 插入基础NPC数据
INSERT IGNORE INTO npc (id, name, type, scene_id, scene_name, position_x, position_y, level, description, dialogue, active) VALUES
(1, '村长老李', 2, 1, '新手村', 500, 450, 50, '新手村的村长，为新来的冒险者提供指导。', '欢迎来到新手村，年轻的冒险者！', 1),
(2, '武器商人', 1, 1, '新手村', 450, 500, 30, '售卖各种武器装备的商人。', '需要武器吗？我这里有最好的装备！', 1),
(3, '药剂师', 1, 1, '新手村', 550, 500, 25, '制作和售卖各种药剂的NPC。', '生命药剂、魔法药剂，应有尽有！', 1),
(4, '城主', 2, 2, '洪荒城', 300, 350, 80, '洪荒城的城主，掌管着整个城市的事务。', '洪荒城欢迎每一位勇敢的冒险者！', 1),
(5, '铁匠大师', 1, 2, '洪荒城', 250, 400, 60, '技艺精湛的铁匠，可以强化装备。', '想要更强的装备吗？我可以帮你强化！', 1);

-- 6. 插入基础技能数据（使用现有表结构）
INSERT IGNORE INTO skill (id, name, type, required_level, base_mp_cost, description, active) VALUES
(1, '普通攻击', 1, 1, 0, '最基础的物理攻击技能，不消耗魔法值。', 1),
(2, '重击', 1, 3, 5, '威力较大的物理攻击，消耗少量魔法值。', 1),
(3, '火球术', 1, 5, 10, '发射火球攻击敌人的法术技能。', 1),
(4, '治疗术', 3, 2, 8, '恢复生命值的治疗法术。', 1),
(5, '防御姿态', 2, 4, 0, '提高防御力的被动技能。', 1),
(6, '疾风步', 4, 6, 12, '提高移动速度和闪避率的辅助技能。', 1),
(7, '冰箭术', 1, 8, 12, '发射冰箭攻击敌人，有几率冰冻目标。', 1),
(8, '群体治疗', 3, 10, 20, '同时治疗多个目标的高级治疗法术。', 1);

-- 7. 创建 npc_dialogue 表（如果不存在）
CREATE TABLE IF NOT EXISTS npc_dialogue (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '对话ID',
    npc_id INT NOT NULL COMMENT 'NPC ID',
    dialogue_type INT DEFAULT 1 COMMENT '对话类型 1普通对话 2任务对话 3商店对话',
    content TEXT NOT NULL COMMENT '对话内容',
    next_dialogue_id INT COMMENT '下一个对话ID',
    condition_type INT DEFAULT 0 COMMENT '触发条件类型 0无条件 1等级条件 2任务条件',
    condition_value VARCHAR(100) COMMENT '触发条件值',
    active INT DEFAULT 1 COMMENT '是否激活 0未激活 1已激活',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_npc_id (npc_id),
    INDEX idx_dialogue_type (dialogue_type),
    INDEX idx_active (active),
    FOREIGN KEY (npc_id) REFERENCES npc(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='NPC对话表';

-- 8. 创建 teleport_point 表（如果不存在）
CREATE TABLE IF NOT EXISTS teleport_point (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '传送点ID',
    name VARCHAR(100) NOT NULL COMMENT '传送点名称',
    scene_id INT NOT NULL COMMENT '所在场景ID',
    position_x INT NOT NULL COMMENT 'X坐标',
    position_y INT NOT NULL COMMENT 'Y坐标',
    target_scene_id INT NOT NULL COMMENT '目标场景ID',
    target_x INT NOT NULL COMMENT '目标X坐标',
    target_y INT NOT NULL COMMENT '目标Y坐标',
    required_level INT DEFAULT 1 COMMENT '使用等级要求',
    cost_type INT DEFAULT 1 COMMENT '消耗类型 1铜钱 2银两 3金币',
    cost_amount INT DEFAULT 0 COMMENT '消耗数量',
    description VARCHAR(500) COMMENT '传送点描述',
    active INT DEFAULT 1 COMMENT '是否激活 0未激活 1已激活',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_scene_id (scene_id),
    INDEX idx_target_scene_id (target_scene_id),
    INDEX idx_active (active),
    FOREIGN KEY (scene_id) REFERENCES scene(id) ON DELETE CASCADE,
    FOREIGN KEY (target_scene_id) REFERENCES scene(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='传送点表';

-- 9. 插入基础传送点数据
INSERT IGNORE INTO teleport_point (id, name, scene_id, position_x, position_y, target_scene_id, target_x, target_y, description, active) VALUES
(1, '新手村传送门', 1, 600, 500, 2, 300, 400, '通往洪荒城的传送门', 1),
(2, '洪荒城传送门', 2, 350, 400, 1, 500, 500, '返回新手村的传送门', 1);

-- 完成提示
SELECT '数据库修复脚本执行完成！' AS message;
