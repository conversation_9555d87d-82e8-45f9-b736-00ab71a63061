package com.honghuang.game.repository;

import com.honghuang.game.entity.AuctionBid;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 拍卖出价数据访问层
 */
@Repository
public interface AuctionBidRepository extends JpaRepository<AuctionBid, Integer> {

    /**
     * 根据拍卖物品ID查找出价记录
     */
    List<AuctionBid> findByAuctionItemIdOrderByBidTimeDesc(Integer auctionItemId);

    /**
     * 根据出价者ID查找出价记录
     */
    List<AuctionBid> findByBidderIdOrderByBidTimeDesc(Integer bidderId);

    /**
     * 根据拍卖物品ID查找最高出价
     */
    @Query("SELECT ab FROM AuctionBid ab WHERE ab.auctionItemId = :auctionItemId ORDER BY ab.bidPrice DESC, ab.bidTime ASC")
    List<AuctionBid> findHighestBidByAuctionItemId(@Param("auctionItemId") Integer auctionItemId);

    /**
     * 根据拍卖物品ID查找当前最高出价
     */
    @Query("SELECT ab FROM AuctionBid ab WHERE ab.auctionItemId = :auctionItemId AND ab.isHighest = 1")
    List<AuctionBid> findCurrentHighestBid(@Param("auctionItemId") Integer auctionItemId);

    /**
     * 根据出价类型查找出价记录
     */
    List<AuctionBid> findByBidTypeOrderByBidTimeDesc(Integer bidType);

    /**
     * 查找指定时间范围内的出价记录
     */
    @Query("SELECT ab FROM AuctionBid ab WHERE ab.bidTime BETWEEN :startTime AND :endTime ORDER BY ab.bidTime DESC")
    List<AuctionBid> findBidsByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找最近的出价记录
     */
    @Query("SELECT ab FROM AuctionBid ab WHERE ab.bidTime >= :cutoffTime ORDER BY ab.bidTime DESC")
    List<AuctionBid> findRecentBids(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计拍卖物品的出价次数
     */
    @Query("SELECT COUNT(ab) FROM AuctionBid ab WHERE ab.auctionItemId = :auctionItemId")
    long countBidsByAuctionItemId(@Param("auctionItemId") Integer auctionItemId);

    /**
     * 统计出价者的出价次数
     */
    @Query("SELECT COUNT(ab) FROM AuctionBid ab WHERE ab.bidderId = :bidderId")
    long countBidsByBidderId(@Param("bidderId") Integer bidderId);

    /**
     * 统计出价者在指定拍卖的出价次数
     */
    @Query("SELECT COUNT(ab) FROM AuctionBid ab WHERE ab.auctionItemId = :auctionItemId AND ab.bidderId = :bidderId")
    long countBidsByAuctionItemIdAndBidderId(@Param("auctionItemId") Integer auctionItemId, @Param("bidderId") Integer bidderId);

    /**
     * 查找出价者的最高出价
     */
    @Query("SELECT ab FROM AuctionBid ab WHERE ab.bidderId = :bidderId ORDER BY ab.bidPrice DESC")
    List<AuctionBid> findHighestBidsByBidderId(@Param("bidderId") Integer bidderId);

    /**
     * 查找出价者在指定拍卖的最高出价
     */
    @Query("SELECT ab FROM AuctionBid ab WHERE ab.auctionItemId = :auctionItemId AND ab.bidderId = :bidderId ORDER BY ab.bidPrice DESC")
    List<AuctionBid> findHighestBidByAuctionItemIdAndBidderId(@Param("auctionItemId") Integer auctionItemId, @Param("bidderId") Integer bidderId);

    /**
     * 更新出价的最高状态
     */
    @Modifying
    @Query("UPDATE AuctionBid ab SET ab.isHighest = :isHighest WHERE ab.id = :bidId")
    int updateHighestStatus(@Param("bidId") Integer bidId, @Param("isHighest") Integer isHighest);

    /**
     * 取消拍卖物品所有出价的最高状态
     */
    @Modifying
    @Query("UPDATE AuctionBid ab SET ab.isHighest = 0 WHERE ab.auctionItemId = :auctionItemId")
    int clearHighestStatusByAuctionItemId(@Param("auctionItemId") Integer auctionItemId);

    /**
     * 删除拍卖物品的所有出价记录
     */
    @Modifying
    @Query("DELETE FROM AuctionBid ab WHERE ab.auctionItemId = :auctionItemId")
    int deleteByAuctionItemId(@Param("auctionItemId") Integer auctionItemId);

    /**
     * 删除出价者的所有出价记录
     */
    @Modifying
    @Query("DELETE FROM AuctionBid ab WHERE ab.bidderId = :bidderId")
    int deleteByBidderId(@Param("bidderId") Integer bidderId);

    /**
     * 查找一口价出价记录
     */
    @Query("SELECT ab FROM AuctionBid ab WHERE ab.bidType = 2 ORDER BY ab.bidTime DESC")
    List<AuctionBid> findBuyoutBids();

    /**
     * 查找普通出价记录
     */
    @Query("SELECT ab FROM AuctionBid ab WHERE ab.bidType = 1 ORDER BY ab.bidTime DESC")
    List<AuctionBid> findNormalBids();

    /**
     * 查找当前最高出价记录
     */
    @Query("SELECT ab FROM AuctionBid ab WHERE ab.isHighest = 1 ORDER BY ab.bidTime DESC")
    List<AuctionBid> findCurrentHighestBids();

    /**
     * 查找出价者参与的拍卖
     */
    @Query("SELECT DISTINCT ab.auctionItemId FROM AuctionBid ab WHERE ab.bidderId = :bidderId")
    List<Integer> findAuctionItemIdsByBidderId(@Param("bidderId") Integer bidderId);

    /**
     * 查找活跃出价者
     */
    @Query("SELECT ab.bidderId, COUNT(ab) as bidCount FROM AuctionBid ab WHERE ab.bidTime >= :cutoffTime GROUP BY ab.bidderId ORDER BY bidCount DESC")
    List<Object[]> findActiveBidders(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找热门拍卖物品
     */
    @Query("SELECT ab.auctionItemId, COUNT(ab) as bidCount FROM AuctionBid ab GROUP BY ab.auctionItemId ORDER BY bidCount DESC")
    List<Object[]> findPopularAuctionItems();
}
