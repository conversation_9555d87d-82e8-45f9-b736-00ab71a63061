package com.honghuang.game.repository;

import com.honghuang.game.entity.NpcDialogue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * NPC对话数据访问层
 */
@Repository
public interface NpcDialogueRepository extends JpaRepository<NpcDialogue, Integer> {

    /**
     * 根据角色ID查找对话记录
     */
    List<NpcDialogue> findByRoleIdOrderByDialogueTimeDesc(Integer roleId);

    /**
     * 根据NPC ID查找对话记录
     */
    List<NpcDialogue> findByNpcIdOrderByDialogueTimeDesc(Integer npcId);

    /**
     * 根据角色ID和NPC ID查找对话记录
     */
    List<NpcDialogue> findByRoleIdAndNpcIdOrderByDialogueTimeDesc(Integer roleId, Integer npcId);

    /**
     * 根据对话类型查找记录
     */
    List<NpcDialogue> findByRoleIdAndDialogueTypeOrderByDialogueTimeDesc(Integer roleId, Integer dialogueType);

    /**
     * 查找最近的对话记录
     */
    @Query("SELECT nd FROM NpcDialogue nd WHERE nd.roleId = :roleId AND nd.dialogueTime >= :cutoffTime ORDER BY nd.dialogueTime DESC")
    List<NpcDialogue> findRecentDialogues(@Param("roleId") Integer roleId, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找角色与指定NPC的最后一次对话
     */
    @Query("SELECT nd FROM NpcDialogue nd WHERE nd.roleId = :roleId AND nd.npcId = :npcId ORDER BY nd.dialogueTime DESC")
    List<NpcDialogue> findLastDialogueWithNpc(@Param("roleId") Integer roleId, @Param("npcId") Integer npcId);

    /**
     * 统计角色的对话次数
     */
    @Query("SELECT COUNT(nd) FROM NpcDialogue nd WHERE nd.roleId = :roleId")
    long countByRoleId(@Param("roleId") Integer roleId);

    /**
     * 统计角色与指定NPC的对话次数
     */
    @Query("SELECT COUNT(nd) FROM NpcDialogue nd WHERE nd.roleId = :roleId AND nd.npcId = :npcId")
    long countByRoleIdAndNpcId(@Param("roleId") Integer roleId, @Param("npcId") Integer npcId);

    /**
     * 统计指定类型的对话次数
     */
    @Query("SELECT COUNT(nd) FROM NpcDialogue nd WHERE nd.roleId = :roleId AND nd.dialogueType = :dialogueType")
    long countByRoleIdAndDialogueType(@Param("roleId") Integer roleId, @Param("dialogueType") Integer dialogueType);

    /**
     * 查找今日对话记录
     */
    @Query("SELECT nd FROM NpcDialogue nd WHERE nd.roleId = :roleId AND DATE(nd.dialogueTime) = CURRENT_DATE ORDER BY nd.dialogueTime DESC")
    List<NpcDialogue> findTodayDialogues(@Param("roleId") Integer roleId);

    /**
     * 查找指定时间范围内的对话记录
     */
    @Query("SELECT nd FROM NpcDialogue nd WHERE nd.roleId = :roleId AND nd.dialogueTime BETWEEN :startTime AND :endTime ORDER BY nd.dialogueTime DESC")
    List<NpcDialogue> findDialoguesByTimeRange(@Param("roleId") Integer roleId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找有选项的对话记录
     */
    @Query("SELECT nd FROM NpcDialogue nd WHERE nd.roleId = :roleId AND nd.options IS NOT NULL AND nd.options != '' ORDER BY nd.dialogueTime DESC")
    List<NpcDialogue> findDialoguesWithOptions(@Param("roleId") Integer roleId);

    /**
     * 查找未选择选项的对话记录
     */
    @Query("SELECT nd FROM NpcDialogue nd WHERE nd.roleId = :roleId AND nd.options IS NOT NULL AND nd.options != '' AND nd.selectedOption IS NULL ORDER BY nd.dialogueTime DESC")
    List<NpcDialogue> findUnselectedDialogues(@Param("roleId") Integer roleId);

    /**
     * 删除角色的所有对话记录
     */
    void deleteByRoleId(Integer roleId);

    /**
     * 删除指定NPC的所有对话记录
     */
    void deleteByNpcId(Integer npcId);

    /**
     * 删除过期的对话记录
     */
    @Query("DELETE FROM NpcDialogue nd WHERE nd.dialogueTime <= :cutoffTime")
    void deleteExpiredDialogues(@Param("cutoffTime") LocalDateTime cutoffTime);
}
