package com.honghuang.game.service;

import com.honghuang.game.entity.PlayerItem;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.GameItem;
import com.honghuang.game.repository.PlayerItemRepository;
import com.honghuang.game.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;

/**
 * 玩家物品服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class PlayerItemService {

    @Autowired
    private PlayerItemRepository playerItemRepository;

    @Autowired
    private GameItemService gameItemService;

    /**
     * 根据ID查找玩家物品
     */
    public PlayerItem findById(Integer id) {
        return playerItemRepository.findById(id)
                .orElseThrow(() -> new BusinessException("物品不存在"));
    }

    /**
     * 查找玩家所有物品
     */
    public List<PlayerItem> findByPlayerRole(PlayerRole playerRole) {
        return playerItemRepository.findByPlayerRole(playerRole);
    }

    /**
     * 查找玩家背包物品
     */
    public List<PlayerItem> findBagItems(PlayerRole playerRole) {
        return playerItemRepository.findBagItems(playerRole);
    }

    /**
     * 查找玩家装备物品
     */
    public List<PlayerItem> findEquippedItems(PlayerRole playerRole) {
        return playerItemRepository.findEquippedItems(playerRole);
    }

    /**
     * 查找玩家仓库物品
     */
    public List<PlayerItem> findWarehouseItems(PlayerRole playerRole) {
        return playerItemRepository.findWarehouseItems(playerRole);
    }

    /**
     * 根据位置查找玩家物品
     */
    public List<PlayerItem> findByPosition(PlayerRole playerRole, Integer position) {
        return playerItemRepository.findByPlayerRoleAndPosition(playerRole, position);
    }

    /**
     * 分页查找玩家物品
     */
    public Page<PlayerItem> findByPosition(PlayerRole playerRole, Integer position, Pageable pageable) {
        return playerItemRepository.findByPlayerRoleAndPosition(playerRole, position, pageable);
    }

    /**
     * 给玩家添加物品
     */
    public PlayerItem addItem(PlayerRole playerRole, GameItem gameItem, Integer quantity) {
        return addItem(playerRole, gameItem, quantity, 1, false); // 默认放入背包，不绑定
    }

    /**
     * 给玩家添加物品（指定位置和绑定状态）
     */
    public PlayerItem addItem(PlayerRole playerRole, GameItem gameItem, Integer quantity, Integer position, boolean bound) {
        if (quantity <= 0) {
            throw new BusinessException("物品数量必须大于0");
        }

        // 检查是否可以堆叠
        if (gameItem.getIsStackable() == 1) {
            List<PlayerItem> stackableItems = playerItemRepository.findStackableItems(
                playerRole, gameItem, position, bound ? 1 : 0);
            
            for (PlayerItem existingItem : stackableItems) {
                int canStack = gameItem.getMaxStack() - existingItem.getQuantity();
                if (canStack > 0) {
                    int stackAmount = Math.min(quantity, canStack);
                    existingItem.setQuantity(existingItem.getQuantity() + stackAmount);
                    playerItemRepository.save(existingItem);
                    
                    quantity -= stackAmount;
                    if (quantity <= 0) {
                        log.info("物品堆叠成功: {} x{}", gameItem.getName(), stackAmount);
                        return existingItem;
                    }
                }
            }
        }

        // 如果还有剩余数量，创建新的物品记录
        if (quantity > 0) {
            // 检查背包空间
            if (position == 1 && !hasEnoughBagSpace(playerRole, 1)) {
                throw new BusinessException("背包空间不足");
            }

            PlayerItem newItem = new PlayerItem(playerRole, gameItem, quantity);
            newItem.setPosition(position);
            newItem.setIsBound(bound ? 1 : 0);
            
            // 分配位置索引
            Integer positionIndex = findAvailableSlot(playerRole, position);
            newItem.setPositionIndex(positionIndex);

            PlayerItem savedItem = playerItemRepository.save(newItem);
            log.info("添加新物品: {} x{} 到位置 {}", gameItem.getName(), quantity, position);
            return savedItem;
        }

        return null;
    }

    /**
     * 移除玩家物品
     */
    public void removeItem(PlayerRole playerRole, Integer itemId, Integer quantity) {
        PlayerItem playerItem = findById(itemId);
        
        if (!playerItem.getPlayerRole().getId().equals(playerRole.getId())) {
            throw new BusinessException("无权操作此物品");
        }

        if (playerItem.getQuantity() < quantity) {
            throw new BusinessException("物品数量不足");
        }

        if (playerItem.getQuantity().equals(quantity)) {
            // 删除整个物品
            playerItemRepository.delete(playerItem);
            log.info("删除物品: {} x{}", playerItem.getGameItem().getName(), quantity);
        } else {
            // 减少数量
            playerItem.setQuantity(playerItem.getQuantity() - quantity);
            playerItemRepository.save(playerItem);
            log.info("减少物品数量: {} x{}", playerItem.getGameItem().getName(), quantity);
        }
    }

    /**
     * 使用物品
     */
    public void useItem(PlayerRole playerRole, Integer itemId, Integer quantity) {
        PlayerItem playerItem = findById(itemId);
        
        if (!playerItem.getPlayerRole().getId().equals(playerRole.getId())) {
            throw new BusinessException("无权操作此物品");
        }

        GameItem gameItem = playerItem.getGameItem();
        
        // 检查是否为消耗品
        if (!gameItem.isConsumable()) {
            throw new BusinessException("此物品不能使用");
        }

        // 检查使用条件
        if (!gameItemService.canUseItem(gameItem, playerRole.getGrade(), playerRole.getSex(), playerRole.getRace())) {
            throw new BusinessException("不满足使用条件");
        }

        if (playerItem.getQuantity() < quantity) {
            throw new BusinessException("物品数量不足");
        }

        // 应用物品效果
        applyItemEffect(playerRole, gameItem, quantity);

        // 更新最后使用时间
        playerItem.setLastUseTime(LocalDateTime.now());

        // 减少物品数量
        removeItem(playerRole, itemId, quantity);

        log.info("使用物品: {} x{}", gameItem.getName(), quantity);
    }

    /**
     * 装备物品
     */
    public void equipItem(PlayerRole playerRole, Integer itemId) {
        PlayerItem playerItem = findById(itemId);
        
        if (!playerItem.getPlayerRole().getId().equals(playerRole.getId())) {
            throw new BusinessException("无权操作此物品");
        }

        GameItem gameItem = playerItem.getGameItem();
        
        // 检查是否为装备
        if (!gameItem.isEquipment()) {
            throw new BusinessException("此物品不能装备");
        }

        // 检查装备条件
        if (!gameItemService.canUseItem(gameItem, playerRole.getGrade(), playerRole.getSex(), playerRole.getRace())) {
            throw new BusinessException("不满足装备条件");
        }

        // 检查是否已在装备栏
        if (playerItem.isEquipped()) {
            throw new BusinessException("物品已装备");
        }

        // 获取装备部位
        String equipSlot = playerItem.getEquipSlot();
        Integer slotIndex = getEquipSlotIndex(equipSlot);

        // 检查该部位是否已有装备
        Optional<PlayerItem> existingEquip = playerItemRepository.findByPlayerRoleAndPositionAndPositionIndex(
            playerRole, 2, slotIndex);
        
        if (existingEquip.isPresent()) {
            // 卸下原有装备
            unequipItem(playerRole, existingEquip.get().getId());
        }

        // 装备新物品
        playerItem.setPosition(2); // 装备栏
        playerItem.setPositionIndex(slotIndex);
        playerItemRepository.save(playerItem);

        log.info("装备物品: {}", gameItem.getName());
    }

    /**
     * 卸下装备
     */
    public void unequipItem(PlayerRole playerRole, Integer itemId) {
        PlayerItem playerItem = findById(itemId);
        
        if (!playerItem.getPlayerRole().getId().equals(playerRole.getId())) {
            throw new BusinessException("无权操作此物品");
        }

        if (!playerItem.isEquipped()) {
            throw new BusinessException("物品未装备");
        }

        // 检查背包空间
        if (!hasEnoughBagSpace(playerRole, 1)) {
            throw new BusinessException("背包空间不足");
        }

        // 移动到背包
        playerItem.setPosition(1); // 背包
        Integer bagSlot = findAvailableSlot(playerRole, 1);
        playerItem.setPositionIndex(bagSlot);
        playerItemRepository.save(playerItem);

        log.info("卸下装备: {}", playerItem.getGameItem().getName());
    }

    /**
     * 移动物品
     */
    public void moveItem(PlayerRole playerRole, Integer itemId, Integer targetPosition, Integer targetIndex) {
        PlayerItem playerItem = findById(itemId);
        
        if (!playerItem.getPlayerRole().getId().equals(playerRole.getId())) {
            throw new BusinessException("无权操作此物品");
        }

        // 检查目标位置是否被占用
        Optional<PlayerItem> targetItem = playerItemRepository.findByPlayerRoleAndPositionAndPositionIndex(
            playerRole, targetPosition, targetIndex);
        
        if (targetItem.isPresent() && !targetItem.get().getId().equals(itemId)) {
            throw new BusinessException("目标位置已被占用");
        }

        playerItem.setPosition(targetPosition);
        playerItem.setPositionIndex(targetIndex);
        playerItemRepository.save(playerItem);

        log.info("移动物品: {} 到位置 {}:{}", playerItem.getGameItem().getName(), targetPosition, targetIndex);
    }

    /**
     * 检查背包空间是否足够
     */
    public boolean hasEnoughBagSpace(PlayerRole playerRole, Integer needSlots) {
        Long usedSlots = playerItemRepository.countBagItems(playerRole);
        Integer totalSlots = playerRole.getWrapContent(); // 背包总容量
        return (totalSlots - usedSlots) >= needSlots;
    }

    /**
     * 查找可用的位置索引
     */
    private Integer findAvailableSlot(PlayerRole playerRole, Integer position) {
        List<Integer> usedSlots;
        Integer maxSlots;
        
        if (position == 1) { // 背包
            usedSlots = playerItemRepository.findUsedBagSlots(playerRole);
            maxSlots = playerRole.getWrapContent();
        } else if (position == 2) { // 装备栏
            usedSlots = playerItemRepository.findUsedEquipSlots(playerRole);
            maxSlots = 10; // 假设有10个装备部位
        } else {
            usedSlots = new ArrayList<>();
            maxSlots = 100; // 其他位置默认100个槽位
        }

        return IntStream.range(0, maxSlots)
                .filter(i -> !usedSlots.contains(i))
                .findFirst()
                .orElseThrow(() -> new BusinessException("没有可用的位置"));
    }

    /**
     * 获取装备部位索引
     */
    private Integer getEquipSlotIndex(String equipSlot) {
        switch (equipSlot) {
            case "weapon": return 0;
            case "helmet": return 1;
            case "armor": return 2;
            case "pants": return 3;
            case "shoes": return 4;
            case "gloves": return 5;
            case "necklace": return 6;
            case "ring": return 7;
            case "belt": return 8;
            default: return 9;
        }
    }

    /**
     * 应用物品效果
     */
    private void applyItemEffect(PlayerRole playerRole, GameItem gameItem, Integer quantity) {
        // 根据物品类型应用不同效果
        if (gameItem.getHpBonus() != null && gameItem.getHpBonus() > 0) {
            // 恢复生命值
            int healAmount = gameItem.getHpBonus() * quantity;
            playerRole.restoreHp(healAmount);
            log.info("恢复生命值: {}", healAmount);
        }

        if (gameItem.getMpBonus() != null && gameItem.getMpBonus() > 0) {
            // 恢复法力值
            int restoreAmount = gameItem.getMpBonus() * quantity;
            playerRole.restoreMp(restoreAmount);
            log.info("恢复法力值: {}", restoreAmount);
        }

        // TODO: 实现其他物品效果
    }

    /**
     * 清理过期物品
     */
    @Transactional
    public void cleanExpiredItems() {
        List<PlayerItem> expiredItems = playerItemRepository.findExpiredItems(LocalDateTime.now());
        for (PlayerItem item : expiredItems) {
            playerItemRepository.delete(item);
            log.info("清理过期物品: {}", item.getGameItem().getName());
        }
    }

    /**
     * 统计玩家各类型物品数量
     */
    public List<Object[]> countByItemType(PlayerRole playerRole) {
        return playerItemRepository.countByItemType(playerRole);
    }

    /**
     * 统计玩家各品质物品数量
     */
    public List<Object[]> countByItemQuality(PlayerRole playerRole) {
        return playerItemRepository.countByItemQuality(playerRole);
    }
}
