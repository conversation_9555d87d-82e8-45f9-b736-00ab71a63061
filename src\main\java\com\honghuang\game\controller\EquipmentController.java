package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.PlayerEquipment;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.service.EquipmentService;
import com.honghuang.game.service.PlayerRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 装备控制器
 */
@RestController
@RequestMapping("/api/equipment")
public class EquipmentController {

    @Autowired
    private EquipmentService equipmentService;
    
    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 获取角色当前装备
     */
    @GetMapping("/current")
    public Result<Map<String, PlayerEquipment>> getCurrentEquipment(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, PlayerEquipment> equipment = equipmentService.getCurrentEquipment(currentRoleId);
            return Result.success("获取装备成功", equipment);
        } catch (Exception e) {
            return Result.error(500, "获取装备失败: " + e.getMessage());
        }
    }

    /**
     * 获取背包中的装备
     */
    @GetMapping("/bag")
    public Result<List<PlayerEquipment>> getBagEquipment(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<PlayerEquipment> equipment = equipmentService.getBagEquipment(currentRoleId);
            return Result.success("获取背包装备成功", equipment);
        } catch (Exception e) {
            return Result.error(500, "获取背包装备失败: " + e.getMessage());
        }
    }

    /**
     * 装备物品
     */
    @PostMapping("/equip")
    public Result<String> equipItem(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer equipmentId = (Integer) params.get("equipmentId");
            if (equipmentId == null) {
                return Result.error(400, "装备ID不能为空");
            }
            
            PlayerRole role = playerRoleService.findById(currentRoleId);
            boolean success = equipmentService.equipItem(role, equipmentId);
            
            if (success) {
                return Result.success("装备成功");
            } else {
                return Result.error(400, "装备失败");
            }
        } catch (Exception e) {
            return Result.error(500, "装备失败: " + e.getMessage());
        }
    }

    /**
     * 卸下装备
     */
    @PostMapping("/unequip")
    public Result<String> unequipItem(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer position = (Integer) params.get("position");
            if (position == null) {
                return Result.error(400, "装备位置不能为空");
            }
            
            PlayerRole role = playerRoleService.findById(currentRoleId);
            boolean success = equipmentService.unequipItem(role, position);
            
            if (success) {
                return Result.success("卸下装备成功");
            } else {
                return Result.error(400, "卸下装备失败");
            }
        } catch (Exception e) {
            return Result.error(500, "卸下装备失败: " + e.getMessage());
        }
    }

    /**
     * 强化装备
     */
    @PostMapping("/enhance")
    public Result<Map<String, Object>> enhanceEquipment(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer equipmentId = (Integer) params.get("equipmentId");
            if (equipmentId == null) {
                return Result.error(400, "装备ID不能为空");
            }
            
            PlayerRole role = playerRoleService.findById(currentRoleId);
            Map<String, Object> result = equipmentService.enhanceEquipment(role, equipmentId);
            
            return Result.success("强化完成", result);
        } catch (Exception e) {
            return Result.error(500, "强化失败: " + e.getMessage());
        }
    }

    /**
     * 修理装备
     */
    @PostMapping("/repair")
    public Result<String> repairEquipment(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer equipmentId = (Integer) params.get("equipmentId");
            if (equipmentId == null) {
                return Result.error(400, "装备ID不能为空");
            }
            
            PlayerRole role = playerRoleService.findById(currentRoleId);
            long cost = equipmentService.repairEquipment(role, equipmentId);
            
            return Result.success("修理成功，花费 " + cost + " 铜钱");
        } catch (Exception e) {
            return Result.error(500, "修理失败: " + e.getMessage());
        }
    }

    /**
     * 获取装备详情
     */
    @GetMapping("/{equipmentId}")
    public Result<PlayerEquipment> getEquipmentDetail(@PathVariable Integer equipmentId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            PlayerEquipment equipment = equipmentService.getEquipmentDetail(currentRoleId, equipmentId);
            if (equipment != null) {
                return Result.success("获取装备详情成功", equipment);
            } else {
                return Result.error(404, "装备不存在");
            }
        } catch (Exception e) {
            return Result.error(500, "获取装备详情失败: " + e.getMessage());
        }
    }

    /**
     * 计算角色总属性（包含装备加成）
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getEquipmentStats(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, Object> stats = equipmentService.calculateTotalStats(currentRoleId);
            return Result.success("获取属性加成成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取属性加成失败: " + e.getMessage());
        }
    }

    /**
     * 获取强化预览
     */
    @GetMapping("/enhance-preview/{equipmentId}")
    public Result<Map<String, Object>> getEnhancePreview(@PathVariable Integer equipmentId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, Object> preview = equipmentService.getEnhancePreview(currentRoleId, equipmentId);
            return Result.success("获取强化预览成功", preview);
        } catch (Exception e) {
            return Result.error(500, "获取强化预览失败: " + e.getMessage());
        }
    }

    /**
     * 批量修理装备
     */
    @PostMapping("/repair-all")
    public Result<String> repairAllEquipment(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            PlayerRole role = playerRoleService.findById(currentRoleId);
            long totalCost = equipmentService.repairAllEquipment(role);
            
            return Result.success("全部修理完成，总花费 " + totalCost + " 铜钱");
        } catch (Exception e) {
            return Result.error(500, "批量修理失败: " + e.getMessage());
        }
    }
}
