package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.service.UserService;
import com.honghuang.game.service.PlayerRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 游戏控制器
 */
@RestController
@RequestMapping("/api/game")
public class GameController {

    @Autowired
    private UserService userService;

    @Autowired
    private PlayerRoleService playerRoleService;

    @GetMapping("/status")
    public Result<Map<String, Object>> getStatus() {
        try {
            Map<String, Object> statusData = new HashMap<>();
            statusData.put("gameStateName", "正常运行");
            statusData.put("serverTime", LocalDateTime.now());

            // 获取用户统计
            Map<String, Object> userStats = userService.getUserStatistics();
            statusData.put("onlineUsers", userStats.getOrDefault("onlineUsers", 0));
            statusData.put("totalUsers", userStats.getOrDefault("totalUsers", 0));
            statusData.put("todayRegistrations", userStats.getOrDefault("todayRegistrations", 0));
            statusData.put("todayActiveUsers", userStats.getOrDefault("todayActiveUsers", 0));

            // 获取角色统计（暂时使用固定值）
            statusData.put("onlineRoles", 0);
            statusData.put("totalRoles", 0);

            return Result.success("获取状态成功", statusData);
        } catch (Exception e) {
            // 如果服务调用失败，返回基础状态
            Map<String, Object> statusData = new HashMap<>();
            statusData.put("gameStateName", "正常运行");
            statusData.put("onlineUsers", 0);
            statusData.put("totalUsers", 0);
            statusData.put("todayRegistrations", 0);
            statusData.put("onlineRoles", 0);
            statusData.put("serverTime", LocalDateTime.now());

            return Result.success("获取状态成功", statusData);
        }
    }

    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("status", "UP");
        healthData.put("database", "UP");
        healthData.put("redis", "UP");
        healthData.put("timestamp", System.currentTimeMillis());
        
        return Result.success("健康检查成功", healthData);
    }

    @GetMapping("/info")
    public Result<Map<String, Object>> getGameInfo() {
        Map<String, Object> gameInfo = new HashMap<>();
        gameInfo.put("gameName", "洪荒Online");
        gameInfo.put("version", "1.0.0");
        gameInfo.put("description", "基于Spring Boot的洪荒题材网页游戏");
        gameInfo.put("maxLevel", 100);
        
        return Result.success("获取游戏信息成功", gameInfo);
    }
}
