package com.honghuang.game.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 排行榜实体类
 */
@Data
@TableName("ranking")
public class Ranking {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 角色ID
     */
    private Integer roleId;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 角色种族 1-巫族 2-妖族
     */
    private Integer race;
    
    /**
     * 角色等级
     */
    private Integer level;
    
    /**
     * 经验值
     */
    private Long experience;
    
    /**
     * 经验更新时间
     */
    private LocalDateTime experienceTime;
    
    /**
     * 铜钱数量
     */
    private Long copper;
    
    /**
     * 铜钱更新时间
     */
    private LocalDateTime copperTime;
    
    /**
     * 攻击力
     */
    private Integer attack;
    
    /**
     * 攻击力更新时间
     */
    private LocalDateTime attackTime;
    
    /**
     * 防御力
     */
    private Integer defense;
    
    /**
     * 防御力更新时间
     */
    private LocalDateTime defenseTime;
    
    /**
     * 敏捷
     */
    private Integer agility;
    
    /**
     * 敏捷更新时间
     */
    private LocalDateTime agilityTime;
    
    /**
     * 杀怪数量
     */
    private Integer killMonster;
    
    /**
     * 杀怪更新时间
     */
    private LocalDateTime killMonsterTime;
    
    /**
     * PK胜利次数
     */
    private Integer pkWin;
    
    /**
     * PK胜利更新时间
     */
    private LocalDateTime pkWinTime;
    
    /**
     * 在线时长（分钟）
     */
    private Integer onlineTime;
    
    /**
     * 在线时长更新时间
     */
    private LocalDateTime onlineTimeUpdate;
    
    /**
     * 宠物等级
     */
    private Integer petLevel;
    
    /**
     * 宠物等级更新时间
     */
    private LocalDateTime petLevelTime;
    
    /**
     * 宠物攻击力
     */
    private Integer petAttack;
    
    /**
     * 宠物攻击力更新时间
     */
    private LocalDateTime petAttackTime;
    
    /**
     * 装备评分
     */
    private Integer equipScore;
    
    /**
     * 装备评分更新时间
     */
    private LocalDateTime equipScoreTime;
    
    /**
     * 综合实力评分
     */
    private Integer totalScore;
    
    /**
     * 综合实力评分更新时间
     */
    private LocalDateTime totalScoreTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
