package com.honghuang.game.repository;

import com.honghuang.game.entity.GuildApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 帮派申请数据访问层
 */
@Repository
public interface GuildApplicationRepository extends JpaRepository<GuildApplication, Integer> {

    /**
     * 根据帮派ID查找申请列表
     */
    List<GuildApplication> findByGuildIdOrderByApplyTimeDesc(Integer guildId);

    /**
     * 根据申请者ID查找申请列表
     */
    List<GuildApplication> findByApplicantIdOrderByApplyTimeDesc(Integer applicantId);

    /**
     * 根据帮派ID和申请者ID查找申请
     */
    Optional<GuildApplication> findByGuildIdAndApplicantIdAndStatus(Integer guildId, Integer applicantId, Integer status);

    /**
     * 根据帮派ID和状态查找申请
     */
    List<GuildApplication> findByGuildIdAndStatusOrderByApplyTimeDesc(Integer guildId, Integer status);

    /**
     * 查找待处理的申请
     */
    @Query("SELECT ga FROM GuildApplication ga WHERE ga.guildId = :guildId AND ga.status = 0 ORDER BY ga.applyTime ASC")
    List<GuildApplication> findPendingApplications(@Param("guildId") Integer guildId);

    /**
     * 查找已通过的申请
     */
    @Query("SELECT ga FROM GuildApplication ga WHERE ga.guildId = :guildId AND ga.status = 1 ORDER BY ga.processTime DESC")
    List<GuildApplication> findApprovedApplications(@Param("guildId") Integer guildId);

    /**
     * 查找已拒绝的申请
     */
    @Query("SELECT ga FROM GuildApplication ga WHERE ga.guildId = :guildId AND ga.status = 2 ORDER BY ga.processTime DESC")
    List<GuildApplication> findRejectedApplications(@Param("guildId") Integer guildId);

    /**
     * 查找过期的申请
     */
    @Query("SELECT ga FROM GuildApplication ga WHERE ga.status = 0 AND ga.applyTime <= :cutoffTime")
    List<GuildApplication> findExpiredApplications(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计帮派待处理申请数量
     */
    @Query("SELECT COUNT(ga) FROM GuildApplication ga WHERE ga.guildId = :guildId AND ga.status = 0")
    long countPendingApplications(@Param("guildId") Integer guildId);

    /**
     * 统计申请者的申请数量
     */
    @Query("SELECT COUNT(ga) FROM GuildApplication ga WHERE ga.applicantId = :applicantId")
    long countByApplicantId(@Param("applicantId") Integer applicantId);

    /**
     * 统计申请者指定状态的申请数量
     */
    @Query("SELECT COUNT(ga) FROM GuildApplication ga WHERE ga.applicantId = :applicantId AND ga.status = :status")
    long countByApplicantIdAndStatus(@Param("applicantId") Integer applicantId, @Param("status") Integer status);

    /**
     * 更新申请状态
     */
    @Modifying
    @Query("UPDATE GuildApplication ga SET ga.status = :status, ga.processTime = :processTime WHERE ga.id = :applicationId")
    int updateStatus(@Param("applicationId") Integer applicationId, @Param("status") Integer status, @Param("processTime") LocalDateTime processTime);

    /**
     * 批量过期申请
     */
    @Modifying
    @Query("UPDATE GuildApplication ga SET ga.status = 3, ga.processTime = CURRENT_TIMESTAMP WHERE ga.status = 0 AND ga.applyTime <= :cutoffTime")
    int expireApplications(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除帮派的所有申请
     */
    @Modifying
    @Query("DELETE FROM GuildApplication ga WHERE ga.guildId = :guildId")
    int deleteByGuildId(@Param("guildId") Integer guildId);

    /**
     * 删除申请者的所有申请
     */
    @Modifying
    @Query("DELETE FROM GuildApplication ga WHERE ga.applicantId = :applicantId")
    int deleteByApplicantId(@Param("applicantId") Integer applicantId);

    /**
     * 查找最近的申请
     */
    @Query("SELECT ga FROM GuildApplication ga WHERE ga.guildId = :guildId ORDER BY ga.applyTime DESC")
    List<GuildApplication> findRecentApplications(@Param("guildId") Integer guildId);

    /**
     * 根据处理者查找申请
     */
    @Query("SELECT ga FROM GuildApplication ga WHERE ga.processorId = :processorId AND ga.status IN (1, 2) ORDER BY ga.processTime DESC")
    List<GuildApplication> findByProcessor(@Param("processorId") Integer processorId);

    /**
     * 查找指定时间范围内的申请
     */
    @Query("SELECT ga FROM GuildApplication ga WHERE ga.guildId = :guildId AND ga.applyTime BETWEEN :startTime AND :endTime ORDER BY ga.applyTime DESC")
    List<GuildApplication> findByTimeRange(@Param("guildId") Integer guildId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
