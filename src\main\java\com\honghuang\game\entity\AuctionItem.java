package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 拍卖物品实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "auction_item")
public class AuctionItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 卖家角色ID
     */
    @Column(name = "seller_id")
    private Integer sellerId;

    /**
     * 卖家角色名称
     */
    @Column(name = "seller_name", length = 50)
    private String sellerName;

    /**
     * 物品ID
     */
    @Column(name = "item_id")
    private Integer itemId;

    /**
     * 物品数量
     */
    @Column(name = "quantity")
    private Integer quantity;

    /**
     * 起拍价格
     */
    @Column(name = "start_price")
    private Long startPrice;

    /**
     * 当前价格
     */
    @Column(name = "current_price")
    private Long currentPrice;

    /**
     * 一口价（可选）
     */
    @Column(name = "buyout_price")
    private Long buyoutPrice;

    /**
     * 当前最高出价者ID
     */
    @Column(name = "highest_bidder_id")
    private Integer highestBidderId;

    /**
     * 当前最高出价者名称
     */
    @Column(name = "highest_bidder_name", length = 50)
    private String highestBidderName;

    /**
     * 拍卖状态 1进行中 2已成交 3已流拍 4已取消
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 货币类型 1铜钱 2灵石 3仙晶
     */
    @Column(name = "currency_type")
    private Integer currencyType = 1;

    /**
     * 拍卖开始时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 拍卖结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 成交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "deal_time")
    private LocalDateTime dealTime;

    /**
     * 物品信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "item_id", insertable = false, updatable = false)
    private Item item;

    /**
     * 卖家信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "seller_id", insertable = false, updatable = false)
    private PlayerRole seller;

    /**
     * 最高出价者信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "highest_bidder_id", insertable = false, updatable = false)
    private PlayerRole highestBidder;

    // 业务方法

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        switch (status) {
            case 1: return "进行中";
            case 2: return "已成交";
            case 3: return "已流拍";
            case 4: return "已取消";
            default: return "未知";
        }
    }

    /**
     * 获取货币类型名称
     */
    public String getCurrencyTypeName() {
        switch (currencyType) {
            case 1: return "铜钱";
            case 2: return "灵石";
            case 3: return "仙晶";
            default: return "未知";
        }
    }

    /**
     * 是否进行中
     */
    public boolean isActive() {
        return status == 1 && !isExpired();
    }

    /**
     * 是否已成交
     */
    public boolean isDeal() {
        return status == 2;
    }

    /**
     * 是否已流拍
     */
    public boolean isFailed() {
        return status == 3;
    }

    /**
     * 是否已取消
     */
    public boolean isCancelled() {
        return status == 4;
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return endTime != null && LocalDateTime.now().isAfter(endTime);
    }

    /**
     * 是否有一口价
     */
    public boolean hasBuyoutPrice() {
        return buyoutPrice != null && buyoutPrice > 0;
    }

    /**
     * 是否有出价者
     */
    public boolean hasBidder() {
        return highestBidderId != null;
    }

    /**
     * 获取剩余时间（分钟）
     */
    public Long getRemainingMinutes() {
        if (endTime == null || isExpired()) {
            return 0L;
        }
        return java.time.Duration.between(LocalDateTime.now(), endTime).toMinutes();
    }

    /**
     * 获取剩余时间描述
     */
    public String getRemainingTimeDescription() {
        Long minutes = getRemainingMinutes();
        if (minutes <= 0) {
            return "已结束";
        } else if (minutes < 60) {
            return minutes + "分钟";
        } else if (minutes < 1440) {
            return (minutes / 60) + "小时" + (minutes % 60) + "分钟";
        } else {
            long days = minutes / 1440;
            long hours = (minutes % 1440) / 60;
            return days + "天" + hours + "小时";
        }
    }

    /**
     * 出价
     */
    public boolean bid(Integer bidderId, String bidderName, Long bidPrice) {
        if (!isActive()) {
            return false;
        }

        if (bidPrice <= currentPrice) {
            return false;
        }

        // 检查是否达到一口价
        if (hasBuyoutPrice() && bidPrice >= buyoutPrice) {
            this.currentPrice = buyoutPrice;
            this.highestBidderId = bidderId;
            this.highestBidderName = bidderName;
            this.status = 2; // 成交
            this.dealTime = LocalDateTime.now();
        } else {
            this.currentPrice = bidPrice;
            this.highestBidderId = bidderId;
            this.highestBidderName = bidderName;
            
            // 如果剩余时间少于5分钟，延长5分钟
            if (getRemainingMinutes() < 5) {
                this.endTime = LocalDateTime.now().plusMinutes(5);
            }
        }

        return true;
    }

    /**
     * 一口价购买
     */
    public boolean buyout(Integer buyerId, String buyerName) {
        if (!isActive() || !hasBuyoutPrice()) {
            return false;
        }

        this.currentPrice = buyoutPrice;
        this.highestBidderId = buyerId;
        this.highestBidderName = buyerName;
        this.status = 2; // 成交
        this.dealTime = LocalDateTime.now();

        return true;
    }

    /**
     * 取消拍卖
     */
    public boolean cancel() {
        if (!isActive() || hasBidder()) {
            return false; // 有人出价后不能取消
        }

        this.status = 4; // 已取消
        return true;
    }

    /**
     * 流拍
     */
    public void fail() {
        this.status = 3; // 已流拍
    }

    /**
     * 获取下次最低出价
     */
    public Long getMinNextBid() {
        long increment = calculateBidIncrement();
        return currentPrice + increment;
    }

    /**
     * 计算出价增幅
     */
    private long calculateBidIncrement() {
        if (currentPrice < 1000) {
            return 10; // 1000以下，增幅10
        } else if (currentPrice < 10000) {
            return 50; // 1万以下，增幅50
        } else if (currentPrice < 100000) {
            return 100; // 10万以下，增幅100
        } else {
            return currentPrice / 1000; // 10万以上，增幅为当前价格的千分之一
        }
    }

    /**
     * 获取拍卖完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        
        if (item != null) {
            sb.append(item.getFullName());
            if (quantity > 1) {
                sb.append(" x").append(quantity);
            }
        }
        
        sb.append(" - 当前价格: ").append(currentPrice).append(getCurrencyTypeName());
        
        if (hasBuyoutPrice()) {
            sb.append(" (一口价: ").append(buyoutPrice).append(getCurrencyTypeName()).append(")");
        }
        
        sb.append(" - ").append(getStatusName());
        
        if (isActive()) {
            sb.append(" (").append(getRemainingTimeDescription()).append(")");
        }
        
        return sb.toString();
    }

    /**
     * 获取拍卖持续时间（小时）
     */
    public Long getAuctionDurationHours() {
        if (startTime == null || endTime == null) {
            return null;
        }
        return java.time.Duration.between(startTime, endTime).toHours();
    }

    /**
     * 计算手续费
     */
    public Long calculateFee() {
        // 成交价的5%作为手续费
        return Math.max(currentPrice * 5 / 100, 1L);
    }

    /**
     * 计算卖家收入
     */
    public Long calculateSellerIncome() {
        return currentPrice - calculateFee();
    }

    /**
     * 检查是否可以出价
     */
    public boolean canBid(Integer roleId, Long bidPrice) {
        if (!isActive()) {
            return false;
        }
        
        if (roleId.equals(sellerId)) {
            return false; // 卖家不能出价
        }
        
        if (bidPrice <= currentPrice) {
            return false; // 出价必须高于当前价格
        }
        
        return true;
    }
}
