package com.honghuang.game.repository;

import com.honghuang.game.entity.Skill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 技能数据访问层
 */
@Repository
public interface SkillRepository extends JpaRepository<Skill, Integer> {

    /**
     * 查找激活的技能
     */
    List<Skill> findByActiveOrderByTypeAscRequiredLevelAsc(Integer active);

    /**
     * 根据类型查找技能
     */
    List<Skill> findByTypeAndActiveOrderByRequiredLevelAsc(Integer type, Integer active);

    /**
     * 根据分类查找技能
     */
    List<Skill> findByCategoryAndActiveOrderByRequiredLevelAsc(Integer category, Integer active);

    /**
     * 根据种族查找技能
     */
    @Query("SELECT s FROM Skill s WHERE (s.requiredRace = 0 OR s.requiredRace = :race) AND s.active = 1 ORDER BY s.type ASC, s.requiredLevel ASC")
    List<Skill> findByRace(@Param("race") Integer race);

    /**
     * 根据性别查找技能
     */
    @Query("SELECT s FROM Skill s WHERE (s.requiredSex = 0 OR s.requiredSex = :sex) AND s.active = 1 ORDER BY s.type ASC, s.requiredLevel ASC")
    List<Skill> findBySex(@Param("sex") Integer sex);

    /**
     * 查找适合角色的技能
     */
    @Query("SELECT s FROM Skill s WHERE s.requiredLevel <= :level " +
           "AND (s.requiredRace = 0 OR s.requiredRace = :race) " +
           "AND (s.requiredSex = 0 OR s.requiredSex = :sex) " +
           "AND s.active = 1 ORDER BY s.type ASC, s.requiredLevel ASC")
    List<Skill> findSuitableSkills(@Param("level") Integer level, @Param("race") Integer race, @Param("sex") Integer sex);

    /**
     * 根据前置技能查找技能
     */
    List<Skill> findByPrerequisiteSkillIdAndActiveOrderByRequiredLevelAsc(Integer prerequisiteSkillId, Integer active);

    /**
     * 查找无前置技能的技能
     */
    @Query("SELECT s FROM Skill s WHERE s.prerequisiteSkillId IS NULL AND s.active = 1 ORDER BY s.type ASC, s.requiredLevel ASC")
    List<Skill> findBasicSkills();

    /**
     * 根据名称模糊查找技能
     */
    @Query("SELECT s FROM Skill s WHERE s.name LIKE %:name% AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Skill> findByNameContaining(@Param("name") String name);

    /**
     * 查找攻击技能
     */
    @Query("SELECT s FROM Skill s WHERE s.type = 1 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Skill> findAttackSkills();

    /**
     * 查找防御技能
     */
    @Query("SELECT s FROM Skill s WHERE s.type = 2 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Skill> findDefenseSkills();

    /**
     * 查找治疗技能
     */
    @Query("SELECT s FROM Skill s WHERE s.type = 3 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Skill> findHealSkills();

    /**
     * 查找辅助技能
     */
    @Query("SELECT s FROM Skill s WHERE s.type = 4 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Skill> findSupportSkills();

    /**
     * 查找生活技能
     */
    @Query("SELECT s FROM Skill s WHERE s.type = 5 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Skill> findLifeSkills();

    /**
     * 查找被动技能
     */
    @Query("SELECT s FROM Skill s WHERE s.category = 3 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Skill> findPassiveSkills();
}
