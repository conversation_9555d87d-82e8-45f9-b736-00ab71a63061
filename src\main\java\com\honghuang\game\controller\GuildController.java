package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.Guild;
import com.honghuang.game.entity.GuildApplication;
import com.honghuang.game.entity.GuildMember;
import com.honghuang.game.service.GuildService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 帮派控制器
 */
@RestController
@RequestMapping("/api/guild")
public class GuildController {

    @Autowired
    private GuildService guildService;

    /**
     * 获取帮派列表
     */
    @GetMapping("/list")
    public Result<List<Guild>> getGuildList() {
        try {
            List<Guild> guilds = guildService.getGuildList();
            return Result.success("获取帮派列表成功", guilds);
        } catch (Exception e) {
            return Result.error(500, "获取帮派列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取帮派详情
     */
    @GetMapping("/{guildId}")
    public Result<Guild> getGuildDetail(@PathVariable Integer guildId) {
        try {
            Guild guild = guildService.getGuildDetail(guildId);
            return Result.success("获取帮派详情成功", guild);
        } catch (Exception e) {
            return Result.error(500, "获取帮派详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建帮派
     */
    @PostMapping("/create")
    public Result<Guild> createGuild(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String guildName = request.get("guildName");
            String description = request.get("description");
            
            if (guildName == null || guildName.trim().isEmpty()) {
                return Result.error(400, "帮派名称不能为空");
            }
            
            Guild guild = guildService.createGuild(currentRoleId, guildName.trim(), description);
            return Result.success("创建帮派成功", guild);
        } catch (Exception e) {
            return Result.error(500, "创建帮派失败: " + e.getMessage());
        }
    }

    /**
     * 申请加入帮派
     */
    @PostMapping("/{guildId}/apply")
    public Result<GuildApplication> applyToJoinGuild(@PathVariable Integer guildId,
                                                    @RequestBody Map<String, String> request,
                                                    HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String message = request.get("message");
            
            GuildApplication application = guildService.applyToJoinGuild(currentRoleId, guildId, message);
            return Result.success("申请提交成功", application);
        } catch (Exception e) {
            return Result.error(500, "申请失败: " + e.getMessage());
        }
    }

    /**
     * 审核申请 - 通过
     */
    @PostMapping("/{guildId}/applications/{applicationId}/approve")
    public Result<Void> approveApplication(@PathVariable Integer guildId,
                                          @PathVariable Integer applicationId,
                                          @RequestBody Map<String, String> request,
                                          HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String note = request.get("note");
            
            boolean success = guildService.approveApplication(guildId, applicationId, currentRoleId, note);
            if (success) {
                return Result.success("申请审核通过");
            } else {
                return Result.error(400, "审核失败");
            }
        } catch (Exception e) {
            return Result.error(500, "审核失败: " + e.getMessage());
        }
    }

    /**
     * 审核申请 - 拒绝
     */
    @PostMapping("/{guildId}/applications/{applicationId}/reject")
    public Result<Void> rejectApplication(@PathVariable Integer guildId,
                                         @PathVariable Integer applicationId,
                                         @RequestBody Map<String, String> request,
                                         HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String note = request.get("note");
            
            boolean success = guildService.rejectApplication(guildId, applicationId, currentRoleId, note);
            if (success) {
                return Result.success("申请已拒绝");
            } else {
                return Result.error(400, "拒绝失败");
            }
        } catch (Exception e) {
            return Result.error(500, "拒绝失败: " + e.getMessage());
        }
    }

    /**
     * 踢出成员
     */
    @PostMapping("/{guildId}/members/{targetRoleId}/kick")
    public Result<Void> kickMember(@PathVariable Integer guildId,
                                  @PathVariable Integer targetRoleId,
                                  @RequestBody Map<String, String> request,
                                  HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String reason = request.get("reason");
            
            boolean success = guildService.kickMember(guildId, targetRoleId, currentRoleId, reason);
            if (success) {
                return Result.success("踢出成员成功");
            } else {
                return Result.error(400, "踢出成员失败");
            }
        } catch (Exception e) {
            return Result.error(500, "踢出成员失败: " + e.getMessage());
        }
    }

    /**
     * 退出帮派
     */
    @PostMapping("/leave")
    public Result<Void> leaveGuild(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = guildService.leaveGuild(currentRoleId);
            if (success) {
                return Result.success("退出帮派成功");
            } else {
                return Result.error(400, "退出帮派失败");
            }
        } catch (Exception e) {
            return Result.error(500, "退出帮派失败: " + e.getMessage());
        }
    }

    /**
     * 获取帮派成员列表
     */
    @GetMapping("/{guildId}/members")
    public Result<List<GuildMember>> getGuildMembers(@PathVariable Integer guildId) {
        try {
            List<GuildMember> members = guildService.getGuildMembers(guildId);
            return Result.success("获取成员列表成功", members);
        } catch (Exception e) {
            return Result.error(500, "获取成员列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色的帮派信息
     */
    @GetMapping("/my")
    public Result<GuildMember> getPlayerGuild(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Optional<GuildMember> guildMember = guildService.getPlayerGuild(currentRoleId);
            if (guildMember.isPresent()) {
                return Result.success("获取帮派信息成功", guildMember.get());
            } else {
                return Result.error(404, "未加入任何帮派");
            }
        } catch (Exception e) {
            return Result.error(500, "获取帮派信息失败: " + e.getMessage());
        }
    }
}
