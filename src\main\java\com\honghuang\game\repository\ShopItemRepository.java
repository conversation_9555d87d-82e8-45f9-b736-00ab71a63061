package com.honghuang.game.repository;

import com.honghuang.game.entity.ShopItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商店商品数据访问层
 */
@Repository
public interface ShopItemRepository extends JpaRepository<ShopItem, Integer> {

    /**
     * 根据商店ID查找商品
     */
    List<ShopItem> findByShopIdAndActiveOrderByItemTypeAscItemQualityDesc(Integer shopId, Integer active);

    /**
     * 根据商店ID和商品类型查找商品
     */
    List<ShopItem> findByShopIdAndItemTypeAndActiveOrderByItemQualityDesc(Integer shopId, Integer itemType, Integer active);

    /**
     * 根据商品类型查找商品
     */
    List<ShopItem> findByItemTypeAndActiveOrderByItemQualityDesc(Integer itemType, Integer active);

    /**
     * 根据商品品质查找商品
     */
    List<ShopItem> findByItemQualityAndActiveOrderByItemTypeAsc(Integer itemQuality, Integer active);

    /**
     * 查找适合角色的商品
     */
    @Query("SELECT si FROM ShopItem si WHERE si.shopId = :shopId AND si.requiredLevel <= :level " +
           "AND (si.requiredRace = 0 OR si.requiredRace = :race) " +
           "AND (si.requiredSex = 0 OR si.requiredSex = :sex) " +
           "AND si.active = 1 ORDER BY si.itemType ASC, si.itemQuality DESC")
    List<ShopItem> findSuitableItems(@Param("shopId") Integer shopId, @Param("level") Integer level, 
                                    @Param("race") Integer race, @Param("sex") Integer sex);

    /**
     * 查找有库存的商品
     */
    @Query("SELECT si FROM ShopItem si WHERE si.shopId = :shopId AND (si.stock = -1 OR si.stock > 0) " +
           "AND si.active = 1 ORDER BY si.itemType ASC, si.itemQuality DESC")
    List<ShopItem> findAvailableItems(@Param("shopId") Integer shopId);

    /**
     * 查找限时商品
     */
    @Query("SELECT si FROM ShopItem si WHERE si.isLimitedTime = 1 AND si.active = 1 " +
           "AND si.limitedStartTime <= CURRENT_TIMESTAMP AND si.limitedEndTime >= CURRENT_TIMESTAMP " +
           "ORDER BY si.limitedEndTime ASC")
    List<ShopItem> findLimitedTimeItems();

    /**
     * 查找折扣商品
     */
    @Query("SELECT si FROM ShopItem si WHERE si.discount < 100 AND si.active = 1 " +
           "ORDER BY si.discount ASC, si.itemQuality DESC")
    List<ShopItem> findDiscountItems();

    /**
     * 根据价格范围查找商品
     */
    @Query("SELECT si FROM ShopItem si WHERE si.shopId = :shopId AND " +
           "(si.priceCopper + si.priceSilver * 100 + si.priceGold * 10000) * si.discount / 100 " +
           "BETWEEN :minPrice AND :maxPrice AND si.active = 1 " +
           "ORDER BY (si.priceCopper + si.priceSilver * 100 + si.priceGold * 10000) * si.discount / 100 ASC")
    List<ShopItem> findByPriceRange(@Param("shopId") Integer shopId, 
                                   @Param("minPrice") Long minPrice, @Param("maxPrice") Long maxPrice);

    /**
     * 根据名称模糊查找商品
     */
    @Query("SELECT si FROM ShopItem si WHERE si.itemName LIKE %:name% AND si.active = 1 " +
           "ORDER BY si.itemQuality DESC")
    List<ShopItem> findByNameContaining(@Param("name") String name);

    /**
     * 更新商品库存
     */
    @Modifying
    @Query("UPDATE ShopItem si SET si.stock = :stock WHERE si.id = :itemId")
    int updateStock(@Param("itemId") Integer itemId, @Param("stock") Integer stock);

    /**
     * 更新商品销售数量
     */
    @Modifying
    @Query("UPDATE ShopItem si SET si.soldCount = si.soldCount + :quantity WHERE si.id = :itemId")
    int updateSoldCount(@Param("itemId") Integer itemId, @Param("quantity") Integer quantity);

    /**
     * 减少商品库存
     */
    @Modifying
    @Query("UPDATE ShopItem si SET si.stock = si.stock - :quantity, si.soldCount = si.soldCount + :quantity " +
           "WHERE si.id = :itemId AND (si.stock = -1 OR si.stock >= :quantity)")
    int reduceStock(@Param("itemId") Integer itemId, @Param("quantity") Integer quantity);

    /**
     * 查找热销商品
     */
    @Query("SELECT si FROM ShopItem si WHERE si.shopId = :shopId AND si.active = 1 " +
           "ORDER BY si.soldCount DESC")
    List<ShopItem> findPopularItems(@Param("shopId") Integer shopId);

    /**
     * 查找新商品
     */
    @Query("SELECT si FROM ShopItem si WHERE si.shopId = :shopId AND si.active = 1 " +
           "ORDER BY si.createTime DESC")
    List<ShopItem> findNewItems(@Param("shopId") Integer shopId);

    /**
     * 统计商店商品数量
     */
    @Query("SELECT COUNT(si) FROM ShopItem si WHERE si.shopId = :shopId AND si.active = 1")
    long countByShopId(@Param("shopId") Integer shopId);

    /**
     * 统计商店指定类型商品数量
     */
    @Query("SELECT COUNT(si) FROM ShopItem si WHERE si.shopId = :shopId AND si.itemType = :itemType AND si.active = 1")
    long countByShopIdAndItemType(@Param("shopId") Integer shopId, @Param("itemType") Integer itemType);

    /**
     * 查找库存不足的商品
     */
    @Query("SELECT si FROM ShopItem si WHERE si.stock != -1 AND si.stock <= :threshold AND si.active = 1")
    List<ShopItem> findLowStockItems(@Param("threshold") Integer threshold);

    /**
     * 删除商店的所有商品
     */
    @Modifying
    @Query("UPDATE ShopItem si SET si.active = 0 WHERE si.shopId = :shopId")
    int deactivateByShopId(@Param("shopId") Integer shopId);
}
