#!/bin/bash

# 洪荒游戏数据库导入脚本
# 使用方法: ./import_database.sh

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="xiayun"
DB_NAME="jygame"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 洪荒游戏数据库导入工具 ===${NC}"
echo ""

# 检查MySQL是否可用
echo -e "${YELLOW}检查MySQL连接...${NC}"
mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASSWORD} -e "SELECT 1;" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${RED}错误: 无法连接到MySQL数据库${NC}"
    echo "请检查数据库配置:"
    echo "  主机: ${DB_HOST}"
    echo "  端口: ${DB_PORT}"
    echo "  用户: ${DB_USER}"
    echo "  密码: ${DB_PASSWORD}"
    exit 1
fi
echo -e "${GREEN}✓ MySQL连接成功${NC}"

# 检查SQL文件是否存在
SQL_FILE="src/main/resources/sql/mysql_init.sql"
if [ ! -f "$SQL_FILE" ]; then
    echo -e "${RED}错误: 找不到SQL文件 ${SQL_FILE}${NC}"
    exit 1
fi
echo -e "${GREEN}✓ 找到SQL文件: ${SQL_FILE}${NC}"

# 备份现有数据库（如果存在）
echo -e "${YELLOW}检查现有数据库...${NC}"
mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASSWORD} -e "USE ${DB_NAME};" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${YELLOW}发现现有数据库 ${DB_NAME}${NC}"
    read -p "是否要备份现有数据库? (y/n): " backup_choice
    if [ "$backup_choice" = "y" ] || [ "$backup_choice" = "Y" ]; then
        BACKUP_FILE="backup_${DB_NAME}_$(date +%Y%m%d_%H%M%S).sql"
        echo -e "${YELLOW}正在备份数据库到 ${BACKUP_FILE}...${NC}"
        mysqldump -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASSWORD} ${DB_NAME} > ${BACKUP_FILE}
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ 数据库备份完成: ${BACKUP_FILE}${NC}"
        else
            echo -e "${RED}✗ 数据库备份失败${NC}"
            exit 1
        fi
    fi
    
    read -p "是否要删除现有数据库并重新创建? (y/n): " recreate_choice
    if [ "$recreate_choice" = "y" ] || [ "$recreate_choice" = "Y" ]; then
        echo -e "${YELLOW}正在删除现有数据库...${NC}"
        mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASSWORD} -e "DROP DATABASE IF EXISTS ${DB_NAME};"
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ 现有数据库已删除${NC}"
        else
            echo -e "${RED}✗ 删除数据库失败${NC}"
            exit 1
        fi
    fi
fi

# 执行SQL文件
echo -e "${YELLOW}正在执行数据库初始化脚本...${NC}"
mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASSWORD} < ${SQL_FILE}
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 数据库初始化完成${NC}"
else
    echo -e "${RED}✗ 数据库初始化失败${NC}"
    exit 1
fi

# 验证数据库结构
echo -e "${YELLOW}验证数据库结构...${NC}"
TABLES=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASSWORD} -D${DB_NAME} -e "SHOW TABLES;" | grep -v "Tables_in_" | wc -l)
echo -e "${GREEN}✓ 成功创建 ${TABLES} 个数据表${NC}"

# 显示主要表的记录数
echo -e "${YELLOW}检查基础数据...${NC}"
echo "表名                  记录数"
echo "==================== ======"
mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASSWORD} -D${DB_NAME} -e "
SELECT 'user_info' as table_name, COUNT(*) as count FROM user_info
UNION ALL
SELECT 'player_role', COUNT(*) FROM player_role
UNION ALL
SELECT 'item', COUNT(*) FROM item
UNION ALL
SELECT 'scene_info', COUNT(*) FROM scene_info
UNION ALL
SELECT 'npc_info', COUNT(*) FROM npc_info
UNION ALL
SELECT 'skill', COUNT(*) FROM skill
UNION ALL
SELECT 'quest', COUNT(*) FROM quest
UNION ALL
SELECT 'shop', COUNT(*) FROM shop
UNION ALL
SELECT 'shop_item', COUNT(*) FROM shop_item;
" | tail -n +2 | while read line; do
    printf "%-20s %s\n" $line
done

echo ""
echo -e "${GREEN}=== 数据库导入完成 ===${NC}"
echo ""
echo "数据库信息:"
echo "  数据库名: ${DB_NAME}"
echo "  主机: ${DB_HOST}:${DB_PORT}"
echo "  用户: ${DB_USER}"
echo ""
echo "测试账号:"
echo "  用户名: admin, 密码: admin123"
echo "  用户名: test,  密码: admin123"
echo ""
echo -e "${YELLOW}注意: 请确保Spring Boot应用的数据库配置与此一致${NC}"
