package com.honghuang.game.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 玩家角色视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@ApiModel(description = "玩家角色信息")
public class PlayerRoleVO {

    @ApiModelProperty(value = "角色ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "角色名称", example = "英雄角色")
    private String name;

    @ApiModelProperty(value = "性别", example = "1", notes = "1-男，2-女")
    private Integer sex;

    @ApiModelProperty(value = "等级", example = "10")
    private Integer grade;

    @ApiModelProperty(value = "最大生命值", example = "500")
    private Integer maxHp;

    @ApiModelProperty(value = "当前生命值", example = "450")
    private Integer hp;

    @ApiModelProperty(value = "最大法力值", example = "200")
    private Integer maxMp;

    @ApiModelProperty(value = "当前法力值", example = "180")
    private Integer mp;

    @ApiModelProperty(value = "力量", example = "25")
    private Integer force;

    @ApiModelProperty(value = "敏捷", example = "20")
    private Integer agile;

    @ApiModelProperty(value = "体魄", example = "30")
    private Integer physique;

    @ApiModelProperty(value = "悟性", example = "15")
    private Integer savvy;

    @ApiModelProperty(value = "攻击力", example = "100")
    private Integer attack;

    @ApiModelProperty(value = "防御力", example = "80")
    private Integer defense;

    @ApiModelProperty(value = "称号", example = "新手")
    private String title;

    @ApiModelProperty(value = "称号名称", example = "初出茅庐")
    private String titleName;

    @ApiModelProperty(value = "种族", example = "1", notes = "1-人，2-妖")
    private Integer race;

    @ApiModelProperty(value = "门派", example = "少林")
    private String school;

    @ApiModelProperty(value = "门派名称", example = "少林寺")
    private String schoolName;

    @ApiModelProperty(value = "阵营", example = "1")
    private Integer camp;

    @ApiModelProperty(value = "阵营名称", example = "正派")
    private String campName;

    @ApiModelProperty(value = "经验", example = "1500")
    private String experience;

    @ApiModelProperty(value = "本级经验", example = "500")
    private String benjiExperience;

    @ApiModelProperty(value = "下级经验", example = "2000")
    private String xiaExperience;

    @ApiModelProperty(value = "银子", example = "100")
    private String silver;

    @ApiModelProperty(value = "铜钱", example = "5000")
    private String copper;

    @ApiModelProperty(value = "PK值", example = "0")
    private Integer pkValue;

    @ApiModelProperty(value = "PK状态", example = "2", notes = "1-红名，2-白名")
    private Integer pks;

    @ApiModelProperty(value = "所在场景", example = "1001")
    private String map;

    @ApiModelProperty(value = "帮派", example = "1")
    private Integer tong;

    @ApiModelProperty(value = "帮派名称", example = "天下第一帮")
    private String tongName;

    @ApiModelProperty(value = "包裹容量", example = "20")
    private Integer wrapContent;

    @ApiModelProperty(value = "包裹剩余容量", example = "15")
    private Integer wrapSpare;

    @ApiModelProperty(value = "登录状态", example = "1", notes = "1-在线，0-离线")
    private Integer loginState;

    @ApiModelProperty(value = "杀人数量", example = "5")
    private Integer killNum;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "玩家状态", example = "1", notes = "1-新手，0-非新手")
    private Integer playerStateByNew;
}
