package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 帮派实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "guild")
public class Guild {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 帮派名称
     */
    @Column(name = "name", unique = true, length = 50)
    private String name;

    /**
     * 帮派简介
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 帮主角色ID
     */
    @Column(name = "leader_id")
    private Integer leaderId;

    /**
     * 帮主角色名称
     */
    @Column(name = "leader_name", length = 50)
    private String leaderName;

    /**
     * 帮派等级
     */
    @Column(name = "level")
    private Integer level = 1;

    /**
     * 帮派经验
     */
    @Column(name = "experience")
    private Long experience = 0L;

    /**
     * 帮派资金
     */
    @Column(name = "funds")
    private Long funds = 0L;

    /**
     * 当前成员数
     */
    @Column(name = "member_count")
    private Integer memberCount = 1;

    /**
     * 最大成员数
     */
    @Column(name = "max_members")
    private Integer maxMembers = 20;

    /**
     * 帮派状态 1正常 2解散中 3已解散
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 入帮条件 - 最低等级要求
     */
    @Column(name = "min_level")
    private Integer minLevel = 1;

    /**
     * 入帮方式 1自由加入 2需要审核 3禁止加入
     */
    @Column(name = "join_type")
    private Integer joinType = 2;

    /**
     * 帮派公告
     */
    @Column(name = "notice", length = 1000)
    private String notice;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 最后活跃时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_active_time")
    private LocalDateTime lastActiveTime;

    // 业务方法

    /**
     * 获取帮派状态名称
     */
    public String getStatusName() {
        switch (status) {
            case 1: return "正常";
            case 2: return "解散中";
            case 3: return "已解散";
            default: return "未知";
        }
    }

    /**
     * 获取入帮方式名称
     */
    public String getJoinTypeName() {
        switch (joinType) {
            case 1: return "自由加入";
            case 2: return "需要审核";
            case 3: return "禁止加入";
            default: return "未知";
        }
    }

    /**
     * 是否正常状态
     */
    public boolean isActive() {
        return status == 1;
    }

    /**
     * 是否可以加入新成员
     */
    public boolean canJoin() {
        return isActive() && memberCount < maxMembers && joinType != 3;
    }

    /**
     * 是否需要审核
     */
    public boolean needsApproval() {
        return joinType == 2;
    }

    /**
     * 是否满员
     */
    public boolean isFull() {
        return memberCount >= maxMembers;
    }

    /**
     * 计算升级所需经验
     */
    public Long getNextLevelExp() {
        return (long) (level * level * 1000 + level * 500);
    }

    /**
     * 判断是否可以升级
     */
    public boolean canLevelUp() {
        return experience >= getNextLevelExp() && funds >= getUpgradeCost();
    }

    /**
     * 计算升级费用
     */
    public Long getUpgradeCost() {
        return (long) (level * 10000);
    }

    /**
     * 升级帮派
     */
    public boolean levelUp() {
        if (!canLevelUp()) {
            return false;
        }

        this.experience -= getNextLevelExp();
        this.funds -= getUpgradeCost();
        this.level++;

        // 升级时增加最大成员数
        this.maxMembers += 5;

        return true;
    }

    /**
     * 增加帮派经验
     */
    public void addExperience(Long exp) {
        this.experience += exp;
    }

    /**
     * 增加帮派资金
     */
    public void addFunds(Long amount) {
        this.funds += amount;
    }

    /**
     * 扣除帮派资金
     */
    public boolean spendFunds(Long amount) {
        if (this.funds >= amount) {
            this.funds -= amount;
            return true;
        }
        return false;
    }

    /**
     * 增加成员
     */
    public void addMember() {
        this.memberCount++;
    }

    /**
     * 减少成员
     */
    public void removeMember() {
        if (this.memberCount > 0) {
            this.memberCount--;
        }
    }

    /**
     * 更新活跃时间
     */
    public void updateActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }

    /**
     * 获取帮派完整信息
     */
    public String getFullInfo() {
        return String.format("%s (Lv.%d) [%d/%d人] - %s", 
                name, level, memberCount, maxMembers, getStatusName());
    }

    /**
     * 计算帮派活跃度（天数）
     */
    public Long getInactiveDays() {
        if (lastActiveTime == null) {
            return 0L;
        }
        return java.time.Duration.between(lastActiveTime, LocalDateTime.now()).toDays();
    }

    /**
     * 是否长期不活跃
     */
    public boolean isInactive() {
        return getInactiveDays() > 30; // 30天未活跃
    }

    /**
     * 获取帮派成立天数
     */
    public Long getFoundedDays() {
        if (createTime == null) {
            return 0L;
        }
        return java.time.Duration.between(createTime, LocalDateTime.now()).toDays();
    }
}
