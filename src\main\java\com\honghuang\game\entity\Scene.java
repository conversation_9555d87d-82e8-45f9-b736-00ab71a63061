package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 场景实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "scene")
public class Scene {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 场景名称
     */
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 场景类型 1新手村 2城镇 3野外 4副本 5特殊场景
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 场景等级要求
     */
    @Column(name = "required_level")
    private Integer requiredLevel = 1;

    /**
     * 场景描述
     */
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 场景背景图
     */
    @Column(name = "background_image", length = 200)
    private String backgroundImage;

    /**
     * 场景音乐
     */
    @Column(name = "background_music", length = 200)
    private String backgroundMusic;

    /**
     * 地图宽度
     */
    @Column(name = "map_width")
    private Integer mapWidth = 1000;

    /**
     * 地图高度
     */
    @Column(name = "map_height")
    private Integer mapHeight = 1000;

    /**
     * 默认出生点X坐标
     */
    @Column(name = "spawn_x")
    private Integer spawnX = 500;

    /**
     * 默认出生点Y坐标
     */
    @Column(name = "spawn_y")
    private Integer spawnY = 500;

    /**
     * 是否安全区域 0否 1是
     */
    @Column(name = "safe_zone")
    private Integer safeZone = 0;

    /**
     * 是否允许PK 0不允许 1允许
     */
    @Column(name = "allow_pk")
    private Integer allowPk = 1;

    /**
     * 是否允许传送 0不允许 1允许
     */
    @Column(name = "allow_teleport")
    private Integer allowTeleport = 1;

    /**
     * 最大在线人数
     */
    @Column(name = "max_players")
    private Integer maxPlayers = 100;

    /**
     * 当前在线人数
     */
    @Column(name = "current_players")
    private Integer currentPlayers = 0;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    // 业务方法

    /**
     * 获取场景类型名称
     */
    public String getTypeName() {
        switch (type) {
            case 1: return "新手村";
            case 2: return "城镇";
            case 3: return "野外";
            case 4: return "副本";
            case 5: return "特殊场景";
            default: return "未知";
        }
    }

    /**
     * 是否为新手村
     */
    public boolean isNewbieVillage() {
        return type == 1;
    }

    /**
     * 是否为城镇
     */
    public boolean isTown() {
        return type == 2;
    }

    /**
     * 是否为野外
     */
    public boolean isWilderness() {
        return type == 3;
    }

    /**
     * 是否为副本
     */
    public boolean isDungeon() {
        return type == 4;
    }

    /**
     * 是否为特殊场景
     */
    public boolean isSpecialScene() {
        return type == 5;
    }

    /**
     * 是否为安全区域
     */
    public boolean isSafeZone() {
        return safeZone == 1;
    }

    /**
     * 是否允许PK
     */
    public boolean isAllowPk() {
        return allowPk == 1 && !isSafeZone();
    }

    /**
     * 是否允许传送
     */
    public boolean isAllowTeleport() {
        return allowTeleport == 1;
    }

    /**
     * 是否激活
     */
    public boolean isActive() {
        return active == 1;
    }

    /**
     * 是否已满员
     */
    public boolean isFull() {
        return currentPlayers >= maxPlayers;
    }

    /**
     * 检查角色是否可以进入
     */
    public boolean canEnter(PlayerRole role) {
        if (!isActive()) {
            return false;
        }
        
        if (role.getLevel() < requiredLevel) {
            return false;
        }
        
        if (isFull()) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查坐标是否在地图范围内
     */
    public boolean isValidPosition(Integer x, Integer y) {
        return x >= 0 && x <= mapWidth && y >= 0 && y <= mapHeight;
    }

    /**
     * 获取随机位置
     */
    public int[] getRandomPosition() {
        int x = (int) (Math.random() * mapWidth);
        int y = (int) (Math.random() * mapHeight);
        return new int[]{x, y};
    }

    /**
     * 计算两点距离
     */
    public double calculateDistance(Integer x1, Integer y1, Integer x2, Integer y2) {
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    }

    /**
     * 玩家进入场景
     */
    public void playerEnter() {
        this.currentPlayers++;
    }

    /**
     * 玩家离开场景
     */
    public void playerLeave() {
        if (this.currentPlayers > 0) {
            this.currentPlayers--;
        }
    }

    /**
     * 获取场景完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(name).append(" [").append(getTypeName()).append("]");
        sb.append(" (Lv.").append(requiredLevel).append("+)");
        sb.append(" [").append(currentPlayers).append("/").append(maxPlayers).append("]");
        
        if (isSafeZone()) {
            sb.append(" [安全区]");
        }
        
        return sb.toString();
    }

    /**
     * 获取地图大小描述
     */
    public String getMapSizeDescription() {
        return mapWidth + "x" + mapHeight;
    }

    /**
     * 获取出生点描述
     */
    public String getSpawnPointDescription() {
        return "(" + spawnX + ", " + spawnY + ")";
    }

    /**
     * 获取在线率
     */
    public double getOnlineRate() {
        if (maxPlayers == 0) return 0.0;
        return (double) currentPlayers / maxPlayers * 100;
    }

    /**
     * 是否拥挤
     */
    public boolean isCrowded() {
        return getOnlineRate() > 80; // 超过80%算拥挤
    }

    /**
     * 重置在线人数
     */
    public void resetPlayerCount() {
        this.currentPlayers = 0;
    }

    /**
     * 设置地图大小
     */
    public void setMapSize(Integer width, Integer height) {
        this.mapWidth = width;
        this.mapHeight = height;
        
        // 确保出生点在地图范围内
        if (spawnX > width) spawnX = width / 2;
        if (spawnY > height) spawnY = height / 2;
    }

    /**
     * 设置出生点
     */
    public void setSpawnPoint(Integer x, Integer y) {
        if (isValidPosition(x, y)) {
            this.spawnX = x;
            this.spawnY = y;
        }
    }
}
