package com.honghuang.game.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 战斗记录实体类
 */
@Entity
@Table(name = "battle_record")
public class BattleRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "player_role_id")
    private Integer playerRoleId;
    
    @Column(name = "monster_id")
    private Integer monsterId;
    
    @Column(name = "monster_name")
    private String monsterName;
    
    @Column(name = "battle_result")
    private Integer battleResult; // 1:胜利 0:失败
    
    @Column(name = "rounds")
    private Integer rounds;
    
    @Column(name = "exp_gained")
    private Integer expGained;
    
    @Column(name = "copper_gained")
    private Integer copperGained;
    
    @Column(name = "items_dropped")
    private String itemsDropped; // JSON格式存储掉落物品
    
    @Column(name = "battle_log", columnDefinition = "TEXT")
    private String battleLog; // 战斗日志
    
    @Column(name = "battle_time")
    private LocalDateTime battleTime;
    
    @Column(name = "player_hp_before")
    private Integer playerHpBefore;
    
    @Column(name = "player_hp_after")
    private Integer playerHpAfter;
    
    @Column(name = "player_level")
    private Integer playerLevel;
    
    @Column(name = "monster_level")
    private Integer monsterLevel;
    
    // 构造函数
    public BattleRecord() {
        this.battleTime = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getPlayerRoleId() {
        return playerRoleId;
    }
    
    public void setPlayerRoleId(Integer playerRoleId) {
        this.playerRoleId = playerRoleId;
    }
    
    public Integer getMonsterId() {
        return monsterId;
    }
    
    public void setMonsterId(Integer monsterId) {
        this.monsterId = monsterId;
    }
    
    public String getMonsterName() {
        return monsterName;
    }
    
    public void setMonsterName(String monsterName) {
        this.monsterName = monsterName;
    }
    
    public Integer getBattleResult() {
        return battleResult;
    }
    
    public void setBattleResult(Integer battleResult) {
        this.battleResult = battleResult;
    }
    
    public Integer getRounds() {
        return rounds;
    }
    
    public void setRounds(Integer rounds) {
        this.rounds = rounds;
    }
    
    public Integer getExpGained() {
        return expGained;
    }
    
    public void setExpGained(Integer expGained) {
        this.expGained = expGained;
    }
    
    public Integer getCopperGained() {
        return copperGained;
    }
    
    public void setCopperGained(Integer copperGained) {
        this.copperGained = copperGained;
    }
    
    public String getItemsDropped() {
        return itemsDropped;
    }
    
    public void setItemsDropped(String itemsDropped) {
        this.itemsDropped = itemsDropped;
    }
    
    public String getBattleLog() {
        return battleLog;
    }
    
    public void setBattleLog(String battleLog) {
        this.battleLog = battleLog;
    }
    
    public LocalDateTime getBattleTime() {
        return battleTime;
    }
    
    public void setBattleTime(LocalDateTime battleTime) {
        this.battleTime = battleTime;
    }
    
    public Integer getPlayerHpBefore() {
        return playerHpBefore;
    }
    
    public void setPlayerHpBefore(Integer playerHpBefore) {
        this.playerHpBefore = playerHpBefore;
    }
    
    public Integer getPlayerHpAfter() {
        return playerHpAfter;
    }
    
    public void setPlayerHpAfter(Integer playerHpAfter) {
        this.playerHpAfter = playerHpAfter;
    }
    
    public Integer getPlayerLevel() {
        return playerLevel;
    }
    
    public void setPlayerLevel(Integer playerLevel) {
        this.playerLevel = playerLevel;
    }
    
    public Integer getMonsterLevel() {
        return monsterLevel;
    }
    
    public void setMonsterLevel(Integer monsterLevel) {
        this.monsterLevel = monsterLevel;
    }
    
    // 业务方法
    public boolean isVictory() {
        return battleResult != null && battleResult == 1;
    }
    
    public boolean isDefeat() {
        return battleResult != null && battleResult == 0;
    }
}
