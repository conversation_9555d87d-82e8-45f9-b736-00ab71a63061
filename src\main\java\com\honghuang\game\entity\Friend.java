package com.honghuang.game.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 好友实体类
 */
@Entity
@Table(name = "friend")
public class Friend {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "player_id")
    private Integer playerId;
    
    @Column(name = "friend_id")
    private Integer friendId;
    
    @Column(name = "friend_name")
    private String friendName;
    
    @Column(name = "friend_level")
    private Integer friendLevel;
    
    @Column(name = "add_time")
    private LocalDateTime addTime;
    
    @Column(name = "last_chat_time")
    private LocalDateTime lastChatTime;
    
    @Column(name = "friendship_level")
    private Integer friendshipLevel; // 友好度等级
    
    @Column(name = "friendship_exp")
    private Integer friendshipExp; // 友好度经验
    
    @Column(name = "is_online")
    private Integer isOnline;
    
    @Column(name = "last_online_time")
    private LocalDateTime lastOnlineTime;
    
    @Column(name = "friend_status")
    private Integer status; // 状态：1-正常 2-黑名单 3-待确认
    
    @Column(name = "remark")
    private String remark; // 备注
    
    @Column(name = "intimacy")
    private Integer intimacy; // 亲密度
    
    // 构造函数
    public Friend() {
        this.addTime = LocalDateTime.now();
        this.friendshipLevel = 1;
        this.friendshipExp = 0;
        this.status = 1;
        this.intimacy = 0;
        this.isOnline = 0;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getPlayerId() {
        return playerId;
    }
    
    public void setPlayerId(Integer playerId) {
        this.playerId = playerId;
    }
    
    public Integer getFriendId() {
        return friendId;
    }
    
    public void setFriendId(Integer friendId) {
        this.friendId = friendId;
    }
    
    public String getFriendName() {
        return friendName;
    }
    
    public void setFriendName(String friendName) {
        this.friendName = friendName;
    }
    
    public Integer getFriendLevel() {
        return friendLevel;
    }
    
    public void setFriendLevel(Integer friendLevel) {
        this.friendLevel = friendLevel;
    }
    
    public LocalDateTime getAddTime() {
        return addTime;
    }
    
    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }
    
    public LocalDateTime getLastChatTime() {
        return lastChatTime;
    }
    
    public void setLastChatTime(LocalDateTime lastChatTime) {
        this.lastChatTime = lastChatTime;
    }
    
    public Integer getFriendshipLevel() {
        return friendshipLevel;
    }
    
    public void setFriendshipLevel(Integer friendshipLevel) {
        this.friendshipLevel = friendshipLevel;
    }
    
    public Integer getFriendshipExp() {
        return friendshipExp;
    }
    
    public void setFriendshipExp(Integer friendshipExp) {
        this.friendshipExp = friendshipExp;
    }
    
    public Integer getIsOnline() {
        return isOnline;
    }
    
    public void setIsOnline(Integer isOnline) {
        this.isOnline = isOnline;
    }
    
    public LocalDateTime getLastOnlineTime() {
        return lastOnlineTime;
    }
    
    public void setLastOnlineTime(LocalDateTime lastOnlineTime) {
        this.lastOnlineTime = lastOnlineTime;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public Integer getIntimacy() {
        return intimacy;
    }
    
    public void setIntimacy(Integer intimacy) {
        this.intimacy = intimacy;
    }
    
    // 业务方法
    public boolean isOnline() {
        return isOnline != null && isOnline == 1;
    }
    
    public boolean isNormal() {
        return status != null && status == 1;
    }
    
    public boolean isBlocked() {
        return status != null && status == 2;
    }
    
    public boolean isPending() {
        return status != null && status == 3;
    }
    
    public String getStatusName() {
        if (status == null) return "未知";
        switch (status) {
            case 1: return "正常";
            case 2: return "黑名单";
            case 3: return "待确认";
            default: return "未知";
        }
    }
    
    public String getOnlineStatusName() {
        return isOnline() ? "在线" : "离线";
    }
}
