package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 师徒申请实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "mentor_application")
public class MentorApplication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 申请类型 1拜师申请 2收徒申请
     */
    @Column(name = "application_type")
    private Integer applicationType;

    /**
     * 申请者角色ID
     */
    @Column(name = "applicant_id")
    private Integer applicantId;

    /**
     * 申请者角色名称
     */
    @Column(name = "applicant_name", length = 50)
    private String applicantName;

    /**
     * 申请者等级
     */
    @Column(name = "applicant_level")
    private Integer applicantLevel;

    /**
     * 目标角色ID
     */
    @Column(name = "target_id")
    private Integer targetId;

    /**
     * 目标角色名称
     */
    @Column(name = "target_name", length = 50)
    private String targetName;

    /**
     * 目标角色等级
     */
    @Column(name = "target_level")
    private Integer targetLevel;

    /**
     * 申请状态 0待处理 1已同意 2已拒绝 3已过期
     */
    @Column(name = "status")
    private Integer status = 0;

    /**
     * 申请消息
     */
    @Column(name = "message", length = 200)
    private String message;

    /**
     * 申请时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "apply_time")
    private LocalDateTime applyTime;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "process_time")
    private LocalDateTime processTime;

    /**
     * 处理备注
     */
    @Column(name = "process_note", length = 200)
    private String processNote;

    /**
     * 申请者信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "applicant_id", insertable = false, updatable = false)
    private PlayerRole applicant;

    /**
     * 目标角色信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_id", insertable = false, updatable = false)
    private PlayerRole target;

    // 业务方法

    /**
     * 获取申请类型名称
     */
    public String getApplicationTypeName() {
        switch (applicationType) {
            case 1: return "拜师申请";
            case 2: return "收徒申请";
            default: return "未知";
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        switch (status) {
            case 0: return "待处理";
            case 1: return "已同意";
            case 2: return "已拒绝";
            case 3: return "已过期";
            default: return "未知";
        }
    }

    /**
     * 是否为拜师申请
     */
    public boolean isApprenticeApplication() {
        return applicationType == 1;
    }

    /**
     * 是否为收徒申请
     */
    public boolean isMentorApplication() {
        return applicationType == 2;
    }

    /**
     * 是否待处理
     */
    public boolean isPending() {
        return status == 0;
    }

    /**
     * 是否已同意
     */
    public boolean isApproved() {
        return status == 1;
    }

    /**
     * 是否已拒绝
     */
    public boolean isRejected() {
        return status == 2;
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return status == 3;
    }

    /**
     * 同意申请
     */
    public void approve(String note) {
        this.status = 1;
        this.processTime = LocalDateTime.now();
        this.processNote = note;
    }

    /**
     * 拒绝申请
     */
    public void reject(String note) {
        this.status = 2;
        this.processTime = LocalDateTime.now();
        this.processNote = note;
    }

    /**
     * 设置为过期
     */
    public void expire() {
        this.status = 3;
        this.processTime = LocalDateTime.now();
    }

    /**
     * 检查是否应该过期
     */
    public boolean shouldExpire() {
        if (!isPending()) {
            return false;
        }
        
        // 申请3天后自动过期
        LocalDateTime expireTime = applyTime.plusDays(3);
        return LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 获取申请等待时间（小时）
     */
    public Long getPendingHours() {
        if (!isPending() || applyTime == null) {
            return null;
        }
        return java.time.Duration.between(applyTime, LocalDateTime.now()).toHours();
    }

    /**
     * 获取处理用时（小时）
     */
    public Long getProcessHours() {
        if (applyTime == null || processTime == null) {
            return null;
        }
        return java.time.Duration.between(applyTime, processTime).toHours();
    }

    /**
     * 获取等级差距
     */
    public Integer getLevelGap() {
        if (applicantLevel == null || targetLevel == null) {
            return 0;
        }
        
        if (isApprenticeApplication()) {
            // 拜师申请：目标等级 - 申请者等级
            return targetLevel - applicantLevel;
        } else {
            // 收徒申请：申请者等级 - 目标等级
            return applicantLevel - targetLevel;
        }
    }

    /**
     * 检查等级差距是否合适
     */
    public boolean isLevelGapAppropriate() {
        int gap = getLevelGap();
        return gap >= 5 && gap <= 50; // 师父比徒弟高5-50级比较合适
    }

    /**
     * 获取申请完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        
        if (isApprenticeApplication()) {
            sb.append(applicantName).append("(Lv.").append(applicantLevel).append(") ");
            sb.append("申请拜 ").append(targetName).append("(Lv.").append(targetLevel).append(") 为师");
        } else {
            sb.append(applicantName).append("(Lv.").append(applicantLevel).append(") ");
            sb.append("申请收 ").append(targetName).append("(Lv.").append(targetLevel).append(") 为徒");
        }
        
        sb.append(" - ").append(getStatusName());
        
        if (message != null && !message.trim().isEmpty()) {
            sb.append(" [").append(message).append("]");
        }
        
        return sb.toString();
    }

    /**
     * 获取剩余有效时间（小时）
     */
    public Long getRemainingValidHours() {
        if (!isPending()) {
            return 0L;
        }
        
        LocalDateTime expireTime = applyTime.plusDays(3);
        LocalDateTime now = LocalDateTime.now();
        
        if (now.isAfter(expireTime)) {
            return 0L;
        }
        
        return java.time.Duration.between(now, expireTime).toHours();
    }

    /**
     * 获取师父和徒弟ID（根据申请类型）
     */
    public Integer getMentorId() {
        if (isApprenticeApplication()) {
            return targetId; // 拜师申请中，目标是师父
        } else {
            return applicantId; // 收徒申请中，申请者是师父
        }
    }

    /**
     * 获取师父和徒弟ID（根据申请类型）
     */
    public Integer getApprenticeId() {
        if (isApprenticeApplication()) {
            return applicantId; // 拜师申请中，申请者是徒弟
        } else {
            return targetId; // 收徒申请中，目标是徒弟
        }
    }

    /**
     * 获取师父名称
     */
    public String getMentorName() {
        if (isApprenticeApplication()) {
            return targetName;
        } else {
            return applicantName;
        }
    }

    /**
     * 获取徒弟名称
     */
    public String getApprenticeName() {
        if (isApprenticeApplication()) {
            return applicantName;
        } else {
            return targetName;
        }
    }

    /**
     * 获取师父等级
     */
    public Integer getMentorLevel() {
        if (isApprenticeApplication()) {
            return targetLevel;
        } else {
            return applicantLevel;
        }
    }

    /**
     * 获取徒弟等级
     */
    public Integer getApprenticeLevel() {
        if (isApprenticeApplication()) {
            return applicantLevel;
        } else {
            return targetLevel;
        }
    }
}
