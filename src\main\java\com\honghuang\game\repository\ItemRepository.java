package com.honghuang.game.repository;

import com.honghuang.game.entity.Item;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 物品数据访问层
 */
@Repository
public interface ItemRepository extends JpaRepository<Item, Integer> {

    /**
     * 查找激活的物品
     */
    List<Item> findByActiveOrderByTypeAscQualityDesc(Integer active);

    /**
     * 根据类型查找物品
     */
    List<Item> findByTypeAndActiveOrderByQualityDesc(Integer type, Integer active);

    /**
     * 根据品质查找物品
     */
    List<Item> findByQualityAndActiveOrderByTypeAsc(Integer quality, Integer active);

    /**
     * 查找适合角色的物品
     */
    @Query("SELECT i FROM Item i WHERE i.requiredLevel <= :level " +
           "AND (i.requiredRace = 0 OR i.requiredRace = :race) " +
           "AND (i.requiredSex = 0 OR i.requiredSex = :sex) " +
           "AND i.active = 1 ORDER BY i.type ASC, i.quality DESC")
    List<Item> findSuitableItems(@Param("level") Integer level, @Param("race") Integer race, @Param("sex") Integer sex);

    /**
     * 根据名称模糊查找物品
     */
    @Query("SELECT i FROM Item i WHERE i.name LIKE %:name% AND i.active = 1 ORDER BY i.quality DESC")
    List<Item> findByNameContaining(@Param("name") String name);

    /**
     * 查找装备类物品
     */
    @Query("SELECT i FROM Item i WHERE i.type = 1 AND i.active = 1 ORDER BY i.quality DESC")
    List<Item> findEquipmentItems();

    /**
     * 查找消耗品
     */
    @Query("SELECT i FROM Item i WHERE i.type = 2 AND i.active = 1 ORDER BY i.quality DESC")
    List<Item> findConsumableItems();

    /**
     * 查找材料
     */
    @Query("SELECT i FROM Item i WHERE i.type = 3 AND i.active = 1 ORDER BY i.quality DESC")
    List<Item> findMaterialItems();

    /**
     * 查找任务物品
     */
    @Query("SELECT i FROM Item i WHERE i.type = 4 AND i.active = 1 ORDER BY i.quality DESC")
    List<Item> findQuestItems();

    /**
     * 查找可堆叠物品
     */
    @Query("SELECT i FROM Item i WHERE i.stackable = 1 AND i.active = 1 ORDER BY i.type ASC")
    List<Item> findStackableItems();

    /**
     * 查找可交易物品
     */
    @Query("SELECT i FROM Item i WHERE i.tradeable = 1 AND i.active = 1 ORDER BY i.quality DESC")
    List<Item> findTradeableItems();

    /**
     * 查找有使用效果的物品
     */
    @Query("SELECT i FROM Item i WHERE i.effectType > 0 AND i.active = 1 ORDER BY i.effectType ASC")
    List<Item> findItemsWithEffect();

    /**
     * 根据价格范围查找物品
     */
    @Query("SELECT i FROM Item i WHERE i.sellPrice BETWEEN :minPrice AND :maxPrice AND i.active = 1 ORDER BY i.sellPrice ASC")
    List<Item> findByPriceRange(@Param("minPrice") Long minPrice, @Param("maxPrice") Long maxPrice);

    /**
     * 查找指定等级要求的物品
     */
    List<Item> findByRequiredLevelAndActiveOrderByQualityDesc(Integer requiredLevel, Integer active);

    /**
     * 查找低于指定等级的物品
     */
    @Query("SELECT i FROM Item i WHERE i.requiredLevel <= :level AND i.active = 1 ORDER BY i.requiredLevel DESC")
    List<Item> findByMaxLevel(@Param("level") Integer level);

    /**
     * 查找高于指定等级的物品
     */
    @Query("SELECT i FROM Item i WHERE i.requiredLevel >= :level AND i.active = 1 ORDER BY i.requiredLevel ASC")
    List<Item> findByMinLevel(@Param("level") Integer level);
}
