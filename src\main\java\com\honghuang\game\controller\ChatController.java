package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.ChatMessage;
import com.honghuang.game.service.ChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 聊天控制器
 */
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    @Autowired
    private ChatService chatService;

    /**
     * 发送聊天消息
     */
    @PostMapping("/send")
    public Result<ChatMessage> sendMessage(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String content = (String) request.get("content");
            Integer channelType = (Integer) request.get("channelType");
            Integer receiverId = (Integer) request.get("receiverId");
            Integer factionId = (Integer) request.get("factionId");
            
            if (content == null || content.trim().isEmpty()) {
                return Result.error(400, "消息内容不能为空");
            }
            
            if (channelType == null) {
                return Result.error(400, "频道类型不能为空");
            }
            
            ChatMessage message = chatService.sendMessage(currentRoleId, content, channelType, receiverId, factionId);
            return Result.success("消息发送成功", message);
        } catch (Exception e) {
            return Result.error(500, "消息发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送世界消息
     */
    @PostMapping("/world")
    public Result<ChatMessage> sendWorldMessage(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String content = request.get("content");
            
            if (content == null || content.trim().isEmpty()) {
                return Result.error(400, "消息内容不能为空");
            }
            
            ChatMessage message = chatService.sendWorldMessage(currentRoleId, content);
            return Result.success("世界消息发送成功", message);
        } catch (Exception e) {
            return Result.error(500, "世界消息发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送私聊消息
     */
    @PostMapping("/private")
    public Result<ChatMessage> sendPrivateMessage(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            String content = (String) request.get("content");
            Integer receiverId = (Integer) request.get("receiverId");
            
            if (content == null || content.trim().isEmpty()) {
                return Result.error(400, "消息内容不能为空");
            }
            
            if (receiverId == null) {
                return Result.error(400, "接收者不能为空");
            }
            
            ChatMessage message = chatService.sendPrivateMessage(currentRoleId, receiverId, content);
            return Result.success("私聊消息发送成功", message);
        } catch (Exception e) {
            return Result.error(500, "私聊消息发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取世界频道消息
     */
    @GetMapping("/world")
    public Result<List<ChatMessage>> getWorldMessages(@RequestParam(defaultValue = "50") int limit) {
        try {
            List<ChatMessage> messages = chatService.getWorldMessages(limit);
            return Result.success("获取世界消息成功", messages);
        } catch (Exception e) {
            return Result.error(500, "获取世界消息失败: " + e.getMessage());
        }
    }

    /**
     * 获取私聊消息
     */
    @GetMapping("/private/{friendId}")
    public Result<List<ChatMessage>> getPrivateMessages(@PathVariable Integer friendId,
                                                        @RequestParam(defaultValue = "50") int limit,
                                                        HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<ChatMessage> messages = chatService.getPrivateMessages(currentRoleId, friendId, limit);
            return Result.success("获取私聊消息成功", messages);
        } catch (Exception e) {
            return Result.error(500, "获取私聊消息失败: " + e.getMessage());
        }
    }

    /**
     * 获取帮派频道消息
     */
    @GetMapping("/faction/{factionId}")
    public Result<List<ChatMessage>> getFactionMessages(@PathVariable Integer factionId,
                                                        @RequestParam(defaultValue = "50") int limit) {
        try {
            List<ChatMessage> messages = chatService.getFactionMessages(factionId, limit);
            return Result.success("获取帮派消息成功", messages);
        } catch (Exception e) {
            return Result.error(500, "获取帮派消息失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统消息
     */
    @GetMapping("/system")
    public Result<List<ChatMessage>> getSystemMessages(@RequestParam(defaultValue = "50") int limit) {
        try {
            List<ChatMessage> messages = chatService.getSystemMessages(limit);
            return Result.success("获取系统消息成功", messages);
        } catch (Exception e) {
            return Result.error(500, "获取系统消息失败: " + e.getMessage());
        }
    }

    /**
     * 获取聊天记录
     */
    @GetMapping("/history")
    public Result<List<ChatMessage>> getChatHistory(@RequestParam(defaultValue = "50") int limit,
                                                   HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<ChatMessage> messages = chatService.getPlayerChatHistory(currentRoleId, limit);
            return Result.success("获取聊天记录成功", messages);
        } catch (Exception e) {
            return Result.error(500, "获取聊天记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近消息
     */
    @GetMapping("/recent/{channelType}")
    public Result<List<ChatMessage>> getRecentMessages(@PathVariable Integer channelType,
                                                       @RequestParam(defaultValue = "20") int limit) {
        try {
            List<ChatMessage> messages = chatService.getRecentMessages(channelType, limit);
            return Result.success("获取最近消息成功", messages);
        } catch (Exception e) {
            return Result.error(500, "获取最近消息失败: " + e.getMessage());
        }
    }

    /**
     * 删除聊天消息
     */
    @DeleteMapping("/{messageId}")
    public Result<Void> deleteMessage(@PathVariable Integer messageId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = chatService.deleteMessage(messageId, currentRoleId);
            if (success) {
                return Result.success("消息删除成功");
            } else {
                return Result.error(400, "消息删除失败");
            }
        } catch (Exception e) {
            return Result.error(500, "消息删除失败: " + e.getMessage());
        }
    }

    /**
     * 广播系统公告
     */
    @PostMapping("/announcement")
    public Result<Void> broadcastAnnouncement(@RequestBody Map<String, String> request) {
        try {
            String announcement = request.get("announcement");
            
            if (announcement == null || announcement.trim().isEmpty()) {
                return Result.error(400, "公告内容不能为空");
            }
            
            chatService.broadcastAnnouncement(announcement);
            return Result.success("系统公告发送成功");
        } catch (Exception e) {
            return Result.error(500, "系统公告发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取聊天统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getChatStatistics() {
        try {
            Map<String, Object> stats = chatService.getChatStatistics();
            return Result.success("获取聊天统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取聊天统计失败: " + e.getMessage());
        }
    }

    /**
     * 检查敏感词
     */
    @PostMapping("/check-sensitive")
    public Result<Map<String, Object>> checkSensitiveWords(@RequestBody Map<String, String> request) {
        try {
            String content = request.get("content");
            
            if (content == null) {
                return Result.error(400, "内容不能为空");
            }
            
            boolean containsSensitive = chatService.containsSensitiveWords(content);
            
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("containsSensitive", containsSensitive);
            result.put("content", content);
            
            return Result.success("敏感词检查完成", result);
        } catch (Exception e) {
            return Result.error(500, "敏感词检查失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期消息
     */
    @PostMapping("/cleanup")
    public Result<Void> cleanupExpiredMessages() {
        try {
            chatService.cleanupExpiredMessages();
            return Result.success("清理过期消息成功");
        } catch (Exception e) {
            return Result.error(500, "清理过期消息失败: " + e.getMessage());
        }
    }
}
