#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
jygame项目中文编码转换脚本
将GBK/GB2312编码的文件转换为UTF-8编码
"""

import os
import shutil
import chardet
from datetime import datetime
import sys

def detect_encoding(file_path):
    """检测文件编码"""
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            return result['encoding'], result['confidence']
    except Exception as e:
        print(f"检测编码失败 {file_path}: {e}")
        return None, 0

def convert_file_encoding(file_path, source_encoding, target_encoding='utf-8'):
    """转换文件编码"""
    try:
        # 读取原文件
        with open(file_path, 'r', encoding=source_encoding) as f:
            content = f.read()
        
        # 写入UTF-8编码
        with open(file_path, 'w', encoding=target_encoding) as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"转换编码失败 {file_path}: {e}")
        return False

def backup_file(file_path, backup_dir):
    """备份文件"""
    try:
        # 计算相对路径
        rel_path = os.path.relpath(file_path, '.')
        backup_path = os.path.join(backup_dir, rel_path)
        
        # 创建备份目录
        backup_file_dir = os.path.dirname(backup_path)
        os.makedirs(backup_file_dir, exist_ok=True)
        
        # 复制文件
        shutil.copy2(file_path, backup_path)
        return True
    except Exception as e:
        print(f"备份文件失败 {file_path}: {e}")
        return False

def should_process_file(file_path):
    """判断是否需要处理该文件"""
    # 文件扩展名白名单
    extensions = {'.java', '.sql', '.properties', '.xml', '.jsp', '.js', '.html', '.txt'}
    
    # 排除的目录
    exclude_dirs = {'target', '.git', 'logs', 'data', 'node_modules', '.idea'}
    
    # 检查扩展名
    _, ext = os.path.splitext(file_path)
    if ext.lower() not in extensions:
        return False
    
    # 检查路径中是否包含排除的目录
    path_parts = file_path.split(os.sep)
    for part in path_parts:
        if part in exclude_dirs:
            return False
    
    return True

def main():
    print("开始修复jygame项目中文编码问题...")
    print("=" * 50)
    
    # 创建备份目录
    backup_dir = f"encoding_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    print(f"创建备份目录: {backup_dir}")
    
    # 统计信息
    total_files = 0
    processed_files = 0
    converted_files = 0
    failed_files = 0
    
    # 遍历所有文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            file_path = os.path.join(root, file)
            
            # 跳过备份目录
            if backup_dir in file_path:
                continue
                
            if should_process_file(file_path):
                total_files += 1
                
                # 检测编码
                encoding, confidence = detect_encoding(file_path)
                
                if encoding is None:
                    continue
                
                # 如果已经是UTF-8，跳过
                if encoding.lower() in ['utf-8', 'ascii']:
                    continue
                
                processed_files += 1
                print(f"\n处理文件: {file_path}")
                print(f"  检测到编码: {encoding} (置信度: {confidence:.2f})")
                
                # 备份原文件
                if not backup_file(file_path, backup_dir):
                    failed_files += 1
                    continue
                
                # 尝试转换编码
                success = False
                
                # 尝试不同的源编码
                encodings_to_try = []
                
                if encoding.lower() in ['gb2312', 'gbk', 'gb18030']:
                    encodings_to_try = ['gbk', 'gb2312', 'gb18030']
                elif encoding.lower() in ['iso-8859-1', 'windows-1252']:
                    # 这些可能是错误检测的GBK编码
                    encodings_to_try = ['gbk', 'gb2312', 'iso-8859-1']
                else:
                    encodings_to_try = [encoding, 'gbk', 'gb2312']
                
                for src_encoding in encodings_to_try:
                    try:
                        if convert_file_encoding(file_path, src_encoding):
                            print(f"  ✓ 成功从 {src_encoding} 转换为 UTF-8")
                            converted_files += 1
                            success = True
                            break
                    except:
                        continue
                
                if not success:
                    print(f"  ✗ 转换失败")
                    failed_files += 1
                    # 恢复原文件
                    backup_path = os.path.join(backup_dir, os.path.relpath(file_path, '.'))
                    if os.path.exists(backup_path):
                        shutil.copy2(backup_path, file_path)
    
    # 输出统计信息
    print("\n" + "=" * 50)
    print("编码转换完成！")
    print(f"扫描文件总数: {total_files}")
    print(f"需要处理的文件: {processed_files}")
    print(f"成功转换: {converted_files}")
    print(f"转换失败: {failed_files}")
    print(f"备份目录: {backup_dir}")
    
    # 验证转换结果
    print("\n验证转换结果...")
    remaining_non_utf8 = 0
    for root, dirs, files in os.walk('.'):
        for file in files:
            file_path = os.path.join(root, file)
            
            if backup_dir in file_path:
                continue
                
            if should_process_file(file_path):
                encoding, confidence = detect_encoding(file_path)
                if encoding and encoding.lower() not in ['utf-8', 'ascii']:
                    if remaining_non_utf8 < 10:  # 只显示前10个
                        print(f"  仍为非UTF-8: {file_path} ({encoding})")
                    remaining_non_utf8 += 1
    
    if remaining_non_utf8 == 0:
        print("  ✓ 所有文件都已转换为UTF-8编码")
    else:
        print(f"  还有 {remaining_non_utf8} 个文件不是UTF-8编码")
    
    print(f"\n如果转换结果满意，可以删除备份目录: rm -rf {backup_dir}")
    print("如果需要恢复，可以从备份目录恢复文件")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n脚本执行出错: {e}")
        sys.exit(1)
