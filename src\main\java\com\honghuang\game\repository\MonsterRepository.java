package com.honghuang.game.repository;

import com.honghuang.game.entity.Monster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 怪物数据访问接口
 */
@Repository
public interface MonsterRepository extends JpaRepository<Monster, Integer> {
    
    /**
     * 根据类型查找怪物
     */
    List<Monster> findByType(Integer type);
    
    /**
     * 根据等级范围查找怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.level BETWEEN :minLevel AND :maxLevel")
    List<Monster> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);
    
    /**
     * 查找可攻击的怪物
     */
    @Query("SELECT m FROM Monster m WHERE m.isActive = 1 AND m.type != 4")
    List<Monster> findAttackableMonsters();

    /**
     * 根据场景查找怪物（简化查询，直接返回所有可攻击怪物）
     */
    @Query("SELECT m FROM Monster m WHERE m.isActive = 1 AND m.type != 4")
    List<Monster> findBySceneId(@Param("sceneId") Integer sceneId);
    
    /**
     * 根据名称模糊查找
     */
    List<Monster> findByNameContaining(String name);
    
    /**
     * 查找指定等级的怪物
     */
    List<Monster> findByLevel(Integer level);
    
    /**
     * 查找有掉落物品的怪物（简化查询，返回所有激活的怪物）
     */
    @Query("SELECT m FROM Monster m WHERE m.isActive = 1")
    List<Monster> findMonstersWithDrops();
}
