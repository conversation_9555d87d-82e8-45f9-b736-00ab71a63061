package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 物品实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "item")
public class Item {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 物品名称
     */
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 物品类型 1装备 2消耗品 3材料 4任务物品 5宝石 6书籍 7其他
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 物品子类型
     */
    @Column(name = "sub_type")
    private Integer subType;

    /**
     * 物品品质 1白色 2绿色 3蓝色 4紫色 5橙色 6红色
     */
    @Column(name = "quality")
    private Integer quality = 1;

    /**
     * 使用等级要求
     */
    @Column(name = "required_level")
    private Integer requiredLevel = 1;

    /**
     * 种族要求 0无要求 1巫族 2妖族
     */
    @Column(name = "required_race")
    private Integer requiredRace = 0;

    /**
     * 性别要求 0无要求 1男性 2女性
     */
    @Column(name = "required_sex")
    private Integer requiredSex = 0;

    /**
     * 物品描述
     */
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 物品图标
     */
    @Column(name = "icon", length = 100)
    private String icon;

    /**
     * 出售价格
     */
    @Column(name = "sell_price")
    private Long sellPrice = 0L;

    /**
     * 购买价格
     */
    @Column(name = "buy_price")
    private Long buyPrice = 0L;

    /**
     * 是否可堆叠 0不可 1可以
     */
    @Column(name = "stackable")
    private Integer stackable = 0;

    /**
     * 最大堆叠数量
     */
    @Column(name = "max_stack")
    private Integer maxStack = 1;

    /**
     * 是否可交易 0不可 1可以
     */
    @Column(name = "tradeable")
    private Integer tradeable = 1;

    /**
     * 是否可丢弃 0不可 1可以
     */
    @Column(name = "droppable")
    private Integer droppable = 1;

    /**
     * 使用效果类型 0无效果 1恢复HP 2恢复MP 3增加经验 4学习技能 5其他
     */
    @Column(name = "effect_type")
    private Integer effectType = 0;

    /**
     * 使用效果值
     */
    @Column(name = "effect_value")
    private Integer effectValue = 0;

    /**
     * 使用冷却时间（秒）
     */
    @Column(name = "cooldown")
    private Integer cooldown = 0;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    // 业务方法

    /**
     * 获取物品类型名称
     */
    public String getTypeName() {
        switch (type) {
            case 1: return "装备";
            case 2: return "消耗品";
            case 3: return "材料";
            case 4: return "任务物品";
            case 5: return "宝石";
            case 6: return "书籍";
            case 7: return "其他";
            default: return "未知";
        }
    }

    /**
     * 获取品质名称
     */
    public String getQualityName() {
        switch (quality) {
            case 1: return "白色";
            case 2: return "绿色";
            case 3: return "蓝色";
            case 4: return "紫色";
            case 5: return "橙色";
            case 6: return "红色";
            default: return "未知";
        }
    }

    /**
     * 获取品质颜色代码
     */
    public String getQualityColor() {
        switch (quality) {
            case 1: return "#FFFFFF"; // 白色
            case 2: return "#00FF00"; // 绿色
            case 3: return "#0080FF"; // 蓝色
            case 4: return "#8000FF"; // 紫色
            case 5: return "#FF8000"; // 橙色
            case 6: return "#FF0000"; // 红色
            default: return "#FFFFFF";
        }
    }

    /**
     * 是否可堆叠
     */
    public boolean isStackable() {
        return stackable == 1;
    }

    /**
     * 是否可交易
     */
    public boolean isTradeable() {
        return tradeable == 1;
    }

    /**
     * 是否可丢弃
     */
    public boolean isDroppable() {
        return droppable == 1;
    }

    /**
     * 是否有使用效果
     */
    public boolean hasEffect() {
        return effectType > 0;
    }

    /**
     * 是否为装备
     */
    public boolean isEquipment() {
        return type == 1;
    }

    /**
     * 是否为消耗品
     */
    public boolean isConsumable() {
        return type == 2;
    }

    /**
     * 是否为材料
     */
    public boolean isMaterial() {
        return type == 3;
    }

    /**
     * 是否为任务物品
     */
    public boolean isQuestItem() {
        return type == 4;
    }

    /**
     * 检查角色是否可以使用此物品
     */
    public boolean canUse(PlayerRole role) {
        // 检查等级要求
        if (role.getLevel() < requiredLevel) {
            return false;
        }
        
        // 检查种族要求
        if (requiredRace != 0 && !role.getRace().equals(requiredRace)) {
            return false;
        }
        
        // 检查性别要求
        if (requiredSex != 0 && !role.getSex().equals(requiredSex)) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取使用效果描述
     */
    public String getEffectDescription() {
        if (!hasEffect()) {
            return "无特殊效果";
        }
        
        switch (effectType) {
            case 1: return "恢复生命值 " + effectValue;
            case 2: return "恢复法力值 " + effectValue;
            case 3: return "增加经验值 " + effectValue;
            case 4: return "学习技能";
            case 5: return "特殊效果";
            default: return "未知效果";
        }
    }

    /**
     * 获取物品完整名称（包含品质）
     */
    public String getFullName() {
        return "[" + getQualityName() + "] " + name;
    }

    /**
     * 获取种族要求名称
     */
    public String getRequiredRaceName() {
        switch (requiredRace) {
            case 0: return "无要求";
            case 1: return "巫族";
            case 2: return "妖族";
            default: return "未知";
        }
    }

    /**
     * 获取性别要求名称
     */
    public String getRequiredSexName() {
        switch (requiredSex) {
            case 0: return "无要求";
            case 1: return "男性";
            case 2: return "女性";
            default: return "未知";
        }
    }
}
