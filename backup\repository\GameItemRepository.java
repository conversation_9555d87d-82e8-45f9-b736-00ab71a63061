package com.honghuang.game.repository;

import com.honghuang.game.entity.GameItem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 游戏物品Repository
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface GameItemRepository extends JpaRepository<GameItem, Integer> {

    /**
     * 根据物品名称查找
     */
    Optional<GameItem> findByName(String name);

    /**
     * 根据物品类型查找
     */
    List<GameItem> findByType(Integer type);

    /**
     * 根据物品类型和子类型查找
     */
    List<GameItem> findByTypeAndSubType(Integer type, Integer subType);

    /**
     * 根据物品等级范围查找
     */
    List<GameItem> findByLevelBetween(Integer minLevel, Integer maxLevel);

    /**
     * 根据物品品质查找
     */
    List<GameItem> findByQuality(Integer quality);

    /**
     * 根据是否激活查找
     */
    List<GameItem> findByIsActive(Integer isActive);

    /**
     * 根据物品名称模糊查找
     */
    @Query("SELECT gi FROM GameItem gi WHERE gi.name LIKE %:name% AND gi.isActive = 1")
    List<GameItem> findByNameContaining(@Param("name") String name);

    /**
     * 根据多个条件查找物品
     */
    @Query("SELECT gi FROM GameItem gi WHERE " +
           "(:type IS NULL OR gi.type = :type) AND " +
           "(:subType IS NULL OR gi.subType = :subType) AND " +
           "(:quality IS NULL OR gi.quality = :quality) AND " +
           "(:minLevel IS NULL OR gi.level >= :minLevel) AND " +
           "(:maxLevel IS NULL OR gi.level <= :maxLevel) AND " +
           "gi.isActive = 1")
    Page<GameItem> findByConditions(@Param("type") Integer type,
                                   @Param("subType") Integer subType,
                                   @Param("quality") Integer quality,
                                   @Param("minLevel") Integer minLevel,
                                   @Param("maxLevel") Integer maxLevel,
                                   Pageable pageable);

    /**
     * 查找装备类物品
     */
    @Query("SELECT gi FROM GameItem gi WHERE gi.type IN (1, 2, 3) AND gi.isActive = 1")
    List<GameItem> findEquipments();

    /**
     * 查找消耗品
     */
    @Query("SELECT gi FROM GameItem gi WHERE gi.type = 4 AND gi.isActive = 1")
    List<GameItem> findConsumables();

    /**
     * 查找材料
     */
    @Query("SELECT gi FROM GameItem gi WHERE gi.type = 5 AND gi.isActive = 1")
    List<GameItem> findMaterials();

    /**
     * 查找任务物品
     */
    @Query("SELECT gi FROM GameItem gi WHERE gi.type = 6 AND gi.isActive = 1")
    List<GameItem> findQuestItems();

    /**
     * 根据价格范围查找
     */
    @Query("SELECT gi FROM GameItem gi WHERE gi.buyPrice BETWEEN :minPrice AND :maxPrice AND gi.isActive = 1")
    List<GameItem> findByPriceRange(@Param("minPrice") Long minPrice, @Param("maxPrice") Long maxPrice);

    /**
     * 查找可交易的物品
     */
    @Query("SELECT gi FROM GameItem gi WHERE gi.isTradeable = 1 AND gi.isActive = 1")
    List<GameItem> findTradeableItems();

    /**
     * 查找可堆叠的物品
     */
    @Query("SELECT gi FROM GameItem gi WHERE gi.isStackable = 1 AND gi.isActive = 1")
    List<GameItem> findStackableItems();

    /**
     * 根据使用等级限制查找
     */
    @Query("SELECT gi FROM GameItem gi WHERE gi.useLevelLimit <= :level AND gi.isActive = 1")
    List<GameItem> findByUseLevelLimit(@Param("level") Integer level);

    /**
     * 根据性别限制查找
     */
    @Query("SELECT gi FROM GameItem gi WHERE (gi.sexLimit = 0 OR gi.sexLimit = :sex) AND gi.isActive = 1")
    List<GameItem> findBySexLimit(@Param("sex") Integer sex);

    /**
     * 根据种族限制查找
     */
    @Query("SELECT gi FROM GameItem gi WHERE (gi.raceLimit = 0 OR gi.raceLimit = :race) AND gi.isActive = 1")
    List<GameItem> findByRaceLimit(@Param("race") Integer race);

    /**
     * 查找有属性加成的装备
     */
    @Query("SELECT gi FROM GameItem gi WHERE " +
           "(gi.attackBonus > 0 OR gi.defenseBonus > 0 OR gi.hpBonus > 0 OR gi.mpBonus > 0 OR " +
           "gi.forceBonus > 0 OR gi.agileBonus > 0 OR gi.physiqueBonus > 0 OR gi.savvyBonus > 0) " +
           "AND gi.isActive = 1")
    List<GameItem> findItemsWithBonus();

    /**
     * 统计各类型物品数量
     */
    @Query("SELECT gi.type, COUNT(gi) FROM GameItem gi WHERE gi.isActive = 1 GROUP BY gi.type")
    List<Object[]> countByType();

    /**
     * 统计各品质物品数量
     */
    @Query("SELECT gi.quality, COUNT(gi) FROM GameItem gi WHERE gi.isActive = 1 GROUP BY gi.quality")
    List<Object[]> countByQuality();

    /**
     * 根据激活状态统计物品数量
     */
    List<GameItem> countByIsActive(Integer isActive);
}
