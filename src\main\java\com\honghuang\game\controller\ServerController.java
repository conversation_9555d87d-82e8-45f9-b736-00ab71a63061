package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 服务器控制器
 */
@RestController
@RequestMapping("/api/server")
public class ServerController {

    @GetMapping("/status")
    public Result<Map<String, Object>> getServerStatus() {
        Map<String, Object> statusData = new HashMap<>();
        statusData.put("status", "运行中");
        statusData.put("onlineUsers", 1);
        statusData.put("totalUsers", 1);
        statusData.put("uptime", "1小时30分钟");
        statusData.put("version", "1.0.0");
        statusData.put("lastUpdate", System.currentTimeMillis());
        
        return Result.success("获取服务器状态成功", statusData);
    }
}
