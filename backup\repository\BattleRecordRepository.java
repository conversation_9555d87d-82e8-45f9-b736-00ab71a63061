package com.honghuang.game.repository;

import com.honghuang.game.entity.BattleRecord;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.Monster;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 战斗记录Repository
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface BattleRecordRepository extends JpaRepository<BattleRecord, Integer> {

    /**
     * 根据玩家角色查找战斗记录
     */
    List<BattleRecord> findByPlayerRole(PlayerRole playerRole);

    /**
     * 根据玩家角色分页查找战斗记录
     */
    Page<BattleRecord> findByPlayerRoleOrderByBattleTimeDesc(PlayerRole playerRole, Pageable pageable);

    /**
     * 根据怪物查找战斗记录
     */
    List<BattleRecord> findByMonster(Monster monster);

    /**
     * 根据战斗结果查找记录
     */
    List<BattleRecord> findByBattleResult(Integer battleResult);

    /**
     * 查找玩家的胜利记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.battleResult = 1 ORDER BY br.battleTime DESC")
    List<BattleRecord> findVictoryRecords(@Param("playerRole") PlayerRole playerRole);

    /**
     * 查找玩家的失败记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.battleResult = 2 ORDER BY br.battleTime DESC")
    List<BattleRecord> findDefeatRecords(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计玩家胜利次数
     */
    @Query("SELECT COUNT(br) FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.battleResult = 1")
    Long countVictories(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计玩家失败次数
     */
    @Query("SELECT COUNT(br) FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.battleResult = 2")
    Long countDefeats(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计玩家逃跑次数
     */
    @Query("SELECT COUNT(br) FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.battleResult = 3")
    Long countEscapes(@Param("playerRole") PlayerRole playerRole);

    /**
     * 计算玩家胜率
     */
    @Query("SELECT (SUM(CASE WHEN br.battleResult = 1 THEN 1.0 ELSE 0.0 END) / COUNT(br)) * 100.0 " +
           "FROM BattleRecord br WHERE br.playerRole = :playerRole")
    Double calculateWinRate(@Param("playerRole") PlayerRole playerRole);

    /**
     * 查找玩家最近的战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole ORDER BY br.battleTime DESC")
    List<BattleRecord> findRecentBattles(@Param("playerRole") PlayerRole playerRole, Pageable pageable);

    /**
     * 根据时间范围查找战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.battleTime BETWEEN :startTime AND :endTime")
    List<BattleRecord> findByTimeRange(@Param("playerRole") PlayerRole playerRole,
                                      @Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 查找今日战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole AND DATE(br.battleTime) = CURRENT_DATE")
    List<BattleRecord> findTodayBattles(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计今日战斗次数
     */
    @Query("SELECT COUNT(br) FROM BattleRecord br WHERE br.playerRole = :playerRole AND DATE(br.battleTime) = CURRENT_DATE")
    Long countTodayBattles(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计今日获得经验
     */
    @Query("SELECT COALESCE(SUM(br.expGained), 0) FROM BattleRecord br WHERE br.playerRole = :playerRole AND DATE(br.battleTime) = CURRENT_DATE")
    Long sumTodayExp(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计今日获得铜钱
     */
    @Query("SELECT COALESCE(SUM(br.copperGained), 0) FROM BattleRecord br WHERE br.playerRole = :playerRole AND DATE(br.battleTime) = CURRENT_DATE")
    Long sumTodayCopper(@Param("playerRole") PlayerRole playerRole);

    /**
     * 查找最长战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole ORDER BY br.battleRounds DESC")
    List<BattleRecord> findLongestBattles(@Param("playerRole") PlayerRole playerRole, Pageable pageable);

    /**
     * 查找最短战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.battleResult = 1 ORDER BY br.battleRounds ASC")
    List<BattleRecord> findQuickestVictories(@Param("playerRole") PlayerRole playerRole, Pageable pageable);

    /**
     * 查找经验收益最高的战斗
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole ORDER BY br.expGained DESC")
    List<BattleRecord> findHighestExpBattles(@Param("playerRole") PlayerRole playerRole, Pageable pageable);

    /**
     * 查找铜钱收益最高的战斗
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole ORDER BY br.copperGained DESC")
    List<BattleRecord> findHighestCopperBattles(@Param("playerRole") PlayerRole playerRole, Pageable pageable);

    /**
     * 统计与特定怪物的战斗记录
     */
    @Query("SELECT br.battleResult, COUNT(br) FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.monster = :monster GROUP BY br.battleResult")
    List<Object[]> countBattlesByResult(@Param("playerRole") PlayerRole playerRole, @Param("monster") Monster monster);

    /**
     * 查找连胜记录
     */
    @Query(value = "SELECT COUNT(*) as streak FROM (" +
                   "SELECT br.*, ROW_NUMBER() OVER (ORDER BY br.battle_time DESC) - " +
                   "ROW_NUMBER() OVER (PARTITION BY br.battle_result ORDER BY br.battle_time DESC) as grp " +
                   "FROM battle_record br WHERE br.player_role_id = :playerRoleId " +
                   "ORDER BY br.battle_time DESC" +
                   ") t WHERE t.battle_result = 1 AND t.grp = 0",
           nativeQuery = true)
    Integer getCurrentWinStreak(@Param("playerRoleId") Integer playerRoleId);

    /**
     * 查找连败记录
     */
    @Query(value = "SELECT COUNT(*) as streak FROM (" +
                   "SELECT br.*, ROW_NUMBER() OVER (ORDER BY br.battle_time DESC) - " +
                   "ROW_NUMBER() OVER (PARTITION BY br.battle_result ORDER BY br.battle_time DESC) as grp " +
                   "FROM battle_record br WHERE br.player_role_id = :playerRoleId " +
                   "ORDER BY br.battle_time DESC" +
                   ") t WHERE t.battle_result = 2 AND t.grp = 0",
           nativeQuery = true)
    Integer getCurrentLoseStreak(@Param("playerRoleId") Integer playerRoleId);

    /**
     * 统计平均战斗回合数
     */
    @Query("SELECT AVG(br.battleRounds) FROM BattleRecord br WHERE br.playerRole = :playerRole")
    Double getAverageBattleRounds(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计平均战斗时长
     */
    @Query("SELECT AVG(br.battleDuration) FROM BattleRecord br WHERE br.playerRole = :playerRole")
    Double getAverageBattleDuration(@Param("playerRole") PlayerRole playerRole);

    /**
     * 查找PVE战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.battleType = 1 ORDER BY br.battleTime DESC")
    List<BattleRecord> findPveBattles(@Param("playerRole") PlayerRole playerRole);

    /**
     * 查找PVP战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.battleType = 2 ORDER BY br.battleTime DESC")
    List<BattleRecord> findPvpBattles(@Param("playerRole") PlayerRole playerRole);

    /**
     * 根据战斗类型统计
     */
    @Query("SELECT br.battleType, COUNT(br) FROM BattleRecord br WHERE br.playerRole = :playerRole GROUP BY br.battleType")
    List<Object[]> countByBattleType(@Param("playerRole") PlayerRole playerRole);

    /**
     * 查找有掉落物品的战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRole = :playerRole AND br.droppedItems IS NOT NULL AND br.droppedItems != ''")
    List<BattleRecord> findBattlesWithDrops(@Param("playerRole") PlayerRole playerRole);

    /**
     * 删除过期的战斗记录（保留最近30天）
     */
    @Query("DELETE FROM BattleRecord br WHERE br.battleTime < :cutoffTime")
    void deleteOldRecords(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计服务器总战斗次数
     */
    @Query("SELECT COUNT(br) FROM BattleRecord br")
    Long countTotalBattles();

    /**
     * 统计服务器今日战斗次数
     */
    @Query("SELECT COUNT(br) FROM BattleRecord br WHERE DATE(br.battleTime) = CURRENT_DATE")
    Long countTodayTotalBattles();

    /**
     * 查找战斗最多的玩家
     */
    @Query("SELECT br.playerRole, COUNT(br) as battleCount FROM BattleRecord br GROUP BY br.playerRole ORDER BY battleCount DESC")
    List<Object[]> findMostActivePlayers(Pageable pageable);

    /**
     * 查找胜率最高的玩家
     */
    @Query("SELECT br.playerRole, " +
           "(SUM(CASE WHEN br.battleResult = 1 THEN 1.0 ELSE 0.0 END) / COUNT(br)) * 100.0 as winRate " +
           "FROM BattleRecord br GROUP BY br.playerRole HAVING COUNT(br) >= 10 ORDER BY winRate DESC")
    List<Object[]> findHighestWinRatePlayers(Pageable pageable);
}
