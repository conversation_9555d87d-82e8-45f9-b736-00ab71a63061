package com.honghuang.game.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 游戏场景实体类
 * 对应原表 scene_info
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "scene_info")
public class GameScene {

    @Id
    @Column(name = "scene_id", length = 10)
    private String id;

    /**
     * 场景名称
     */
    @Column(name = "scene_name", length = 50)
    private String name;

    /**
     * 场景描述
     */
    @Column(name = "scene_description", length = 500)
    private String description;

    /**
     * 场景类型 1:普通场景 2:副本 3:城镇 4:野外 5:特殊场景
     */
    @Column(name = "scene_type")
    private Integer type = 1;

    /**
     * 场景等级限制
     */
    @Column(name = "level_limit")
    private Integer levelLimit = 1;

    /**
     * 最大玩家数量
     */
    @Column(name = "max_players")
    private Integer maxPlayers = 100;

    /**
     * 是否安全区域 0:非安全区 1:安全区
     */
    @Column(name = "is_safe_zone")
    private Integer isSafeZone = 0;

    /**
     * 是否允许PK 0:不允许 1:允许
     */
    @Column(name = "allow_pk")
    private Integer allowPk = 1;

    /**
     * 是否允许使用回城卷 0:不允许 1:允许
     */
    @Column(name = "allow_teleport")
    private Integer allowTeleport = 1;

    /**
     * 背景音乐
     */
    @Column(name = "background_music", length = 100)
    private String backgroundMusic;

    /**
     * 背景图片
     */
    @Column(name = "background_image", length = 100)
    private String backgroundImage;

    /**
     * 地图宽度
     */
    @Column(name = "map_width")
    private Integer mapWidth = 100;

    /**
     * 地图高度
     */
    @Column(name = "map_height")
    private Integer mapHeight = 100;

    /**
     * 默认出生点X坐标
     */
    @Column(name = "spawn_x")
    private Integer spawnX = 50;

    /**
     * 默认出生点Y坐标
     */
    @Column(name = "spawn_y")
    private Integer spawnY = 50;

    /**
     * 北方连接场景ID
     */
    @Column(name = "north_scene", length = 10)
    private String northScene;

    /**
     * 南方连接场景ID
     */
    @Column(name = "south_scene", length = 10)
    private String southScene;

    /**
     * 东方连接场景ID
     */
    @Column(name = "east_scene", length = 10)
    private String eastScene;

    /**
     * 西方连接场景ID
     */
    @Column(name = "west_scene", length = 10)
    private String westScene;

    /**
     * 上层场景ID（楼上）
     */
    @Column(name = "up_scene", length = 10)
    private String upScene;

    /**
     * 下层场景ID（楼下）
     */
    @Column(name = "down_scene", length = 10)
    private String downScene;

    /**
     * 场景刷新怪物配置JSON
     */
    @Column(name = "monster_config", length = 2000)
    private String monsterConfig;

    /**
     * 场景NPC配置JSON
     */
    @Column(name = "npc_config", length = 2000)
    private String npcConfig;

    /**
     * 场景传送点配置JSON
     */
    @Column(name = "teleport_config", length = 1000)
    private String teleportConfig;

    /**
     * 场景特殊事件配置JSON
     */
    @Column(name = "event_config", length = 1000)
    private String eventConfig;

    /**
     * 天气类型 1:晴天 2:雨天 3:雪天 4:雾天
     */
    @Column(name = "weather_type")
    private Integer weatherType = 1;

    /**
     * 时间类型 1:白天 2:黄昏 3:夜晚 4:黎明
     */
    @Column(name = "time_type")
    private Integer timeType = 1;

    /**
     * 是否激活 0:未激活 1:激活
     */
    @Column(name = "is_active")
    private Integer isActive = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    // 业务方法

    /**
     * 是否为城镇
     */
    public boolean isTown() {
        return type != null && type == 3;
    }

    /**
     * 是否为副本
     */
    public boolean isDungeon() {
        return type != null && type == 2;
    }

    /**
     * 是否为野外
     */
    public boolean isWilderness() {
        return type != null && type == 4;
    }

    /**
     * 是否为安全区域
     */
    public boolean isSafe() {
        return isSafeZone != null && isSafeZone == 1;
    }

    /**
     * 是否允许PK
     */
    public boolean isPkAllowed() {
        return allowPk != null && allowPk == 1 && !isSafe();
    }

    /**
     * 是否允许传送
     */
    public boolean isTeleportAllowed() {
        return allowTeleport != null && allowTeleport == 1;
    }

    /**
     * 获取场景类型名称
     */
    public String getTypeName() {
        if (type == null) return "普通场景";
        switch (type) {
            case 1: return "普通场景";
            case 2: return "副本";
            case 3: return "城镇";
            case 4: return "野外";
            case 5: return "特殊场景";
            default: return "普通场景";
        }
    }

    /**
     * 获取天气名称
     */
    public String getWeatherName() {
        if (weatherType == null) return "晴天";
        switch (weatherType) {
            case 1: return "晴天";
            case 2: return "雨天";
            case 3: return "雪天";
            case 4: return "雾天";
            default: return "晴天";
        }
    }

    /**
     * 获取时间名称
     */
    public String getTimeName() {
        if (timeType == null) return "白天";
        switch (timeType) {
            case 1: return "白天";
            case 2: return "黄昏";
            case 3: return "夜晚";
            case 4: return "黎明";
            default: return "白天";
        }
    }

    /**
     * 检查坐标是否在场景范围内
     */
    public boolean isValidPosition(int x, int y) {
        return x >= 0 && x < mapWidth && y >= 0 && y < mapHeight;
    }

    /**
     * 获取随机出生点
     */
    public int[] getRandomSpawnPoint() {
        // 简单实现，返回默认出生点附近的随机位置
        int randomX = Math.max(0, Math.min(mapWidth - 1, spawnX + (int)(Math.random() * 10) - 5));
        int randomY = Math.max(0, Math.min(mapHeight - 1, spawnY + (int)(Math.random() * 10) - 5));
        return new int[]{randomX, randomY};
    }
}
