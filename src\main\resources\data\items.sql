-- 物品数据初始化
-- 插入基础物品数据到item表

-- 武器类物品
INSERT INTO item (name, type, sub_type, quality, required_level, required_race, required_sex, description, icon, sell_price, buy_price, stackable, max_stack, tradeable, droppable, effect_type, effect_value, cooldown, active) VALUES
('新手木剑', 1, 1, 1, 1, 0, 0, '新手专用的木制长剑，攻击力较低但易于使用。', '⚔️', 50, 100, 0, 1, 1, 1, 0, 0, 0, 1),
('铁剑', 1, 1, 2, 5, 0, 0, '用精铁打造的长剑，锋利且坚固。', '⚔️', 250, 500, 0, 1, 1, 1, 0, 0, 0, 1),
('青钢剑', 1, 1, 2, 10, 0, 0, '青钢锻造的宝剑，剑身泛着青光。', '⚔️', 600, 1200, 0, 1, 1, 1, 0, 0, 0, 1),
('龙鳞剑', 1, 1, 3, 20, 0, 0, '传说中用龙鳞打造的神剑，威力惊人。', '⚔️', 2500, 5000, 0, 1, 1, 1, 0, 0, 0, 1),
('新手法杖', 1, 2, 1, 1, 0, 0, '新手法师的入门法杖，能够聚集微弱的法力。', '🔮', 60, 120, 0, 1, 1, 1, 0, 0, 0, 1);

-- 防具类物品
INSERT INTO item (name, type, sub_type, quality, required_level, required_race, required_sex, description, icon, sell_price, buy_price, stackable, max_stack, tradeable, droppable, effect_type, effect_value, cooldown, active) VALUES
('布衣', 1, 3, 1, 1, 0, 0, '普通的布制衣服，提供基础防护。', '👕', 40, 80, 0, 1, 1, 1, 0, 0, 0, 1),
('皮甲', 1, 3, 2, 5, 0, 0, '用兽皮制作的轻甲，防护力不错。', '👕', 150, 300, 0, 1, 1, 1, 0, 0, 0, 1),
('铁甲', 1, 3, 2, 10, 0, 0, '铁制的重甲，防护力强但影响敏捷。', '👕', 400, 800, 0, 1, 1, 1, 0, 0, 0, 1),
('新手头盔', 1, 2, 1, 1, 0, 0, '简单的头部防护装备。', '⛑️', 25, 50, 0, 1, 1, 1, 0, 0, 0, 1),
('新手鞋子', 1, 4, 1, 1, 0, 0, '普通的布鞋，提供基本的脚部保护。', '👟', 20, 40, 0, 1, 1, 1, 0, 0, 0, 1);

-- 饰品类物品
INSERT INTO item (name, type, sub_type, quality, required_level, required_race, required_sex, description, icon, sell_price, buy_price, stackable, max_stack, tradeable, droppable, effect_type, effect_value, cooldown, active) VALUES
('力量戒指', 1, 7, 2, 5, 0, 0, '蕴含力量之源的戒指，佩戴后力量大增。', '💍', 300, 600, 0, 1, 1, 1, 0, 0, 0, 1),
('敏捷项链', 1, 6, 2, 8, 0, 0, '轻盈的项链，能够提升佩戴者的敏捷。', '📿', 400, 800, 0, 1, 1, 1, 0, 0, 0, 1),
('智慧护符', 1, 8, 3, 10, 0, 0, '古老的护符，蕴含着智慧的力量。', '🔮', 750, 1500, 0, 1, 1, 1, 0, 0, 0, 1);

-- 消耗品类物品
INSERT INTO item (name, type, sub_type, quality, required_level, required_race, required_sex, description, icon, sell_price, buy_price, stackable, max_stack, tradeable, droppable, effect_type, effect_value, cooldown, active) VALUES
('小血瓶', 2, 1, 1, 1, 0, 0, '恢复少量生命值的药水。', '🧪', 10, 20, 1, 99, 1, 1, 1, 30, 0, 1),
('中血瓶', 2, 1, 2, 5, 0, 0, '恢复中等生命值的药水。', '🧪', 25, 50, 1, 99, 1, 1, 1, 80, 0, 1),
('大血瓶', 2, 1, 2, 10, 0, 0, '恢复大量生命值的药水。', '🧪', 60, 120, 1, 99, 1, 1, 1, 200, 0, 1),
('小蓝瓶', 2, 2, 1, 1, 0, 0, '恢复少量法力值的药水。', '💙', 12, 25, 1, 99, 1, 1, 2, 20, 0, 1),
('中蓝瓶', 2, 2, 2, 5, 0, 0, '恢复中等法力值的药水。', '💙', 30, 60, 1, 99, 1, 1, 2, 60, 0, 1),
('回城卷轴', 2, 3, 1, 1, 0, 0, '使用后可以瞬间回到城镇的神奇卷轴。', '📜', 50, 100, 1, 20, 1, 1, 5, 0, 0, 1);

-- 材料类物品
INSERT INTO item (name, type, sub_type, quality, required_level, required_race, required_sex, description, icon, sell_price, buy_price, stackable, max_stack, tradeable, droppable, effect_type, effect_value, cooldown, active) VALUES
('铁矿石', 3, 1, 1, 1, 0, 0, '普通的铁矿石，可用于锻造装备。', '⛏️', 5, 10, 1, 99, 1, 1, 0, 0, 0, 1),
('铜矿石', 3, 1, 1, 1, 0, 0, '常见的铜矿石，锻造的基础材料。', '⛏️', 4, 8, 1, 99, 1, 1, 0, 0, 0, 1),
('银矿石', 3, 1, 2, 5, 0, 0, '珍贵的银矿石，可锻造高级装备。', '⛏️', 25, 50, 1, 99, 1, 1, 0, 0, 0, 1),
('魔法水晶', 3, 2, 2, 8, 0, 0, '蕴含魔法能量的水晶，制作法器的重要材料。', '💎', 50, 100, 1, 50, 1, 1, 0, 0, 0, 1),
('龙鳞片', 3, 3, 4, 20, 0, 0, '传说中巨龙的鳞片，极其珍贵的锻造材料。', '🐉', 500, 1000, 1, 10, 1, 1, 0, 0, 0, 1);

-- 任务物品
INSERT INTO item (name, type, sub_type, quality, required_level, required_race, required_sex, description, icon, sell_price, buy_price, stackable, max_stack, tradeable, droppable, effect_type, effect_value, cooldown, active) VALUES
('村长的信件', 4, 1, 1, 1, 0, 0, '村长托付的重要信件，需要送达指定地点。', '📋', 0, 0, 0, 1, 0, 0, 0, 0, 0, 1),
('神秘的钥匙', 4, 2, 2, 5, 0, 0, '一把神秘的钥匙，似乎能打开某个重要的门。', '🗝️', 0, 0, 0, 1, 0, 0, 0, 0, 0, 1),
('古老的地图', 4, 3, 3, 10, 0, 0, '标记着宝藏位置的古老地图。', '🗺️', 0, 0, 0, 1, 0, 0, 0, 0, 0, 1);

-- 其他类物品
INSERT INTO item (name, type, sub_type, quality, required_level, required_race, required_sex, description, icon, sell_price, buy_price, stackable, max_stack, tradeable, droppable, effect_type, effect_value, cooldown, active) VALUES
('金币袋', 7, 1, 2, 1, 0, 0, '装满金币的袋子，使用后获得金币。', '💰', 0, 0, 1, 10, 1, 1, 3, 100, 0, 1),
('经验丹', 7, 2, 3, 5, 0, 0, '蕴含丰富经验的丹药，服用后可获得大量经验。', '💊', 250, 500, 1, 5, 1, 1, 3, 500, 0, 1),
('随机宝箱', 7, 3, 4, 10, 0, 0, '神秘的宝箱，打开后可获得随机奖励。', '📦', 500, 1000, 1, 1, 1, 1, 0, 0, 0, 1);
