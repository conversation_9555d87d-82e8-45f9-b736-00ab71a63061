package com.honghuang.game.service;

import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.Shop;
import com.honghuang.game.entity.ShopItem;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.ShopItemRepository;
import com.honghuang.game.repository.ShopRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 商店服务类
 */
@Service
@Transactional
public class ShopService {

    @Autowired
    private ShopRepository shopRepository;

    @Autowired
    private ShopItemRepository shopItemRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 获取所有可用商店
     */
    @Transactional(readOnly = true)
    public List<Shop> getAllShops() {
        return shopRepository.findByActiveOrderByTypeAscNameAsc(1);
    }

    /**
     * 根据角色获取可访问的商店
     */
    @Transactional(readOnly = true)
    public List<Shop> getAccessibleShops(Integer roleId) {
        PlayerRole role = playerRoleService.findById(roleId);
        return shopRepository.findSuitableShops(role.getLevel(), role.getRace(), role.getSex());
    }

    /**
     * 根据场景获取商店
     */
    @Transactional(readOnly = true)
    public List<Shop> getShopsByScene(Integer sceneId) {
        return shopRepository.findBySceneIdAndActiveOrderByTypeAsc(sceneId, 1);
    }

    /**
     * 根据类型获取商店
     */
    @Transactional(readOnly = true)
    public List<Shop> getShopsByType(Integer type) {
        return shopRepository.findByTypeAndActiveOrderByNameAsc(type, 1);
    }

    /**
     * 获取商店详情
     */
    @Transactional(readOnly = true)
    public Shop getShopDetail(Integer shopId) {
        return shopRepository.findById(shopId)
                .orElseThrow(() -> new BusinessException(404, "商店不存在"));
    }

    /**
     * 获取商店商品列表
     */
    @Transactional(readOnly = true)
    public List<ShopItem> getShopItems(Integer shopId) {
        return shopItemRepository.findByShopIdAndActiveOrderByItemTypeAscItemQualityDesc(shopId, 1);
    }

    /**
     * 根据角色获取可购买的商品
     */
    @Transactional(readOnly = true)
    public List<ShopItem> getAvailableItems(Integer shopId, Integer roleId) {
        PlayerRole role = playerRoleService.findById(roleId);
        return shopItemRepository.findSuitableItems(shopId, role.getLevel(), role.getRace(), role.getSex());
    }

    /**
     * 购买商品
     */
    public boolean buyItem(Integer roleId, Integer itemId, Integer quantity) {
        System.out.println("购买商品: roleId=" + roleId + ", itemId=" + itemId + ", quantity=" + quantity);

        if (quantity <= 0) {
            throw new BusinessException(400, "购买数量必须大于0");
        }

        PlayerRole role = playerRoleService.findById(roleId);
        ShopItem item = shopItemRepository.findById(itemId)
                .orElseThrow(() -> new BusinessException(404, "商品不存在"));

        // 检查购买条件
        if (!item.canBuy(role)) {
            throw new BusinessException(400, "不满足购买条件");
        }

        // 检查购买数量限制
        if (quantity > item.getBuyLimit()) {
            throw new BusinessException(400, "超过单次购买限制");
        }

        // 检查库存
        if (item.getStock() != -1 && item.getStock() < quantity) {
            throw new BusinessException(400, "库存不足");
        }

        // 计算总价格
        Long totalPrice = item.getTotalCopperPrice() * quantity;

        // 检查金钱
        if (!role.spendCopper(totalPrice)) {
            throw new BusinessException(400, "金钱不足");
        }

        // 减少库存
        if (item.getStock() != -1) {
            item.setStock(item.getStock() - quantity);
        }
        item.setSoldCount(item.getSoldCount() + quantity);
        shopItemRepository.save(item);

        // TODO: 将商品添加到玩家背包
        System.out.println("商品购买成功: roleId=" + roleId + ", itemId=" + itemId + 
                         ", quantity=" + quantity + ", totalPrice=" + totalPrice);

        return true;
    }

    /**
     * 出售物品给商店
     */
    public boolean sellItem(Integer roleId, Integer itemId, Integer quantity) {
        System.out.println("出售物品: roleId=" + roleId + ", itemId=" + itemId + ", quantity=" + quantity);

        if (quantity <= 0) {
            throw new BusinessException(400, "出售数量必须大于0");
        }

        PlayerRole role = playerRoleService.findById(roleId);

        // TODO: 检查玩家是否拥有该物品
        // TODO: 计算出售价格（通常是购买价格的一定比例）
        // TODO: 从玩家背包中移除物品
        // TODO: 给玩家增加金钱

        Long sellPrice = 100L * quantity; // 临时价格
        role.addCopper(sellPrice);

        System.out.println("物品出售成功: roleId=" + roleId + ", itemId=" + itemId + 
                         ", quantity=" + quantity + ", sellPrice=" + sellPrice);

        return true;
    }

    /**
     * 刷新商店
     */
    public void refreshShop(Integer shopId) {
        System.out.println("刷新商店: shopId=" + shopId);

        Shop shop = getShopDetail(shopId);
        
        if (!shop.needsRefresh()) {
            throw new BusinessException(400, "商店暂时不需要刷新");
        }

        // 刷新商店商品（这里可以实现随机生成商品的逻辑）
        shop.refresh();
        shopRepository.save(shop);

        System.out.println("商店刷新成功: shopId=" + shopId);
    }

    /**
     * 获取限时商品
     */
    @Transactional(readOnly = true)
    public List<ShopItem> getLimitedTimeItems() {
        return shopItemRepository.findLimitedTimeItems();
    }

    /**
     * 获取折扣商品
     */
    @Transactional(readOnly = true)
    public List<ShopItem> getDiscountItems() {
        return shopItemRepository.findDiscountItems();
    }

    /**
     * 根据价格范围搜索商品
     */
    @Transactional(readOnly = true)
    public List<ShopItem> searchItemsByPrice(Integer shopId, Long minPrice, Long maxPrice) {
        return shopItemRepository.findByPriceRange(shopId, minPrice, maxPrice);
    }

    /**
     * 根据名称搜索商品
     */
    @Transactional(readOnly = true)
    public List<ShopItem> searchItemsByName(String name) {
        return shopItemRepository.findByNameContaining(name);
    }

    /**
     * 获取热销商品
     */
    @Transactional(readOnly = true)
    public List<ShopItem> getPopularItems(Integer shopId) {
        return shopItemRepository.findPopularItems(shopId);
    }

    /**
     * 获取新商品
     */
    @Transactional(readOnly = true)
    public List<ShopItem> getNewItems(Integer shopId) {
        return shopItemRepository.findNewItems(shopId);
    }

    /**
     * 获取商店统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getShopStatistics(Integer shopId) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        stats.put("totalItems", shopItemRepository.countByShopId(shopId));
        stats.put("equipmentItems", shopItemRepository.countByShopIdAndItemType(shopId, 1));
        stats.put("skillItems", shopItemRepository.countByShopIdAndItemType(shopId, 2));
        stats.put("consumableItems", shopItemRepository.countByShopIdAndItemType(shopId, 3));
        stats.put("materialItems", shopItemRepository.countByShopIdAndItemType(shopId, 4));
        
        return stats;
    }

    /**
     * 检查商店访问权限
     */
    public boolean checkShopAccess(Integer shopId, Integer roleId) {
        Shop shop = getShopDetail(shopId);
        PlayerRole role = playerRoleService.findById(roleId);
        return shop.canAccess(role);
    }

    /**
     * 自动刷新需要刷新的商店
     */
    @Transactional
    public void autoRefreshShops() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(60); // 1小时前
        List<Shop> shopsNeedingRefresh = shopRepository.findShopsNeedingRefresh(cutoffTime);
        
        for (Shop shop : shopsNeedingRefresh) {
            try {
                refreshShop(shop.getId());
                System.out.println("自动刷新商店成功: shopId=" + shop.getId());
            } catch (Exception e) {
                System.out.println("自动刷新商店失败: shopId=" + shop.getId() + ", error=" + e.getMessage());
            }
        }
    }

    /**
     * 获取库存不足的商品
     */
    @Transactional(readOnly = true)
    public List<ShopItem> getLowStockItems(Integer threshold) {
        return shopItemRepository.findLowStockItems(threshold);
    }
}
