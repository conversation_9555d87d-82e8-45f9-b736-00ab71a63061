package com.honghuang.game.service;

import com.honghuang.game.entity.MentorApplication;
import com.honghuang.game.entity.MentorRelation;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.MentorApplicationRepository;
import com.honghuang.game.repository.MentorRelationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 师徒系统服务类
 */
@Service
@Transactional
public class MentorService {

    @Autowired
    private MentorRelationRepository mentorRelationRepository;

    @Autowired
    private MentorApplicationRepository mentorApplicationRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    private static final int MAX_APPRENTICES = 3; // 最多收3个徒弟
    private static final int MIN_LEVEL_GAP = 5; // 最小等级差距
    private static final int MAX_LEVEL_GAP = 50; // 最大等级差距

    /**
     * 申请拜师
     */
    public MentorApplication applyForMentor(Integer apprenticeId, Integer mentorId, String message) {
        System.out.println("申请拜师: apprenticeId=" + apprenticeId + ", mentorId=" + mentorId);

        PlayerRole apprentice = playerRoleService.findById(apprenticeId);
        PlayerRole mentor = playerRoleService.findById(mentorId);

        // 检查基本条件
        validateMentorRelationConditions(apprentice, mentor, true);

        // 检查是否已有待处理的申请
        if (mentorApplicationRepository.findByApplicantIdAndTargetIdAndStatus(apprenticeId, mentorId, 0).isPresent()) {
            throw new BusinessException(400, "已有待处理的拜师申请");
        }

        // 创建申请
        MentorApplication application = new MentorApplication();
        application.setApplicationType(1); // 拜师申请
        application.setApplicantId(apprenticeId);
        application.setApplicantName(apprentice.getName());
        application.setApplicantLevel(apprentice.getLevel());
        application.setTargetId(mentorId);
        application.setTargetName(mentor.getName());
        application.setTargetLevel(mentor.getLevel());
        application.setMessage(message);

        application = mentorApplicationRepository.save(application);

        System.out.println("拜师申请提交成功: applicationId=" + application.getId());
        return application;
    }

    /**
     * 申请收徒
     */
    public MentorApplication applyForApprentice(Integer mentorId, Integer apprenticeId, String message) {
        System.out.println("申请收徒: mentorId=" + mentorId + ", apprenticeId=" + apprenticeId);

        PlayerRole mentor = playerRoleService.findById(mentorId);
        PlayerRole apprentice = playerRoleService.findById(apprenticeId);

        // 检查基本条件
        validateMentorRelationConditions(apprentice, mentor, false);

        // 检查是否已有待处理的申请
        if (mentorApplicationRepository.findByApplicantIdAndTargetIdAndStatus(mentorId, apprenticeId, 0).isPresent()) {
            throw new BusinessException(400, "已有待处理的收徒申请");
        }

        // 创建申请
        MentorApplication application = new MentorApplication();
        application.setApplicationType(2); // 收徒申请
        application.setApplicantId(mentorId);
        application.setApplicantName(mentor.getName());
        application.setApplicantLevel(mentor.getLevel());
        application.setTargetId(apprenticeId);
        application.setTargetName(apprentice.getName());
        application.setTargetLevel(apprentice.getLevel());
        application.setMessage(message);

        application = mentorApplicationRepository.save(application);

        System.out.println("收徒申请提交成功: applicationId=" + application.getId());
        return application;
    }

    /**
     * 同意师徒申请
     */
    public MentorRelation approveApplication(Integer targetId, Integer applicationId, String note) {
        System.out.println("同意师徒申请: targetId=" + targetId + ", applicationId=" + applicationId);

        MentorApplication application = mentorApplicationRepository.findById(applicationId)
                .orElseThrow(() -> new BusinessException(404, "申请不存在"));

        if (!application.getTargetId().equals(targetId)) {
            throw new BusinessException(403, "无权限处理此申请");
        }

        if (!application.isPending()) {
            throw new BusinessException(400, "申请已处理");
        }

        // 再次检查师徒关系条件
        PlayerRole apprentice = playerRoleService.findById(application.getApprenticeId());
        PlayerRole mentor = playerRoleService.findById(application.getMentorId());
        validateMentorRelationConditions(apprentice, mentor, application.isApprenticeApplication());

        // 通过申请
        application.approve(note);
        mentorApplicationRepository.save(application);

        // 建立师徒关系
        MentorRelation relation = new MentorRelation();
        relation.setMentorId(application.getMentorId());
        relation.setMentorName(application.getMentorName());
        relation.setMentorLevel(application.getMentorLevel());
        relation.setApprenticeId(application.getApprenticeId());
        relation.setApprenticeName(application.getApprenticeName());
        relation.setApprenticeLevel(application.getApprenticeLevel());
        relation.setStatus(1);
        relation.setLastInteractionTime(LocalDateTime.now());

        relation = mentorRelationRepository.save(relation);

        System.out.println("师徒关系建立成功: relationId=" + relation.getId());
        return relation;
    }

    /**
     * 拒绝师徒申请
     */
    public boolean rejectApplication(Integer targetId, Integer applicationId, String note) {
        System.out.println("拒绝师徒申请: targetId=" + targetId + ", applicationId=" + applicationId);

        MentorApplication application = mentorApplicationRepository.findById(applicationId)
                .orElseThrow(() -> new BusinessException(404, "申请不存在"));

        if (!application.getTargetId().equals(targetId)) {
            throw new BusinessException(403, "无权限处理此申请");
        }

        if (!application.isPending()) {
            throw new BusinessException(400, "申请已处理");
        }

        // 拒绝申请
        application.reject(note);
        mentorApplicationRepository.save(application);

        System.out.println("师徒申请已拒绝: applicationId=" + applicationId);
        return true;
    }

    /**
     * 解除师徒关系
     */
    public boolean dissolveMentorRelation(Integer roleId, Integer relationId, String reason) {
        System.out.println("解除师徒关系: roleId=" + roleId + ", relationId=" + relationId);

        MentorRelation relation = mentorRelationRepository.findById(relationId)
                .orElseThrow(() -> new BusinessException(404, "师徒关系不存在"));

        if (!relation.isActive()) {
            throw new BusinessException(400, "师徒关系已解除");
        }

        // 检查权限
        if (!relation.getMentorId().equals(roleId) && !relation.getApprenticeId().equals(roleId)) {
            throw new BusinessException(403, "无权限解除此师徒关系");
        }

        // 根据操作者身份设置解除状态
        if (relation.getMentorId().equals(roleId)) {
            relation.dissolveByMentor();
        } else {
            relation.dissolveByApprentice();
        }

        mentorRelationRepository.save(relation);

        System.out.println("师徒关系解除成功: relationId=" + relationId + ", reason=" + reason);
        return true;
    }

    /**
     * 出师
     */
    public boolean graduateApprentice(Integer mentorId, Integer relationId) {
        System.out.println("徒弟出师: mentorId=" + mentorId + ", relationId=" + relationId);

        MentorRelation relation = mentorRelationRepository.findById(relationId)
                .orElseThrow(() -> new BusinessException(404, "师徒关系不存在"));

        if (!relation.getMentorId().equals(mentorId)) {
            throw new BusinessException(403, "只有师父可以让徒弟出师");
        }

        if (!relation.isActive()) {
            throw new BusinessException(400, "师徒关系已解除");
        }

        if (!relation.canGraduate()) {
            throw new BusinessException(400, "不满足出师条件");
        }

        // 执行出师
        relation.graduate();
        mentorRelationRepository.save(relation);

        System.out.println("徒弟出师成功: relationId=" + relationId);
        return true;
    }

    /**
     * 获取师父的徒弟列表
     */
    @Transactional(readOnly = true)
    public List<MentorRelation> getApprentices(Integer mentorId) {
        return mentorRelationRepository.findByMentorIdAndStatusOrderByEstablishTimeDesc(mentorId, 1);
    }

    /**
     * 获取徒弟的师父
     */
    @Transactional(readOnly = true)
    public Optional<MentorRelation> getMentor(Integer apprenticeId) {
        return mentorRelationRepository.findByApprenticeIdAndStatus(apprenticeId, 1);
    }

    /**
     * 获取角色的所有师徒关系
     */
    @Transactional(readOnly = true)
    public List<MentorRelation> getAllMentorRelations(Integer roleId) {
        return mentorRelationRepository.findByRoleIdAndStatus(roleId, 1);
    }

    /**
     * 获取待处理的申请列表
     */
    @Transactional(readOnly = true)
    public List<MentorApplication> getPendingApplications(Integer targetId) {
        return mentorApplicationRepository.findPendingApplicationsForTarget(targetId);
    }

    /**
     * 获取申请历史
     */
    @Transactional(readOnly = true)
    public List<MentorApplication> getApplicationHistory(Integer roleId) {
        List<MentorApplication> applications = mentorApplicationRepository.findByApplicantIdOrderByApplyTimeDesc(roleId);
        applications.addAll(mentorApplicationRepository.findByTargetIdOrderByApplyTimeDesc(roleId));
        applications.sort((a, b) -> b.getApplyTime().compareTo(a.getApplyTime()));
        return applications;
    }

    /**
     * 师父传功（增加徒弟经验）
     */
    public boolean teachApprentice(Integer mentorId, Integer apprenticeId, Long experience) {
        System.out.println("师父传功: mentorId=" + mentorId + ", apprenticeId=" + apprenticeId + ", exp=" + experience);

        MentorRelation relation = mentorRelationRepository.findByMentorIdAndApprenticeIdAndStatus(mentorId, apprenticeId, 1)
                .orElseThrow(() -> new BusinessException(404, "师徒关系不存在"));

        if (experience <= 0) {
            throw new BusinessException(400, "传功经验必须大于0");
        }

        // 师父消耗一定经验，徒弟获得更多经验
        PlayerRole mentor = playerRoleService.findById(mentorId);
        Long mentorCost = experience / 2; // 师父消耗一半经验

        if (mentor.getExperience() < mentorCost) {
            throw new BusinessException(400, "师父经验不足");
        }

        // 扣除师父经验，增加徒弟经验
        playerRoleService.addExperience(mentorId, -mentorCost);
        playerRoleService.addExperience(apprenticeId, experience);

        // 增加师徒贡献度
        relation.addMentorContribution(experience / 10);
        relation.addApprenticeContribution(experience / 20);
        mentorRelationRepository.save(relation);

        System.out.println("师父传功成功: 师父消耗" + mentorCost + "经验，徒弟获得" + experience + "经验");
        return true;
    }

    /**
     * 获取师徒统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getMentorStatistics(Integer roleId) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        // 作为师父的统计
        List<MentorRelation> apprentices = getApprentices(roleId);
        long totalApprentices = apprentices.size();
        long totalMentorContribution = apprentices.stream().mapToLong(MentorRelation::getMentorContribution).sum();
        
        // 作为徒弟的统计
        Optional<MentorRelation> mentor = getMentor(roleId);
        long apprenticeContribution = mentor.map(MentorRelation::getApprenticeContribution).orElse(0L);
        
        stats.put("totalApprentices", totalApprentices);
        stats.put("maxApprentices", MAX_APPRENTICES);
        stats.put("totalMentorContribution", totalMentorContribution);
        stats.put("apprenticeContribution", apprenticeContribution);
        stats.put("hasMentor", mentor.isPresent());
        
        if (mentor.isPresent()) {
            stats.put("mentorName", mentor.get().getMentorName());
            stats.put("mentorLevel", mentor.get().getMentorLevel());
            stats.put("canGraduate", mentor.get().canGraduate());
        }
        
        return stats;
    }

    /**
     * 验证师徒关系条件
     */
    private void validateMentorRelationConditions(PlayerRole apprentice, PlayerRole mentor, boolean isApprenticeApplication) {
        // 检查是否为同一角色
        if (apprentice.getId().equals(mentor.getId())) {
            throw new BusinessException(400, "不能与自己建立师徒关系");
        }

        // 检查等级差距
        int levelGap = mentor.getLevel() - apprentice.getLevel();
        if (levelGap < MIN_LEVEL_GAP) {
            throw new BusinessException(400, "等级差距不足，师父至少要比徒弟高" + MIN_LEVEL_GAP + "级");
        }
        if (levelGap > MAX_LEVEL_GAP) {
            throw new BusinessException(400, "等级差距过大，师父不能比徒弟高超过" + MAX_LEVEL_GAP + "级");
        }

        // 检查徒弟是否已有师父
        if (mentorRelationRepository.findByApprenticeIdAndStatus(apprentice.getId(), 1).isPresent()) {
            throw new BusinessException(400, "该角色已有师父");
        }

        // 检查师父徒弟数量限制
        long apprenticeCount = mentorRelationRepository.countApprenticesByMentorId(mentor.getId());
        if (apprenticeCount >= MAX_APPRENTICES) {
            throw new BusinessException(400, "师父徒弟数量已达上限");
        }

        // 检查是否已有师徒关系
        if (mentorRelationRepository.findByMentorIdAndApprenticeIdAndStatus(mentor.getId(), apprentice.getId(), 1).isPresent()) {
            throw new BusinessException(400, "已存在师徒关系");
        }
    }
}
