package com.honghuang.game.repository;

import com.honghuang.game.entity.MailAttachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 邮件附件数据访问层
 */
@Repository
public interface MailAttachmentRepository extends JpaRepository<MailAttachment, Integer> {

    /**
     * 根据邮件ID查找附件
     */
    List<MailAttachment> findByMailIdOrderByCreateTimeAsc(Integer mailId);

    /**
     * 根据邮件ID查找未领取的附件
     */
    @Query("SELECT ma FROM MailAttachment ma WHERE ma.mailId = :mailId AND ma.claimed = 0 ORDER BY ma.createTime ASC")
    List<MailAttachment> findUnclaimedByMailId(@Param("mailId") Integer mailId);

    /**
     * 根据邮件ID查找已领取的附件
     */
    @Query("SELECT ma FROM MailAttachment ma WHERE ma.mailId = :mailId AND ma.claimed = 1 ORDER BY ma.claimTime DESC")
    List<MailAttachment> findClaimedByMailId(@Param("mailId") Integer mailId);

    /**
     * 根据物品ID查找附件
     */
    List<MailAttachment> findByItemIdOrderByCreateTimeDesc(Integer itemId);

    /**
     * 查找指定邮件列表的所有附件
     */
    @Query("SELECT ma FROM MailAttachment ma WHERE ma.mailId IN :mailIds ORDER BY ma.mailId ASC, ma.createTime ASC")
    List<MailAttachment> findByMailIds(@Param("mailIds") List<Integer> mailIds);

    /**
     * 统计邮件的附件数量
     */
    @Query("SELECT COUNT(ma) FROM MailAttachment ma WHERE ma.mailId = :mailId")
    long countByMailId(@Param("mailId") Integer mailId);

    /**
     * 统计邮件的未领取附件数量
     */
    @Query("SELECT COUNT(ma) FROM MailAttachment ma WHERE ma.mailId = :mailId AND ma.claimed = 0")
    long countUnclaimedByMailId(@Param("mailId") Integer mailId);

    /**
     * 标记附件为已领取
     */
    @Modifying
    @Query("UPDATE MailAttachment ma SET ma.claimed = 1, ma.claimTime = CURRENT_TIMESTAMP WHERE ma.id = :attachmentId")
    int markAsClaimed(@Param("attachmentId") Integer attachmentId);

    /**
     * 批量标记邮件的所有附件为已领取
     */
    @Modifying
    @Query("UPDATE MailAttachment ma SET ma.claimed = 1, ma.claimTime = CURRENT_TIMESTAMP WHERE ma.mailId = :mailId AND ma.claimed = 0")
    int markAllAsClaimedByMailId(@Param("mailId") Integer mailId);

    /**
     * 删除邮件的所有附件
     */
    @Modifying
    @Query("DELETE FROM MailAttachment ma WHERE ma.mailId = :mailId")
    int deleteByMailId(@Param("mailId") Integer mailId);

    /**
     * 删除指定邮件列表的所有附件
     */
    @Modifying
    @Query("DELETE FROM MailAttachment ma WHERE ma.mailId IN :mailIds")
    int deleteByMailIds(@Param("mailIds") List<Integer> mailIds);

    /**
     * 查找包含指定物品的附件
     */
    @Query("SELECT ma FROM MailAttachment ma JOIN ma.mail m WHERE ma.itemId = :itemId AND m.receiverId = :receiverId AND ma.claimed = 0 ORDER BY ma.createTime DESC")
    List<MailAttachment> findUnclaimedByItemAndReceiver(@Param("itemId") Integer itemId, @Param("receiverId") Integer receiverId);
}
