-- 游戏物品初始化数据
-- 插入基础物品数据

-- 武器类物品
INSERT INTO goods_info (goods_id, goods_name, goods_type, goods_sub_type, goods_level, goods_quality, goods_description, 
                       is_stackable, max_stack, is_tradeable, is_droppable, buy_price, sell_price, use_level_limit,
                       sex_limit, race_limit, attack_bonus, defense_bonus, hp_bonus, mp_bonus, 
                       force_bonus, agile_bonus, physique_bonus, savvy_bonus, is_active) VALUES
(1, '新手木剑', 1, 1, 1, 1, '新手专用的木制长剑，攻击力较低但易于使用。', 0, 1, 1, 1, 100, 50, 1, 0, 0, 5, 0, 0, 0, 1, 0, 0, 0, 1),
(2, '铁剑', 1, 1, 5, 2, '用精铁打造的长剑，锋利且坚固。', 0, 1, 1, 1, 500, 250, 5, 0, 0, 12, 0, 0, 0, 2, 1, 0, 0, 1),
(3, '青钢剑', 1, 1, 10, 2, '青钢锻造的宝剑，剑身泛着青光。', 0, 1, 1, 1, 1200, 600, 10, 0, 0, 20, 0, 0, 0, 3, 2, 0, 0, 1),
(4, '龙鳞剑', 1, 1, 20, 3, '传说中用龙鳞打造的神剑，威力惊人。', 0, 1, 1, 1, 5000, 2500, 20, 0, 0, 35, 0, 0, 0, 5, 3, 0, 1, 1),
(5, '新手法杖', 1, 2, 1, 1, '新手法师的入门法杖，能够聚集微弱的法力。', 0, 1, 1, 1, 120, 60, 1, 0, 0, 2, 0, 0, 10, 0, 0, 0, 2, 1),

-- 防具类物品
(10, '布衣', 2, 3, 1, 1, '普通的布制衣服，提供基础防护。', 0, 1, 1, 1, 80, 40, 1, 0, 0, 0, 3, 5, 0, 0, 0, 1, 0, 1),
(11, '皮甲', 2, 3, 5, 2, '用兽皮制作的轻甲，防护力不错。', 0, 1, 1, 1, 300, 150, 5, 0, 0, 0, 8, 15, 0, 1, 1, 2, 0, 1),
(12, '铁甲', 2, 3, 10, 2, '铁制的重甲，防护力强但影响敏捷。', 0, 1, 1, 1, 800, 400, 10, 0, 0, 0, 15, 25, 0, 2, -1, 3, 0, 1),
(13, '新手头盔', 2, 2, 1, 1, '简单的头部防护装备。', 0, 1, 1, 1, 50, 25, 1, 0, 0, 0, 2, 3, 0, 0, 0, 1, 0, 1),
(14, '新手鞋子', 2, 5, 1, 1, '普通的布鞋，提供基本的脚部保护。', 0, 1, 1, 1, 40, 20, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1),

-- 饰品类物品
(20, '力量戒指', 3, 8, 5, 2, '蕴含力量之源的戒指，佩戴后力量大增。', 0, 1, 1, 1, 600, 300, 5, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 1),
(21, '敏捷项链', 3, 7, 8, 2, '轻盈的项链，能够提升佩戴者的敏捷。', 0, 1, 1, 1, 800, 400, 8, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 1),
(22, '智慧护符', 3, 9, 10, 3, '古老的护符，蕴含着智慧的力量。', 0, 1, 1, 1, 1500, 750, 10, 0, 0, 0, 0, 0, 15, 0, 0, 0, 3, 1),

-- 消耗品类物品
(30, '小血瓶', 4, 1, 1, 1, '恢复少量生命值的药水。', 1, 99, 1, 1, 20, 10, 1, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 1),
(31, '中血瓶', 4, 1, 5, 2, '恢复中等生命值的药水。', 1, 99, 1, 1, 50, 25, 5, 0, 0, 0, 0, 80, 0, 0, 0, 0, 0, 1),
(32, '大血瓶', 4, 1, 10, 2, '恢复大量生命值的药水。', 1, 99, 1, 1, 120, 60, 10, 0, 0, 0, 0, 200, 0, 0, 0, 0, 0, 1),
(33, '小蓝瓶', 4, 2, 1, 1, '恢复少量法力值的药水。', 1, 99, 1, 1, 25, 12, 1, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 1),
(34, '中蓝瓶', 4, 2, 5, 2, '恢复中等法力值的药水。', 1, 99, 1, 1, 60, 30, 5, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 1),
(35, '回城卷轴', 4, 3, 1, 1, '使用后可以瞬间回到城镇的神奇卷轴。', 1, 20, 1, 1, 100, 50, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),

-- 材料类物品
(40, '铁矿石', 5, 1, 1, 1, '普通的铁矿石，可用于锻造装备。', 1, 99, 1, 1, 10, 5, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(41, '铜矿石', 5, 1, 1, 1, '常见的铜矿石，锻造的基础材料。', 1, 99, 1, 1, 8, 4, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(42, '银矿石', 5, 1, 5, 2, '珍贵的银矿石，可锻造高级装备。', 1, 99, 1, 1, 50, 25, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(43, '魔法水晶', 5, 2, 8, 2, '蕴含魔法能量的水晶，制作法器的重要材料。', 1, 50, 1, 1, 100, 50, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(44, '龙鳞片', 5, 3, 20, 4, '传说中巨龙的鳞片，极其珍贵的锻造材料。', 1, 10, 1, 1, 1000, 500, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),

-- 任务物品
(50, '村长的信件', 6, 1, 1, 1, '村长托付的重要信件，需要送达指定地点。', 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(51, '神秘的钥匙', 6, 2, 5, 2, '一把神秘的钥匙，似乎能打开某个重要的门。', 0, 1, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(52, '古老的地图', 6, 3, 10, 3, '标记着宝藏位置的古老地图。', 0, 1, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),

-- 其他类物品
(60, '金币袋', 7, 1, 1, 2, '装满金币的袋子，使用后获得金币。', 1, 10, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(61, '经验丹', 7, 2, 5, 3, '蕴含丰富经验的丹药，服用后可获得大量经验。', 1, 5, 1, 1, 500, 250, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1),
(62, '随机宝箱', 7, 3, 10, 4, '神秘的宝箱，打开后可获得随机奖励。', 1, 1, 1, 1, 1000, 500, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1);
