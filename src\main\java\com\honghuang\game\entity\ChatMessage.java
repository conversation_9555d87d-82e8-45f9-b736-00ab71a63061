package com.honghuang.game.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 聊天消息实体类
 */
@Entity
@Table(name = "chat_message")
public class ChatMessage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "sender_id")
    private Integer senderId;
    
    @Column(name = "sender_name")
    private String senderName;
    
    @Column(name = "receiver_id")
    private Integer receiverId; // 私聊接收者ID，为空表示公共频道
    
    @Column(name = "receiver_name")
    private String receiverName;
    
    @Column(name = "channel_type")
    private Integer channelType; // 1-世界 2-私聊 3-帮派 4-系统
    
    @Column(name = "message_content", columnDefinition = "TEXT")
    private String content;
    
    @Column(name = "send_time")
    private LocalDateTime sendTime;
    
    @Column(name = "faction_id")
    private Integer factionId; // 帮派ID，用于帮派频道
    
    @Column(name = "is_system")
    private Integer isSystem; // 是否系统消息
    
    @Column(name = "message_type")
    private Integer messageType; // 消息类型：1-普通 2-表情 3-物品链接
    
    @Column(name = "extra_data")
    private String extraData; // 额外数据，JSON格式
    
    // 构造函数
    public ChatMessage() {
        this.sendTime = LocalDateTime.now();
        this.isSystem = 0;
        this.messageType = 1;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getSenderId() {
        return senderId;
    }
    
    public void setSenderId(Integer senderId) {
        this.senderId = senderId;
    }
    
    public String getSenderName() {
        return senderName;
    }
    
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }
    
    public Integer getReceiverId() {
        return receiverId;
    }
    
    public void setReceiverId(Integer receiverId) {
        this.receiverId = receiverId;
    }
    
    public String getReceiverName() {
        return receiverName;
    }
    
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }
    
    public Integer getChannelType() {
        return channelType;
    }
    
    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public LocalDateTime getSendTime() {
        return sendTime;
    }
    
    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }
    
    public Integer getFactionId() {
        return factionId;
    }
    
    public void setFactionId(Integer factionId) {
        this.factionId = factionId;
    }
    
    public Integer getIsSystem() {
        return isSystem;
    }
    
    public void setIsSystem(Integer isSystem) {
        this.isSystem = isSystem;
    }
    
    public Integer getMessageType() {
        return messageType;
    }
    
    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }
    
    public String getExtraData() {
        return extraData;
    }
    
    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }
    
    // 业务方法
    public boolean isWorldMessage() {
        return channelType != null && channelType == 1;
    }
    
    public boolean isPrivateMessage() {
        return channelType != null && channelType == 2;
    }
    
    public boolean isFactionMessage() {
        return channelType != null && channelType == 3;
    }
    
    public boolean isSystemMessage() {
        return channelType != null && channelType == 4 || (isSystem != null && isSystem == 1);
    }
    
    public String getChannelName() {
        if (channelType == null) return "未知";
        switch (channelType) {
            case 1: return "世界";
            case 2: return "私聊";
            case 3: return "帮派";
            case 4: return "系统";
            default: return "未知";
        }
    }
}
