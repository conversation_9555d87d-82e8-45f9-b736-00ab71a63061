package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 玩家角色实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "player_role")
public class PlayerRole {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Integer userId;

    /**
     * 角色名称
     */
    @Column(name = "name", unique = true, length = 20)
    private String name;

    /**
     * 性别 1男 2女
     */
    @Column(name = "sex")
    private Integer sex = 1;

    /**
     * 种族 1妖族 2巫族（与旧系统保持一致）
     */
    @Column(name = "race")
    private Integer race = 1;

    /**
     * 等级
     */
    @Column(name = "level")
    private Integer level = 1;

    /**
     * 经验值
     */
    @Column(name = "experience")
    private Long experience = 0L;

    /**
     * 当前生命值
     */
    @Column(name = "current_hp")
    private Integer currentHp = 100;

    /**
     * 最大生命值
     */
    @Column(name = "max_hp")
    private Integer maxHp = 100;

    /**
     * 当前法力值
     */
    @Column(name = "current_mp")
    private Integer currentMp = 50;

    /**
     * 最大法力值
     */
    @Column(name = "max_mp")
    private Integer maxMp = 50;

    /**
     * 基础攻击力
     */
    @Column(name = "base_attack")
    private Integer baseAttack = 10;

    /**
     * 基础防御力
     */
    @Column(name = "base_defense")
    private Integer baseDefense = 5;

    /**
     * 基础敏捷
     */
    @Column(name = "base_agility")
    private Integer baseAgility = 8;

    /**
     * 基础智力
     */
    @Column(name = "base_intelligence")
    private Integer baseIntelligence = 6;

    /**
     * 基础体质
     */
    @Column(name = "base_constitution")
    private Integer baseConstitution = 7;

    /**
     * 可分配属性点
     */
    @Column(name = "attribute_points")
    private Integer attributePoints = 0;

    /**
     * 金钱（铜钱）
     */
    @Column(name = "copper")
    private Long copper = 1000L;

    /**
     * 银两
     */
    @Column(name = "silver")
    private Long silver = 0L;

    /**
     * 元宝
     */
    @Column(name = "gold")
    private Long gold = 0L;

    /**
     * 当前场景ID
     */
    @Column(name = "scene_id")
    private Integer sceneId = 1;

    /**
     * 场景名称
     */
    @Column(name = "scene_name")
    private String sceneName = "新手村";

    /**
     * 是否在线 0离线 1在线
     */
    @Column(name = "online_status")
    private Integer onlineStatus = 0;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 删除标志 0正常 1已删除
     */
    @Column(name = "deleted")
    private Integer deleted = 0;

    // 计算属性方法

    /**
     * 获取总攻击力（基础+装备加成）
     */
    public Integer getTotalAttack() {
        return baseAttack + getEquipmentAttackBonus();
    }

    /**
     * 获取总防御力（基础+装备加成）
     */
    public Integer getTotalDefense() {
        return baseDefense + getEquipmentDefenseBonus();
    }

    /**
     * 获取总敏捷（基础+装备加成）
     */
    public Integer getTotalAgility() {
        return baseAgility + getEquipmentAgilityBonus();
    }

    /**
     * 获取总智力（基础+装备加成）
     */
    public Integer getTotalIntelligence() {
        return baseIntelligence + getEquipmentIntelligenceBonus();
    }

    /**
     * 获取总体质（基础+装备加成）
     */
    public Integer getTotalConstitution() {
        return baseConstitution + getEquipmentConstitutionBonus();
    }

    /**
     * 获取装备攻击力加成（暂时返回0，后续实现装备系统时完善）
     */
    private Integer getEquipmentAttackBonus() {
        return 0;
    }

    /**
     * 获取装备防御力加成
     */
    private Integer getEquipmentDefenseBonus() {
        return 0;
    }

    /**
     * 获取装备敏捷加成
     */
    private Integer getEquipmentAgilityBonus() {
        return 0;
    }

    /**
     * 获取装备智力加成
     */
    private Integer getEquipmentIntelligenceBonus() {
        return 0;
    }

    /**
     * 获取装备体质加成
     */
    private Integer getEquipmentConstitutionBonus() {
        return 0;
    }

    /**
     * 计算升级所需经验
     */
    public Long getNextLevelExp() {
        return (long) (level * level * 100 + level * 50);
    }

    /**
     * 判断是否可以升级
     */
    public boolean canLevelUp() {
        return experience >= getNextLevelExp();
    }

    /**
     * 判断角色是否存活
     */
    public boolean isAlive() {
        return currentHp > 0;
    }

    /**
     * 恢复生命值
     */
    public void restoreHp(Integer amount) {
        this.currentHp = Math.min(this.currentHp + amount, this.maxHp);
    }

    /**
     * 恢复法力值
     */
    public void restoreMp(Integer amount) {
        this.currentMp = Math.min(this.currentMp + amount, this.maxMp);
    }

    /**
     * 扣除生命值
     */
    public void takeDamage(Integer damage) {
        this.currentHp = Math.max(0, this.currentHp - damage);
    }

    /**
     * 消耗法力值
     */
    public void consumeMp(Integer amount) {
        this.currentMp = Math.max(0, this.currentMp - amount);
    }

    /**
     * 增加经验值
     */
    public void addExperience(Long exp) {
        this.experience += exp;
    }

    /**
     * 增加金钱
     */
    public void addCopper(Long amount) {
        this.copper += amount;
    }

    /**
     * 扣除金钱
     */
    public boolean spendCopper(Long amount) {
        if (this.copper >= amount) {
            this.copper -= amount;
            return true;
        }
        return false;
    }

    /**
     * 获取种族名称
     */
    public String getRaceName() {
        return race == 1 ? "巫族" : "妖族";
    }

    /**
     * 获取性别名称
     */
    public String getSexName() {
        return sex == 1 ? "男" : "女";
    }

    /**
     * 增加银两
     */
    public void addSilver(Long amount) {
        this.silver += amount;
    }

    /**
     * 扣除银两
     */
    public boolean spendSilver(Long amount) {
        if (this.silver >= amount) {
            this.silver -= amount;
            return true;
        }
        return false;
    }

    /**
     * 增加元宝
     */
    public void addGold(Long amount) {
        this.gold += amount;
    }

    /**
     * 扣除元宝
     */
    public boolean spendGold(Long amount) {
        if (this.gold >= amount) {
            this.gold -= amount;
            return true;
        }
        return false;
    }
}
