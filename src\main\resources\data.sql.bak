-- 插入基础怪物数据
INSERT INTO monster (name, level, hp, attack, defense, agility, exp_reward, copper_reward, scene_id, scene_name, description, monster_type, can_attack, active) VALUES
('野狼', 1, 50, 8, 3, 6, 15, 10, 1, '新手村', '新手村附近的野狼，攻击性较弱', 1, 1, 1),
('森林熊', 3, 120, 15, 8, 4, 45, 30, 1, '新手村', '森林中的棕熊，力量强大但行动缓慢', 1, 1, 1),
('毒蛇', 2, 40, 12, 2, 10, 25, 15, 1, '新手村', '有毒的蛇类，速度很快', 1, 1, 1),
('山贼', 5, 200, 25, 12, 8, 80, 50, 2, '山林', '占山为王的山贼，装备简陋', 2, 1, 1),
('巨型蜘蛛', 4, 80, 18, 6, 12, 60, 35, 2, '山林', '变异的巨型蜘蛛，会吐丝', 1, 1, 1);

-- 插入基础技能数据
INSERT INTO skill (name, type, category, required_level, required_race, required_sex, max_level, base_value, level_growth, base_mp_cost, mp_cost_growth, cooldown, description, learn_cost, active) VALUES
('基础剑法', 1, 1, 1, 0, 0, 10, 20, 5, 5, 1, 3, '基础的剑术攻击技能', 100, 1),
('治疗术', 3, 2, 1, 0, 0, 10, 30, 8, 10, 2, 0, '恢复生命值的法术', 150, 1),
('火球术', 1, 2, 3, 2, 0, 10, 35, 10, 15, 3, 5, '妖族专属的火系攻击法术', 300, 1),
('铁壁防御', 2, 3, 2, 1, 0, 5, 15, 3, 8, 1, 0, '巫族专属的防御技能，提升防御力', 200, 1),
('疾风步', 4, 3, 4, 0, 0, 8, 10, 2, 12, 2, 10, '提升移动速度的辅助技能', 400, 1);

-- 插入基础任务数据
INSERT INTO quest (name, type, required_level, required_race, required_sex, description, objective, objective_type, objective_id, objective_count, exp_reward, copper_reward, giver_npc_id, finisher_npc_id, active) VALUES
('清理野狼', 1, 1, 0, 0, '新手村附近出现了野狼，请帮助清理', '击败野狼', 1, 1, 5, 100, 50, 1, 1, 1),
('收集草药', 2, 1, 0, 0, '村医需要一些草药来制作药剂', '收集草药', 2, 101, 10, 80, 30, 2, 2, 1),
('拜访长老', 3, 1, 0, 0, '去拜访村中的长老，听取他的教导', '与长老对话', 3, 3, 1, 50, 20, 1, 3, 1),
('探索山林', 5, 3, 0, 0, '前往山林探索，了解那里的情况', '到达山林', 4, 2, 1, 150, 80, 1, 1, 1),
('击败山贼头目', 1, 5, 0, 0, '山贼头目威胁着过往商旅的安全', '击败山贼头目', 1, 4, 1, 300, 200, 4, 4, 1);

-- 插入基础商店数据
INSERT INTO shop (name, type, description, npc_id, npc_name, scene_id, scene_name, required_level, refresh_interval, active) VALUES
('新手装备店', 1, '为新手冒险者提供基础装备', 10, '铁匠老王', 1, '新手村', 1, 60, 1),
('草药铺', 2, '出售各种药品和草药', 11, '药师小李', 1, '新手村', 1, 30, 1),
('杂货店', 3, '出售日常用品和材料', 12, '商人老张', 1, '新手村', 1, 120, 1),
('技能导师', 4, '传授各种技能', 13, '武术大师', 1, '新手村', 1, 1440, 1),
('高级装备店', 1, '出售高级装备', 14, '大师铁匠', 2, '山林', 5, 180, 1);

-- 插入基础装备数据
INSERT INTO equipment (name, type, quality, required_level, required_sex, required_race, attack_bonus, defense_bonus, agility_bonus, intelligence_bonus, constitution_bonus, hp_bonus, mp_bonus, description, sell_price, buy_price, active) VALUES
('新手木剑', 1, 1, 1, 0, 0, 5, 0, 0, 0, 0, 0, 0, '新手使用的木制剑', 10, 50, 1),
('布衣', 3, 1, 1, 0, 0, 0, 2, 0, 0, 1, 10, 0, '简单的布制衣服', 8, 40, 1),
('草鞋', 4, 1, 1, 0, 0, 0, 1, 2, 0, 0, 0, 0, '用草编制的鞋子', 5, 25, 1),
('铁剑', 1, 2, 3, 0, 0, 12, 0, 0, 0, 0, 0, 0, '锋利的铁制剑', 50, 200, 1),
('皮甲', 3, 2, 3, 0, 0, 0, 8, 1, 0, 2, 20, 0, '坚韧的皮制护甲', 40, 160, 1),
('法师帽', 2, 2, 2, 0, 2, 0, 1, 0, 5, 0, 0, 15, '妖族法师专用帽子', 30, 120, 1),
('战士头盔', 2, 2, 2, 0, 1, 0, 6, 0, 0, 2, 15, 0, '巫族战士专用头盔', 35, 140, 1);

-- 插入商店商品数据
INSERT INTO shop_item (shop_id, item_type, item_id, item_name, item_description, item_quality, price_copper, stock, buy_limit, required_level, active) VALUES
(1, 1, 1, '新手木剑', '新手使用的木制剑', 1, 50, 10, 1, 1, 1),
(1, 1, 2, '布衣', '简单的布制衣服', 1, 40, 10, 1, 1, 1),
(1, 1, 3, '草鞋', '用草编制的鞋子', 1, 25, 10, 1, 1, 1),
(1, 1, 4, '铁剑', '锋利的铁制剑', 2, 200, 5, 1, 3, 1),
(1, 1, 5, '皮甲', '坚韧的皮制护甲', 2, 160, 5, 1, 3, 1),
(2, 3, 201, '生命药水', '恢复50点生命值', 1, 20, 50, 5, 1, 1),
(2, 3, 202, '法力药水', '恢复30点法力值', 1, 15, 50, 5, 1, 1),
(2, 3, 203, '大生命药水', '恢复100点生命值', 2, 50, 20, 3, 3, 1),
(3, 4, 301, '铁矿石', '制作装备的基础材料', 1, 10, 100, 10, 1, 1),
(3, 4, 302, '布料', '制作衣服的材料', 1, 8, 100, 10, 1, 1),
(4, 2, 1, '基础剑法', '基础的剑术攻击技能', 1, 100, -1, 1, 1, 1),
(4, 2, 2, '治疗术', '恢复生命值的法术', 1, 150, -1, 1, 1, 1);

-- 插入场景数据（如果scene表存在）
-- INSERT INTO scene (id, name, description, level_range_min, level_range_max, scene_type, active) VALUES
-- (1, '新手村', '新手冒险者的起始地点', 1, 5, 1, 1),
-- (2, '山林', '危险的山林地带', 3, 8, 2, 1),
-- (3, '古墓', '神秘的古代墓穴', 8, 15, 3, 1);

-- 更新序列值（如果使用的是PostgreSQL）
-- SELECT setval('monster_id_seq', (SELECT MAX(id) FROM monster));
-- SELECT setval('skill_id_seq', (SELECT MAX(id) FROM skill));
-- SELECT setval('quest_id_seq', (SELECT MAX(id) FROM quest));
-- SELECT setval('shop_id_seq', (SELECT MAX(id) FROM shop));
-- SELECT setval('equipment_id_seq', (SELECT MAX(id) FROM equipment));
-- SELECT setval('shop_item_id_seq', (SELECT MAX(id) FROM shop_item));
