package com.honghuang.game.service;

import com.honghuang.game.entity.Monster;
import com.honghuang.game.repository.MonsterRepository;
import com.honghuang.game.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 怪物服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class MonsterService {

    @Autowired
    private MonsterRepository monsterRepository;

    /**
     * 根据ID查找怪物
     */
    public Monster findById(Integer id) {
        return monsterRepository.findById(id)
                .orElseThrow(() -> new BusinessException("怪物不存在"));
    }

    /**
     * 根据名称查找怪物
     */
    public Monster findByName(String name) {
        return monsterRepository.findByName(name)
                .orElseThrow(() -> new BusinessException("怪物不存在"));
    }

    /**
     * 查找所有激活的怪物
     */
    public List<Monster> findAllActive() {
        return monsterRepository.findActiveMonsters();
    }

    /**
     * 根据类型查找怪物
     */
    public List<Monster> findByType(Integer type) {
        return monsterRepository.findByType(type);
    }

    /**
     * 根据等级范围查找怪物
     */
    public List<Monster> findByLevelRange(Integer minLevel, Integer maxLevel) {
        return monsterRepository.findByLevelBetween(minLevel, maxLevel);
    }

    /**
     * 根据名称模糊查找怪物
     */
    public List<Monster> findByNameContaining(String name) {
        return monsterRepository.findByNameContaining(name);
    }

    /**
     * 查找普通怪物
     */
    public List<Monster> findNormalMonsters() {
        return monsterRepository.findNormalMonsters();
    }

    /**
     * 查找精英怪物
     */
    public List<Monster> findEliteMonsters() {
        return monsterRepository.findEliteMonsters();
    }

    /**
     * 查找BOSS
     */
    public List<Monster> findBosses() {
        return monsterRepository.findBosses();
    }

    /**
     * 查找NPC
     */
    public List<Monster> findNpcs() {
        return monsterRepository.findNpcs();
    }

    /**
     * 查找适合等级的怪物
     */
    public List<Monster> findSuitableMonsters(Integer minLevel, Integer maxLevel) {
        return monsterRepository.findSuitableMonsters(minLevel, maxLevel);
    }

    /**
     * 根据玩家等级推荐怪物
     */
    public List<Monster> findRecommendedMonsters(Integer playerLevel) {
        return monsterRepository.findRecommendedMonsters(playerLevel);
    }

    /**
     * 查找可攻击的怪物
     */
    public List<Monster> findAttackableMonsters() {
        return monsterRepository.findAttackableMonsters();
    }

    /**
     * 根据AI类型查找怪物
     */
    public List<Monster> findByAiType(Integer aiType) {
        return monsterRepository.findByAiType(aiType);
    }

    /**
     * 查找主动攻击的怪物
     */
    public List<Monster> findAggressiveMonsters() {
        return monsterRepository.findAggressiveMonsters();
    }

    /**
     * 查找被动怪物
     */
    public List<Monster> findPassiveMonsters() {
        return monsterRepository.findPassiveMonsters();
    }

    /**
     * 根据攻击力范围查找怪物
     */
    public List<Monster> findByAttackRange(Integer minAttack, Integer maxAttack) {
        return monsterRepository.findByAttackRange(minAttack, maxAttack);
    }

    /**
     * 根据生命值范围查找怪物
     */
    public List<Monster> findByHpRange(Integer minHp, Integer maxHp) {
        return monsterRepository.findByHpRange(minHp, maxHp);
    }

    /**
     * 查找经验值奖励丰富的怪物
     */
    public List<Monster> findHighExpMonsters(Long minExp) {
        return monsterRepository.findHighExpMonsters(minExp);
    }

    /**
     * 查找铜钱奖励丰富的怪物
     */
    public List<Monster> findHighCopperMonsters(Long minCopper) {
        return monsterRepository.findHighCopperMonsters(minCopper);
    }

    /**
     * 分页查找怪物
     */
    public Page<Monster> findByConditions(Integer type, Integer minLevel, Integer maxLevel, Pageable pageable) {
        return monsterRepository.findByConditions(type, minLevel, maxLevel, pageable);
    }

    /**
     * 查找有掉落物品的怪物
     */
    public List<Monster> findMonstersWithDrops() {
        return monsterRepository.findMonstersWithDrops();
    }

    /**
     * 查找有技能的怪物
     */
    public List<Monster> findMonstersWithSkills() {
        return monsterRepository.findMonstersWithSkills();
    }

    /**
     * 根据刷新时间查找怪物
     */
    public List<Monster> findByMaxRespawnTime(Integer maxRespawnTime) {
        return monsterRepository.findByMaxRespawnTime(maxRespawnTime);
    }

    /**
     * 查找快速刷新的怪物
     */
    public List<Monster> findFastRespawnMonsters() {
        return monsterRepository.findFastRespawnMonsters();
    }

    /**
     * 根据移动速度查找怪物
     */
    public List<Monster> findByMinMoveSpeed(Integer minSpeed) {
        return monsterRepository.findByMinMoveSpeed(minSpeed);
    }

    /**
     * 查找远程攻击怪物
     */
    public List<Monster> findRangedMonsters() {
        return monsterRepository.findRangedMonsters();
    }

    /**
     * 查找近战怪物
     */
    public List<Monster> findMeleeMonsters() {
        return monsterRepository.findMeleeMonsters();
    }

    /**
     * 根据视野范围查找怪物
     */
    public List<Monster> findByMinSightRange(Integer minSight) {
        return monsterRepository.findByMinSightRange(minSight);
    }

    /**
     * 创建新怪物
     */
    public Monster createMonster(Monster monster) {
        // 验证怪物名称唯一性
        Optional<Monster> existingMonster = monsterRepository.findByName(monster.getName());
        if (existingMonster.isPresent()) {
            throw new BusinessException("怪物名称已存在");
        }

        // 设置默认值
        if (monster.getIsActive() == null) {
            monster.setIsActive(1);
        }
        if (monster.getType() == null) {
            monster.setType(1);
        }
        if (monster.getLevel() == null) {
            monster.setLevel(1);
        }

        log.info("创建新怪物: {}", monster.getName());
        return monsterRepository.save(monster);
    }

    /**
     * 更新怪物信息
     */
    public Monster updateMonster(Monster monster) {
        Monster existingMonster = findById(monster.getId());
        
        // 更新字段
        existingMonster.setName(monster.getName());
        existingMonster.setType(monster.getType());
        existingMonster.setLevel(monster.getLevel());
        existingMonster.setMaxHp(monster.getMaxHp());
        existingMonster.setMaxMp(monster.getMaxMp());
        existingMonster.setAttack(monster.getAttack());
        existingMonster.setDefense(monster.getDefense());
        existingMonster.setAgility(monster.getAgility());
        existingMonster.setIntelligence(monster.getIntelligence());
        existingMonster.setConstitution(monster.getConstitution());
        existingMonster.setExpReward(monster.getExpReward());
        existingMonster.setCopperReward(monster.getCopperReward());
        existingMonster.setDropItems(monster.getDropItems());
        existingMonster.setSkills(monster.getSkills());
        existingMonster.setDescription(monster.getDescription());
        existingMonster.setIcon(monster.getIcon());
        existingMonster.setRespawnTime(monster.getRespawnTime());
        existingMonster.setMoveSpeed(monster.getMoveSpeed());
        existingMonster.setAttackRange(monster.getAttackRange());
        existingMonster.setSightRange(monster.getSightRange());
        existingMonster.setAiType(monster.getAiType());

        log.info("更新怪物信息: {}", existingMonster.getName());
        return monsterRepository.save(existingMonster);
    }

    /**
     * 删除怪物（软删除）
     */
    public void deleteMonster(Integer id) {
        Monster monster = findById(id);
        monster.setIsActive(0);
        monsterRepository.save(monster);
        log.info("删除怪物: {}", monster.getName());
    }

    /**
     * 激活怪物
     */
    public void activateMonster(Integer id) {
        Monster monster = findById(id);
        monster.setIsActive(1);
        monsterRepository.save(monster);
        log.info("激活怪物: {}", monster.getName());
    }

    /**
     * 统计各类型怪物数量
     */
    public List<Object[]> countByType() {
        return monsterRepository.countByType();
    }

    /**
     * 统计各等级段怪物数量
     */
    public List<Object[]> countByLevelRange() {
        return monsterRepository.countByLevelRange();
    }

    /**
     * 验证怪物是否可以被玩家攻击
     */
    public boolean canPlayerAttack(Monster monster, Integer playerLevel) {
        // 检查怪物是否激活
        if (monster.getIsActive() != 1) {
            return false;
        }

        // 检查是否为NPC
        if (monster.isNpc()) {
            return false;
        }

        // 检查等级差距
        int levelDiff = Math.abs(playerLevel - monster.getLevel());
        if (levelDiff > 10) {
            return false;
        }

        return true;
    }

    /**
     * 计算怪物战斗力评级
     */
    public String getMonsterRating(Monster monster) {
        int combatPower = monster.getCombatPower();
        
        if (combatPower < 100) {
            return "弱小";
        } else if (combatPower < 300) {
            return "普通";
        } else if (combatPower < 600) {
            return "强大";
        } else if (combatPower < 1000) {
            return "精英";
        } else {
            return "传说";
        }
    }

    /**
     * 获取怪物推荐等级
     */
    public String getRecommendedPlayerLevel(Monster monster) {
        int monsterLevel = monster.getLevel();
        int minLevel = Math.max(1, monsterLevel - 3);
        int maxLevel = monsterLevel + 3;
        
        return String.format("%d-%d级", minLevel, maxLevel);
    }
}
