package com.honghuang.game.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honghuang.game.entity.Ranking;
import com.honghuang.game.dto.RankingDTO;

import java.util.List;
import java.util.Map;

/**
 * 排行榜服务接口
 */
public interface RankingService extends IService<Ranking> {

    /**
     * 获取排行榜数据
     * @param type 排行榜类型
     * @param limit 限制数量
     * @return 排行榜列表
     */
    List<RankingDTO> getRankingList(String type, int limit);

    /**
     * 获取玩家在指定排行榜中的排名
     * @param roleId 角色ID
     * @param type 排行榜类型
     * @return 排名（从1开始，0表示未上榜）
     */
    Integer getPlayerRank(Integer roleId, String type);

    /**
     * 更新玩家排行榜数据
     * @param roleId 角色ID
     * @param type 数据类型
     * @param value 数值
     */
    void updateRankingData(Integer roleId, String type, Long value);

    /**
     * 初始化玩家排行榜记录
     * @param roleId 角色ID
     * @param roleName 角色名称
     * @param race 种族
     * @param level 等级
     */
    void initPlayerRanking(Integer roleId, String roleName, Integer race, Integer level);

    /**
     * 获取所有排行榜分类
     * @return 分类映射
     */
    Map<Integer, List<Map<String, Object>>> getAllRankingCategories();

    /**
     * 删除玩家排行榜记录
     * @param roleId 角色ID
     */
    void deletePlayerRanking(Integer roleId);

    /**
     * 批量更新排行榜数据
     * @param roleId 角色ID
     * @param dataMap 数据映射
     */
    void batchUpdateRankingData(Integer roleId, Map<String, Long> dataMap);
}
