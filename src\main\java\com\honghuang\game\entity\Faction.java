package com.honghuang.game.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 帮派实体类
 */
@Entity
@Table(name = "faction")
public class Faction {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "faction_id")
    private Integer id;
    
    @Column(name = "faction_name")
    private String name;
    
    @Column(name = "faction_description")
    private String description;
    
    @Column(name = "faction_level")
    private Integer level;
    
    @Column(name = "faction_exp")
    private Integer exp;
    
    @Column(name = "faction_max_exp")
    private Integer maxExp;
    
    @Column(name = "leader_id")
    private Integer leaderId;
    
    @Column(name = "leader_name")
    private String leaderName;
    
    @Column(name = "member_count")
    private Integer memberCount;
    
    @Column(name = "max_member_count")
    private Integer maxMemberCount;
    
    @Column(name = "faction_funds")
    private Integer funds; // 帮派资金
    
    @Column(name = "faction_prestige")
    private Integer prestige; // 帮派声望
    
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    @Column(name = "faction_notice", columnDefinition = "TEXT")
    private String notice; // 帮派公告
    
    @Column(name = "faction_type")
    private Integer type; // 帮派类型
    
    @Column(name = "join_condition")
    private Integer joinCondition; // 加入条件：1-自由加入 2-需要审核 3-禁止加入
    
    @Column(name = "min_level")
    private Integer minLevel; // 最低等级要求
    
    @Column(name = "faction_logo")
    private String logo; // 帮派标志
    
    @Column(name = "is_active")
    private Integer isActive; // 是否活跃
    
    // 构造函数
    public Faction() {
        this.createTime = LocalDateTime.now();
        this.level = 1;
        this.exp = 0;
        this.maxExp = 1000;
        this.memberCount = 1;
        this.maxMemberCount = 20;
        this.funds = 0;
        this.prestige = 0;
        this.joinCondition = 2;
        this.minLevel = 1;
        this.isActive = 1;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getLevel() {
        return level;
    }
    
    public void setLevel(Integer level) {
        this.level = level;
    }
    
    public Integer getExp() {
        return exp;
    }
    
    public void setExp(Integer exp) {
        this.exp = exp;
    }
    
    public Integer getMaxExp() {
        return maxExp;
    }
    
    public void setMaxExp(Integer maxExp) {
        this.maxExp = maxExp;
    }
    
    public Integer getLeaderId() {
        return leaderId;
    }
    
    public void setLeaderId(Integer leaderId) {
        this.leaderId = leaderId;
    }
    
    public String getLeaderName() {
        return leaderName;
    }
    
    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }
    
    public Integer getMemberCount() {
        return memberCount;
    }
    
    public void setMemberCount(Integer memberCount) {
        this.memberCount = memberCount;
    }
    
    public Integer getMaxMemberCount() {
        return maxMemberCount;
    }
    
    public void setMaxMemberCount(Integer maxMemberCount) {
        this.maxMemberCount = maxMemberCount;
    }
    
    public Integer getFunds() {
        return funds;
    }
    
    public void setFunds(Integer funds) {
        this.funds = funds;
    }
    
    public Integer getPrestige() {
        return prestige;
    }
    
    public void setPrestige(Integer prestige) {
        this.prestige = prestige;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public String getNotice() {
        return notice;
    }
    
    public void setNotice(String notice) {
        this.notice = notice;
    }
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    public Integer getJoinCondition() {
        return joinCondition;
    }
    
    public void setJoinCondition(Integer joinCondition) {
        this.joinCondition = joinCondition;
    }
    
    public Integer getMinLevel() {
        return minLevel;
    }
    
    public void setMinLevel(Integer minLevel) {
        this.minLevel = minLevel;
    }
    
    public String getLogo() {
        return logo;
    }
    
    public void setLogo(String logo) {
        this.logo = logo;
    }
    
    public Integer getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }
    
    // 业务方法
    public boolean canJoinFreely() {
        return joinCondition != null && joinCondition == 1;
    }
    
    public boolean needsApproval() {
        return joinCondition != null && joinCondition == 2;
    }
    
    public boolean isFull() {
        return memberCount != null && maxMemberCount != null && memberCount >= maxMemberCount;
    }
    
    public boolean canLevelUp() {
        return exp != null && maxExp != null && exp >= maxExp;
    }
    
    public String getJoinConditionName() {
        if (joinCondition == null) return "未知";
        switch (joinCondition) {
            case 1: return "自由加入";
            case 2: return "需要审核";
            case 3: return "禁止加入";
            default: return "未知";
        }
    }
}
