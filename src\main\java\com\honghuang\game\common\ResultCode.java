package com.honghuang.game.common;

/**
 * 返回状态码枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    
    // 用户相关状态码
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    USER_PASSWORD_ERROR(1003, "密码错误"),
    USER_DISABLED(1004, "用户已被禁用"),
    USER_LOGIN_EXPIRED(1005, "登录已过期"),
    USER_NOT_LOGIN(1006, "用户未登录"),
    
    // 角色相关状态码
    ROLE_NOT_FOUND(2001, "角色不存在"),
    ROLE_ALREADY_EXISTS(2002, "角色已存在"),
    ROLE_NAME_DUPLICATE(2003, "角色名称重复"),
    ROLE_LEVEL_NOT_ENOUGH(2004, "角色等级不足"),
    
    // 游戏相关状态码
    GAME_MAINTENANCE(3001, "游戏维护中"),
    GAME_SERVER_FULL(3002, "服务器人数已满"),
    GAME_LEVEL_LIMIT(3003, "等级限制"),
    GAME_ITEM_NOT_ENOUGH(3004, "物品不足"),
    GAME_MONEY_NOT_ENOUGH(3005, "金钱不足"),
    GAME_EXP_NOT_ENOUGH(3006, "经验不足"),
    
    // 战斗相关状态码
    BATTLE_NOT_FOUND(4001, "战斗不存在"),
    BATTLE_ALREADY_FINISHED(4002, "战斗已结束"),
    BATTLE_NOT_YOUR_TURN(4003, "不是你的回合"),
    BATTLE_INVALID_ACTION(4004, "无效的战斗行为"),
    
    // 装备相关状态码
    EQUIP_NOT_FOUND(5001, "装备不存在"),
    EQUIP_ALREADY_EQUIPPED(5002, "装备已穿戴"),
    EQUIP_NOT_EQUIPPED(5003, "装备未穿戴"),
    EQUIP_LEVEL_NOT_ENOUGH(5004, "装备等级不足"),
    EQUIP_DURABILITY_ZERO(5005, "装备耐久度为0"),
    
    // 帮派相关状态码
    FACTION_NOT_FOUND(6001, "帮派不存在"),
    FACTION_ALREADY_EXISTS(6002, "帮派已存在"),
    FACTION_MEMBER_LIMIT(6003, "帮派成员已满"),
    FACTION_NOT_MEMBER(6004, "不是帮派成员"),
    FACTION_NO_PERMISSION(6005, "没有帮派权限"),
    
    // 商城相关状态码
    MALL_ITEM_NOT_FOUND(7001, "商城物品不存在"),
    MALL_ITEM_SOLD_OUT(7002, "商城物品已售完"),
    MALL_INSUFFICIENT_CURRENCY(7003, "货币不足"),
    
    // 任务相关状态码
    TASK_NOT_FOUND(8001, "任务不存在"),
    TASK_ALREADY_COMPLETED(8002, "任务已完成"),
    TASK_NOT_COMPLETED(8003, "任务未完成"),
    TASK_CONDITION_NOT_MET(8004, "任务条件不满足");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
