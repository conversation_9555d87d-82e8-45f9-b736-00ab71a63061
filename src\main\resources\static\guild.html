<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帮派系统 - 洪荒游戏</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f8ff;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: #2c5aa0;
            border-bottom: 2px solid #66ccff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .guild-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            flex-wrap: wrap;
        }
        
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            margin-right: 5px;
        }
        
        .tab.active {
            color: #2c5aa0;
            border-bottom-color: #66ccff;
            background-color: #f8f9fa;
        }
        
        .tab:hover {
            background-color: #f8f9fa;
        }
        
        .guild-status {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .guild-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            min-height: 300px;
        }
        
        .guild-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .guild-item:hover {
            background-color: #f0f8ff;
            border-color: #66ccff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .guild-name {
            font-weight: bold;
            color: #2c5aa0;
            font-size: 16px;
            margin-bottom: 8px;
        }
        
        .guild-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .guild-leader {
            color: #28a745;
            font-weight: bold;
        }
        
        .guild-actions {
            text-align: right;
            margin-top: 10px;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 5px;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #2c5aa0;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1e3d6f;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .my-guild-panel {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 2px solid #28a745;
        }
        
        .guild-detail {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        
        .detail-item {
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .detail-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }
        
        .detail-value {
            color: #333;
        }
        
        .member-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .member-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .member-info {
            flex: 1;
        }
        
        .member-name {
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .member-position {
            color: #666;
            font-size: 12px;
        }
        
        .position-1 { color: #dc3545; } /* 帮主 */
        .position-2 { color: #fd7e14; } /* 副帮主 */
        .position-3 { color: #6f42c1; } /* 长老 */
        .position-4 { color: #007bff; } /* 堂主 */
        .position-5 { color: #28a745; } /* 精英 */
        .position-6 { color: #6c757d; } /* 普通成员 */
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal.show {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        
        .modal-title {
            color: #2c5aa0;
            font-weight: bold;
            font-size: 18px;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }
        
        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .empty-message {
            text-align: center;
            color: #666;
            padding: 50px;
            font-style: italic;
            grid-column: 1 / -1;
        }
        
        .navigation {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .navigation a {
            color: #2c5aa0;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .navigation a:hover {
            text-decoration: underline;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .success {
            color: #155724;
            text-align: center;
            padding: 20px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s;
        }
        
        .guild-level {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            color: white;
            background-color: #2c5aa0;
            margin-left: 5px;
        }
        
        .join-type-1 { color: #28a745; } /* 自由加入 */
        .join-type-2 { color: #ffc107; } /* 需要审核 */
        .join-type-3 { color: #dc3545; } /* 禁止加入 */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>【帮派系统】</h1>
        </div>
        
        <div id="message-area"></div>
        
        <!-- 我的帮派状态 -->
        <div id="my-guild-status" class="guild-status">
            <div id="loading-status" class="loading">检查帮派状态...</div>
            <div id="no-guild" style="display: none;">
                <p>您还没有加入任何帮派</p>
                <button class="btn btn-primary" onclick="switchTab('list')">查看帮派列表</button>
                <button class="btn btn-success" onclick="openCreateGuildModal()">创建帮派</button>
            </div>
            <div id="has-guild" style="display: none;"></div>
        </div>
        
        <div class="guild-tabs">
            <button class="tab active" onclick="switchTab('list')">帮派列表</button>
            <button class="tab" onclick="switchTab('my')" id="my-tab" style="display: none;">我的帮派</button>
            <button class="tab" onclick="switchTab('members')" id="members-tab" style="display: none;">帮派成员</button>
            <button class="tab" onclick="switchTab('applications')" id="applications-tab" style="display: none;">申请管理</button>
        </div>
        
        <div id="guild-content">
            <div id="loading" class="loading">加载中...</div>
            <div id="guild-list" class="guild-list" style="display: none;"></div>
            <div id="my-guild" style="display: none;"></div>
            <div id="guild-members" style="display: none;"></div>
            <div id="guild-applications" style="display: none;"></div>
        </div>
        
        <div class="navigation">
            <a href="game.html">返回游戏</a>
            <a href="javascript:void(0)" onclick="refreshGuild()">刷新</a>
        </div>
    </div>

    <!-- 创建帮派模态框 -->
    <div id="create-guild-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">创建帮派</div>
                <button class="close-btn" onclick="closeCreateGuildModal()">×</button>
            </div>
            <div>
                <div class="form-group">
                    <label class="form-label">帮派名称：</label>
                    <input type="text" id="guild-name-input" class="form-input" placeholder="请输入帮派名称" maxlength="20">
                </div>
                <div class="form-group">
                    <label class="form-label">帮派简介：</label>
                    <textarea id="guild-description-input" class="form-textarea" placeholder="请输入帮派简介" maxlength="200"></textarea>
                </div>
                <div style="color: #666; font-size: 12px; margin-bottom: 15px;">
                    创建帮派需要：等级10级以上，消耗10000铜钱
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-success" onclick="createGuild()">创建帮派</button>
                <button class="btn btn-primary" onclick="closeCreateGuildModal()">取消</button>
            </div>
        </div>
    </div>

    <!-- 申请加入帮派模态框 -->
    <div id="apply-guild-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">申请加入帮派</div>
                <button class="close-btn" onclick="closeApplyGuildModal()">×</button>
            </div>
            <div>
                <div id="apply-guild-info"></div>
                <div class="form-group">
                    <label class="form-label">申请留言：</label>
                    <textarea id="apply-message-input" class="form-textarea" placeholder="请输入申请留言" maxlength="100"></textarea>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-success" onclick="submitApplication()">提交申请</button>
                <button class="btn btn-primary" onclick="closeApplyGuildModal()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let currentTab = 'list';
        let currentGuild = null;
        let guildList = [];
        let guildMembers = [];
        let guildApplications = [];
        let selectedGuildForApply = null;
        
        // 职位映射
        const positionMap = {
            1: '帮主',
            2: '副帮主', 
            3: '长老',
            4: '堂主',
            5: '精英',
            6: '普通成员'
        };
        
        // 入帮方式映射
        const joinTypeMap = {
            1: '自由加入',
            2: '需要审核',
            3: '禁止加入'
        };
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkGuildStatus();
        });
        
        // 检查帮派状态
        function checkGuildStatus() {
            fetch('/honghuang-game/api/guild/my')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading-status').style.display = 'none';
                    
                    if (data.code === 200) {
                        // 已加入帮派
                        currentGuild = data.data;
                        showHasGuild();
                        showGuildTabs();
                    } else {
                        // 未加入帮派
                        currentGuild = null;
                        showNoGuild();
                        hideGuildTabs();
                    }
                    
                    loadGuildList();
                })
                .catch(error => {
                    document.getElementById('loading-status').style.display = 'none';
                    showNoGuild();
                    hideGuildTabs();
                    loadGuildList();
                });
        }
        
        // 显示已加入帮派状态
        function showHasGuild() {
            const hasGuildDiv = document.getElementById('has-guild');
            hasGuildDiv.style.display = 'block';
            hasGuildDiv.innerHTML = `
                <p><strong>当前帮派：</strong>${currentGuild.guild ? currentGuild.guild.name : '未知帮派'} 
                   <span class="guild-level">Lv.${currentGuild.guild ? currentGuild.guild.level : 1}</span></p>
                <p><strong>我的职位：</strong><span class="position-${currentGuild.position}">${positionMap[currentGuild.position]}</span></p>
                <p><strong>贡献度：</strong>${currentGuild.contribution}</p>
                <div style="margin-top: 10px;">
                    <button class="btn btn-primary" onclick="switchTab('my')">帮派详情</button>
                    <button class="btn btn-success" onclick="switchTab('members')">帮派成员</button>
                    ${currentGuild.canManage() ? '<button class="btn btn-warning" onclick="switchTab(\'applications\')">申请管理</button>' : ''}
                    <button class="btn btn-danger" onclick="leaveGuild()">退出帮派</button>
                </div>
            `;
            document.getElementById('no-guild').style.display = 'none';
        }
        
        // 显示未加入帮派状态
        function showNoGuild() {
            document.getElementById('no-guild').style.display = 'block';
            document.getElementById('has-guild').style.display = 'none';
        }
        
        // 显示帮派相关标签页
        function showGuildTabs() {
            document.getElementById('my-tab').style.display = 'block';
            document.getElementById('members-tab').style.display = 'block';
            if (currentGuild && (currentGuild.position <= 3)) { // 帮主、副帮主、长老可以管理申请
                document.getElementById('applications-tab').style.display = 'block';
            }
        }
        
        // 隐藏帮派相关标签页
        function hideGuildTabs() {
            document.getElementById('my-tab').style.display = 'none';
            document.getElementById('members-tab').style.display = 'none';
            document.getElementById('applications-tab').style.display = 'none';
        }
        
        // 切换标签页
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelector(`[onclick="switchTab('${tab}')"]`).classList.add('active');
            
            // 隐藏所有内容
            document.getElementById('guild-list').style.display = 'none';
            document.getElementById('my-guild').style.display = 'none';
            document.getElementById('guild-members').style.display = 'none';
            document.getElementById('guild-applications').style.display = 'none';
            
            // 显示对应内容
            switch(tab) {
                case 'list':
                    loadGuildList();
                    break;
                case 'my':
                    loadMyGuild();
                    break;
                case 'members':
                    loadGuildMembers();
                    break;
                case 'applications':
                    loadGuildApplications();
                    break;
            }
        }
        
        // 加载帮派列表
        function loadGuildList() {
            showLoading();
            
            fetch('/honghuang-game/api/guild/list')
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        guildList = data.data;
                        displayGuildList();
                    } else {
                        showError(data.message || '加载帮派列表失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示帮派列表
        function displayGuildList() {
            const guildListDiv = document.getElementById('guild-list');
            guildListDiv.style.display = 'grid';
            
            if (!guildList || guildList.length === 0) {
                guildListDiv.innerHTML = '<div class="empty-message">暂无帮派</div>';
                return;
            }
            
            let html = '';
            guildList.forEach(guild => {
                const canJoin = !currentGuild && guild.canJoin();
                html += `
                    <div class="guild-item" onclick="showGuildDetail(${guild.id})">
                        <div class="guild-name">
                            ${guild.name}
                            <span class="guild-level">Lv.${guild.level}</span>
                        </div>
                        <div class="guild-info">帮主：<span class="guild-leader">${guild.leaderName}</span></div>
                        <div class="guild-info">成员：${guild.memberCount}/${guild.maxMembers}</div>
                        <div class="guild-info">等级要求：${guild.minLevel}级</div>
                        <div class="guild-info join-type-${guild.joinType}">${joinTypeMap[guild.joinType]}</div>
                        <div class="guild-info">${guild.description || '暂无简介'}</div>
                        <div class="guild-actions">
                            ${canJoin ? `<button class="btn btn-primary" onclick="openApplyGuildModal(${guild.id}); event.stopPropagation();">申请加入</button>` : ''}
                        </div>
                    </div>
                `;
            });
            
            guildListDiv.innerHTML = html;
        }
        
        // 加载我的帮派详情
        function loadMyGuild() {
            if (!currentGuild) {
                showError('未加入任何帮派');
                return;
            }
            
            showLoading();
            
            fetch(`/honghuang-game/api/guild/${currentGuild.guildId}/detail`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        displayMyGuild(data.data);
                    } else {
                        showError(data.message || '加载帮派详情失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示我的帮派详情
        function displayMyGuild(guild) {
            const myGuildDiv = document.getElementById('my-guild');
            myGuildDiv.style.display = 'block';
            
            const expPercent = guild.level >= 10 ? 100 : (guild.experience / getRequiredExp(guild.level)) * 100;
            
            myGuildDiv.innerHTML = `
                <div class="my-guild-panel">
                    <h3>${guild.name} <span class="guild-level">Lv.${guild.level}</span></h3>
                    <div class="guild-detail">
                        <div class="detail-item">
                            <div class="detail-label">帮主</div>
                            <div class="detail-value">${guild.leaderName}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">成员数量</div>
                            <div class="detail-value">${guild.memberCount}/${guild.maxMembers}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">帮派资金</div>
                            <div class="detail-value">${guild.funds} 铜钱</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">入帮方式</div>
                            <div class="detail-value join-type-${guild.joinType}">${joinTypeMap[guild.joinType]}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">等级要求</div>
                            <div class="detail-value">${guild.minLevel}级</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">帮派经验</div>
                            <div class="detail-value">
                                ${guild.experience}
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${expPercent}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <div class="detail-label">帮派简介</div>
                        <div class="detail-value">${guild.description || '暂无简介'}</div>
                    </div>
                    ${guild.notice ? `
                    <div style="margin-top: 15px;">
                        <div class="detail-label">帮派公告</div>
                        <div class="detail-value">${guild.notice}</div>
                    </div>
                    ` : ''}
                </div>
            `;
        }
        
        // 加载帮派成员
        function loadGuildMembers() {
            if (!currentGuild) {
                showError('未加入任何帮派');
                return;
            }
            
            showLoading();
            
            fetch(`/honghuang-game/api/guild/${currentGuild.guildId}/members`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        guildMembers = data.data;
                        displayGuildMembers();
                    } else {
                        showError(data.message || '加载帮派成员失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示帮派成员
        function displayGuildMembers() {
            const membersDiv = document.getElementById('guild-members');
            membersDiv.style.display = 'block';
            
            if (!guildMembers || guildMembers.length === 0) {
                membersDiv.innerHTML = '<div class="empty-message">暂无成员</div>';
                return;
            }
            
            let html = '<div class="member-list">';
            guildMembers.forEach(member => {
                const isOnline = member.isOnline ? '在线' : '离线';
                const onlineClass = member.isOnline ? 'text-success' : 'text-muted';
                
                html += `
                    <div class="member-item">
                        <div class="member-info">
                            <div class="member-name">${member.roleName} (Lv.${member.roleLevel})</div>
                            <div class="member-position position-${member.position}">${positionMap[member.position]}</div>
                            <div style="font-size: 12px; color: #666;">
                                贡献: ${member.contribution} | <span class="${onlineClass}">${isOnline}</span>
                            </div>
                        </div>
                        <div class="member-actions">
                            ${canManageMember(member) ? getMemberActionButtons(member) : ''}
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            membersDiv.innerHTML = html;
        }
        
        // 检查是否可以管理成员
        function canManageMember(member) {
            if (!currentGuild) return false;
            if (member.roleId === currentGuild.roleId) return false; // 不能管理自己
            return currentGuild.position < member.position; // 职位高于对方才能管理
        }
        
        // 获取成员操作按钮
        function getMemberActionButtons(member) {
            let buttons = '';
            if (currentGuild.position === 1) { // 帮主
                if (member.position > 2) {
                    buttons += `<button class="btn btn-warning" onclick="promoteMember(${member.roleId})">提升</button>`;
                }
                if (member.position < 6) {
                    buttons += `<button class="btn btn-warning" onclick="demoteMember(${member.roleId})">降职</button>`;
                }
                buttons += `<button class="btn btn-danger" onclick="kickMember(${member.roleId})">踢出</button>`;
            } else if (currentGuild.position === 2) { // 副帮主
                if (member.position > 3) {
                    buttons += `<button class="btn btn-warning" onclick="promoteMember(${member.roleId})">提升</button>`;
                }
                if (member.position > 2 && member.position < 6) {
                    buttons += `<button class="btn btn-warning" onclick="demoteMember(${member.roleId})">降职</button>`;
                }
                if (member.position > 2) {
                    buttons += `<button class="btn btn-danger" onclick="kickMember(${member.roleId})">踢出</button>`;
                }
            }
            return buttons;
        }
        
        // 计算升级所需经验
        function getRequiredExp(level) {
            return level * 1000; // 简单的经验计算公式
        }
        
        // 打开创建帮派模态框
        function openCreateGuildModal() {
            document.getElementById('create-guild-modal').classList.add('show');
            document.getElementById('guild-name-input').focus();
        }
        
        // 关闭创建帮派模态框
        function closeCreateGuildModal() {
            document.getElementById('create-guild-modal').classList.remove('show');
            document.getElementById('guild-name-input').value = '';
            document.getElementById('guild-description-input').value = '';
        }
        
        // 创建帮派
        function createGuild() {
            const guildName = document.getElementById('guild-name-input').value.trim();
            const description = document.getElementById('guild-description-input').value.trim();
            
            if (!guildName) {
                showError('请输入帮派名称');
                return;
            }
            
            if (guildName.length < 2 || guildName.length > 20) {
                showError('帮派名称长度应在2-20字符之间');
                return;
            }
            
            fetch('/honghuang-game/api/guild/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    guildName: guildName,
                    description: description
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('帮派创建成功！');
                    closeCreateGuildModal();
                    checkGuildStatus(); // 重新检查帮派状态
                } else {
                    showError(data.message || '创建帮派失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 打开申请加入帮派模态框
        function openApplyGuildModal(guildId) {
            selectedGuildForApply = guildList.find(g => g.id === guildId);
            if (!selectedGuildForApply) return;
            
            document.getElementById('apply-guild-info').innerHTML = `
                <p><strong>帮派名称：</strong>${selectedGuildForApply.name}</p>
                <p><strong>帮主：</strong>${selectedGuildForApply.leaderName}</p>
                <p><strong>成员：</strong>${selectedGuildForApply.memberCount}/${selectedGuildForApply.maxMembers}</p>
                <p><strong>入帮方式：</strong><span class="join-type-${selectedGuildForApply.joinType}">${joinTypeMap[selectedGuildForApply.joinType]}</span></p>
            `;
            
            document.getElementById('apply-guild-modal').classList.add('show');
            document.getElementById('apply-message-input').focus();
        }
        
        // 关闭申请加入帮派模态框
        function closeApplyGuildModal() {
            document.getElementById('apply-guild-modal').classList.remove('show');
            document.getElementById('apply-message-input').value = '';
            selectedGuildForApply = null;
        }
        
        // 提交申请
        function submitApplication() {
            if (!selectedGuildForApply) return;
            
            const message = document.getElementById('apply-message-input').value.trim();
            
            fetch(`/honghuang-game/api/guild/${selectedGuildForApply.id}/apply`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('申请提交成功，请等待审核');
                    closeApplyGuildModal();
                } else {
                    showError(data.message || '申请提交失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 退出帮派
        function leaveGuild() {
            if (!confirm('确定要退出帮派吗？')) {
                return;
            }
            
            fetch('/honghuang-game/api/guild/leave', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('已退出帮派');
                    checkGuildStatus(); // 重新检查帮派状态
                } else {
                    showError(data.message || '退出帮派失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 加载帮派申请
        function loadGuildApplications() {
            if (!currentGuild) {
                showError('未加入任何帮派');
                return;
            }

            showLoading();

            fetch(`/honghuang-game/api/guild/${currentGuild.guildId}/applications`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        guildApplications = data.data;
                        displayGuildApplications();
                    } else {
                        showError(data.message || '加载申请列表失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }

        // 显示帮派申请
        function displayGuildApplications() {
            const applicationsDiv = document.getElementById('guild-applications');
            applicationsDiv.style.display = 'block';

            if (!guildApplications || guildApplications.length === 0) {
                applicationsDiv.innerHTML = '<div class="empty-message">暂无申请</div>';
                return;
            }

            let html = '<div class="member-list">';
            guildApplications.forEach(application => {
                const timeAgo = getTimeAgo(application.createTime);

                html += `
                    <div class="member-item">
                        <div class="member-info">
                            <div class="member-name">${application.applicantName} (Lv.${application.applicantLevel})</div>
                            <div style="font-size: 12px; color: #666;">
                                申请时间: ${timeAgo}
                            </div>
                            ${application.message ? `<div style="font-size: 12px; color: #333; margin-top: 5px;">留言: ${application.message}</div>` : ''}
                        </div>
                        <div class="member-actions">
                            <button class="btn btn-success" onclick="approveApplication(${application.id})">同意</button>
                            <button class="btn btn-danger" onclick="rejectApplication(${application.id})">拒绝</button>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            applicationsDiv.innerHTML = html;
        }

        // 同意申请
        function approveApplication(applicationId) {
            fetch(`/honghuang-game/api/guild/application/${applicationId}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('申请已同意');
                    loadGuildApplications();
                    loadGuildMembers(); // 刷新成员列表
                } else {
                    showError(data.message || '操作失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }

        // 拒绝申请
        function rejectApplication(applicationId) {
            fetch(`/honghuang-game/api/guild/application/${applicationId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('申请已拒绝');
                    loadGuildApplications();
                } else {
                    showError(data.message || '操作失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }

        // 踢出成员
        function kickMember(roleId) {
            if (!confirm('确定要踢出该成员吗？')) {
                return;
            }

            fetch(`/honghuang-game/api/guild/member/${roleId}/kick`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('成员已被踢出');
                    loadGuildMembers();
                } else {
                    showError(data.message || '操作失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }

        // 提升成员
        function promoteMember(roleId) {
            if (!confirm('确定要提升该成员职位吗？')) {
                return;
            }

            fetch(`/honghuang-game/api/guild/member/${roleId}/promote`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('成员职位已提升');
                    loadGuildMembers();
                } else {
                    showError(data.message || '操作失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }

        // 降职成员
        function demoteMember(roleId) {
            if (!confirm('确定要降低该成员职位吗？')) {
                return;
            }

            fetch(`/honghuang-game/api/guild/member/${roleId}/demote`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('成员职位已降低');
                    loadGuildMembers();
                } else {
                    showError(data.message || '操作失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }

        // 获取时间差描述
        function getTimeAgo(timeString) {
            const time = new Date(timeString);
            const now = new Date();
            const diffMs = now - time;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return '刚刚';
            if (diffMins < 60) return `${diffMins}分钟前`;
            if (diffHours < 24) return `${diffHours}小时前`;
            if (diffDays < 7) return `${diffDays}天前`;
            return time.toLocaleDateString();
        }

        // 刷新帮派信息
        function refreshGuild() {
            checkGuildStatus();
            if (currentTab === 'list') {
                loadGuildList();
            } else if (currentTab === 'my') {
                loadMyGuild();
            } else if (currentTab === 'members') {
                loadGuildMembers();
            } else if (currentTab === 'applications') {
                loadGuildApplications();
            }
        }
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 显示错误消息
        function showError(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="error">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="success">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
