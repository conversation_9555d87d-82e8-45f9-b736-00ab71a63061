package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * NPC对话记录实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "npc_dialogue")
public class NpcDialogue {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * NPC ID
     */
    @Column(name = "npc_id")
    private Integer npcId;

    /**
     * 角色ID
     */
    @Column(name = "role_id")
    private Integer roleId;

    /**
     * 对话类型 1普通对话 2任务对话 3商店对话 4功能对话
     */
    @Column(name = "dialogue_type")
    private Integer dialogueType;

    /**
     * 对话内容
     */
    @Column(name = "content", length = 1000)
    private String content;

    /**
     * 选项内容（JSON格式）
     */
    @Column(name = "options", length = 2000)
    private String options;

    /**
     * 玩家选择的选项
     */
    @Column(name = "selected_option")
    private Integer selectedOption;

    /**
     * 对话结果
     */
    @Column(name = "result", length = 500)
    private String result;

    /**
     * 对话时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "dialogue_time")
    private LocalDateTime dialogueTime;

    /**
     * NPC信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "npc_id", insertable = false, updatable = false)
    private Npc npc;

    /**
     * 角色信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", insertable = false, updatable = false)
    private PlayerRole role;

    // 业务方法

    /**
     * 获取对话类型名称
     */
    public String getDialogueTypeName() {
        switch (dialogueType) {
            case 1: return "普通对话";
            case 2: return "任务对话";
            case 3: return "商店对话";
            case 4: return "功能对话";
            default: return "未知";
        }
    }

    /**
     * 是否为普通对话
     */
    public boolean isNormalDialogue() {
        return dialogueType == 1;
    }

    /**
     * 是否为任务对话
     */
    public boolean isQuestDialogue() {
        return dialogueType == 2;
    }

    /**
     * 是否为商店对话
     */
    public boolean isShopDialogue() {
        return dialogueType == 3;
    }

    /**
     * 是否为功能对话
     */
    public boolean isFunctionDialogue() {
        return dialogueType == 4;
    }

    /**
     * 是否有选项
     */
    public boolean hasOptions() {
        return options != null && !options.trim().isEmpty();
    }

    /**
     * 是否已选择选项
     */
    public boolean hasSelectedOption() {
        return selectedOption != null;
    }

    /**
     * 获取对话经过时间（分钟）
     */
    public Long getElapsedMinutes() {
        if (dialogueTime == null) {
            return null;
        }
        return java.time.Duration.between(dialogueTime, LocalDateTime.now()).toMinutes();
    }

    /**
     * 获取格式化的对话时间
     */
    public String getFormattedDialogueTime() {
        if (dialogueTime == null) return "";
        
        LocalDateTime now = LocalDateTime.now();
        long minutes = java.time.Duration.between(dialogueTime, now).toMinutes();
        
        if (minutes < 1) {
            return "刚刚";
        } else if (minutes < 60) {
            return minutes + "分钟前";
        } else if (minutes < 1440) { // 24小时
            return (minutes / 60) + "小时前";
        } else {
            return dialogueTime.toLocalDate().toString();
        }
    }

    /**
     * 获取对话摘要
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        
        if (npc != null) {
            sb.append("[").append(npc.getName()).append("] ");
        }
        
        sb.append(getDialogueTypeName()).append(": ");
        
        if (content != null && content.length() > 50) {
            sb.append(content.substring(0, 50)).append("...");
        } else {
            sb.append(content);
        }
        
        return sb.toString();
    }

    /**
     * 设置对话选项（简单实现）
     */
    public void setDialogueOptions(String... optionTexts) {
        if (optionTexts == null || optionTexts.length == 0) {
            this.options = null;
            return;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < optionTexts.length; i++) {
            if (i > 0) sb.append(",");
            sb.append("\"").append(optionTexts[i]).append("\"");
        }
        sb.append("]");
        
        this.options = sb.toString();
    }

    /**
     * 获取对话选项数组（简单实现）
     */
    public String[] getDialogueOptions() {
        if (options == null || options.trim().isEmpty()) {
            return new String[0];
        }
        
        // 简单的JSON解析，实际项目中应该使用专业的JSON库
        String cleanOptions = options.replace("[", "").replace("]", "").replace("\"", "");
        if (cleanOptions.trim().isEmpty()) {
            return new String[0];
        }
        
        return cleanOptions.split(",");
    }

    /**
     * 选择对话选项
     */
    public void selectOption(Integer optionIndex, String result) {
        this.selectedOption = optionIndex;
        this.result = result;
    }

    /**
     * 检查是否为最近的对话
     */
    public boolean isRecentDialogue() {
        return getElapsedMinutes() != null && getElapsedMinutes() <= 30; // 30分钟内算最近
    }
}
