package com.honghuang.game.repository;

import com.honghuang.game.entity.Pet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 宠物数据访问接口
 */
@Repository
public interface PetRepository extends JpaRepository<Pet, Integer> {
    
    /**
     * 根据主人ID查找宠物
     */
    List<Pet> findByOwnerIdOrderByCaptureTimeDesc(Integer ownerId);
    
    /**
     * 根据主人ID和宠物名称查找
     */
    Optional<Pet> findByOwnerIdAndNickname(Integer ownerId, String nickname);
    
    /**
     * 查找主人的出战宠物
     */
    @Query("SELECT p FROM Pet p WHERE p.ownerId = :ownerId AND p.isActive = 1")
    Optional<Pet> findActivePetByOwnerId(@Param("ownerId") Integer ownerId);
    
    /**
     * 查找主人的跟随宠物
     */
    @Query("SELECT p FROM Pet p WHERE p.ownerId = :ownerId AND p.isFollow = 1")
    List<Pet> findFollowingPetsByOwnerId(@Param("ownerId") Integer ownerId);
    
    /**
     * 根据宠物类型查找
     */
    List<Pet> findByType(Integer type);
    
    /**
     * 根据等级范围查找宠物
     */
    @Query("SELECT p FROM Pet p WHERE p.level BETWEEN :minLevel AND :maxLevel")
    List<Pet> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);
    
    /**
     * 根据稀有度查找宠物
     */
    List<Pet> findByRarity(Integer rarity);
    
    /**
     * 查找需要休息的宠物
     */
    @Query("SELECT p FROM Pet p WHERE p.ownerId = :ownerId AND p.fatigue > 80")
    List<Pet> findTiredPetsByOwnerId(@Param("ownerId") Integer ownerId);
    
    /**
     * 查找饥饿的宠物
     */
    @Query("SELECT p FROM Pet p WHERE p.ownerId = :ownerId AND p.hunger > 80")
    List<Pet> findHungryPetsByOwnerId(@Param("ownerId") Integer ownerId);
    
    /**
     * 查找可以升级的宠物
     */
    @Query("SELECT p FROM Pet p WHERE p.ownerId = :ownerId AND p.exp >= p.maxExp")
    List<Pet> findLevelUpReadyPetsByOwnerId(@Param("ownerId") Integer ownerId);
    
    /**
     * 统计主人的宠物数量
     */
    @Query("SELECT COUNT(p) FROM Pet p WHERE p.ownerId = :ownerId")
    Long countByOwnerId(@Param("ownerId") Integer ownerId);
    
    /**
     * 查找高忠诚度宠物
     */
    @Query("SELECT p FROM Pet p WHERE p.ownerId = :ownerId AND p.loyalty >= :minLoyalty")
    List<Pet> findHighLoyaltyPets(@Param("ownerId") Integer ownerId, @Param("minLoyalty") Integer minLoyalty);
    
    /**
     * 根据成长率排序查找宠物
     */
    @Query("SELECT p FROM Pet p WHERE p.ownerId = :ownerId ORDER BY p.growthRate DESC")
    List<Pet> findByOwnerIdOrderByGrowthRateDesc(@Param("ownerId") Integer ownerId);
}
