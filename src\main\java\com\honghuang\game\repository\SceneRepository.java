package com.honghuang.game.repository;

import com.honghuang.game.entity.Scene;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 场景数据访问层
 */
@Repository
public interface SceneRepository extends JpaRepository<Scene, Integer> {

    /**
     * 查找激活的场景
     */
    List<Scene> findByActiveOrderByTypeAscRequiredLevelAsc(Integer active);

    /**
     * 根据类型查找场景
     */
    List<Scene> findByTypeAndActiveOrderByRequiredLevelAsc(Integer type, Integer active);

    /**
     * 根据等级要求查找场景
     */
    List<Scene> findByRequiredLevelAndActiveOrderByTypeAsc(Integer requiredLevel, Integer active);

    /**
     * 查找适合角色等级的场景
     */
    @Query("SELECT s FROM Scene s WHERE s.requiredLevel <= :level AND s.active = 1 ORDER BY s.requiredLevel DESC")
    List<Scene> findSuitableScenes(@Param("level") Integer level);

    /**
     * 根据名称模糊查找场景
     */
    @Query("SELECT s FROM Scene s WHERE s.name LIKE %:name% AND s.active = 1 ORDER BY s.name ASC")
    List<Scene> findByNameContaining(@Param("name") String name);

    /**
     * 查找新手村
     */
    @Query("SELECT s FROM Scene s WHERE s.type = 1 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Scene> findNewbieVillages();

    /**
     * 查找城镇
     */
    @Query("SELECT s FROM Scene s WHERE s.type = 2 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Scene> findTowns();

    /**
     * 查找野外场景
     */
    @Query("SELECT s FROM Scene s WHERE s.type = 3 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Scene> findWilderness();

    /**
     * 查找副本
     */
    @Query("SELECT s FROM Scene s WHERE s.type = 4 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Scene> findDungeons();

    /**
     * 查找安全区域
     */
    @Query("SELECT s FROM Scene s WHERE s.safeZone = 1 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Scene> findSafeZones();

    /**
     * 查找PK区域
     */
    @Query("SELECT s FROM Scene s WHERE s.allowPk = 1 AND s.safeZone = 0 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Scene> findPkZones();

    /**
     * 查找允许传送的场景
     */
    @Query("SELECT s FROM Scene s WHERE s.allowTeleport = 1 AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Scene> findTeleportableScenes();

    /**
     * 查找未满员的场景
     */
    @Query("SELECT s FROM Scene s WHERE s.currentPlayers < s.maxPlayers AND s.active = 1 ORDER BY s.currentPlayers ASC")
    List<Scene> findAvailableScenes();

    /**
     * 查找拥挤的场景
     */
    @Query("SELECT s FROM Scene s WHERE s.currentPlayers >= s.maxPlayers * 0.8 AND s.active = 1 ORDER BY s.currentPlayers DESC")
    List<Scene> findCrowdedScenes();

    /**
     * 根据等级范围查找场景
     */
    @Query("SELECT s FROM Scene s WHERE s.requiredLevel BETWEEN :minLevel AND :maxLevel AND s.active = 1 ORDER BY s.requiredLevel ASC")
    List<Scene> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 更新场景在线人数
     */
    @Modifying
    @Query("UPDATE Scene s SET s.currentPlayers = :playerCount WHERE s.id = :sceneId")
    int updatePlayerCount(@Param("sceneId") Integer sceneId, @Param("playerCount") Integer playerCount);

    /**
     * 增加场景在线人数
     */
    @Modifying
    @Query("UPDATE Scene s SET s.currentPlayers = s.currentPlayers + 1 WHERE s.id = :sceneId")
    int incrementPlayerCount(@Param("sceneId") Integer sceneId);

    /**
     * 减少场景在线人数
     */
    @Modifying
    @Query("UPDATE Scene s SET s.currentPlayers = s.currentPlayers - 1 WHERE s.id = :sceneId AND s.currentPlayers > 0")
    int decrementPlayerCount(@Param("sceneId") Integer sceneId);

    /**
     * 重置所有场景在线人数
     */
    @Modifying
    @Query("UPDATE Scene s SET s.currentPlayers = 0")
    int resetAllPlayerCounts();

    /**
     * 统计激活场景数量
     */
    @Query("SELECT COUNT(s) FROM Scene s WHERE s.active = 1")
    long countActiveScenes();

    /**
     * 统计指定类型场景数量
     */
    @Query("SELECT COUNT(s) FROM Scene s WHERE s.type = :type AND s.active = 1")
    long countByType(@Param("type") Integer type);

    /**
     * 统计总在线人数
     */
    @Query("SELECT SUM(s.currentPlayers) FROM Scene s WHERE s.active = 1")
    Long getTotalOnlinePlayers();

    /**
     * 查找热门场景
     */
    @Query("SELECT s FROM Scene s WHERE s.active = 1 ORDER BY s.currentPlayers DESC")
    List<Scene> findPopularScenes();

    /**
     * 查找冷门场景
     */
    @Query("SELECT s FROM Scene s WHERE s.active = 1 ORDER BY s.currentPlayers ASC")
    List<Scene> findUnpopularScenes();
}
