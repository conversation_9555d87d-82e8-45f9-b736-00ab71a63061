package com.honghuang.game.dto;

import java.util.List;
import java.util.Map;

/**
 * 战斗结果DTO
 */
public class BattleResultDTO {
    
    private boolean victory; // 是否胜利
    private int rounds; // 战斗回合数
    private int expGained; // 获得经验
    private int copperGained; // 获得铜钱
    private List<Map<String, Object>> itemsDropped; // 掉落物品
    private List<String> battleLog; // 战斗日志
    private int playerHpAfter; // 战斗后玩家血量
    private int playerMpAfter; // 战斗后玩家法力
    private boolean levelUp; // 是否升级
    private int newLevel; // 新等级
    private String monsterName; // 怪物名称
    private int monsterLevel; // 怪物等级
    
    // 构造函数
    public BattleResultDTO() {}
    
    // Getter和Setter方法
    public boolean isVictory() {
        return victory;
    }
    
    public void setVictory(boolean victory) {
        this.victory = victory;
    }
    
    public int getRounds() {
        return rounds;
    }
    
    public void setRounds(int rounds) {
        this.rounds = rounds;
    }
    
    public int getExpGained() {
        return expGained;
    }
    
    public void setExpGained(int expGained) {
        this.expGained = expGained;
    }
    
    public int getCopperGained() {
        return copperGained;
    }
    
    public void setCopperGained(int copperGained) {
        this.copperGained = copperGained;
    }
    
    public List<Map<String, Object>> getItemsDropped() {
        return itemsDropped;
    }
    
    public void setItemsDropped(List<Map<String, Object>> itemsDropped) {
        this.itemsDropped = itemsDropped;
    }
    
    public List<String> getBattleLog() {
        return battleLog;
    }
    
    public void setBattleLog(List<String> battleLog) {
        this.battleLog = battleLog;
    }
    
    public int getPlayerHpAfter() {
        return playerHpAfter;
    }
    
    public void setPlayerHpAfter(int playerHpAfter) {
        this.playerHpAfter = playerHpAfter;
    }
    
    public int getPlayerMpAfter() {
        return playerMpAfter;
    }
    
    public void setPlayerMpAfter(int playerMpAfter) {
        this.playerMpAfter = playerMpAfter;
    }
    
    public boolean isLevelUp() {
        return levelUp;
    }
    
    public void setLevelUp(boolean levelUp) {
        this.levelUp = levelUp;
    }
    
    public int getNewLevel() {
        return newLevel;
    }
    
    public void setNewLevel(int newLevel) {
        this.newLevel = newLevel;
    }
    
    public String getMonsterName() {
        return monsterName;
    }
    
    public void setMonsterName(String monsterName) {
        this.monsterName = monsterName;
    }
    
    public int getMonsterLevel() {
        return monsterLevel;
    }
    
    public void setMonsterLevel(int monsterLevel) {
        this.monsterLevel = monsterLevel;
    }
}
