package com.honghuang.game.repository;

import com.honghuang.game.entity.ChatMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天消息数据访问接口
 */
@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {
    
    /**
     * 根据频道类型查找最近消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.channelType = :channelType ORDER BY cm.sendTime DESC")
    List<ChatMessage> findRecentMessagesByChannel(@Param("channelType") Integer channelType);
    
    /**
     * 查找世界频道消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.channelType = 1 ORDER BY cm.sendTime DESC")
    List<ChatMessage> findWorldMessages();
    
    /**
     * 查找私聊消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.channelType = 2 AND " +
           "((cm.senderId = :playerId AND cm.receiverId = :friendId) OR " +
           "(cm.senderId = :friendId AND cm.receiverId = :playerId)) " +
           "ORDER BY cm.sendTime DESC")
    List<ChatMessage> findPrivateMessages(@Param("playerId") Integer playerId, @Param("friendId") Integer friendId);
    
    /**
     * 查找帮派频道消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.channelType = 3 AND cm.factionId = :factionId ORDER BY cm.sendTime DESC")
    List<ChatMessage> findFactionMessages(@Param("factionId") Integer factionId);
    
    /**
     * 查找系统消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.channelType = 4 OR cm.isSystem = 1 ORDER BY cm.sendTime DESC")
    List<ChatMessage> findSystemMessages();
    
    /**
     * 根据发送者查找消息
     */
    List<ChatMessage> findBySenderIdOrderBySendTimeDesc(Integer senderId);

    /**
     * 根据频道类型统计消息数量
     */
    @Query("SELECT COUNT(cm) FROM ChatMessage cm WHERE cm.channelType = :channelType")
    long countByChannelType(@Param("channelType") Integer channelType);

    /**
     * 删除过期消息
     */
    @Modifying
    @Query("DELETE FROM ChatMessage cm WHERE cm.sendTime < :cutoffTime")
    int deleteExpiredMessages(@Param("cutoffTime") java.time.LocalDateTime cutoffTime);
    
    /**
     * 根据时间范围查找消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.sendTime BETWEEN :startTime AND :endTime ORDER BY cm.sendTime DESC")
    List<ChatMessage> findMessagesByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找玩家的未读私聊消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.channelType = 2 AND cm.receiverId = :playerId AND cm.sendTime > :lastReadTime ORDER BY cm.sendTime DESC")
    List<ChatMessage> findUnreadPrivateMessages(@Param("playerId") Integer playerId, @Param("lastReadTime") LocalDateTime lastReadTime);
    

    
    /**
     * 查找包含关键词的消息
     */
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.content LIKE %:keyword% ORDER BY cm.sendTime DESC")
    List<ChatMessage> findMessagesByKeyword(@Param("keyword") String keyword);
}
