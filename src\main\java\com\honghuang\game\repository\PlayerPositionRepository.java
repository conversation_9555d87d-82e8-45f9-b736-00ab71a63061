package com.honghuang.game.repository;

import com.honghuang.game.entity.PlayerPosition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 玩家位置数据访问层
 */
@Repository
public interface PlayerPositionRepository extends JpaRepository<PlayerPosition, Integer> {

    /**
     * 根据角色ID查找位置
     */
    Optional<PlayerPosition> findByRoleId(Integer roleId);

    /**
     * 根据场景ID查找在线玩家
     */
    List<PlayerPosition> findBySceneIdAndOnlineOrderByUpdateTimeDesc(Integer sceneId, Integer online);

    /**
     * 查找所有在线玩家
     */
    List<PlayerPosition> findByOnlineOrderByUpdateTimeDesc(Integer online);

    /**
     * 根据位置范围查找玩家
     */
    @Query("SELECT pp FROM PlayerPosition pp WHERE pp.sceneId = :sceneId AND pp.online = 1 " +
           "AND ABS(pp.positionX - :x) + ABS(pp.positionY - :y) <= :range " +
           "ORDER BY (ABS(pp.positionX - :x) + ABS(pp.positionY - :y)) ASC")
    List<PlayerPosition> findPlayersInRange(@Param("sceneId") Integer sceneId, @Param("x") Integer x, 
                                           @Param("y") Integer y, @Param("range") Integer range);

    /**
     * 查找最近的玩家
     */
    @Query("SELECT pp FROM PlayerPosition pp WHERE pp.sceneId = :sceneId AND pp.online = 1 AND pp.roleId != :excludeRoleId " +
           "ORDER BY (ABS(pp.positionX - :x) + ABS(pp.positionY - :y)) ASC")
    List<PlayerPosition> findNearestPlayers(@Param("sceneId") Integer sceneId, @Param("x") Integer x, @Param("y") Integer y, @Param("excludeRoleId") Integer excludeRoleId);

    /**
     * 更新玩家位置
     */
    @Modifying
    @Query("UPDATE PlayerPosition pp SET pp.positionX = :x, pp.positionY = :y, pp.direction = :direction WHERE pp.roleId = :roleId")
    int updatePosition(@Param("roleId") Integer roleId, @Param("x") Integer x, @Param("y") Integer y, @Param("direction") Integer direction);

    /**
     * 更新玩家场景
     */
    @Modifying
    @Query("UPDATE PlayerPosition pp SET pp.lastSceneId = pp.sceneId, pp.lastPositionX = pp.positionX, pp.lastPositionY = pp.positionY, " +
           "pp.sceneId = :sceneId, pp.positionX = :x, pp.positionY = :y WHERE pp.roleId = :roleId")
    int updateScene(@Param("roleId") Integer roleId, @Param("sceneId") Integer sceneId, @Param("x") Integer x, @Param("y") Integer y);

    /**
     * 更新在线状态
     */
    @Modifying
    @Query("UPDATE PlayerPosition pp SET pp.online = :online WHERE pp.roleId = :roleId")
    int updateOnlineStatus(@Param("roleId") Integer roleId, @Param("online") Integer online);

    /**
     * 批量设置离线状态
     */
    @Modifying
    @Query("UPDATE PlayerPosition pp SET pp.online = 0")
    int setAllOffline();

    /**
     * 统计场景在线人数
     */
    @Query("SELECT COUNT(pp) FROM PlayerPosition pp WHERE pp.sceneId = :sceneId AND pp.online = 1")
    long countOnlinePlayersInScene(@Param("sceneId") Integer sceneId);

    /**
     * 统计总在线人数
     */
    @Query("SELECT COUNT(pp) FROM PlayerPosition pp WHERE pp.online = 1")
    long countTotalOnlinePlayers();

    /**
     * 查找指定朝向的玩家
     */
    @Query("SELECT pp FROM PlayerPosition pp WHERE pp.sceneId = :sceneId AND pp.direction = :direction AND pp.online = 1")
    List<PlayerPosition> findPlayersByDirection(@Param("sceneId") Integer sceneId, @Param("direction") Integer direction);

    /**
     * 查找长时间未更新位置的玩家
     */
    @Query("SELECT pp FROM PlayerPosition pp WHERE pp.online = 1 AND pp.updateTime <= :cutoffTime")
    List<PlayerPosition> findInactivePlayers(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除角色位置记录
     */
    @Modifying
    @Query("DELETE FROM PlayerPosition pp WHERE pp.roleId = :roleId")
    int deleteByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找场景中的所有玩家（包括离线）
     */
    List<PlayerPosition> findBySceneIdOrderByOnlineDescUpdateTimeDesc(Integer sceneId);

    /**
     * 查找有上次位置记录的玩家
     */
    @Query("SELECT pp FROM PlayerPosition pp WHERE pp.lastSceneId IS NOT NULL AND pp.online = 1")
    List<PlayerPosition> findPlayersWithLastPosition();

    /**
     * 查找在指定区域的玩家
     */
    @Query("SELECT pp FROM PlayerPosition pp WHERE pp.sceneId = :sceneId AND pp.online = 1 " +
           "AND pp.positionX BETWEEN :minX AND :maxX AND pp.positionY BETWEEN :minY AND :maxY")
    List<PlayerPosition> findPlayersInArea(@Param("sceneId") Integer sceneId, @Param("minX") Integer minX, @Param("maxX") Integer maxX, 
                                          @Param("minY") Integer minY, @Param("maxY") Integer maxY);

    /**
     * 查找场景中心区域的玩家
     */
    @Query("SELECT pp FROM PlayerPosition pp JOIN pp.scene s WHERE pp.sceneId = :sceneId AND pp.online = 1 " +
           "AND ABS(pp.positionX - s.mapWidth/2) <= :range AND ABS(pp.positionY - s.mapHeight/2) <= :range")
    List<PlayerPosition> findPlayersInSceneCenter(@Param("sceneId") Integer sceneId, @Param("range") Integer range);
}
