<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洪荒Online - 游戏主界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: #ecf0f1;
            overflow-x: hidden;
        }

        .game-header {
            background: linear-gradient(90deg, #1a252f 0%, #2c3e50 50%, #1a252f 100%);
            color: #ecf0f1;
            padding: 10px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
            border-bottom: 2px solid #3498db;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .game-title {
            font-size: 1.8em;
            font-weight: bold;
            color: #3498db;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .username {
            font-weight: bold;
            color: #3498db;
        }

        .user-level {
            font-size: 0.9em;
            color: #bdc3c7;
        }

        .game-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 250px 1fr 300px;
            gap: 20px;
            min-height: calc(100vh - 80px);
        }

        /* 角色信息面板 */
        .character-panel {
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #3498db;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .character-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            border: 3px solid #3498db;
        }

        .character-name {
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 15px;
        }

        .character-stats {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-label {
            color: #bdc3c7;
            font-size: 0.9em;
        }

        .stat-value {
            color: #3498db;
            font-weight: bold;
        }

        .hp-bar, .mp-bar {
            width: 100%;
            height: 8px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 3px;
        }

        .hp-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c, #c0392b);
            transition: width 0.3s ease;
        }

        .mp-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            transition: width 0.3s ease;
        }

        /* 主游戏区域 */
        .main-game-area {
            display: grid;
            grid-template-rows: auto 1fr auto;
            gap: 15px;
        }

        .scene-info {
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #27ae60;
            text-align: center;
        }

        .scene-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 5px;
        }

        .scene-description {
            color: #bdc3c7;
            font-size: 0.9em;
        }

        .game-content {
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #f39c12;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .action-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: linear-gradient(45deg, #2980b9, #3498db);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
        }

        .action-btn.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .action-btn.danger:hover {
            background: linear-gradient(45deg, #c0392b, #e74c3c);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
        }

        .action-btn.success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }

        .action-btn.success:hover {
            background: linear-gradient(45deg, #229954, #27ae60);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
        }

        /* 侧边栏 */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .sidebar-panel {
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #9b59b6;
        }

        .panel-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #9b59b6;
            margin-bottom: 10px;
            text-align: center;
            border-bottom: 1px solid rgba(155, 89, 182, 0.3);
            padding-bottom: 8px;
        }

        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
            margin-bottom: 10px;
        }

        .inventory-slot {
            width: 50px;
            height: 50px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid #7f8c8d;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .inventory-slot:hover {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.2);
        }

        .inventory-slot.has-item {
            border-color: #f39c12;
            background: rgba(243, 156, 18, 0.2);
        }

        .item-icon {
            width: 30px;
            height: 30px;
            background: #3498db;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8em;
            font-weight: bold;
        }

        .item-quantity {
            position: absolute;
            bottom: 2px;
            right: 2px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            font-size: 0.7em;
            padding: 1px 3px;
            border-radius: 2px;
        }

        /* 聊天面板 */
        .chat-messages {
            height: 150px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #7f8c8d;
        }

        .chat-message {
            margin-bottom: 5px;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .chat-input {
            display: flex;
            gap: 5px;
        }

        .chat-input input {
            flex: 1;
            padding: 8px;
            border: 1px solid #7f8c8d;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.3);
            color: #ecf0f1;
        }

        .chat-input button {
            padding: 8px 15px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .game-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        /* 装备管理样式 */
        .equipment-slot {
            background: rgba(0, 0, 0, 0.5);
            border: 2px solid #34495e;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            min-height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            transition: all 0.3s;
        }

        .equipment-slot:hover {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.1);
        }

        .slot-label {
            font-size: 0.8em;
            color: #bdc3c7;
            margin-bottom: 5px;
        }

        .slot-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }

        .equipment-item {
            background: rgba(52, 73, 94, 0.9);
            border: 2px solid #34495e;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .equipment-item:hover {
            border-color: #f39c12;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
        }

        .equipment-item.equipped {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.2);
        }

        .equipment-name {
            font-size: 0.9em;
            color: #ecf0f1;
            margin-bottom: 3px;
        }

        .equipment-type {
            font-size: 0.7em;
            color: #bdc3c7;
        }

        .equipment-stats {
            font-size: 0.7em;
            color: #f39c12;
            margin-top: 3px;
        }
    </style>
</head>
<body>
    <!-- 游戏头部 -->
    <div class="game-header">
        <div class="header-content">
            <div class="game-title">洪荒Online</div>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">?</div>
                <div class="user-details">
                    <div class="username" id="username">未登录</div>
                    <div class="user-level" id="userLevel">等级: --</div>
                </div>
                <button onclick="logout()" style="margin-left: 15px; padding: 8px 15px; background: #e74c3c; color: white; border: none; border-radius: 5px; cursor: pointer;">登出</button>
            </div>
        </div>
    </div>

    <!-- 游戏主容器 -->
    <div class="game-container">
        <!-- 角色信息面板 -->
        <div class="character-panel">
            <div class="character-avatar" id="characterAvatar">⚔</div>
            <div class="character-name" id="characterName">选择角色</div>

            <div class="character-stats">
                <div class="stat-item">
                    <span class="stat-label">等级</span>
                    <span class="stat-value" id="characterLevel">1</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">生命值</span>
                    <span class="stat-value" id="characterHp">100/100</span>
                </div>
                <div class="hp-bar">
                    <div class="hp-fill" id="hpFill" style="width: 100%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">法力值</span>
                    <span class="stat-value" id="characterMp">50/50</span>
                </div>
                <div class="mp-bar">
                    <div class="mp-fill" id="mpFill" style="width: 100%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">攻击力</span>
                    <span class="stat-value" id="characterAttack">10</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">防御力</span>
                    <span class="stat-value" id="characterDefense">5</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">铜钱</span>
                    <span class="stat-value" id="characterCopper">1000</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">元宝</span>
                    <span class="stat-value" id="characterYuanbao">0</span>
                </div>
            </div>
        </div>

        <!-- 主游戏区域 -->
        <div class="main-game-area">
            <!-- 场景信息 -->
            <div class="scene-info">
                <div class="scene-name" id="sceneName">新手村</div>
                <div class="scene-description" id="sceneDescription">宁静祥和的小村庄，新冒险者的起点</div>
            </div>

            <!-- 游戏内容 -->
            <div class="game-content">
                <div id="gameMessages" style="height: 200px; overflow-y: auto; background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; margin-bottom: 15px; border: 1px solid #7f8c8d;">
                    <div style="color: #27ae60;">欢迎来到洪荒世界！</div>
                    <div style="color: #bdc3c7;">在这个充满神秘与冒险的世界中，你将踏上修仙之路...</div>
                </div>

                <div class="action-buttons">
                    <button class="action-btn" onclick="exploreArea()">🗺️ 探索</button>
                    <button class="action-btn danger" onclick="startBattle()">⚔️ 战斗</button>
                    <button class="action-btn success" onclick="rest()">🏠 休息</button>
                    <button class="action-btn" onclick="window.location.href='/honghuang-game/shop.html'">🏪 商店</button>
                    <button class="action-btn" onclick="window.location.href='/honghuang-game/quest.html'">📋 任务</button>
                    <button class="action-btn" onclick="moveScene()">🚪 移动</button>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 背包面板 -->
            <div class="sidebar-panel">
                <div class="panel-title">🎒 背包</div>
                <div class="inventory-grid" id="inventoryGrid">
                    <!-- 背包格子将通过JavaScript动态生成 -->
                </div>
                <button class="action-btn" onclick="openInventory()" style="width: 100%; margin-top: 10px;">管理背包</button>
            </div>

            <!-- 聊天面板 -->
            <div class="sidebar-panel">
                <div class="panel-title">💬 聊天</div>
                <div class="chat-messages" id="chatMessages">
                    <div class="chat-message" style="color: #27ae60;">[系统] 欢迎来到洪荒世界！</div>
                    <div class="chat-message" style="color: #3498db;">[世界] 玩家123: 大家好！</div>
                </div>
                <div class="chat-input">
                    <input type="text" id="chatInput" placeholder="输入消息..." onkeypress="handleChatInput(event)">
                    <button onclick="sendMessage()">发送</button>
                </div>
            </div>

            <!-- 快捷操作面板 -->
            <div class="sidebar-panel">
                <div class="panel-title">⚡ 快捷操作</div>
                <div style="display: flex; flex-direction: column; gap: 8px;">
                    <button class="action-btn" onclick="showEquipmentPanel()" style="width: 100%;">🏺 装备系统</button>
                    <button class="action-btn" onclick="window.location.href='/honghuang-game/skill.html'" style="width: 100%;">⚡ 技能系统</button>
                    <button class="action-btn" onclick="window.location.href='/honghuang-game/chat.html'" style="width: 100%;">💬 聊天系统</button>
                    <button class="action-btn" onclick="window.location.href='/honghuang-game/guild.html'" style="width: 100%;">🏰 帮派系统</button>
                    <button class="action-btn" onclick="window.location.href='/honghuang-game/auction.html'" style="width: 100%;">🔨 拍卖系统</button>
                    <button class="action-btn" onclick="window.location.href='/honghuang-game/mail.html'" style="width: 100%;">📧 邮件系统</button>
                    <button class="action-btn" onclick="window.location.href='/honghuang-game/battle.html'" style="width: 100%;">⚔️ 战斗系统</button>
                    <button class="action-btn" onclick="window.location.href='/honghuang-game/pet.html'" style="width: 100%;">🐾 宠物系统</button>
                    <button class="action-btn" onclick="openRanking()" style="width: 100%;">🏆 排行榜</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 装备管理面板 -->
    <div id="equipmentPanel" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header">
                <h2>🏺 装备管理</h2>
                <span class="close" onclick="closeEquipmentPanel()">&times;</span>
            </div>
            <div class="modal-body">
                <div style="display: flex; gap: 20px;">
                    <!-- 当前装备 -->
                    <div style="flex: 1;">
                        <h3 style="color: #f39c12; margin-bottom: 15px;">当前装备</h3>
                        <div id="currentEquipment" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                            <!-- 装备位置 -->
                            <div class="equipment-slot" data-position="2">
                                <div class="slot-label">头盔</div>
                                <div class="slot-item" id="slot-2"></div>
                            </div>
                            <div class="equipment-slot" data-position="6">
                                <div class="slot-label">项链</div>
                                <div class="slot-item" id="slot-6"></div>
                            </div>
                            <div class="equipment-slot" data-position="1">
                                <div class="slot-label">武器</div>
                                <div class="slot-item" id="slot-1"></div>
                            </div>
                            <div class="equipment-slot" data-position="3">
                                <div class="slot-label">衣服</div>
                                <div class="slot-item" id="slot-3"></div>
                            </div>
                            <div class="equipment-slot" data-position="5">
                                <div class="slot-label">腰带</div>
                                <div class="slot-item" id="slot-5"></div>
                            </div>
                            <div class="equipment-slot" data-position="7">
                                <div class="slot-label">戒指</div>
                                <div class="slot-item" id="slot-7"></div>
                            </div>
                            <div class="equipment-slot" data-position="4">
                                <div class="slot-label">鞋子</div>
                                <div class="slot-item" id="slot-4"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 背包装备 -->
                    <div style="flex: 1;">
                        <h3 style="color: #f39c12; margin-bottom: 15px;">背包装备</h3>
                        <div id="bagEquipment" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; max-height: 400px; overflow-y: auto;">
                            <!-- 背包装备将通过JavaScript加载 -->
                        </div>
                    </div>
                </div>

                <!-- 装备详情 -->
                <div id="equipmentDetails" style="margin-top: 20px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; display: none;">
                    <h4 style="color: #f39c12; margin-bottom: 10px;">装备详情</h4>
                    <div id="equipmentInfo"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = '/honghuang-game/api';

        // 全局变量
        let currentUser = null;
        let currentRole = null;
        let gameData = {
            scenes: {},
            items: {},
            inventory: []
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            initializeInventory();
            loadGameData();

            // 尝试恢复游戏状态
            setTimeout(() => {
                if (!restoreGameState()) {
                    addGameMessage('欢迎来到洪荒Online！请选择角色开始游戏。', 'info');
                }
            }, 1000); // 延迟1秒确保其他初始化完成
        });

        // 检查登录状态
        function checkLoginStatus() {
            fetch(`${API_BASE}/user/current`, {
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        currentUser = data.data;
                        updateUserInfo();
                        loadCurrentRole();
                    } else {
                        window.location.href = '/honghuang-game/';
                    }
                })
                .catch(error => {
                    console.error('检查登录状态失败:', error);
                    window.location.href = '/honghuang-game/';
                });
        }

        // 更新用户信息显示
        function updateUserInfo() {
            if (currentUser) {
                document.getElementById('userAvatar').textContent = currentUser.username.charAt(0).toUpperCase();
                document.getElementById('username').textContent = currentUser.username;
            }
        }

        // 加载当前角色
        function loadCurrentRole() {
            fetch(`${API_BASE}/role/current`, {
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200 && data.data) {
                        currentRole = data.data;
                        updateCharacterInfo();
                        loadInventory();
                    } else {
                        // 如果没有角色，显示默认信息
                        currentRole = {
                            name: '未选择角色',
                            level: 1,
                            currentHp: 100,
                            maxHp: 100,
                            currentMp: 50,
                            maxMp: 50,
                            baseAttack: 10,
                            baseDefense: 5,
                            copper: 0,
                            gold: 0,
                            race: 0  // 默认种族，用于显示默认头像
                        };
                        updateCharacterInfo();
                        showMessage('请先选择角色', 'info');
                    }
                })
                .catch(error => {
                    console.error('加载角色失败:', error);
                    // 网络错误时也显示默认信息
                    currentRole = {
                        name: '加载失败',
                        level: 1,
                        currentHp: 100,
                        maxHp: 100,
                        currentMp: 50,
                        maxMp: 50,
                        baseAttack: 10,
                        baseDefense: 5,
                        copper: 0,
                        gold: 0
                    };
                    updateCharacterInfo();
                });
        }

        // 更新角色信息显示
        function updateCharacterInfo() {
            if (currentRole) {
                document.getElementById('characterName').textContent = currentRole.name || '未知角色';
                document.getElementById('characterLevel').textContent = currentRole.level || 1;
                document.getElementById('characterHp').textContent = `${currentRole.currentHp || 100}/${currentRole.maxHp || 100}`;
                document.getElementById('characterMp').textContent = `${currentRole.currentMp || 50}/${currentRole.maxMp || 50}`;
                document.getElementById('characterAttack').textContent = currentRole.totalAttack || currentRole.baseAttack || 10;
                document.getElementById('characterDefense').textContent = currentRole.totalDefense || currentRole.baseDefense || 5;
                document.getElementById('characterCopper').textContent = currentRole.copper || 0;
                document.getElementById('characterYuanbao').textContent = currentRole.gold || 0;

                // 更新血量和法力条
                const currentHp = currentRole.currentHp || 100;
                const maxHp = currentRole.maxHp || 100;
                const currentMp = currentRole.currentMp || 50;
                const maxMp = currentRole.maxMp || 50;

                const hpPercent = maxHp > 0 ? (currentHp / maxHp) * 100 : 100;
                const mpPercent = maxMp > 0 ? (currentMp / maxMp) * 100 : 100;
                document.getElementById('hpFill').style.width = Math.max(0, Math.min(100, hpPercent)) + '%';
                document.getElementById('mpFill').style.width = Math.max(0, Math.min(100, mpPercent)) + '%';

                // 更新用户等级显示
                document.getElementById('userLevel').textContent = `等级: ${currentRole.level || 1}`;

                // 更新角色头像（根据种族显示不同图片）
                // 注意：数据库中的种族值可能来自旧系统，需要正确映射
                const avatarElement = document.getElementById('characterAvatar');
                console.log('当前角色种族:', currentRole.race); // 调试信息
                if (currentRole.race === 1) {
                    // 种族1：根据旧系统，这是妖族
                    avatarElement.innerHTML = '<img src="/honghuang-game/images/role/yao.png" alt="妖族" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;" onerror="console.log(\'妖族头像加载失败\'); this.style.display=\'none\'; this.parentElement.textContent=\'妖\';">';
                } else if (currentRole.race === 2) {
                    // 种族2：根据旧系统，这是巫族
                    avatarElement.innerHTML = '<img src="/honghuang-game/images/role/wu.png" alt="巫族" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;" onerror="console.log(\'巫族头像加载失败\'); this.style.display=\'none\'; this.parentElement.textContent=\'巫\';">';
                } else {
                    // 默认图标
                    console.log('使用默认头像，种族值:', currentRole.race);
                    avatarElement.textContent = '⚔';
                }
            }
        }

        // 初始化背包格子
        function initializeInventory() {
            const inventoryGrid = document.getElementById('inventoryGrid');
            inventoryGrid.innerHTML = '';

            // 创建16个背包格子
            for (let i = 0; i < 16; i++) {
                const slot = document.createElement('div');
                slot.className = 'inventory-slot';
                slot.dataset.slotIndex = i;
                slot.onclick = () => handleInventorySlotClick(i);
                inventoryGrid.appendChild(slot);
            }
        }

        // 加载背包物品
        function loadInventory() {
            if (!currentRole) {
                // 如果没有角色，初始化空背包
                gameData.inventory = [];
                updateInventoryDisplay();
                return;
            }

            console.log('加载背包物品，当前角色:', currentRole.name);
            fetch(`${API_BASE}/inventory/list`, {
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    console.log('背包API响应:', data);
                    if (data.code === 200) {
                        gameData.inventory = data.data || [];
                        console.log('背包物品数量:', gameData.inventory.length);
                        updateInventoryDisplay();
                    } else {
                        console.log('背包API错误:', data.message);
                        gameData.inventory = [];
                        updateInventoryDisplay();
                    }
                })
                .catch(error => {
                    console.error('加载背包失败:', error);
                    gameData.inventory = [];
                    updateInventoryDisplay();
                });
        }

        // 更新背包显示
        function updateInventoryDisplay() {
            const slots = document.querySelectorAll('.inventory-slot');

            // 清空所有格子
            slots.forEach(slot => {
                slot.innerHTML = '';
                slot.classList.remove('has-item');
            });

            // 填充物品
            if (gameData.inventory && Array.isArray(gameData.inventory)) {
                console.log('填充背包物品，数量:', gameData.inventory.length);
                gameData.inventory.forEach(inventoryItem => {
                    console.log('处理背包物品:', inventoryItem);
                    if (inventoryItem && inventoryItem.slotIndex !== undefined) {
                        const slotIndex = inventoryItem.slotIndex;
                        if (slotIndex < slots.length) {
                            const slot = slots[slotIndex];
                            slot.classList.add('has-item');

                            const icon = document.createElement('div');
                            icon.className = 'item-icon';
                            icon.textContent = getItemIcon(inventoryItem.item ? inventoryItem.item.type : 1);
                            icon.title = inventoryItem.item ? inventoryItem.item.name : '未知物品';

                            if (inventoryItem.quantity && inventoryItem.quantity > 1) {
                                const quantity = document.createElement('div');
                                quantity.className = 'item-quantity';
                                quantity.textContent = inventoryItem.quantity;
                                slot.appendChild(quantity);
                            }

                            slot.appendChild(icon);
                        }
                    }
                });
            }
        }

        // 获取物品图标
        function getItemIcon(itemType) {
            const icons = {
                1: '⚔️', // 武器
                2: '🛡️', // 防具
                3: '💍', // 饰品
                4: '🧪', // 消耗品
                5: '⚒️', // 材料
                6: '📜', // 任务物品
                7: '💎'  // 其他
            };
            return icons[itemType] || '❓';
        }

        // 处理背包格子点击
        function handleInventorySlotClick(slotIndex) {
            const inventoryItem = gameData.inventory.find(item => item.slotIndex === slotIndex);
            if (inventoryItem) {
                showItemTooltip(inventoryItem);
            }
        }

        // 显示物品提示
        function showItemTooltip(inventoryItem) {
            const item = inventoryItem.item;
            if (!item) {
                alert('物品信息不完整');
                return;
            }

            const tooltip = `
                物品: ${item.name}
                类型: ${getItemTypeName(item.type)}
                品质: ${getQualityName(item.quality)}
                数量: ${inventoryItem.quantity}
                描述: ${item.description || '无描述'}
            `;
            alert(tooltip); // 简单实现，实际应该用更好的UI
        }

        // 获取物品类型名称
        function getItemTypeName(type) {
            const types = {
                1: '装备',
                2: '消耗品',
                3: '材料',
                4: '任务物品',
                5: '宝石',
                6: '书籍',
                7: '其他'
            };
            return types[type] || '未知';
        }

        // 获取品质名称
        function getQualityName(quality) {
            const qualities = {
                1: '白色',
                2: '绿色',
                3: '蓝色',
                4: '紫色',
                5: '橙色',
                6: '红色'
            };
            return qualities[quality] || '未知';
        }

        // 游戏功能函数
        function exploreArea() {
            if (!currentRole) {
                addGameMessage('请先选择角色', 'warning');
                return;
            }

            addGameMessage('你开始探索周围的区域...', 'info');

            // 随机探索事件
            const events = [
                {
                    type: 'treasure',
                    message: '你发现了一个宝箱！',
                    reward: () => {
                        const copper = Math.floor(Math.random() * 50) + 10;
                        currentRole.copper = (currentRole.copper || 0) + copper;
                        addGameMessage(`获得了 ${copper} 铜钱！`, 'success');
                        updateCharacterInfo();
                    }
                },
                {
                    type: 'monster',
                    message: '你遇到了野生怪物！',
                    reward: () => {
                        // 随机选择一个怪物
                        const monsters = [
                            { id: 1, name: '野狼', level: 1 },
                            { id: 2, name: '山贼', level: 2 },
                            { id: 3, name: '野猪', level: 3 },
                            { id: 4, name: '毒蛇', level: 2 },
                            { id: 5, name: '土匪', level: 4 }
                        ];
                        const randomMonster = monsters[Math.floor(Math.random() * monsters.length)];

                        // 保存遇到的怪物信息到sessionStorage
                        sessionStorage.setItem('encounteredMonster', JSON.stringify(randomMonster));

                        addGameMessage(`你遇到了 ${randomMonster.name}(Lv.${randomMonster.level})！点击战斗按钮开始战斗！`, 'warning');

                        // 显示战斗按钮高亮效果
                        const battleBtn = document.querySelector('.action-btn.danger');
                        if (battleBtn) {
                            battleBtn.style.animation = 'pulse 1s infinite';
                            battleBtn.style.boxShadow = '0 0 20px #e74c3c';
                        }
                    }
                },
                {
                    type: 'herb',
                    message: '你发现了一些草药！',
                    reward: () => {
                        const healAmount = Math.floor(Math.random() * 30) + 10;
                        currentRole.currentHp = Math.min(currentRole.maxHp, currentRole.currentHp + healAmount);
                        addGameMessage(`恢复了 ${healAmount} 点生命值！`, 'success');
                        updateCharacterInfo();
                    }
                },
                {
                    type: 'nothing',
                    message: '这里什么都没有...',
                    reward: () => {
                        addGameMessage('继续探索吧！', 'info');
                    }
                }
            ];

            // 随机选择事件
            const randomEvent = events[Math.floor(Math.random() * events.length)];

            setTimeout(() => {
                addGameMessage(randomEvent.message, randomEvent.type === 'monster' ? 'warning' : 'info');
                randomEvent.reward();
            }, 1000);
        }

        function startBattle() {
            // 检查是否有遇到的怪物
            const encounteredMonster = sessionStorage.getItem('encounteredMonster');

            if (encounteredMonster) {
                // 有遇到的怪物，直接战斗
                const monster = JSON.parse(encounteredMonster);
                addGameMessage(`开始与 ${monster.name} 战斗！`, 'warning');

                // 清除遇到的怪物信息
                sessionStorage.removeItem('encounteredMonster');

                // 保存当前游戏状态
                saveGameState();

                // 跳转到战斗系统，并传递怪物ID
                window.location.href = `/honghuang-game/battle.html?monsterId=${monster.id}&monsterName=${encodeURIComponent(monster.name)}`;
            } else {
                // 没有遇到怪物，去战斗系统寻找
                addGameMessage('前往战斗系统寻找敌人...', 'warning');

                // 保存当前游戏状态
                saveGameState();

                // 跳转到战斗系统
                window.location.href = '/honghuang-game/battle.html';
            }
        }

        function rest() {
            if (!currentRole) {
                addGameMessage('请先选择角色', 'warning');
                return;
            }

            addGameMessage('你在安全的地方休息，恢复了一些体力...', 'success');

            // 模拟休息恢复
            if (currentRole.currentHp < currentRole.maxHp) {
                const healAmount = Math.floor(currentRole.maxHp * 0.3); // 恢复30%血量
                currentRole.currentHp = Math.min(currentRole.maxHp, currentRole.currentHp + healAmount);
                addGameMessage(`恢复了 ${healAmount} 点生命值`, 'success');
            }

            if (currentRole.currentMp < currentRole.maxMp) {
                const manaAmount = Math.floor(currentRole.maxMp * 0.5); // 恢复50%法力
                currentRole.currentMp = Math.min(currentRole.maxMp, currentRole.currentMp + manaAmount);
                addGameMessage(`恢复了 ${manaAmount} 点法力值`, 'success');
            }

            updateCharacterInfo();
            addGameMessage('休息完毕，精神焕发！', 'success');
        }

        function openShop() {
            if (!currentRole) {
                addGameMessage('请先选择角色', 'warning');
                return;
            }

            addGameMessage('欢迎来到商店！', 'info');

            // 模拟商店物品
            const shopItems = [
                { id: 1, name: '生命药水', price: 50, description: '恢复50点生命值' },
                { id: 2, name: '法力药水', price: 30, description: '恢复30点法力值' },
                { id: 3, name: '铁剑', price: 200, description: '攻击力+5' },
                { id: 4, name: '皮甲', price: 150, description: '防御力+3' },
                { id: 5, name: '敏捷靴', price: 100, description: '敏捷+2' }
            ];

            let shopList = `你的铜钱: ${currentRole.copper || 0}\n\n商店物品：\n`;
            shopItems.forEach((item, index) => {
                shopList += `${index + 1}. ${item.name} - ${item.price}铜钱 (${item.description})\n`;
            });

            const choice = prompt(shopList + '\n请输入物品编号购买 (1-' + shopItems.length + ')，或按取消退出：');

            if (choice && !isNaN(choice)) {
                const itemIndex = parseInt(choice) - 1;
                if (itemIndex >= 0 && itemIndex < shopItems.length) {
                    const selectedItem = shopItems[itemIndex];
                    const playerCopper = currentRole.copper || 0;

                    if (playerCopper >= selectedItem.price) {
                        currentRole.copper = playerCopper - selectedItem.price;
                        addGameMessage(`购买了 ${selectedItem.name}！`, 'success');

                        // 简单的物品效果
                        if (selectedItem.name === '生命药水') {
                            currentRole.currentHp = Math.min(currentRole.maxHp, currentRole.currentHp + 50);
                            addGameMessage('恢复了50点生命值！', 'success');
                        } else if (selectedItem.name === '法力药水') {
                            currentRole.currentMp = Math.min(currentRole.maxMp, currentRole.currentMp + 30);
                            addGameMessage('恢复了30点法力值！', 'success');
                        } else {
                            addGameMessage(`${selectedItem.name} 已添加到背包！`, 'info');
                        }

                        updateCharacterInfo();
                    } else {
                        addGameMessage('铜钱不足！', 'warning');
                    }
                } else {
                    addGameMessage('无效的物品编号', 'warning');
                }
            }
        }

        function openQuests() {
            if (!currentRole) {
                addGameMessage('请先选择角色', 'warning');
                return;
            }

            addGameMessage('查看可用任务...', 'info');

            // 模拟任务列表
            const quests = [
                {
                    id: 1,
                    name: '新手训练',
                    description: '击败3只怪物',
                    reward: '经验+100, 铜钱+50',
                    level: 1
                },
                {
                    id: 2,
                    name: '收集草药',
                    description: '探索5次区域',
                    reward: '经验+150, 铜钱+80',
                    level: 3
                },
                {
                    id: 3,
                    name: '商人委托',
                    description: '购买任意物品',
                    reward: '经验+80, 铜钱+30',
                    level: 1
                }
            ];

            // 根据角色等级过滤任务
            const availableQuests = quests.filter(quest => quest.level <= (currentRole.level || 1));

            let questList = '可接受的任务：\n\n';
            availableQuests.forEach((quest, index) => {
                questList += `${index + 1}. ${quest.name} (Lv.${quest.level})\n`;
                questList += `   ${quest.description}\n`;
                questList += `   奖励: ${quest.reward}\n\n`;
            });

            const choice = prompt(questList + '请输入任务编号接受任务，或按取消退出：');

            if (choice && !isNaN(choice)) {
                const questIndex = parseInt(choice) - 1;
                if (questIndex >= 0 && questIndex < availableQuests.length) {
                    const selectedQuest = availableQuests[questIndex];
                    addGameMessage(`接受了任务: ${selectedQuest.name}`, 'success');
                    addGameMessage(`任务目标: ${selectedQuest.description}`, 'info');
                    addGameMessage('完成任务后将获得丰厚奖励！', 'info');
                } else {
                    addGameMessage('无效的任务编号', 'warning');
                }
            }
        }

        function moveScene() {
            if (!currentRole) {
                addGameMessage('请先选择角色', 'warning');
                return;
            }

            // 模拟场景列表
            const scenes = [
                { id: 1, name: '新手村', level: 1, description: '适合新手的安全区域' },
                { id: 2, name: '森林', level: 5, description: '充满野生动物的森林' },
                { id: 3, name: '山洞', level: 10, description: '阴暗潮湿的洞穴' },
                { id: 4, name: '古墓', level: 15, description: '危险的古代墓穴' },
                { id: 5, name: '魔法塔', level: 20, description: '充满魔法能量的高塔' }
            ];

            // 根据角色等级过滤可进入的场景
            const availableScenes = scenes.filter(scene => scene.level <= (currentRole.level || 1) + 5);

            let sceneList = '可前往的场景：\n';
            availableScenes.forEach((scene, index) => {
                sceneList += `${index + 1}. ${scene.name} (Lv.${scene.level}) - ${scene.description}\n`;
            });

            const choice = prompt(sceneList + '\n请输入场景编号 (1-' + availableScenes.length + ')：');

            if (choice && !isNaN(choice)) {
                const sceneIndex = parseInt(choice) - 1;
                if (sceneIndex >= 0 && sceneIndex < availableScenes.length) {
                    const selectedScene = availableScenes[sceneIndex];
                    addGameMessage(`正在前往 ${selectedScene.name}...`, 'info');

                    setTimeout(() => {
                        addGameMessage(`成功到达 ${selectedScene.name}！`, 'success');
                        addGameMessage(selectedScene.description, 'info');

                        // 更新当前场景显示
                        document.getElementById('currentScene').textContent = selectedScene.name;
                    }, 1500);
                } else {
                    addGameMessage('无效的场景编号', 'warning');
                }
            }
        }

        function openInventory() {
            if (!currentRole) {
                addGameMessage('请先选择角色', 'warning');
                return;
            }

            addGameMessage('打开背包管理...', 'info');

            // 检查背包物品
            if (!gameData.inventory || gameData.inventory.length === 0) {
                addGameMessage('背包是空的', 'info');
                return;
            }

            let inventoryList = '背包物品：\n\n';
            gameData.inventory.forEach((item, index) => {
                const itemName = item.gameItem ? item.gameItem.name : '未知物品';
                const quantity = item.quantity || 1;
                inventoryList += `${index + 1}. ${itemName} x${quantity}\n`;
            });

            inventoryList += '\n背包中的物品可以在右侧背包栏中查看和使用';
            alert(inventoryList);

            addGameMessage('背包管理完成', 'success');
        }

        function openRanking() {
            window.location.href = '/honghuang-game/ranking/index';
        }

        function openSettings() {
            addGameMessage('设置功能开发中...', 'info');
            // TODO: 实现设置
        }

        function getServerStatus() {
            fetch(`${API_BASE}/server/status`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        addGameMessage(`服务器状态: ${data.data.status}`, 'success');
                    } else {
                        addGameMessage('获取服务器状态失败', 'error');
                    }
                })
                .catch(error => {
                    addGameMessage('服务器连接失败', 'error');
                });
        }

        // 聊天功能
        function handleChatInput(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message) {
                addChatMessage(`[${currentRole?.name || '玩家'}] ${message}`, '#3498db');
                input.value = '';

                // TODO: 发送到服务器
            }
        }

        function addChatMessage(message, color = '#bdc3c7') {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message';
            messageDiv.style.color = color;
            messageDiv.textContent = message;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 游戏消息系统
        function addGameMessage(message, type = 'info') {
            const gameMessages = document.getElementById('gameMessages');
            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '5px';
            messageDiv.style.padding = '5px';
            messageDiv.style.borderRadius = '3px';

            const colors = {
                'info': '#3498db',
                'success': '#27ae60',
                'warning': '#f39c12',
                'error': '#e74c3c'
            };

            messageDiv.style.color = colors[type] || colors.info;
            messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;

            gameMessages.appendChild(messageDiv);
            gameMessages.scrollTop = gameMessages.scrollHeight;
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            addGameMessage(message, type);
        }

        // 加载游戏数据
        function loadGameData() {
            // 加载场景数据
            fetch(`${API_BASE}/scene/active`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        gameData.scenes = data.data;
                    }
                })
                .catch(error => {
                    console.error('加载场景数据失败:', error);
                });

            // 加载物品数据
            fetch(`${API_BASE}/item/active`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        gameData.items = data.data;
                    }
                })
                .catch(error => {
                    console.error('加载物品数据失败:', error);
                });
        }

        // 登出功能
        function logout() {
            if (confirm('确定要登出吗？')) {
                fetch(`${API_BASE}/user/logout`, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        window.location.href = '/honghuang-game/';
                    })
                    .catch(error => {
                        console.error('登出失败:', error);
                        window.location.href = '/honghuang-game/';
                    });
            }
        }

        // 保存游戏状态
        function saveGameState() {
            const gameState = {
                currentRole: currentRole,
                gameMessages: Array.from(document.querySelectorAll('#gameMessages .message')).map(msg => ({
                    text: msg.textContent,
                    className: msg.className
                })).slice(-10), // 只保存最近10条消息
                timestamp: Date.now()
            };
            sessionStorage.setItem('gameState', JSON.stringify(gameState));
        }

        // 恢复游戏状态
        function restoreGameState() {
            const savedState = sessionStorage.getItem('gameState');
            if (savedState) {
                try {
                    const gameState = JSON.parse(savedState);

                    // 检查状态是否过期（超过1小时）
                    if (Date.now() - gameState.timestamp > 3600000) {
                        sessionStorage.removeItem('gameState');
                        return false;
                    }

                    // 恢复角色信息
                    if (gameState.currentRole) {
                        currentRole = gameState.currentRole;
                        updateCharacterInfo();

                        // 恢复游戏消息
                        const messagesContainer = document.getElementById('gameMessages');
                        messagesContainer.innerHTML = '';

                        if (gameState.gameMessages && gameState.gameMessages.length > 0) {
                            gameState.gameMessages.forEach(msg => {
                                const messageDiv = document.createElement('div');
                                messageDiv.className = msg.className;
                                messageDiv.textContent = msg.text;
                                messagesContainer.appendChild(messageDiv);
                            });
                        }

                        addGameMessage('游戏状态已恢复，继续你的冒险吧！', 'success');
                        return true;
                    }
                } catch (e) {
                    console.error('恢复游戏状态失败:', e);
                    sessionStorage.removeItem('gameState');
                }
            }
            return false;
        }

        // 装备管理功能
        let currentEquipmentData = {};
        let bagEquipmentData = [];

        function showEquipmentPanel() {
            if (!currentRole) {
                addGameMessage('请先选择角色', 'warning');
                return;
            }

            document.getElementById('equipmentPanel').style.display = 'block';
            loadEquipmentData();
        }

        function closeEquipmentPanel() {
            document.getElementById('equipmentPanel').style.display = 'none';
        }

        function loadEquipmentData() {
            // 加载当前装备
            fetch('/honghuang-game/api/equipment/current')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        currentEquipmentData = result.data || {};
                        renderCurrentEquipment();
                    } else {
                        console.error('加载当前装备失败:', result.message);
                    }
                })
                .catch(error => {
                    console.error('加载当前装备出错:', error);
                });

            // 加载背包装备
            fetch('/honghuang-game/api/equipment/bag')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        bagEquipmentData = result.data || [];
                        renderBagEquipment();
                    } else {
                        console.error('加载背包装备失败:', result.message);
                    }
                })
                .catch(error => {
                    console.error('加载背包装备出错:', error);
                });
        }

        function renderCurrentEquipment() {
            // 清空所有装备位
            for (let i = 1; i <= 7; i++) {
                const slot = document.getElementById(`slot-${i}`);
                if (slot) {
                    slot.innerHTML = '';
                }
            }

            // 渲染已装备的物品
            Object.values(currentEquipmentData).forEach(equipment => {
                if (equipment && equipment.position > 0) {
                    const slot = document.getElementById(`slot-${equipment.position}`);
                    if (slot) {
                        slot.innerHTML = `
                            <div class="equipment-item equipped" onclick="unequipItem(${equipment.id})">
                                <div class="equipment-name">${equipment.equipment?.name || '未知装备'}</div>
                                <div class="equipment-stats">
                                    ${equipment.equipment?.attackBonus ? `攻击+${equipment.equipment.attackBonus}` : ''}
                                    ${equipment.equipment?.defenseBonus ? `防御+${equipment.equipment.defenseBonus}` : ''}
                                </div>
                            </div>
                        `;
                    }
                }
            });
        }

        function renderBagEquipment() {
            const container = document.getElementById('bagEquipment');
            container.innerHTML = '';

            bagEquipmentData.forEach(equipment => {
                const div = document.createElement('div');
                div.className = 'equipment-item';
                div.onclick = () => equipItem(equipment.id);
                div.innerHTML = `
                    <div class="equipment-name">${equipment.equipment?.name || '未知装备'}</div>
                    <div class="equipment-type">${equipment.equipment?.typeName || '未知类型'}</div>
                    <div class="equipment-stats">
                        ${equipment.equipment?.attackBonus ? `攻击+${equipment.equipment.attackBonus}` : ''}
                        ${equipment.equipment?.defenseBonus ? `防御+${equipment.equipment.defenseBonus}` : ''}
                    </div>
                `;
                container.appendChild(div);
            });
        }

        function equipItem(equipmentId) {
            fetch('/honghuang-game/api/equipment/equip', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ equipmentId: equipmentId })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    addGameMessage('装备成功！', 'success');
                    loadEquipmentData(); // 重新加载装备数据
                    loadCurrentRole(); // 更新角色信息
                } else {
                    addGameMessage('装备失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                console.error('装备失败:', error);
                addGameMessage('装备失败: 网络错误', 'error');
            });
        }

        function unequipItem(equipmentId) {
            fetch('/honghuang-game/api/equipment/unequip', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ equipmentId: equipmentId })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    addGameMessage('卸下装备成功！', 'success');
                    loadEquipmentData(); // 重新加载装备数据
                    loadCurrentRole(); // 更新角色信息
                } else {
                    addGameMessage('卸下装备失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                console.error('卸下装备失败:', error);
                addGameMessage('卸下装备失败: 网络错误', 'error');
            });
        }

        // 定时更新角色信息
        setInterval(() => {
            if (currentRole) {
                loadCurrentRole();
            }
        }, 30000); // 每30秒更新一次
    </script>
</body>
</html>
