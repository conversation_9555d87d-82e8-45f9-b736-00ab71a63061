<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宠物系统 - 洪荒Online</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            color: #666;
            margin-top: 0;
        }
        .pet-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        .pet-card {
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
            position: relative;
        }
        .pet-card.active {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        .pet-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .pet-stats {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .pet-status {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .status-bar {
            flex: 1;
            height: 20px;
            background-color: #eee;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }
        .status-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .hp-fill { background-color: #ff4444; }
        .mp-fill { background-color: #4444ff; }
        .exp-fill { background-color: #44ff44; }
        .loyalty-fill { background-color: #ffaa44; }
        .status-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            margin: 2px;
            font-size: 12px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .capture-form {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }
        .capture-form input, .capture-form select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .active-pet {
            background-color: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .rarity-1 { border-left: 5px solid #6c757d; } /* 普通 */
        .rarity-2 { border-left: 5px solid #28a745; } /* 优秀 */
        .rarity-3 { border-left: 5px solid #007bff; } /* 稀有 */
        .rarity-4 { border-left: 5px solid #6f42c1; } /* 史诗 */
        .rarity-5 { border-left: 5px solid #fd7e14; } /* 传说 */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐾 宠物系统</h1>
            <p>捕获、培养和管理你的宠物伙伴！</p>
        </div>

        <!-- 出战宠物 -->
        <div class="section">
            <h3>⭐ 出战宠物</h3>
            <div id="activePet">
                <p>暂无出战宠物</p>
            </div>
        </div>

        <!-- 捕获宠物 -->
        <div class="section">
            <h3>🎯 捕获宠物</h3>
            <div class="capture-form">
                <input type="text" id="petName" placeholder="宠物名称" value="小火龙">
                <select id="petType">
                    <option value="1">火系</option>
                    <option value="2">水系</option>
                    <option value="3">草系</option>
                    <option value="4">电系</option>
                    <option value="5">地系</option>
                </select>
                <button class="btn btn-success" onclick="capturePet()">捕获宠物</button>
            </div>
            <p><small>💡 提示：捕获的宠物属性是随机生成的，稀有度也有一定概率！</small></p>
        </div>

        <!-- 宠物列表 -->
        <div class="section">
            <h3>📋 我的宠物</h3>
            <div class="pet-grid" id="petList">
                <p>加载中...</p>
            </div>
            <button class="btn" onclick="loadPets()">刷新宠物列表</button>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="window.location.href='/honghuang-game/game.html'">返回游戏主界面</button>
        </div>
    </div>

    <script>
        const API_BASE = '/honghuang-game/api';

        // 加载宠物列表
        function loadPets() {
            fetch(`${API_BASE}/pet/list`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        displayPets(data.data);
                    } else if (data.code === 401) {
                        document.getElementById('petList').innerHTML = '<p>请先登录</p>';
                    } else {
                        document.getElementById('petList').innerHTML = '<p>加载宠物列表失败: ' + data.message + '</p>';
                    }
                })
                .catch(error => {
                    console.error('加载宠物列表失败:', error);
                    document.getElementById('petList').innerHTML = '<p>加载宠物列表失败</p>';
                });
        }

        // 加载出战宠物
        function loadActivePet() {
            fetch(`${API_BASE}/pet/active`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        displayActivePet(data.data);
                    } else {
                        document.getElementById('activePet').innerHTML = '<p>暂无出战宠物</p>';
                    }
                })
                .catch(error => {
                    console.error('加载出战宠物失败:', error);
                });
        }

        // 显示出战宠物
        function displayActivePet(pet) {
            const container = document.getElementById('activePet');
            container.innerHTML = `
                <div class="active-pet">
                    <h4>🌟 ${pet.nickname || pet.name} (Lv.${pet.level})</h4>
                    <div class="pet-stats">
                        <div>类型: ${getPetTypeName(pet.type)} | 稀有度: ${getRarityName(pet.rarity)}</div>
                        <div>攻击: ${pet.attackMin}-${pet.attackMax} | 防御: ${pet.defense} | 敏捷: ${pet.agility}</div>
                    </div>
                    <div class="pet-status">
                        <div class="status-bar">
                            <div class="status-fill hp-fill" style="width: ${(pet.hp / pet.maxHp) * 100}%"></div>
                            <div class="status-text">HP: ${pet.hp}/${pet.maxHp}</div>
                        </div>
                        <div class="status-bar">
                            <div class="status-fill mp-fill" style="width: ${(pet.mp / pet.maxMp) * 100}%"></div>
                            <div class="status-text">MP: ${pet.mp}/${pet.maxMp}</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示宠物列表
        function displayPets(pets) {
            const container = document.getElementById('petList');
            if (pets.length === 0) {
                container.innerHTML = '<p>暂无宠物，快去捕获一只吧！</p>';
                return;
            }

            container.innerHTML = pets.map(pet => `
                <div class="pet-card rarity-${pet.rarity || 1} ${pet.isActive === 1 ? 'active' : ''}">
                    <h4>${pet.nickname || pet.name} (Lv.${pet.level})</h4>
                    <div class="pet-stats">
                        <div>类型: ${getPetTypeName(pet.type)} | 稀有度: ${getRarityName(pet.rarity)}</div>
                        <div>攻击: ${pet.attackMin}-${pet.attackMax}</div>
                        <div>防御: ${pet.defense} | 敏捷: ${pet.agility}</div>
                        <div>成长率: ${(pet.growthRate || 1.0).toFixed(2)}</div>
                        <div>忠诚度: ${pet.loyalty || 0} | 疲劳度: ${pet.fatigue || 0}</div>
                    </div>
                    <div class="pet-status">
                        <div class="status-bar">
                            <div class="status-fill hp-fill" style="width: ${(pet.hp / pet.maxHp) * 100}%"></div>
                            <div class="status-text">HP: ${pet.hp}/${pet.maxHp}</div>
                        </div>
                        <div class="status-bar">
                            <div class="status-fill exp-fill" style="width: ${(pet.exp / pet.maxExp) * 100}%"></div>
                            <div class="status-text">EXP: ${pet.exp}/${pet.maxExp}</div>
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        ${pet.isActive !== 1 ? `<button class="btn btn-success" onclick="setActivePet(${pet.id})">设为出战</button>` : '<span style="color: #28a745;">★ 出战中</span>'}
                        ${pet.exp >= pet.maxExp ? `<button class="btn btn-warning" onclick="levelUpPet(${pet.id})">升级</button>` : ''}
                        ${pet.fatigue > 80 ? `<button class="btn" onclick="restPet(${pet.id})">休息</button>` : ''}
                        ${pet.hunger > 80 ? `<button class="btn" onclick="feedPet(${pet.id})">喂食</button>` : ''}
                        <button class="btn" onclick="renamePet(${pet.id})">重命名</button>
                        <button class="btn btn-danger" onclick="releasePet(${pet.id})">释放</button>
                    </div>
                </div>
            `).join('');
        }

        // 捕获宠物
        function capturePet() {
            const petName = document.getElementById('petName').value.trim();
            const petType = parseInt(document.getElementById('petType').value);

            if (!petName) {
                alert('请输入宠物名称');
                return;
            }

            fetch(`${API_BASE}/pet/capture`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    petName: petName,
                    petType: petType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert('捕获成功！获得了 ' + data.data.name);
                    loadPets();
                    // 清空输入框
                    document.getElementById('petName').value = '';
                } else {
                    alert('捕获失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('捕获宠物失败:', error);
                alert('捕获失败');
            });
        }

        // 设置出战宠物
        function setActivePet(petId) {
            fetch(`${API_BASE}/pet/setActive`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ petId: petId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert('设置出战宠物成功');
                    loadPets();
                    loadActivePet();
                } else {
                    alert('设置失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('设置出战宠物失败:', error);
                alert('设置失败');
            });
        }

        // 宠物升级
        function levelUpPet(petId) {
            fetch(`${API_BASE}/pet/levelUp`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ petId: petId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert('宠物升级成功！');
                    loadPets();
                    loadActivePet();
                } else {
                    alert('升级失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('宠物升级失败:', error);
                alert('升级失败');
            });
        }

        // 宠物休息
        function restPet(petId) {
            fetch(`${API_BASE}/pet/rest`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ petId: petId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert('宠物休息完毕，恢复了体力！');
                    loadPets();
                    loadActivePet();
                } else {
                    alert('休息失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('宠物休息失败:', error);
                alert('休息失败');
            });
        }

        // 喂养宠物
        function feedPet(petId) {
            fetch(`${API_BASE}/pet/feed`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ petId: petId, foodValue: 50 })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert('喂食成功，宠物很开心！');
                    loadPets();
                    loadActivePet();
                } else {
                    alert('喂食失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('喂食失败:', error);
                alert('喂食失败');
            });
        }

        // 重命名宠物
        function renamePet(petId) {
            const newName = prompt('请输入新的宠物昵称:');
            if (newName && newName.trim()) {
                fetch(`${API_BASE}/pet/rename`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ petId: petId, newNickname: newName.trim() })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        alert('重命名成功！');
                        loadPets();
                        loadActivePet();
                    } else {
                        alert('重命名失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('重命名失败:', error);
                    alert('重命名失败');
                });
            }
        }

        // 释放宠物
        function releasePet(petId) {
            if (confirm('确定要释放这只宠物吗？此操作不可恢复！')) {
                fetch(`${API_BASE}/pet/${petId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        alert('宠物已释放');
                        loadPets();
                        loadActivePet();
                    } else {
                        alert('释放失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('释放宠物失败:', error);
                    alert('释放失败');
                });
            }
        }

        // 获取宠物类型名称
        function getPetTypeName(type) {
            const types = {
                1: '火系', 2: '水系', 3: '草系', 4: '电系', 5: '地系'
            };
            return types[type] || '未知';
        }

        // 获取稀有度名称
        function getRarityName(rarity) {
            const rarities = {
                1: '普通', 2: '优秀', 3: '稀有', 4: '史诗', 5: '传说'
            };
            return rarities[rarity] || '普通';
        }

        // 页面加载时初始化
        window.onload = function() {
            loadPets();
            loadActivePet();
        };
    </script>
</body>
</html>
