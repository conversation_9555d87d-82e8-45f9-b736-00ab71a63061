package com.honghuang.game.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.service.PlayerRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket服务器端点
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/game/{roleId}")
public class GameWebSocketServer {

    /**
     * 静态变量，用来记录当前在线连接数
     */
    private static final AtomicInteger onlineCount = new AtomicInteger(0);

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的WebSocket对象
     */
    private static final ConcurrentHashMap<String, GameWebSocketServer> webSocketMap = new ConcurrentHashMap<>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;

    /**
     * 接收roleId
     */
    private String roleId = "";

    /**
     * 角色信息
     */
    private PlayerRole playerRole;

    /**
     * 静态注入PlayerRoleService
     */
    private static PlayerRoleService playerRoleService;

    @Autowired
    public void setPlayerRoleService(PlayerRoleService playerRoleService) {
        GameWebSocketServer.playerRoleService = playerRoleService;
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("roleId") String roleId) {
        this.session = session;
        this.roleId = roleId;
        
        try {
            // 获取角色信息
            this.playerRole = playerRoleService.findById(Integer.parseInt(roleId));
            if (this.playerRole == null) {
                log.warn("角色不存在: {}", roleId);
                session.close();
                return;
            }
            
            // 添加到连接池
            webSocketMap.put(roleId, this);
            
            // 在线数加1
            addOnlineCount();
            
            log.info("角色{}连接WebSocket成功，当前在线人数为：{}", playerRole.getName(), getOnlineCount());
            
            // 发送连接成功消息
            sendMessage(createMessage("system", "连接成功", "WebSocket连接建立成功"));
            
            // 广播上线消息
            broadcastMessage(createMessage("system", "玩家上线", playerRole.getName() + " 上线了"));
            
        } catch (Exception e) {
            log.error("WebSocket连接异常: {}", e.getMessage(), e);
            try {
                session.close();
            } catch (IOException ioException) {
                log.error("关闭WebSocket连接失败", ioException);
            }
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        // 从连接池中删除
        webSocketMap.remove(roleId);
        
        // 在线数减1
        subOnlineCount();
        
        log.info("角色{}断开WebSocket连接，当前在线人数为：{}", 
                playerRole != null ? playerRole.getName() : roleId, getOnlineCount());
        
        // 广播下线消息
        if (playerRole != null) {
            broadcastMessage(createMessage("system", "玩家下线", playerRole.getName() + " 下线了"));
        }
    }

    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("角色{}发送消息：{}", playerRole != null ? playerRole.getName() : roleId, message);
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> messageData = mapper.readValue(message, Map.class);
            
            String type = (String) messageData.get("type");
            String content = (String) messageData.get("content");
            String channel = (String) messageData.get("channel");
            
            switch (type) {
                case "chat":
                    handleChatMessage(content, channel);
                    break;
                case "heartbeat":
                    // 心跳消息，回复pong
                    sendMessage(createMessage("heartbeat", "pong", ""));
                    break;
                default:
                    log.warn("未知消息类型: {}", type);
                    break;
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("角色{}的WebSocket发生错误", playerRole != null ? playerRole.getName() : roleId, error);
    }

    /**
     * 处理聊天消息
     */
    private void handleChatMessage(String content, String channel) {
        if (playerRole == null || content == null || content.trim().isEmpty()) {
            return;
        }
        
        Map<String, Object> chatMessage = createMessage("chat", "聊天消息", content);
        chatMessage.put("channel", channel);
        chatMessage.put("senderName", playerRole.getName());
        chatMessage.put("senderId", playerRole.getId());
        chatMessage.put("senderLevel", playerRole.getLevel());
        
        switch (channel) {
            case "world":
                // 世界聊天，广播给所有人
                broadcastMessage(chatMessage);
                break;
            case "scene":
                // 场景聊天，广播给同场景的人
                broadcastToScene(chatMessage, playerRole.getSceneId());
                break;
            case "guild":
                // 帮派聊天，广播给帮派成员
                broadcastToGuild(chatMessage, playerRole.getId());
                break;
            default:
                log.warn("未知聊天频道: {}", channel);
                break;
        }
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(Map<String, Object> message) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            String jsonMessage = mapper.writeValueAsString(message);
            this.session.getBasicRemote().sendText(jsonMessage);
        } catch (IOException e) {
            log.error("WebSocket发送消息异常", e);
        }
    }

    /**
     * 发送自定义消息
     */
    public static void sendInfo(String message, String roleId) {
        log.info("发送消息到角色{}，消息内容：{}", roleId, message);
        
        GameWebSocketServer webSocket = webSocketMap.get(roleId);
        if (webSocket != null) {
            webSocket.sendMessage(createMessage("notification", "系统通知", message));
        } else {
            log.warn("角色{}不在线，无法发送消息", roleId);
        }
    }

    /**
     * 广播消息给所有在线用户
     */
    public static void broadcastMessage(Map<String, Object> message) {
        for (GameWebSocketServer webSocket : webSocketMap.values()) {
            webSocket.sendMessage(message);
        }
    }

    /**
     * 广播消息给同场景的用户
     */
    public static void broadcastToScene(Map<String, Object> message, Integer sceneId) {
        if (sceneId == null) return;
        
        for (GameWebSocketServer webSocket : webSocketMap.values()) {
            if (webSocket.playerRole != null &&
                sceneId.equals(webSocket.playerRole.getSceneId())) {
                webSocket.sendMessage(message);
            }
        }
    }

    /**
     * 广播消息给帮派成员
     */
    public static void broadcastToGuild(Map<String, Object> message, Integer roleId) {
        // TODO: 实现帮派聊天广播
        // 需要查询角色的帮派信息，然后广播给帮派成员
        log.info("帮派聊天功能待实现");
    }

    /**
     * 创建消息对象
     */
    private static Map<String, Object> createMessage(String type, String title, String content) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", type);
        message.put("title", title);
        message.put("content", content);
        message.put("timestamp", System.currentTimeMillis());
        return message;
    }

    /**
     * 获取在线用户列表
     */
    public static Map<String, Object> getOnlineUsers() {
        Map<String, Object> result = new HashMap<>();
        result.put("count", getOnlineCount());
        
        Map<String, Object> users = new HashMap<>();
        for (Map.Entry<String, GameWebSocketServer> entry : webSocketMap.entrySet()) {
            GameWebSocketServer webSocket = entry.getValue();
            if (webSocket.playerRole != null) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("name", webSocket.playerRole.getName());
                userInfo.put("level", webSocket.playerRole.getLevel());
                userInfo.put("sceneId", webSocket.playerRole.getSceneId());
                users.put(entry.getKey(), userInfo);
            }
        }
        result.put("users", users);
        
        return result;
    }

    /**
     * 检查用户是否在线
     */
    public static boolean isUserOnline(String roleId) {
        return webSocketMap.containsKey(roleId);
    }

    /**
     * 获取当前在线人数
     */
    public static synchronized int getOnlineCount() {
        return onlineCount.get();
    }

    /**
     * 在线人数加1
     */
    public static synchronized void addOnlineCount() {
        onlineCount.incrementAndGet();
    }

    /**
     * 在线人数减1
     */
    public static synchronized void subOnlineCount() {
        onlineCount.decrementAndGet();
    }
}
