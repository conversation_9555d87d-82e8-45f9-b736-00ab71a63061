<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洪荒Online - 修仙之旅</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .logo {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .links {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
            font-size: 14px;
        }

        .links a:hover {
            text-decoration: underline;
        }

        .status {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            text-align: left;
        }

        .status h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .status-item span:first-child {
            color: #666;
        }

        .status-item span:last-child {
            color: #333;
            font-weight: 500;
        }

        .message {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">洪荒Online</div>
        <div class="subtitle">踏上修仙之路，成就无上道果</div>
        
        <div id="message" class="message"></div>
        
        <div id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" placeholder="请输入密码">
            </div>
            <button class="btn" onclick="login()">登录游戏</button>
            <button class="btn btn-secondary" onclick="showRegister()">注册账号</button>
        </div>

        <div id="registerForm" style="display: none;">
            <div class="form-group">
                <label for="regUsername">用户名</label>
                <input type="text" id="regUsername" placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label for="regPassword">密码</label>
                <input type="password" id="regPassword" placeholder="请输入密码">
            </div>
            <div class="form-group">
                <label for="confirmPassword">确认密码</label>
                <input type="password" id="confirmPassword" placeholder="请再次输入密码">
            </div>
            <button class="btn" onclick="register()">注册</button>
            <button class="btn btn-secondary" onclick="showLogin()">返回登录</button>
        </div>

        <div class="status">
            <h3>服务器状态</h3>
            <div class="status-item">
                <span>服务器状态:</span>
                <span id="serverStatus">检查中...</span>
            </div>
            <div class="status-item">
                <span>在线用户:</span>
                <span id="onlineUsers">-</span>
            </div>
            <div class="status-item">
                <span>在线角色:</span>
                <span id="onlineRoles">-</span>
            </div>
            <div class="status-item">
                <span>今日注册:</span>
                <span id="todayRegistrations">-</span>
            </div>
        </div>

        <div class="links">
            <a href="/swagger-ui.html" target="_blank">API文档</a>
            <a href="#" onclick="checkHealth()">健康检查</a>
            <a href="#" onclick="loadServerStatus()">刷新状态</a>
            <br><br>
            <div style="text-align: center; color: #666; font-size: 12px;">
                💡 提示：手机用户登录后将自动跳转到手机版界面
            </div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = '/honghuang-game/api';

        // 显示消息
        function showMessage(text, type = 'success') {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }

        // 显示注册表单
        function showRegister() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
        }

        // 显示登录表单
        function showLogin() {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('registerForm').style.display = 'none';
        }

        // 用户登录
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showMessage('请输入用户名和密码', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/user/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        channel: 'web'
                    }),
                    credentials: 'include'
                });

                const result = await response.json();
                if (result.code === 200) {
                    showMessage('登录成功！正在跳转...', 'success');
                    // 检测是否为移动设备
                    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

                    setTimeout(() => {
                        if (isMobile) {
                            // 移动设备跳转到手机版
                            window.location.href = '/honghuang-game/mobile-game.html';
                        } else {
                            // PC设备跳转到角色选择页面
                            window.location.href = '/honghuang-game/role-select.html';
                        }
                    }, 1000);
                } else {
                    showMessage(result.message || '登录失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
                console.error('Login error:', error);
            }
        }

        // 用户注册
        async function register() {
            const username = document.getElementById('regUsername').value;
            const password = document.getElementById('regPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (!username || !password || !confirmPassword) {
                showMessage('请填写所有字段', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/user/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        confirmPassword: confirmPassword,
                        channel: 'web'
                    }),
                    credentials: 'include'
                });

                const result = await response.json();
                if (result.code === 200) {
                    showMessage('注册成功！请登录', 'success');
                    showLogin();
                } else {
                    showMessage(result.message || '注册失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
                console.error('Register error:', error);
            }
        }

        // 加载服务器状态
        async function loadServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/game/status`);
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    document.getElementById('serverStatus').textContent = data.gameStateName;
                    document.getElementById('onlineUsers').textContent = data.onlineUsers;
                    document.getElementById('onlineRoles').textContent = data.onlineRoles;
                    document.getElementById('todayRegistrations').textContent = data.todayRegistrations;
                }
            } catch (error) {
                document.getElementById('serverStatus').textContent = '连接失败';
                console.error('Status error:', error);
            }
        }

        // 健康检查
        async function checkHealth() {
            try {
                const response = await fetch(`${API_BASE}/game/health`);
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('服务器运行正常', 'success');
                } else {
                    showMessage('服务器异常', 'error');
                }
            } catch (error) {
                showMessage('无法连接到服务器', 'error');
                console.error('Health check error:', error);
            }
        }

        // 页面加载时获取服务器状态
        window.onload = function() {
            loadServerStatus();
        };

        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                if (document.getElementById('loginForm').style.display !== 'none') {
                    login();
                } else {
                    register();
                }
            }
        });
    </script>
</body>
</html>
