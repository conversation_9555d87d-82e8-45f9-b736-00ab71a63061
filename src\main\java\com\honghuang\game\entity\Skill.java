package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 技能实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "skill")
public class Skill {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 技能名称
     */
    @Column(name = "name", length = 50)
    private String name;

    /**
     * 技能类型 1攻击技能 2防御技能 3治疗技能 4辅助技能 5生活技能
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 技能分类 1武器技能 2法术技能 3被动技能 4生活技能
     */
    @Column(name = "category")
    private Integer category;

    /**
     * 学习等级要求
     */
    @Column(name = "required_level")
    private Integer requiredLevel = 1;

    /**
     * 种族要求 0无要求 1巫族 2妖族
     */
    @Column(name = "required_race")
    private Integer requiredRace = 0;

    /**
     * 性别要求 0无要求 1男性 2女性
     */
    @Column(name = "required_sex")
    private Integer requiredSex = 0;

    /**
     * 前置技能ID
     */
    @Column(name = "prerequisite_skill_id")
    private Integer prerequisiteSkillId;

    /**
     * 前置技能等级要求
     */
    @Column(name = "prerequisite_skill_level")
    private Integer prerequisiteSkillLevel;

    /**
     * 最大技能等级
     */
    @Column(name = "max_level")
    private Integer maxLevel = 10;

    /**
     * 基础伤害/效果值
     */
    @Column(name = "base_value")
    private Integer baseValue = 0;

    /**
     * 每级增长值
     */
    @Column(name = "level_growth")
    private Integer levelGrowth = 0;

    /**
     * 基础消耗MP
     */
    @Column(name = "base_mp_cost")
    private Integer baseMpCost = 0;

    /**
     * 每级MP消耗增长
     */
    @Column(name = "mp_cost_growth")
    private Integer mpCostGrowth = 0;

    /**
     * 冷却时间（秒）
     */
    @Column(name = "cooldown")
    private Integer cooldown = 0;

    /**
     * 施法距离
     */
    @Column(name = "cast_range")
    private Integer castRange = 1;

    /**
     * 技能描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 技能图标
     */
    @Column(name = "icon", length = 100)
    private String icon;

    /**
     * 学习费用（铜钱）
     */
    @Column(name = "learn_cost")
    private Long learnCost = 0L;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    // 业务方法

    /**
     * 获取技能类型名称
     */
    public String getTypeName() {
        switch (type) {
            case 1: return "攻击技能";
            case 2: return "防御技能";
            case 3: return "治疗技能";
            case 4: return "辅助技能";
            case 5: return "生活技能";
            default: return "未知";
        }
    }

    /**
     * 获取技能分类名称
     */
    public String getCategoryName() {
        switch (category) {
            case 1: return "武器技能";
            case 2: return "法术技能";
            case 3: return "被动技能";
            case 4: return "生活技能";
            default: return "未知";
        }
    }

    /**
     * 检查角色是否可以学习此技能
     */
    public boolean canLearn(PlayerRole role) {
        // 检查等级要求
        if (role.getLevel() < requiredLevel) {
            return false;
        }
        
        // 检查种族要求
        if (requiredRace != 0 && !role.getRace().equals(requiredRace)) {
            return false;
        }
        
        // 检查性别要求
        if (requiredSex != 0 && !role.getSex().equals(requiredSex)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算指定等级的技能效果值
     */
    public Integer calculateValue(Integer skillLevel) {
        if (skillLevel <= 0) return baseValue;
        return baseValue + (skillLevel - 1) * levelGrowth;
    }

    /**
     * 计算指定等级的MP消耗
     */
    public Integer calculateMpCost(Integer skillLevel) {
        if (skillLevel <= 0) return baseMpCost;
        return baseMpCost + (skillLevel - 1) * mpCostGrowth;
    }

    /**
     * 计算升级到指定等级的费用
     */
    public Long calculateUpgradeCost(Integer targetLevel) {
        if (targetLevel <= 1) return learnCost;
        return learnCost + (targetLevel - 1) * learnCost / 2;
    }

    /**
     * 是否为攻击技能
     */
    public boolean isAttackSkill() {
        return type == 1;
    }

    /**
     * 是否为防御技能
     */
    public boolean isDefenseSkill() {
        return type == 2;
    }

    /**
     * 是否为治疗技能
     */
    public boolean isHealSkill() {
        return type == 3;
    }

    /**
     * 是否为辅助技能
     */
    public boolean isSupportSkill() {
        return type == 4;
    }

    /**
     * 是否为生活技能
     */
    public boolean isLifeSkill() {
        return type == 5;
    }

    /**
     * 是否为被动技能
     */
    public boolean isPassiveSkill() {
        return category == 3;
    }

    /**
     * 获取种族要求名称
     */
    public String getRequiredRaceName() {
        switch (requiredRace) {
            case 0: return "无要求";
            case 1: return "巫族";
            case 2: return "妖族";
            default: return "未知";
        }
    }

    /**
     * 获取性别要求名称
     */
    public String getRequiredSexName() {
        switch (requiredSex) {
            case 0: return "无要求";
            case 1: return "男性";
            case 2: return "女性";
            default: return "未知";
        }
    }
}
