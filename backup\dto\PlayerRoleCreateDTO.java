package com.honghuang.game.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 角色创建DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "角色创建请求")
public class PlayerRoleCreateDTO {

    @ApiModelProperty(value = "角色名称", required = true, example = "英雄角色")
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 20, message = "角色名称长度必须在2-20个字符之间")
    private String name;

    @ApiModelProperty(value = "性别", required = true, example = "1", notes = "1-男，2-女")
    @NotNull(message = "性别不能为空")
    @Min(value = 1, message = "性别值必须为1或2")
    @Max(value = 2, message = "性别值必须为1或2")
    private Integer sex;

    @ApiModelProperty(value = "种族", example = "1", notes = "1-人，2-妖")
    @Min(value = 1, message = "种族值必须为1或2")
    @Max(value = 2, message = "种族值必须为1或2")
    private Integer race = 1;

    @ApiModelProperty(value = "出生地", example = "1")
    private Integer born = 1;

    @ApiModelProperty(value = "门派", example = "少林")
    @Size(max = 20, message = "门派名称不能超过20个字符")
    private String school;

    @ApiModelProperty(value = "阵营", example = "1")
    private Integer camp = 0;

    // 自定义构造函数
    public PlayerRoleCreateDTO(String name, Integer sex) {
        this.name = name;
        this.sex = sex;
    }
}
