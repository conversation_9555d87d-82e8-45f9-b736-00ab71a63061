package com.honghuang.game.repository;

import com.honghuang.game.entity.PlayerEquipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 玩家装备数据访问层
 */
@Repository
public interface PlayerEquipmentRepository extends JpaRepository<PlayerEquipment, Integer> {

    /**
     * 根据角色ID查找所有装备
     */
    List<PlayerEquipment> findByRoleId(Integer roleId);

    /**
     * 根据角色ID和位置查找装备
     */
    List<PlayerEquipment> findByRoleIdAndPosition(Integer roleId, Integer position);

    /**
     * 根据角色ID查找已装备的物品（位置大于0）
     */
    List<PlayerEquipment> findByRoleIdAndPositionGreaterThan(Integer roleId, Integer position);

    /**
     * 根据角色ID和背包位置查找装备
     */
    Optional<PlayerEquipment> findByRoleIdAndBagIndex(Integer roleId, Integer bagIndex);

    /**
     * 根据角色ID和装备模板ID查找装备
     */
    List<PlayerEquipment> findByRoleIdAndEquipmentId(Integer roleId, Integer equipmentId);

    /**
     * 查找角色在指定位置的装备
     */
    @Query("SELECT pe FROM PlayerEquipment pe WHERE pe.roleId = :roleId AND pe.position = :position")
    List<PlayerEquipment> findEquippedAtPosition(@Param("roleId") Integer roleId, @Param("position") Integer position);

    /**
     * 查找角色背包中的装备（position = 0）
     */
    @Query("SELECT pe FROM PlayerEquipment pe WHERE pe.roleId = :roleId AND pe.position = 0 ORDER BY pe.bagIndex")
    List<PlayerEquipment> findBagEquipment(@Param("roleId") Integer roleId);

    /**
     * 查找角色已装备的物品（position > 0）
     */
    @Query("SELECT pe FROM PlayerEquipment pe WHERE pe.roleId = :roleId AND pe.position > 0 ORDER BY pe.position")
    List<PlayerEquipment> findEquippedItems(@Param("roleId") Integer roleId);

    /**
     * 查找损坏的装备（耐久度为0）
     */
    @Query("SELECT pe FROM PlayerEquipment pe WHERE pe.roleId = :roleId AND pe.currentDurability <= 0")
    List<PlayerEquipment> findDamagedEquipment(@Param("roleId") Integer roleId);

    /**
     * 查找可强化的装备（强化等级小于最大等级）
     */
    @Query("SELECT pe FROM PlayerEquipment pe WHERE pe.roleId = :roleId AND pe.enhanceLevel < 10")
    List<PlayerEquipment> findEnhanceableEquipment(@Param("roleId") Integer roleId);

    /**
     * 根据装备类型查找角色的装备
     */
    @Query("SELECT pe FROM PlayerEquipment pe JOIN pe.equipment e WHERE pe.roleId = :roleId AND e.type = :type")
    List<PlayerEquipment> findByRoleIdAndEquipmentType(@Param("roleId") Integer roleId, @Param("type") Integer type);

    /**
     * 查找角色的绑定装备
     */
    @Query("SELECT pe FROM PlayerEquipment pe WHERE pe.roleId = :roleId AND pe.bound = 1")
    List<PlayerEquipment> findBoundEquipment(@Param("roleId") Integer roleId);

    /**
     * 查找角色的可交易装备
     */
    @Query("SELECT pe FROM PlayerEquipment pe JOIN pe.equipment e WHERE pe.roleId = :roleId AND pe.bound = 0 AND e.tradeable = 1")
    List<PlayerEquipment> findTradeableEquipment(@Param("roleId") Integer roleId);

    /**
     * 统计角色装备数量
     */
    @Query("SELECT COUNT(pe) FROM PlayerEquipment pe WHERE pe.roleId = :roleId")
    long countByRoleId(@Param("roleId") Integer roleId);

    /**
     * 统计角色已装备物品数量
     */
    @Query("SELECT COUNT(pe) FROM PlayerEquipment pe WHERE pe.roleId = :roleId AND pe.position > 0")
    long countEquippedItems(@Param("roleId") Integer roleId);

    /**
     * 统计角色背包装备数量
     */
    @Query("SELECT COUNT(pe) FROM PlayerEquipment pe WHERE pe.roleId = :roleId AND pe.position = 0")
    long countBagEquipment(@Param("roleId") Integer roleId);

    /**
     * 查找角色的套装装备
     */
    @Query("SELECT pe FROM PlayerEquipment pe JOIN pe.equipment e WHERE pe.roleId = :roleId AND e.suitId = :suitId AND pe.position > 0")
    List<PlayerEquipment> findEquippedSuitItems(@Param("roleId") Integer roleId, @Param("suitId") Integer suitId);

    /**
     * 查找角色指定品质的装备
     */
    @Query("SELECT pe FROM PlayerEquipment pe JOIN pe.equipment e WHERE pe.roleId = :roleId AND e.quality = :quality")
    List<PlayerEquipment> findByRoleIdAndQuality(@Param("roleId") Integer roleId, @Param("quality") Integer quality);

    /**
     * 查找角色指定等级要求的装备
     */
    @Query("SELECT pe FROM PlayerEquipment pe JOIN pe.equipment e WHERE pe.roleId = :roleId AND e.requiredLevel <= :level")
    List<PlayerEquipment> findUsableEquipment(@Param("roleId") Integer roleId, @Param("level") Integer level);

    /**
     * 删除角色的所有装备
     */
    void deleteByRoleId(Integer roleId);

    /**
     * 查找过期的装备（如果有时效性）
     */
    @Query("SELECT pe FROM PlayerEquipment pe WHERE pe.roleId = :roleId AND pe.obtainTime < :expireTime")
    List<PlayerEquipment> findExpiredEquipment(@Param("roleId") Integer roleId, @Param("expireTime") java.time.LocalDateTime expireTime);
}
