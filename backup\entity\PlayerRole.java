package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 玩家角色实体类
 * 对应原表 u_part_info
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"user"})
@Entity
@Table(name = "u_part_info")
public class PlayerRole {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "p_pk")
    private Integer id;

    /**
     * 用户ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "u_pk")
    private User user;

    /**
     * 角色名称（用户名唯一）
     */
    @Column(name = "p_name", unique = true, length = 20)
    private String name;

    /**
     * 性别 1男 2女
     */
    @Column(name = "p_sex")
    private Integer sex;

    /**
     * 等级
     */
    @Column(name = "p_grade")
    private Integer grade = 1;

    /**
     * 最大生命值
     */
    @Column(name = "p_up_hp")
    private Integer maxHp = 230;

    /**
     * 当前HP值
     */
    @Column(name = "p_hp")
    private Integer hp = 230;

    /**
     * 最大法力值
     */
    @Column(name = "p_up_mp")
    private Integer maxMp = 70;

    /**
     * 法力值
     */
    @Column(name = "p_mp")
    private Integer mp = 70;

    /**
     * 力量
     */
    @Column(name = "p_force")
    private Integer force = 12;

    /**
     * 敏捷
     */
    @Column(name = "p_agile")
    private Integer agile = 10;

    /**
     * 体魄
     */
    @Column(name = "p_physique")
    private Integer physique = 15;

    /**
     * 悟性
     */
    @Column(name = "p_savvy")
    private Integer savvy = 8;

    /**
     * 攻击力
     */
    @Column(name = "p_gj")
    private Integer attack = 0;

    /**
     * 防御力
     */
    @Column(name = "p_fy")
    private Integer defense = 0;

    /**
     * 装备最小攻击
     */
    @Column(name = "p_zbgj_xiao")
    private Integer equipMinAttack = 0;

    /**
     * 装备最大攻击
     */
    @Column(name = "p_zbgj_da")
    private Integer equipMaxAttack = 0;

    /**
     * 装备最小防御
     */
    @Column(name = "p_zbfy_xiao")
    private Integer equipMinDefense = 0;

    /**
     * 装备最大防御
     */
    @Column(name = "p_zbfy_da")
    private Integer equipMaxDefense = 0;

    /**
     * 师徒类型 1师父 2徒弟
     */
    @Column(name = "p_teacher_type")
    private Integer teacherType = 0;

    /**
     * 师父角色ID
     */
    @Column(name = "p_teacher")
    private Integer teacher = 0;

    /**
     * 是否已婚 1没结婚 2结婚
     */
    @Column(name = "p_harness")
    private Integer harness = 1;

    /**
     * 伴侣ID
     */
    @Column(name = "p_fere")
    private Integer fere = 0;

    /**
     * 称号
     */
    @Column(name = "p_title", length = 20)
    private String title;

    /**
     * 称号名称
     */
    @Column(name = "p_title_name", length = 50)
    private String titleName;

    /**
     * 角色种族 1人 2妖
     */
    @Column(name = "p_race")
    private Integer race = 1;

    /**
     * 出生地
     */
    @Column(name = "p_born")
    private Integer born = 0;

    /**
     * 阵营
     */
    @Column(name = "p_camp")
    private Integer camp = 0;

    /**
     * 阵营名称
     */
    @Column(name = "p_camp_name", length = 50)
    private String campName;

    /**
     * 门派
     */
    @Column(name = "p_school", length = 20)
    private String school;

    /**
     * 门派名称
     */
    @Column(name = "p_school_name", length = 50)
    private String schoolName;

    /**
     * 经验
     */
    @Column(name = "p_experience", length = 20)
    private String experience = "0";

    /**
     * 本级经验
     */
    @Column(name = "p_benji_experience", length = 20)
    private String benjiExperience = "0";

    /**
     * 下级经验
     */
    @Column(name = "p_xia_experience", length = 20)
    private String xiaExperience = "52";

    /**
     * 银子
     */
    @Column(name = "p_silver", length = 5)
    private String silver = "0";

    /**
     * 铜钱（单位：文）
     */
    @Column(name = "p_copper", length = 20)
    private String copper = "100";

    /**
     * 仓库
     */
    @Column(name = "p_depot")
    private Integer depot = 0;

    /**
     * PK值
     */
    @Column(name = "p_pk_value")
    private Integer pkValue = 0;

    /**
     * PK状态 1红 2白
     */
    @Column(name = "p_pks")
    private Integer pks = 2;

    /**
     * 掉落爆率倍数
     */
    @Column(name = "p_drop_multiple")
    private Double dropMultiple = 1.0;

    /**
     * PK名称改变时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "p_pk_changetime")
    private LocalDateTime pkChangeTime;

    /**
     * 标识是否处于主动攻击状态 0否 1是
     */
    @Column(name = "p_isInitiative")
    private Integer isInitiative = 0;

    /**
     * 标识是否处于被动攻击状态 0否 1是
     */
    @Column(name = "p_isPassivity")
    private Integer isPassivity = 0;

    /**
     * 所在场景ID
     */
    @Column(name = "p_map", length = 5)
    private String map;

    /**
     * 是否组队 0否 1是
     */
    @Column(name = "p_procession")
    private Integer procession = 0;

    /**
     * 队伍编号
     */
    @Column(name = "p_procession_numner", length = 5)
    private String processionNumber;

    /**
     * 帮派
     */
    @Column(name = "p_tong")
    private Integer tong = 0;

    /**
     * 帮派名称
     */
    @Column(name = "p_tong_name", length = 50)
    private String tongName;

    /**
     * 包裹容量
     */
    @Column(name = "p_wrap_content")
    private Integer wrapContent = 20;

    /**
     * 包裹剩余容量
     */
    @Column(name = "p_wrap_spare")
    private Integer wrapSpare = 20;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 删除标志位 0表示正常状态 -1表示角色已经被删除 1表示删除待定状态
     */
    @Column(name = "delete_flag")
    private Integer deleteFlag = 0;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "delete_time")
    private LocalDateTime deleteTime;

    /**
     * 师父等级
     */
    @Column(name = "te_level")
    private Integer teLevel = 0;

    /**
     * 传功
     */
    @Column(name = "chuangong", length = 50)
    private String chuangong;

    /**
     * 登录状态
     */
    @Column(name = "login_state")
    private Integer loginState = 0;

    /**
     * 杀人数量
     */
    @Column(name = "p_kill_num")
    private Integer killNum = 0;

    /**
     * 最后收徒时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_shoutu_time")
    private LocalDateTime lastShoutuTime;

    /**
     * 玩家状态 1为新手 0非新手 11为新客引导 10为新客不引导
     */
    @Column(name = "player_state_by_new")
    private Integer playerStateByNew = 0;

    /**
     * 额外攻击力（装备加成）
     */
    @Transient
    private Integer extraAttack = 0;

    /**
     * 额外防御力（装备加成）
     */
    @Transient
    private Integer extraDefense = 0;

    /**
     * 额外敏捷（装备加成）
     */
    @Transient
    private Integer extraAgility = 0;

    /**
     * 额外智力（装备加成）
     */
    @Transient
    private Integer extraIntelligence = 0;

    /**
     * 额外体质（装备加成）
     */
    @Transient
    private Integer extraConstitution = 0;

    /**
     * 可分配属性点
     */
    @Transient
    private Integer attributePoints = 0;

    /**
     * 地图X坐标
     */
    @Transient
    private Integer mapX = 0;

    /**
     * 地图Y坐标
     */
    @Transient
    private Integer mapY = 0;

    // 自定义构造函数
    public PlayerRole(String name, Integer sex, User user) {
        this.name = name;
        this.sex = sex;
        this.user = user;
    }

    // 业务方法

    /**
     * 获取角色名称
     */
    public String getRoleName() {
        return this.name;
    }

    /**
     * 获取当前生命值
     */
    public Integer getCurrentHp() {
        return this.hp;
    }

    /**
     * 设置当前生命值
     */
    public void setCurrentHp(Integer hp) {
        this.hp = hp;
    }

    /**
     * 获取最大生命值
     */
    public Integer getMaxHp() {
        return this.maxHp;
    }

    /**
     * 设置最大生命值
     */
    public void setMaxHp(Integer maxHp) {
        this.maxHp = maxHp;
    }

    /**
     * 获取当前法力值
     */
    public Integer getCurrentMp() {
        return this.mp;
    }

    /**
     * 设置当前法力值
     */
    public void setCurrentMp(Integer mp) {
        this.mp = mp;
    }

    /**
     * 获取最大法力值
     */
    public Integer getMaxMp() {
        return this.maxMp;
    }

    /**
     * 设置最大法力值
     */
    public void setMaxMp(Integer maxMp) {
        this.maxMp = maxMp;
    }

    /**
     * 获取经验值
     */
    public Long getExperience() {
        return Long.parseLong(this.experience);
    }

    /**
     * 设置经验值
     */
    public void setExperience(Long experience) {
        this.experience = String.valueOf(experience);
    }

    /**
     * 获取下一级所需经验
     */
    public Long getNextLevelExp() {
        return Long.parseLong(this.xiaExperience);
    }

    /**
     * 设置下一级所需经验
     */
    public void setNextLevelExp(Long exp) {
        this.xiaExperience = String.valueOf(exp);
    }

    /**
     * 获取铜钱
     */
    public Long getCopper() {
        return Long.parseLong(this.copper);
    }

    /**
     * 设置铜钱
     */
    public void setCopper(Long copper) {
        this.copper = String.valueOf(copper);
    }

    /**
     * 获取银子
     */
    public Integer getSilver() {
        return Integer.parseInt(this.silver);
    }

    /**
     * 设置银子
     */
    public void setSilver(Integer silver) {
        this.silver = String.valueOf(silver);
    }

    /**
     * 获取基础攻击力
     */
    public Integer getBaseAttack() {
        return this.attack;
    }

    /**
     * 获取总攻击力（基础+装备加成）
     */
    public Integer getTotalAttack() {
        return getBaseAttack() + (this.extraAttack != null ? this.extraAttack : 0);
    }

    /**
     * 获取基础防御力
     */
    public Integer getBaseDefense() {
        return this.defense;
    }

    /**
     * 获取总防御力（基础+装备加成）
     */
    public Integer getTotalDefense() {
        return getBaseDefense() + (this.extraDefense != null ? this.extraDefense : 0);
    }

    /**
     * 获取基础敏捷
     */
    public Integer getBaseAgility() {
        return this.agile;
    }

    /**
     * 获取总敏捷（基础+装备加成）
     */
    public Integer getTotalAgility() {
        return getBaseAgility() + (this.extraAgility != null ? this.extraAgility : 0);
    }

    /**
     * 获取基础智力
     */
    public Integer getBaseIntelligence() {
        return this.savvy;
    }

    /**
     * 获取总智力（基础+装备加成）
     */
    public Integer getTotalIntelligence() {
        return getBaseIntelligence() + (this.extraIntelligence != null ? this.extraIntelligence : 0);
    }

    /**
     * 获取基础体质
     */
    public Integer getBaseConstitution() {
        return this.physique;
    }

    /**
     * 获取总体质（基础+装备加成）
     */
    public Integer getTotalConstitution() {
        return getBaseConstitution() + (this.extraConstitution != null ? this.extraConstitution : 0);
    }

    /**
     * 判断角色是否存活
     */
    public boolean isAlive() {
        return getCurrentHp() > 0;
    }

    /**
     * 判断是否可以升级
     */
    public boolean canLevelUp() {
        return getExperience() >= getNextLevelExp();
    }

    /**
     * 获取当前场景ID
     */
    public String getCurrentScene() {
        return this.map;
    }

    /**
     * 设置当前场景ID
     */
    public void setCurrentScene(String sceneId) {
        this.map = sceneId;
    }

    /**
     * 获取地图X坐标
     */
    public Integer getMapX() {
        return this.mapX != null ? this.mapX : 0;
    }

    /**
     * 设置地图X坐标
     */
    public void setMapX(Integer x) {
        this.mapX = x;
    }

    /**
     * 获取地图Y坐标
     */
    public Integer getMapY() {
        return this.mapY != null ? this.mapY : 0;
    }

    /**
     * 设置地图Y坐标
     */
    public void setMapY(Integer y) {
        this.mapY = y;
    }

    /**
     * 获取可分配属性点
     */
    public Integer getAttributePoints() {
        return this.attributePoints != null ? this.attributePoints : 0;
    }

    /**
     * 设置可分配属性点
     */
    public void setAttributePoints(Integer points) {
        this.attributePoints = points;
    }

    /**
     * 获取背包容量
     */
    public Integer getWrapContent() {
        return this.wrapContent;
    }

    /**
     * 获取仓库容量
     */
    public Integer getWrapSpare() {
        return this.wrapSpare;
    }

    /**
     * 获取性别名称
     */
    public String getSexName() {
        return this.sex == 1 ? "男" : "女";
    }

    /**
     * 获取种族名称
     */
    public String getRaceName() {
        if (this.race == null) return "人族";
        switch (this.race) {
            case 1: return "人族";
            case 2: return "妖族";
            case 3: return "魔族";
            default: return "人族";
        }
    }

    /**
     * 获取阵营名称
     */
    public String getCampName() {
        if (this.camp == null) return "中立";
        switch (this.camp) {
            case 1: return "正派";
            case 2: return "邪派";
            case 3: return "中立";
            default: return "中立";
        }
    }

    /**
     * 计算战斗力
     */
    public Integer getCombatPower() {
        return getTotalAttack() + getTotalDefense() + getTotalAgility() + getTotalIntelligence() + getTotalConstitution();
    }

    /**
     * 获取等级
     */
    public Integer getGrade() {
        return this.grade;
    }

    /**
     * 设置等级
     */
    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    /**
     * 是否在线
     */
    public boolean isOnline() {
        return this.loginState != null && this.loginState == 1;
    }

    /**
     * 设置在线状态
     */
    public void setOnlineStatus(boolean online) {
        this.loginState = online ? 1 : 0;
    }

    /**
     * 恢复生命值
     */
    public void restoreHp(int amount) {
        int newHp = Math.min(getCurrentHp() + amount, getMaxHp());
        setCurrentHp(newHp);
    }

    /**
     * 恢复法力值
     */
    public void restoreMp(int amount) {
        int newMp = Math.min(getCurrentMp() + amount, getMaxMp());
        setCurrentMp(newMp);
    }

    /**
     * 减少生命值
     */
    public void takeDamage(int damage) {
        int newHp = Math.max(getCurrentHp() - damage, 0);
        setCurrentHp(newHp);
    }

    /**
     * 消耗法力值
     */
    public void consumeMp(int amount) {
        int newMp = Math.max(getCurrentMp() - amount, 0);
        setCurrentMp(newMp);
    }
}
