-- 商店数据初始化脚本

-- 插入测试商店数据
INSERT INTO shop (id, name, description, type, scene_id, npc_id, required_level, required_race, required_sex, active) VALUES
(1, '新手商店', '为新手冒险者提供基础装备和道具', 1, 1, 1, 1, 0, 0, 1),
(2, '武器店', '专门出售各种武器装备', 2, 1, 2, 1, 0, 0, 1),
(3, '药剂店', '出售各种恢复药剂和消耗品', 3, 1, 3, 1, 0, 0, 1),
(4, '高级装备店', '出售高级装备，需要一定等级', 4, 1, 4, 10, 0, 0, 1),
(5, '巫族专门店', '专为巫族提供的特殊商品', 5, 1, 5, 5, 1, 0, 1),
(6, '妖族专门店', '专为妖族提供的特殊商品', 5, 1, 6, 5, 2, 0, 1);

-- 插入商店商品数据
INSERT INTO shop_item (id, shop_id, item_type, item_id, price_copper, stock, discount, active) VALUES
-- 新手商店商品
(1, 1, 1, 1, 100, 10, 0, 1),  -- 新手木剑
(2, 1, 1, 2, 80, 10, 0, 1),   -- 布帽
(3, 1, 1, 3, 120, 10, 0, 1),  -- 布衣
(4, 1, 1, 4, 60, 10, 0, 1),   -- 草鞋
(5, 1, 2, 1, 50, 99, 0, 1),   -- 生命药水
(6, 1, 2, 2, 50, 99, 0, 1),   -- 魔法药水

-- 武器店商品
(7, 2, 1, 1, 100, 5, 0, 1),    -- 新手木剑
(8, 2, 1, 5, 500, 3, 0, 1),    -- 铁剑
(9, 2, 1, 11, 1200, 1, 0, 1),  -- 精钢剑

-- 药剂店商品
(10, 3, 2, 1, 45, 99, 10, 1), -- 生命药水(打折)
(11, 3, 2, 2, 45, 99, 10, 1), -- 魔法药水(打折)
(12, 3, 2, 3, 90, 50, 10, 1), -- 经验丹(打折)

-- 高级装备店商品
(13, 4, 1, 11, 1200, 2, 0, 1), -- 精钢剑
(14, 4, 1, 12, 1000, 2, 0, 1), -- 精钢盔
(15, 4, 1, 13, 1500, 2, 0, 1), -- 锁子甲
(16, 4, 1, 14, 800, 2, 0, 1),  -- 战靴

-- 巫族专门店商品
(17, 5, 1, 17, 400, 3, 0, 1),  -- 法师帽
(18, 5, 1, 18, 600, 3, 0, 1),  -- 法师袍
(19, 5, 2, 2, 40, 99, 20, 1), -- 魔法药水(大折扣)

-- 妖族专门店商品
(20, 6, 1, 5, 450, 3, 10, 1),  -- 铁剑(打折)
(21, 6, 1, 8, 270, 3, 10, 1),  -- 皮靴(打折)
(22, 6, 2, 1, 40, 99, 20, 1); -- 生命药水(大折扣)
