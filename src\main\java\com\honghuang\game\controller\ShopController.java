package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.Shop;
import com.honghuang.game.entity.ShopItem;
import com.honghuang.game.service.ShopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 商店控制器
 */
@RestController
@RequestMapping("/api/shop")
public class ShopController {

    @Autowired
    private ShopService shopService;

    /**
     * 获取所有可用商店
     */
    @GetMapping("/all")
    public Result<List<Shop>> getAllShops() {
        try {
            List<Shop> shops = shopService.getAllShops();
            return Result.success("获取商店列表成功", shops);
        } catch (Exception e) {
            return Result.error(500, "获取商店列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色可访问的商店
     */
    @GetMapping("/accessible")
    public Result<List<Shop>> getAccessibleShops(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Shop> shops = shopService.getAccessibleShops(currentRoleId);
            return Result.success("获取可访问商店成功", shops);
        } catch (Exception e) {
            return Result.error(500, "获取可访问商店失败: " + e.getMessage());
        }
    }

    /**
     * 根据场景获取商店
     */
    @GetMapping("/scene/{sceneId}")
    public Result<List<Shop>> getShopsByScene(@PathVariable Integer sceneId) {
        try {
            List<Shop> shops = shopService.getShopsByScene(sceneId);
            return Result.success("获取场景商店成功", shops);
        } catch (Exception e) {
            return Result.error(500, "获取场景商店失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取商店
     */
    @GetMapping("/type/{type}")
    public Result<List<Shop>> getShopsByType(@PathVariable Integer type) {
        try {
            List<Shop> shops = shopService.getShopsByType(type);
            return Result.success("获取指定类型商店成功", shops);
        } catch (Exception e) {
            return Result.error(500, "获取指定类型商店失败: " + e.getMessage());
        }
    }

    /**
     * 获取商店详情
     */
    @GetMapping("/{shopId}")
    public Result<Shop> getShopDetail(@PathVariable Integer shopId) {
        try {
            Shop shop = shopService.getShopDetail(shopId);
            return Result.success("获取商店详情成功", shop);
        } catch (Exception e) {
            return Result.error(500, "获取商店详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取商店商品列表
     */
    @GetMapping("/{shopId}/items")
    public Result<List<ShopItem>> getShopItems(@PathVariable Integer shopId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            List<ShopItem> items;
            if (currentRoleId != null) {
                // 检查访问权限
                if (!shopService.checkShopAccess(shopId, currentRoleId)) {
                    return Result.error(403, "无权限访问该商店");
                }
                items = shopService.getAvailableItems(shopId, currentRoleId);
            } else {
                items = shopService.getShopItems(shopId);
            }
            
            return Result.success("获取商店商品成功", items);
        } catch (Exception e) {
            return Result.error(500, "获取商店商品失败: " + e.getMessage());
        }
    }

    /**
     * 购买商品
     */
    @PostMapping("/buy/{itemId}")
    public Result<Void> buyItem(@PathVariable Integer itemId,
                               @RequestParam(defaultValue = "1") Integer quantity,
                               HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = shopService.buyItem(currentRoleId, itemId, quantity);
            if (success) {
                return Result.success("购买商品成功");
            } else {
                return Result.error(400, "购买商品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "购买商品失败: " + e.getMessage());
        }
    }

    /**
     * 出售物品
     */
    @PostMapping("/sell/{itemId}")
    public Result<Void> sellItem(@PathVariable Integer itemId,
                                @RequestParam(defaultValue = "1") Integer quantity,
                                HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = shopService.sellItem(currentRoleId, itemId, quantity);
            if (success) {
                return Result.success("出售物品成功");
            } else {
                return Result.error(400, "出售物品失败");
            }
        } catch (Exception e) {
            return Result.error(500, "出售物品失败: " + e.getMessage());
        }
    }

    /**
     * 刷新商店
     */
    @PostMapping("/{shopId}/refresh")
    public Result<Void> refreshShop(@PathVariable Integer shopId) {
        try {
            shopService.refreshShop(shopId);
            return Result.success("刷新商店成功");
        } catch (Exception e) {
            return Result.error(500, "刷新商店失败: " + e.getMessage());
        }
    }

    /**
     * 获取限时商品
     */
    @GetMapping("/limited")
    public Result<List<ShopItem>> getLimitedTimeItems() {
        try {
            List<ShopItem> items = shopService.getLimitedTimeItems();
            return Result.success("获取限时商品成功", items);
        } catch (Exception e) {
            return Result.error(500, "获取限时商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取折扣商品
     */
    @GetMapping("/discount")
    public Result<List<ShopItem>> getDiscountItems() {
        try {
            List<ShopItem> items = shopService.getDiscountItems();
            return Result.success("获取折扣商品成功", items);
        } catch (Exception e) {
            return Result.error(500, "获取折扣商品失败: " + e.getMessage());
        }
    }

    /**
     * 根据价格范围搜索商品
     */
    @GetMapping("/{shopId}/search/price")
    public Result<List<ShopItem>> searchItemsByPrice(@PathVariable Integer shopId,
                                                    @RequestParam Long minPrice,
                                                    @RequestParam Long maxPrice) {
        try {
            List<ShopItem> items = shopService.searchItemsByPrice(shopId, minPrice, maxPrice);
            return Result.success("搜索商品成功", items);
        } catch (Exception e) {
            return Result.error(500, "搜索商品失败: " + e.getMessage());
        }
    }

    /**
     * 根据名称搜索商品
     */
    @GetMapping("/search")
    public Result<List<ShopItem>> searchItemsByName(@RequestParam String name) {
        try {
            List<ShopItem> items = shopService.searchItemsByName(name);
            return Result.success("搜索商品成功", items);
        } catch (Exception e) {
            return Result.error(500, "搜索商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取热销商品
     */
    @GetMapping("/{shopId}/popular")
    public Result<List<ShopItem>> getPopularItems(@PathVariable Integer shopId) {
        try {
            List<ShopItem> items = shopService.getPopularItems(shopId);
            return Result.success("获取热销商品成功", items);
        } catch (Exception e) {
            return Result.error(500, "获取热销商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取新商品
     */
    @GetMapping("/{shopId}/new")
    public Result<List<ShopItem>> getNewItems(@PathVariable Integer shopId) {
        try {
            List<ShopItem> items = shopService.getNewItems(shopId);
            return Result.success("获取新商品成功", items);
        } catch (Exception e) {
            return Result.error(500, "获取新商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取商店统计信息
     */
    @GetMapping("/{shopId}/statistics")
    public Result<Map<String, Object>> getShopStatistics(@PathVariable Integer shopId) {
        try {
            Map<String, Object> stats = shopService.getShopStatistics(shopId);
            return Result.success("获取商店统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取商店统计失败: " + e.getMessage());
        }
    }
}
