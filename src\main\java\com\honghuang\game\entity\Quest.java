package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 任务实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "quest")
public class Quest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 任务名称
     */
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 任务类型 1杀怪任务 2收集任务 3对话任务 4护送任务 5探索任务
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 任务等级要求
     */
    @Column(name = "required_level")
    private Integer requiredLevel = 1;

    /**
     * 种族要求 0无要求 1巫族 2妖族
     */
    @Column(name = "required_race")
    private Integer requiredRace = 0;

    /**
     * 性别要求 0无要求 1男性 2女性
     */
    @Column(name = "required_sex")
    private Integer requiredSex = 0;

    /**
     * 前置任务ID
     */
    @Column(name = "prerequisite_quest_id")
    private Integer prerequisiteQuestId;

    /**
     * 任务描述
     */
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 任务目标描述
     */
    @Column(name = "objective", length = 500)
    private String objective;

    /**
     * 目标类型 1杀怪 2收集物品 3对话NPC 4到达地点
     */
    @Column(name = "objective_type")
    private Integer objectiveType;

    /**
     * 目标ID（怪物ID、物品ID、NPCID等）
     */
    @Column(name = "objective_id")
    private Integer objectiveId;

    /**
     * 目标数量
     */
    @Column(name = "objective_count")
    private Integer objectiveCount = 1;

    /**
     * 目标场景ID
     */
    @Column(name = "objective_scene_id")
    private Integer objectiveSceneId;

    /**
     * 经验奖励
     */
    @Column(name = "exp_reward")
    private Long expReward = 0L;

    /**
     * 铜钱奖励
     */
    @Column(name = "copper_reward")
    private Long copperReward = 0L;

    /**
     * 银两奖励
     */
    @Column(name = "silver_reward")
    private Long silverReward = 0L;

    /**
     * 元宝奖励
     */
    @Column(name = "gold_reward")
    private Long goldReward = 0L;

    /**
     * 物品奖励ID
     */
    @Column(name = "item_reward_id")
    private Integer itemRewardId;

    /**
     * 物品奖励数量
     */
    @Column(name = "item_reward_count")
    private Integer itemRewardCount = 1;

    /**
     * 是否可重复 0不可重复 1可重复
     */
    @Column(name = "repeatable")
    private Integer repeatable = 0;

    /**
     * 重复间隔（小时）
     */
    @Column(name = "repeat_interval")
    private Integer repeatInterval = 24;

    /**
     * 任务发布NPC ID
     */
    @Column(name = "giver_npc_id")
    private Integer giverNpcId;

    /**
     * 任务完成NPC ID
     */
    @Column(name = "finisher_npc_id")
    private Integer finisherNpcId;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    // 业务方法

    /**
     * 获取任务类型名称
     */
    public String getTypeName() {
        switch (type) {
            case 1: return "杀怪任务";
            case 2: return "收集任务";
            case 3: return "对话任务";
            case 4: return "护送任务";
            case 5: return "探索任务";
            default: return "未知";
        }
    }

    /**
     * 获取目标类型名称
     */
    public String getObjectiveTypeName() {
        switch (objectiveType) {
            case 1: return "击败怪物";
            case 2: return "收集物品";
            case 3: return "对话NPC";
            case 4: return "到达地点";
            default: return "未知";
        }
    }

    /**
     * 检查角色是否可以接取此任务
     */
    public boolean canAccept(PlayerRole role) {
        // 检查等级要求
        if (role.getLevel() < requiredLevel) {
            return false;
        }
        
        // 检查种族要求
        if (requiredRace != 0 && !role.getRace().equals(requiredRace)) {
            return false;
        }
        
        // 检查性别要求
        if (requiredSex != 0 && !role.getSex().equals(requiredSex)) {
            return false;
        }
        
        return true;
    }

    /**
     * 是否为杀怪任务
     */
    public boolean isKillQuest() {
        return type == 1;
    }

    /**
     * 是否为收集任务
     */
    public boolean isCollectQuest() {
        return type == 2;
    }

    /**
     * 是否为对话任务
     */
    public boolean isTalkQuest() {
        return type == 3;
    }

    /**
     * 是否为护送任务
     */
    public boolean isEscortQuest() {
        return type == 4;
    }

    /**
     * 是否为探索任务
     */
    public boolean isExploreQuest() {
        return type == 5;
    }

    /**
     * 获取种族要求名称
     */
    public String getRequiredRaceName() {
        switch (requiredRace) {
            case 0: return "无要求";
            case 1: return "巫族";
            case 2: return "妖族";
            default: return "未知";
        }
    }

    /**
     * 获取性别要求名称
     */
    public String getRequiredSexName() {
        switch (requiredSex) {
            case 0: return "无要求";
            case 1: return "男性";
            case 2: return "女性";
            default: return "未知";
        }
    }

    /**
     * 计算总奖励经验
     */
    public Long getTotalExpReward() {
        return expReward;
    }

    /**
     * 计算总奖励金钱（转换为铜钱）
     */
    public Long getTotalCopperReward() {
        return copperReward + silverReward * 100 + goldReward * 10000;
    }

    /**
     * 是否有物品奖励
     */
    public boolean hasItemReward() {
        return itemRewardId != null && itemRewardId > 0;
    }

    /**
     * 获取完整的任务目标描述
     */
    public String getFullObjective() {
        if (objectiveCount > 1) {
            return objective + " (" + objectiveCount + ")";
        }
        return objective;
    }
}
