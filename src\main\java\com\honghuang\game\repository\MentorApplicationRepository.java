package com.honghuang.game.repository;

import com.honghuang.game.entity.MentorApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 师徒申请数据访问层
 */
@Repository
public interface MentorApplicationRepository extends JpaRepository<MentorApplication, Integer> {

    /**
     * 根据申请者ID查找申请列表
     */
    List<MentorApplication> findByApplicantIdOrderByApplyTimeDesc(Integer applicantId);

    /**
     * 根据目标角色ID查找申请列表
     */
    List<MentorApplication> findByTargetIdOrderByApplyTimeDesc(Integer targetId);

    /**
     * 根据申请者ID和目标ID查找申请
     */
    Optional<MentorApplication> findByApplicantIdAndTargetIdAndStatus(Integer applicantId, Integer targetId, Integer status);

    /**
     * 根据申请类型和状态查找申请
     */
    List<MentorApplication> findByApplicationTypeAndStatusOrderByApplyTimeDesc(Integer applicationType, Integer status);

    /**
     * 查找待处理的拜师申请
     */
    @Query("SELECT ma FROM MentorApplication ma WHERE ma.targetId = :targetId AND ma.applicationType = 1 AND ma.status = 0 ORDER BY ma.applyTime ASC")
    List<MentorApplication> findPendingApprenticeApplications(@Param("targetId") Integer targetId);

    /**
     * 查找待处理的收徒申请
     */
    @Query("SELECT ma FROM MentorApplication ma WHERE ma.targetId = :targetId AND ma.applicationType = 2 AND ma.status = 0 ORDER BY ma.applyTime ASC")
    List<MentorApplication> findPendingMentorApplications(@Param("targetId") Integer targetId);

    /**
     * 查找角色的所有待处理申请（作为目标）
     */
    @Query("SELECT ma FROM MentorApplication ma WHERE ma.targetId = :targetId AND ma.status = 0 ORDER BY ma.applyTime ASC")
    List<MentorApplication> findPendingApplicationsForTarget(@Param("targetId") Integer targetId);

    /**
     * 查找过期的申请
     */
    @Query("SELECT ma FROM MentorApplication ma WHERE ma.status = 0 AND ma.applyTime <= :cutoffTime")
    List<MentorApplication> findExpiredApplications(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计申请者的申请数量
     */
    @Query("SELECT COUNT(ma) FROM MentorApplication ma WHERE ma.applicantId = :applicantId")
    long countByApplicantId(@Param("applicantId") Integer applicantId);

    /**
     * 统计目标角色的申请数量
     */
    @Query("SELECT COUNT(ma) FROM MentorApplication ma WHERE ma.targetId = :targetId")
    long countByTargetId(@Param("targetId") Integer targetId);

    /**
     * 统计待处理申请数量
     */
    @Query("SELECT COUNT(ma) FROM MentorApplication ma WHERE ma.targetId = :targetId AND ma.status = 0")
    long countPendingApplications(@Param("targetId") Integer targetId);

    /**
     * 统计指定类型的申请数量
     */
    @Query("SELECT COUNT(ma) FROM MentorApplication ma WHERE ma.applicantId = :applicantId AND ma.applicationType = :applicationType")
    long countByApplicantIdAndType(@Param("applicantId") Integer applicantId, @Param("applicationType") Integer applicationType);

    /**
     * 更新申请状态
     */
    @Modifying
    @Query("UPDATE MentorApplication ma SET ma.status = :status, ma.processTime = :processTime WHERE ma.id = :applicationId")
    int updateStatus(@Param("applicationId") Integer applicationId, @Param("status") Integer status, @Param("processTime") LocalDateTime processTime);

    /**
     * 批量过期申请
     */
    @Modifying
    @Query("UPDATE MentorApplication ma SET ma.status = 3, ma.processTime = CURRENT_TIMESTAMP WHERE ma.status = 0 AND ma.applyTime <= :cutoffTime")
    int expireApplications(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除申请者的所有申请
     */
    @Modifying
    @Query("DELETE FROM MentorApplication ma WHERE ma.applicantId = :applicantId")
    int deleteByApplicantId(@Param("applicantId") Integer applicantId);

    /**
     * 删除目标角色的所有申请
     */
    @Modifying
    @Query("DELETE FROM MentorApplication ma WHERE ma.targetId = :targetId")
    int deleteByTargetId(@Param("targetId") Integer targetId);

    /**
     * 查找最近的申请
     */
    @Query("SELECT ma FROM MentorApplication ma WHERE ma.targetId = :targetId ORDER BY ma.applyTime DESC")
    List<MentorApplication> findRecentApplications(@Param("targetId") Integer targetId);

    /**
     * 查找指定时间范围内的申请
     */
    @Query("SELECT ma FROM MentorApplication ma WHERE ma.applyTime BETWEEN :startTime AND :endTime ORDER BY ma.applyTime DESC")
    List<MentorApplication> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找等级差距在指定范围内的申请
     */
    @Query("SELECT ma FROM MentorApplication ma WHERE ma.status = 0 AND " +
           "((ma.applicationType = 1 AND (ma.targetLevel - ma.applicantLevel) BETWEEN :minGap AND :maxGap) OR " +
           "(ma.applicationType = 2 AND (ma.applicantLevel - ma.targetLevel) BETWEEN :minGap AND :maxGap))")
    List<MentorApplication> findByLevelGapRange(@Param("minGap") Integer minGap, @Param("maxGap") Integer maxGap);
}
