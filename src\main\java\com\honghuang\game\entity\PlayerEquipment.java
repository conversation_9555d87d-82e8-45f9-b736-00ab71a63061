package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 玩家装备实体类（玩家拥有的装备实例）
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "player_equipment")
public class PlayerEquipment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 玩家角色ID
     */
    @Column(name = "role_id")
    private Integer roleId;

    /**
     * 装备模板ID
     */
    @Column(name = "equipment_id")
    private Integer equipmentId;

    /**
     * 装备位置 0背包 1武器 2头盔 3衣服 4鞋子 5腰带 6项链 7戒指
     */
    @Column(name = "position")
    private Integer position = 0;

    /**
     * 背包位置索引（当position=0时有效）
     */
    @Column(name = "bag_index")
    private Integer bagIndex;

    /**
     * 数量
     */
    @Column(name = "quantity")
    private Integer quantity = 1;

    /**
     * 当前耐久度
     */
    @Column(name = "current_durability")
    private Integer currentDurability;

    /**
     * 最大耐久度
     */
    @Column(name = "max_durability")
    private Integer maxDurability;

    /**
     * 强化等级
     */
    @Column(name = "enhance_level")
    private Integer enhanceLevel = 0;

    /**
     * 是否绑定 0未绑定 1已绑定
     */
    @Column(name = "bound")
    private Integer bound = 0;

    /**
     * 获得时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "obtain_time")
    private LocalDateTime obtainTime;

    /**
     * 装备时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "equip_time")
    private LocalDateTime equipTime;

    /**
     * 装备模板信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "equipment_id", insertable = false, updatable = false)
    private Equipment equipment;

    // 业务方法

    /**
     * 是否已装备
     */
    public boolean isEquipped() {
        return position > 0;
    }

    /**
     * 是否在背包中
     */
    public boolean isInBag() {
        return position == 0;
    }

    /**
     * 装备到指定位置
     */
    public void equipTo(Integer position) {
        this.position = position;
        this.bagIndex = null;
        this.equipTime = LocalDateTime.now();
    }

    /**
     * 卸下装备到背包
     */
    public void unequipToBag(Integer bagIndex) {
        this.position = 0;
        this.bagIndex = bagIndex;
        this.equipTime = null;
    }

    /**
     * 获取装备位置名称
     */
    public String getPositionName() {
        switch (position) {
            case 0: return "背包";
            case 1: return "武器";
            case 2: return "头盔";
            case 3: return "衣服";
            case 4: return "鞋子";
            case 5: return "腰带";
            case 6: return "项链";
            case 7: return "戒指";
            default: return "未知";
        }
    }

    /**
     * 计算强化后的攻击力加成
     */
    public Integer getEnhancedAttackBonus() {
        if (equipment == null) return 0;
        return equipment.getAttackBonus() + enhanceLevel * 2;
    }

    /**
     * 计算强化后的防御力加成
     */
    public Integer getEnhancedDefenseBonus() {
        if (equipment == null) return 0;
        return equipment.getDefenseBonus() + enhanceLevel * 2;
    }

    /**
     * 计算强化后的敏捷加成
     */
    public Integer getEnhancedAgilityBonus() {
        if (equipment == null) return 0;
        return equipment.getAgilityBonus() + enhanceLevel;
    }

    /**
     * 计算强化后的智力加成
     */
    public Integer getEnhancedIntelligenceBonus() {
        if (equipment == null) return 0;
        return equipment.getIntelligenceBonus() + enhanceLevel;
    }

    /**
     * 计算强化后的体质加成
     */
    public Integer getEnhancedConstitutionBonus() {
        if (equipment == null) return 0;
        return equipment.getConstitutionBonus() + enhanceLevel;
    }

    /**
     * 计算强化后的生命值加成
     */
    public Integer getEnhancedHpBonus() {
        if (equipment == null) return 0;
        return equipment.getHpBonus() + enhanceLevel * 10;
    }

    /**
     * 计算强化后的法力值加成
     */
    public Integer getEnhancedMpBonus() {
        if (equipment == null) return 0;
        return equipment.getMpBonus() + enhanceLevel * 5;
    }

    /**
     * 装备是否损坏
     */
    public boolean isDamaged() {
        return currentDurability <= 0;
    }

    /**
     * 修理装备
     */
    public void repair() {
        this.currentDurability = this.maxDurability;
    }

    /**
     * 减少耐久度
     */
    public void reduceDurability(int amount) {
        this.currentDurability = Math.max(0, this.currentDurability - amount);
    }

    /**
     * 强化装备
     */
    public boolean enhance() {
        // 强化成功率随等级递减
        double successRate = Math.max(0.1, 1.0 - enhanceLevel * 0.1);
        if (Math.random() < successRate) {
            this.enhanceLevel++;
            return true;
        }
        return false;
    }

    /**
     * 获取强化成功率
     */
    public double getEnhanceSuccessRate() {
        return Math.max(0.1, 1.0 - enhanceLevel * 0.1);
    }

    /**
     * 计算强化费用
     */
    public long getEnhanceCost() {
        return (long) Math.pow(2, enhanceLevel) * 1000;
    }

    /**
     * 检查角色是否可以装备
     */
    public boolean canEquip(PlayerRole role) {
        if (equipment == null) return false;
        return equipment.canEquip(role);
    }

    /**
     * 获取装备完整名称（包含强化等级）
     */
    public String getFullName() {
        if (equipment == null) return "未知装备";
        String name = equipment.getName();
        if (enhanceLevel > 0) {
            name = "+" + enhanceLevel + " " + name;
        }
        return name;
    }
}
