package com.honghuang.game.repository;

import com.honghuang.game.entity.FactionMember;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 帮派成员数据访问接口
 */
@Repository
public interface FactionMemberRepository extends JpaRepository<FactionMember, Long> {
    
    /**
     * 根据帮派ID查找成员
     */
    List<FactionMember> findByFactionIdOrderByPositionAscJoinTimeAsc(Integer factionId);
    
    /**
     * 根据玩家ID查找帮派成员信息
     */
    Optional<FactionMember> findByPlayerId(Integer playerId);
    
    /**
     * 根据帮派ID和玩家ID查找
     */
    Optional<FactionMember> findByFactionIdAndPlayerId(Integer factionId, Integer playerId);
    
    /**
     * 查找帮派的帮主
     */
    @Query("SELECT fm FROM FactionMember fm WHERE fm.factionId = :factionId AND fm.position = 1")
    Optional<FactionMember> findLeaderByFactionId(@Param("factionId") Integer factionId);
    
    /**
     * 查找帮派的管理层
     */
    @Query("SELECT fm FROM FactionMember fm WHERE fm.factionId = :factionId AND fm.position <= 3 ORDER BY fm.position ASC")
    List<FactionMember> findManagersByFactionId(@Param("factionId") Integer factionId);
    
    /**
     * 根据职位查找成员
     */
    @Query("SELECT fm FROM FactionMember fm WHERE fm.factionId = :factionId AND fm.position = :position")
    List<FactionMember> findByFactionIdAndPosition(@Param("factionId") Integer factionId, @Param("position") Integer position);
    
    /**
     * 查找在线成员
     */
    @Query("SELECT fm FROM FactionMember fm WHERE fm.factionId = :factionId AND fm.isOnline = 1")
    List<FactionMember> findOnlineMembersByFactionId(@Param("factionId") Integer factionId);
    
    /**
     * 根据贡献度排序查找成员
     */
    @Query("SELECT fm FROM FactionMember fm WHERE fm.factionId = :factionId ORDER BY fm.totalContribution DESC")
    List<FactionMember> findByFactionIdOrderByContributionDesc(@Param("factionId") Integer factionId);
    
    /**
     * 查找本周贡献排行
     */
    @Query("SELECT fm FROM FactionMember fm WHERE fm.factionId = :factionId ORDER BY fm.weeklyContribution DESC")
    List<FactionMember> findByFactionIdOrderByWeeklyContributionDesc(@Param("factionId") Integer factionId);
    
    /**
     * 统计帮派成员数量
     */
    @Query("SELECT COUNT(fm) FROM FactionMember fm WHERE fm.factionId = :factionId")
    Long countByFactionId(@Param("factionId") Integer factionId);
    
    /**
     * 统计在线成员数量
     */
    @Query("SELECT COUNT(fm) FROM FactionMember fm WHERE fm.factionId = :factionId AND fm.isOnline = 1")
    Long countOnlineMembersByFactionId(@Param("factionId") Integer factionId);
    
    /**
     * 根据玩家名称模糊查找
     */
    @Query("SELECT fm FROM FactionMember fm WHERE fm.factionId = :factionId AND fm.playerName LIKE %:playerName%")
    List<FactionMember> findByFactionIdAndPlayerNameContaining(@Param("factionId") Integer factionId, @Param("playerName") String playerName);
}
