package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 装备实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "equipment")
public class Equipment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 装备名称
     */
    @Column(name = "name", length = 50)
    private String name;

    /**
     * 装备类型 1武器 2头盔 3衣服 4鞋子 5腰带 6项链 7戒指
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 装备品质 1白色 2绿色 3蓝色 4紫色 5橙色 6红色
     */
    @Column(name = "quality")
    private Integer quality = 1;

    /**
     * 使用等级要求
     */
    @Column(name = "required_level")
    private Integer requiredLevel = 1;

    /**
     * 性别要求 0无要求 1男性 2女性
     */
    @Column(name = "required_sex")
    private Integer requiredSex = 0;

    /**
     * 种族要求 0无要求 1巫族 2妖族
     */
    @Column(name = "required_race")
    private Integer requiredRace = 0;

    /**
     * 攻击力加成
     */
    @Column(name = "attack_bonus")
    private Integer attackBonus = 0;

    /**
     * 防御力加成
     */
    @Column(name = "defense_bonus")
    private Integer defenseBonus = 0;

    /**
     * 敏捷加成
     */
    @Column(name = "agility_bonus")
    private Integer agilityBonus = 0;

    /**
     * 智力加成
     */
    @Column(name = "intelligence_bonus")
    private Integer intelligenceBonus = 0;

    /**
     * 体质加成
     */
    @Column(name = "constitution_bonus")
    private Integer constitutionBonus = 0;

    /**
     * 生命值加成
     */
    @Column(name = "hp_bonus")
    private Integer hpBonus = 0;

    /**
     * 法力值加成
     */
    @Column(name = "mp_bonus")
    private Integer mpBonus = 0;

    /**
     * 装备描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 装备图片
     */
    @Column(name = "image", length = 100)
    private String image;

    /**
     * 出售价格
     */
    @Column(name = "sell_price")
    private Long sellPrice = 0L;

    /**
     * 购买价格
     */
    @Column(name = "buy_price")
    private Long buyPrice = 0L;

    /**
     * 耐久度
     */
    @Column(name = "durability")
    private Integer durability = 100;

    /**
     * 最大耐久度
     */
    @Column(name = "max_durability")
    private Integer maxDurability = 100;

    /**
     * 是否可交易 0不可交易 1可交易
     */
    @Column(name = "tradeable")
    private Integer tradeable = 1;

    /**
     * 是否绑定 0未绑定 1已绑定
     */
    @Column(name = "bound")
    private Integer bound = 0;

    /**
     * 强化等级
     */
    @Column(name = "enhance_level")
    private Integer enhanceLevel = 0;

    /**
     * 套装ID（0表示不属于套装）
     */
    @Column(name = "suit_id")
    private Integer suitId = 0;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    // 业务方法

    /**
     * 获取装备类型名称
     */
    public String getTypeName() {
        switch (type) {
            case 1: return "武器";
            case 2: return "头盔";
            case 3: return "衣服";
            case 4: return "鞋子";
            case 5: return "腰带";
            case 6: return "项链";
            case 7: return "戒指";
            default: return "未知";
        }
    }

    /**
     * 获取品质名称
     */
    public String getQualityName() {
        switch (quality) {
            case 1: return "白色";
            case 2: return "绿色";
            case 3: return "蓝色";
            case 4: return "紫色";
            case 5: return "橙色";
            case 6: return "红色";
            default: return "未知";
        }
    }

    /**
     * 获取品质颜色代码
     */
    public String getQualityColor() {
        switch (quality) {
            case 1: return "#FFFFFF"; // 白色
            case 2: return "#00FF00"; // 绿色
            case 3: return "#0080FF"; // 蓝色
            case 4: return "#8000FF"; // 紫色
            case 5: return "#FF8000"; // 橙色
            case 6: return "#FF0000"; // 红色
            default: return "#FFFFFF";
        }
    }

    /**
     * 检查角色是否可以装备
     */
    public boolean canEquip(PlayerRole role) {
        // 检查等级要求
        if (role.getLevel() < requiredLevel) {
            return false;
        }
        
        // 检查性别要求
        if (requiredSex != 0 && !role.getSex().equals(requiredSex)) {
            return false;
        }
        
        // 检查种族要求
        if (requiredRace != 0 && !role.getRace().equals(requiredRace)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算强化后的攻击力加成
     */
    public Integer getEnhancedAttackBonus() {
        return attackBonus + enhanceLevel * 2;
    }

    /**
     * 计算强化后的防御力加成
     */
    public Integer getEnhancedDefenseBonus() {
        return defenseBonus + enhanceLevel * 2;
    }

    /**
     * 计算强化后的其他属性加成
     */
    public Integer getEnhancedAgilityBonus() {
        return agilityBonus + enhanceLevel;
    }

    public Integer getEnhancedIntelligenceBonus() {
        return intelligenceBonus + enhanceLevel;
    }

    public Integer getEnhancedConstitutionBonus() {
        return constitutionBonus + enhanceLevel;
    }

    public Integer getEnhancedHpBonus() {
        return hpBonus + enhanceLevel * 10;
    }

    public Integer getEnhancedMpBonus() {
        return mpBonus + enhanceLevel * 5;
    }

    /**
     * 装备是否损坏
     */
    public boolean isDamaged() {
        return durability <= 0;
    }

    /**
     * 修理装备
     */
    public void repair() {
        this.durability = this.maxDurability;
    }

    /**
     * 减少耐久度
     */
    public void reduceDurability(int amount) {
        this.durability = Math.max(0, this.durability - amount);
    }
}
