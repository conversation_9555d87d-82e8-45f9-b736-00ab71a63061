<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技能系统 - 洪荒游戏</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f8ff;
            color: #333;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: #2c5aa0;
            border-bottom: 2px solid #66ccff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .skill-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            color: #2c5aa0;
            border-bottom-color: #66ccff;
            background-color: #f8f9fa;
        }
        
        .tab:hover {
            background-color: #f8f9fa;
        }
        
        .skill-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            min-height: 300px;
        }
        
        .skill-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .skill-item:hover {
            background-color: #f0f8ff;
            border-color: #66ccff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .skill-item.learned {
            border-color: #28a745;
            background-color: #f8fff8;
        }
        
        .skill-item.max-level {
            border-color: #ffc107;
            background-color: #fffdf8;
        }
        
        .skill-name {
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .skill-level {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .skill-description {
            color: #666;
            font-size: 13px;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .skill-stats {
            font-size: 12px;
            color: #888;
            margin-bottom: 10px;
        }
        
        .skill-actions {
            text-align: right;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 5px;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #2c5aa0;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1e3d6f;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .empty-message {
            text-align: center;
            color: #666;
            padding: 50px;
            font-style: italic;
            grid-column: 1 / -1;
        }
        
        .skill-detail {
            display: none;
            border: 2px solid #66ccff;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            background-color: #f8f9fa;
        }
        
        .skill-detail.show {
            display: block;
        }
        
        .skill-detail h3 {
            color: #2c5aa0;
            margin-top: 0;
        }
        
        .skill-detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        
        .skill-detail-item {
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .skill-detail-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }
        
        .skill-detail-value {
            color: #333;
        }
        
        .navigation {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .navigation a {
            color: #2c5aa0;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .navigation a:hover {
            text-decoration: underline;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .success {
            color: #155724;
            text-align: center;
            padding: 20px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s;
        }
        
        .skill-type-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            color: white;
            margin-bottom: 5px;
        }
        
        .skill-type-1 { background-color: #dc3545; } /* 攻击技能 */
        .skill-type-2 { background-color: #007bff; } /* 防御技能 */
        .skill-type-3 { background-color: #28a745; } /* 辅助技能 */
        .skill-type-4 { background-color: #6c757d; } /* 被动技能 */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>【技能系统】</h1>
        </div>
        
        <div id="message-area"></div>
        
        <div class="skill-tabs">
            <button class="tab active" onclick="switchTab('learned')">已学技能</button>
            <button class="tab" onclick="switchTab('available')">可学技能</button>
            <button class="tab" onclick="switchTab('life')">生活技能</button>
        </div>
        
        <div id="skill-content">
            <div id="loading" class="loading">加载中...</div>
            <div id="skill-grid" class="skill-grid" style="display: none;"></div>
        </div>
        
        <div id="skill-detail" class="skill-detail"></div>
        
        <div class="navigation">
            <a href="game.html">返回游戏</a>
            <a href="javascript:void(0)" onclick="refreshSkills()">刷新</a>
        </div>
    </div>

    <script>
        let currentTab = 'learned';
        let currentSkills = [];
        
        // 技能类型映射
        const skillTypeMap = {
            1: { name: '攻击', class: 'skill-type-1' },
            2: { name: '防御', class: 'skill-type-2' },
            3: { name: '辅助', class: 'skill-type-3' },
            4: { name: '被动', class: 'skill-type-4' }
        };
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSkills();
        });
        
        // 切换标签页
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            // 隐藏技能详情
            document.getElementById('skill-detail').classList.remove('show');
            
            loadSkills();
        }
        
        // 加载技能列表
        function loadSkills() {
            showLoading();
            
            let url = '';
            switch(currentTab) {
                case 'learned':
                    url = '/honghuang-game/api/skill/player';
                    break;
                case 'available':
                    url = '/honghuang-game/api/skill/available';
                    break;
                case 'life':
                    url = '/honghuang-game/api/skill/life';
                    break;
            }
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        displaySkills(data.data);
                    } else {
                        showError(data.message || '加载技能失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示技能列表
        function displaySkills(skills) {
            const skillGrid = document.getElementById('skill-grid');
            skillGrid.style.display = 'grid';
            
            if (!skills || skills.length === 0) {
                skillGrid.innerHTML = '<div class="empty-message">暂无技能</div>';
                return;
            }
            
            currentSkills = skills;
            let html = '';
            
            skills.forEach((skill, index) => {
                const skillType = skillTypeMap[skill.type] || { name: '未知', class: 'skill-type-1' };
                const isMaxLevel = skill.level >= skill.maxLevel;
                const itemClass = currentTab === 'learned' ? 
                    (isMaxLevel ? 'skill-item learned max-level' : 'skill-item learned') : 
                    'skill-item';
                
                html += `
                    <div class="${itemClass}" onclick="showSkillDetail(${index})">
                        <div class="skill-type-badge ${skillType.class}">${skillType.name}</div>
                        <div class="skill-name">${skill.name}</div>
                        ${currentTab === 'learned' ? `
                            <div class="skill-level">等级: ${skill.level}/${skill.maxLevel}</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${(skill.experience / skill.nextLevelExp) * 100}%"></div>
                            </div>
                        ` : `
                            <div class="skill-level">需要等级: ${skill.requiredLevel}</div>
                        `}
                        <div class="skill-description">${skill.description || '暂无描述'}</div>
                        ${getSkillStats(skill)}
                        <div class="skill-actions">
                            ${getSkillActions(skill)}
                        </div>
                    </div>
                `;
            });
            
            skillGrid.innerHTML = html;
        }
        
        // 获取技能属性信息
        function getSkillStats(skill) {
            let stats = [];
            if (skill.mpCost > 0) stats.push(`消耗MP: ${skill.mpCost}`);
            if (skill.cooldown > 0) stats.push(`冷却: ${skill.cooldown}秒`);
            if (skill.damageMin > 0) stats.push(`伤害: ${skill.damageMin}-${skill.damageMax}`);
            if (skill.healMin > 0) stats.push(`治疗: ${skill.healMin}-${skill.healMax}`);
            
            return stats.length > 0 ? `<div class="skill-stats">${stats.join(' | ')}</div>` : '';
        }
        
        // 获取技能操作按钮
        function getSkillActions(skill) {
            switch(currentTab) {
                case 'learned':
                    const canUpgrade = skill.level < skill.maxLevel;
                    return `
                        ${skill.type !== 4 ? `<button class="btn btn-success" onclick="useSkill(${skill.id}); event.stopPropagation();">使用</button>` : ''}
                        <button class="btn btn-warning" onclick="upgradeSkill(${skill.id}); event.stopPropagation();" ${!canUpgrade ? 'disabled' : ''}>
                            ${canUpgrade ? '升级' : '满级'}
                        </button>
                    `;
                case 'available':
                    return `<button class="btn btn-primary" onclick="learnSkill(${skill.id}); event.stopPropagation();">学习</button>`;
                case 'life':
                    return `<button class="btn btn-primary" onclick="practiceLifeSkill(${skill.id}); event.stopPropagation();">修炼</button>`;
                default:
                    return '';
            }
        }
        
        // 显示技能详情
        function showSkillDetail(index) {
            const skill = currentSkills[index];
            const detailDiv = document.getElementById('skill-detail');
            const skillType = skillTypeMap[skill.type] || { name: '未知', class: 'skill-type-1' };
            
            detailDiv.innerHTML = `
                <h3>${skill.name} <span class="skill-type-badge ${skillType.class}">${skillType.name}</span></h3>
                <div class="skill-detail-grid">
                    <div class="skill-detail-item">
                        <div class="skill-detail-label">技能等级</div>
                        <div class="skill-detail-value">${currentTab === 'learned' ? `${skill.level}/${skill.maxLevel}` : `需要${skill.requiredLevel}级`}</div>
                    </div>
                    <div class="skill-detail-item">
                        <div class="skill-detail-label">技能类型</div>
                        <div class="skill-detail-value">${skillType.name}技能</div>
                    </div>
                    ${skill.mpCost > 0 ? `
                    <div class="skill-detail-item">
                        <div class="skill-detail-label">魔法消耗</div>
                        <div class="skill-detail-value">${skill.mpCost} MP</div>
                    </div>
                    ` : ''}
                    ${skill.cooldown > 0 ? `
                    <div class="skill-detail-item">
                        <div class="skill-detail-label">冷却时间</div>
                        <div class="skill-detail-value">${skill.cooldown} 秒</div>
                    </div>
                    ` : ''}
                    ${skill.damageMin > 0 ? `
                    <div class="skill-detail-item">
                        <div class="skill-detail-label">伤害范围</div>
                        <div class="skill-detail-value">${skill.damageMin} - ${skill.damageMax}</div>
                    </div>
                    ` : ''}
                    ${skill.healMin > 0 ? `
                    <div class="skill-detail-item">
                        <div class="skill-detail-label">治疗范围</div>
                        <div class="skill-detail-value">${skill.healMin} - ${skill.healMax}</div>
                    </div>
                    ` : ''}
                </div>
                <p><strong>技能描述:</strong> ${skill.description || '暂无描述'}</p>
                <div style="text-align: right; margin-top: 15px;">
                    <button class="btn btn-warning" onclick="hideSkillDetail()">关闭</button>
                </div>
            `;
            
            detailDiv.classList.add('show');
        }
        
        // 隐藏技能详情
        function hideSkillDetail() {
            document.getElementById('skill-detail').classList.remove('show');
        }
        
        // 学习技能
        function learnSkill(skillId) {
            fetch('/honghuang-game/api/skill/learn', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ skillId: skillId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('技能学习成功！');
                    loadSkills();
                } else {
                    showError(data.message || '学习技能失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 升级技能
        function upgradeSkill(skillId) {
            fetch('/honghuang-game/api/skill/upgrade', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ skillId: skillId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('技能升级成功！');
                    loadSkills();
                } else {
                    showError(data.message || '升级技能失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 使用技能
        function useSkill(skillId) {
            fetch('/honghuang-game/api/skill/use', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ skillId: skillId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('技能使用成功！' + (data.data.effect || ''));
                } else {
                    showError(data.message || '使用技能失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 修炼生活技能
        function practiceLifeSkill(skillId) {
            fetch('/honghuang-game/api/skill/practice', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ skillId: skillId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('修炼成功！获得经验：' + (data.data.expGained || 0));
                    loadSkills();
                } else {
                    showError(data.message || '修炼失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 刷新技能
        function refreshSkills() {
            loadSkills();
        }
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('skill-grid').style.display = 'none';
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 显示错误消息
        function showError(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="error">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="success">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
