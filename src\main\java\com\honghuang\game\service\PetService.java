package com.honghuang.game.service;

import com.honghuang.game.entity.Pet;
import com.honghuang.game.entity.PetSkill;
import com.honghuang.game.repository.PetRepository;
import com.honghuang.game.repository.PetSkillRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 宠物服务类
 */
@Service
@Transactional
public class PetService {
    
    @Autowired
    private PetRepository petRepository;
    
    @Autowired
    private PetSkillRepository petSkillRepository;
    
    /**
     * 获取玩家的所有宠物
     */
    public List<Pet> getPlayerPets(Integer ownerId) {
        return petRepository.findByOwnerIdOrderByCaptureTimeDesc(ownerId);
    }
    
    /**
     * 获取玩家的出战宠物
     */
    public Optional<Pet> getActivePet(Integer ownerId) {
        return petRepository.findActivePetByOwnerId(ownerId);
    }
    
    /**
     * 捕获宠物
     */
    public Pet capturePet(Integer ownerId, String petName, Integer petType) {
        Pet pet = new Pet();
        pet.setOwnerId(ownerId);
        pet.setName(petName);
        pet.setNickname(petName);
        pet.setType(petType);
        
        // 随机生成宠物属性
        generateRandomPetStats(pet);
        
        return petRepository.save(pet);
    }
    
    /**
     * 设置出战宠物
     */
    public void setActivePet(Integer ownerId, Integer petId) {
        // 先取消所有宠物的出战状态
        List<Pet> pets = petRepository.findByOwnerIdOrderByCaptureTimeDesc(ownerId);
        for (Pet pet : pets) {
            pet.setIsActive(0);
            petRepository.save(pet);
        }
        
        // 设置指定宠物为出战状态
        Optional<Pet> petOpt = petRepository.findById(petId);
        if (petOpt.isPresent() && petOpt.get().getOwnerId().equals(ownerId)) {
            Pet pet = petOpt.get();
            pet.setIsActive(1);
            petRepository.save(pet);
        }
    }
    
    /**
     * 宠物升级
     */
    public boolean levelUpPet(Integer petId) {
        Optional<Pet> petOpt = petRepository.findById(petId);
        if (!petOpt.isPresent()) {
            return false;
        }
        
        Pet pet = petOpt.get();
        if (!pet.canLevelUp()) {
            return false;
        }
        
        // 升级逻辑
        pet.setLevel(pet.getLevel() + 1);
        pet.setExp(pet.getExp() - pet.getMaxExp());
        pet.setMaxExp(calculateMaxExp(pet.getLevel()));
        
        // 属性成长
        growPetStats(pet);
        
        petRepository.save(pet);
        return true;
    }
    
    /**
     * 喂养宠物
     */
    public void feedPet(Integer petId, Integer foodValue) {
        Optional<Pet> petOpt = petRepository.findById(petId);
        if (petOpt.isPresent()) {
            Pet pet = petOpt.get();
            pet.setHunger(Math.max(0, pet.getHunger() - foodValue));
            pet.setLoyalty(Math.min(100, pet.getLoyalty() + 1));
            petRepository.save(pet);
        }
    }
    
    /**
     * 宠物休息
     */
    public void restPet(Integer petId) {
        Optional<Pet> petOpt = petRepository.findById(petId);
        if (petOpt.isPresent()) {
            Pet pet = petOpt.get();
            pet.setFatigue(0);
            pet.setHp(pet.getMaxHp());
            pet.setMp(pet.getMaxMp());
            petRepository.save(pet);
        }
    }
    
    /**
     * 学习技能
     */
    public boolean learnSkill(Integer petId, Integer skillId) {
        Optional<Pet> petOpt = petRepository.findById(petId);
        Optional<PetSkill> skillOpt = petSkillRepository.findById(skillId);
        
        if (!petOpt.isPresent() || !skillOpt.isPresent()) {
            return false;
        }
        
        Pet pet = petOpt.get();
        PetSkill skill = skillOpt.get();
        
        // 检查学习条件
        if (pet.getLevel() < skill.getLearnLevel()) {
            return false;
        }
        
        // 找到空的技能位
        if (pet.getSkill1() == null) {
            pet.setSkill1(skillId);
        } else if (pet.getSkill2() == null) {
            pet.setSkill2(skillId);
        } else if (pet.getSkill3() == null) {
            pet.setSkill3(skillId);
        } else if (pet.getSkill4() == null) {
            pet.setSkill4(skillId);
        } else if (pet.getSkill5() == null) {
            pet.setSkill5(skillId);
        } else {
            return false; // 技能位已满
        }
        
        petRepository.save(pet);
        return true;
    }
    
    /**
     * 获取宠物的技能列表
     */
    public List<PetSkill> getPetSkills(Integer petId) {
        Optional<Pet> petOpt = petRepository.findById(petId);
        if (!petOpt.isPresent()) {
            return new ArrayList<>();
        }
        
        Pet pet = petOpt.get();
        List<PetSkill> skills = new ArrayList<>();
        
        if (pet.getSkill1() != null) {
            petSkillRepository.findById(pet.getSkill1()).ifPresent(skills::add);
        }
        if (pet.getSkill2() != null) {
            petSkillRepository.findById(pet.getSkill2()).ifPresent(skills::add);
        }
        if (pet.getSkill3() != null) {
            petSkillRepository.findById(pet.getSkill3()).ifPresent(skills::add);
        }
        if (pet.getSkill4() != null) {
            petSkillRepository.findById(pet.getSkill4()).ifPresent(skills::add);
        }
        if (pet.getSkill5() != null) {
            petSkillRepository.findById(pet.getSkill5()).ifPresent(skills::add);
        }
        
        return skills;
    }
    
    /**
     * 获取可学习的技能
     */
    public List<PetSkill> getLearnableSkills(Integer petId) {
        Optional<Pet> petOpt = petRepository.findById(petId);
        if (!petOpt.isPresent()) {
            return new ArrayList<>();
        }
        
        Pet pet = petOpt.get();
        return petSkillRepository.findLearnableSkills(pet.getLevel());
    }
    
    /**
     * 释放宠物
     */
    public void releasePet(Integer petId, Integer ownerId) {
        Optional<Pet> petOpt = petRepository.findById(petId);
        if (petOpt.isPresent() && petOpt.get().getOwnerId().equals(ownerId)) {
            petRepository.deleteById(petId);
        }
    }
    
    /**
     * 重命名宠物
     */
    public void renamePet(Integer petId, Integer ownerId, String newNickname) {
        Optional<Pet> petOpt = petRepository.findById(petId);
        if (petOpt.isPresent() && petOpt.get().getOwnerId().equals(ownerId)) {
            Pet pet = petOpt.get();
            pet.setNickname(newNickname);
            petRepository.save(pet);
        }
    }
    
    /**
     * 随机生成宠物属性
     */
    private void generateRandomPetStats(Pet pet) {
        Random random = new Random();
        
        pet.setLevel(1);
        pet.setExp(0);
        pet.setMaxExp(100);
        
        // 随机生成基础属性
        pet.setMaxHp(80 + random.nextInt(40)); // 80-120
        pet.setHp(pet.getMaxHp());
        pet.setMaxMp(30 + random.nextInt(20)); // 30-50
        pet.setMp(pet.getMaxMp());
        
        pet.setAttackMin(8 + random.nextInt(5)); // 8-12
        pet.setAttackMax(pet.getAttackMin() + 5 + random.nextInt(5)); // *****
        pet.setDefense(5 + random.nextInt(5)); // 5-10
        pet.setAgility(8 + random.nextInt(7)); // 8-15
        pet.setIntelligence(5 + random.nextInt(10)); // 5-15
        
        // 随机成长率
        pet.setGrowthRate(0.8 + random.nextDouble() * 0.4); // 0.8-1.2
        
        // 随机稀有度
        int rarityRoll = random.nextInt(100);
        if (rarityRoll < 50) {
            pet.setRarity(1); // 普通 50%
        } else if (rarityRoll < 80) {
            pet.setRarity(2); // 优秀 30%
        } else if (rarityRoll < 95) {
            pet.setRarity(3); // 稀有 15%
        } else if (rarityRoll < 99) {
            pet.setRarity(4); // 史诗 4%
        } else {
            pet.setRarity(5); // 传说 1%
        }
    }
    
    /**
     * 计算升级所需经验
     */
    private Integer calculateMaxExp(Integer level) {
        return level * 100 + (level - 1) * 50;
    }
    
    /**
     * 宠物属性成长
     */
    private void growPetStats(Pet pet) {
        Random random = new Random();
        double growthRate = pet.getGrowthRate() != null ? pet.getGrowthRate() : 1.0;
        
        // 属性成长
        int hpGrowth = (int) ((5 + random.nextInt(5)) * growthRate);
        int mpGrowth = (int) ((2 + random.nextInt(3)) * growthRate);
        int attackGrowth = (int) ((1 + random.nextInt(3)) * growthRate);
        int defenseGrowth = (int) ((1 + random.nextInt(2)) * growthRate);
        int agilityGrowth = (int) ((1 + random.nextInt(2)) * growthRate);
        int intelligenceGrowth = (int) ((1 + random.nextInt(3)) * growthRate);
        
        pet.setMaxHp(pet.getMaxHp() + hpGrowth);
        pet.setHp(pet.getMaxHp());
        pet.setMaxMp(pet.getMaxMp() + mpGrowth);
        pet.setMp(pet.getMaxMp());
        pet.setAttackMin(pet.getAttackMin() + attackGrowth);
        pet.setAttackMax(pet.getAttackMax() + attackGrowth);
        pet.setDefense(pet.getDefense() + defenseGrowth);
        pet.setAgility(pet.getAgility() + agilityGrowth);
        pet.setIntelligence(pet.getIntelligence() + intelligenceGrowth);
    }

    /**
     * 给出战宠物增加经验
     */
    public void addPetExperience(Integer ownerId, Long exp) {
        Optional<Pet> activePet = getActivePet(ownerId);
        if (activePet.isPresent()) {
            Pet pet = activePet.get();
            pet.setExp(pet.getExp() + exp.intValue());

            // 检查是否可以升级
            while (pet.canLevelUp()) {
                levelUpPet(pet.getId());
                pet = petRepository.findById(pet.getId()).orElse(pet); // 重新获取最新数据
            }

            petRepository.save(pet);
        }
    }
}
