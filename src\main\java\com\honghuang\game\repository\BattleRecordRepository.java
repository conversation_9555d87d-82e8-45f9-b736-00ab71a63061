package com.honghuang.game.repository;

import com.honghuang.game.entity.BattleRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 战斗记录数据访问接口
 */
@Repository
public interface BattleRecordRepository extends JpaRepository<BattleRecord, Long> {
    
    /**
     * 根据玩家角色ID查找战斗记录
     */
    List<BattleRecord> findByPlayerRoleIdOrderByBattleTimeDesc(Integer playerRoleId);
    
    /**
     * 根据玩家角色ID和时间范围查找战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRoleId = :playerRoleId AND br.battleTime BETWEEN :startTime AND :endTime ORDER BY br.battleTime DESC")
    List<BattleRecord> findByPlayerRoleIdAndTimeRange(@Param("playerRoleId") Integer playerRoleId, 
                                                      @Param("startTime") LocalDateTime startTime, 
                                                      @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找玩家的胜利记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRoleId = :playerRoleId AND br.battleResult = 1 ORDER BY br.battleTime DESC")
    List<BattleRecord> findVictoryRecords(@Param("playerRoleId") Integer playerRoleId);
    
    /**
     * 查找玩家的失败记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRoleId = :playerRoleId AND br.battleResult = 0 ORDER BY br.battleTime DESC")
    List<BattleRecord> findDefeatRecords(@Param("playerRoleId") Integer playerRoleId);
    
    /**
     * 统计玩家总战斗次数
     */
    @Query("SELECT COUNT(br) FROM BattleRecord br WHERE br.playerRoleId = :playerRoleId")
    Long countByPlayerRoleId(@Param("playerRoleId") Integer playerRoleId);
    
    /**
     * 统计玩家胜利次数
     */
    @Query("SELECT COUNT(br) FROM BattleRecord br WHERE br.playerRoleId = :playerRoleId AND br.battleResult = 1")
    Long countVictoriesByPlayerRoleId(@Param("playerRoleId") Integer playerRoleId);
    
    /**
     * 统计玩家今日战斗次数
     */
    @Query("SELECT COUNT(br) FROM BattleRecord br WHERE br.playerRoleId = :playerRoleId AND DATE(br.battleTime) = CURRENT_DATE")
    Long countTodayBattlesByPlayerRoleId(@Param("playerRoleId") Integer playerRoleId);
    
    /**
     * 查找最近的战斗记录
     */
    @Query("SELECT br FROM BattleRecord br WHERE br.playerRoleId = :playerRoleId ORDER BY br.battleTime DESC")
    List<BattleRecord> findRecentBattles(@Param("playerRoleId") Integer playerRoleId);
    
    /**
     * 根据怪物ID查找战斗记录
     */
    List<BattleRecord> findByMonsterIdOrderByBattleTimeDesc(Integer monsterId);
}
