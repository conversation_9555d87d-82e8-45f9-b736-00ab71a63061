package com.honghuang.game.service;

import com.honghuang.game.common.ResultCode;
import com.honghuang.game.dto.UserLoginDTO;
import com.honghuang.game.dto.UserRegisterDTO;
import com.honghuang.game.entity.User;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.UserRepository;
import com.honghuang.game.util.PasswordUtil;
import com.honghuang.game.vo.UserVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户服务类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    /**
     * 用户注册
     */
    public UserVO register(UserRegisterDTO registerDTO) {
        logger.info("用户注册: {}", registerDTO.getUsername());

        // 验证密码一致性
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "两次输入的密码不一致");
        }

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(registerDTO.getUsername())) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS);
        }

        // 创建用户
        User user = new User();
        user.setUsername(registerDTO.getUsername());
        user.setPassword(PasswordUtil.encode(registerDTO.getPassword()));
        user.setChannel(registerDTO.getChannel());
        user.setSuperChannel(registerDTO.getSuperChannel());
        user.setLoginState(0);

        user = userRepository.save(user);
        logger.info("用户注册成功: userId={}, username={}", user.getId(), user.getUsername());

        return convertToVO(user);
    }

    /**
     * 用户登录
     */
    public UserVO login(UserLoginDTO loginDTO, HttpServletRequest request) {
        logger.info("用户登录: {}", loginDTO.getUsername());

        // 查找用户
        Optional<User> userOpt = userRepository.findByUsername(loginDTO.getUsername());
        if (!userOpt.isPresent()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        User user = userOpt.get();

        // 验证密码
        if (!PasswordUtil.matches(loginDTO.getPassword(), user.getPassword())) {
            throw new BusinessException(ResultCode.USER_PASSWORD_ERROR);
        }

        // 更新登录信息
        String clientIp = getClientIp(request);
        userRepository.updateLoginStatus(user.getId(), 1, LocalDateTime.now(), clientIp);

        // 重新查询用户信息
        user = userRepository.findById(user.getId()).orElse(user);
        logger.info("用户登录成功: userId={}, username={}, ip={}", user.getId(), user.getUsername(), clientIp);

        return convertToVO(user);
    }

    /**
     * 用户登出
     */
    public void logout(Integer userId) {
        logger.info("用户登出: userId={}", userId);

        userRepository.updateLoginStatus(userId, 0, LocalDateTime.now(), null);
        logger.info("用户登出成功: userId={}", userId);
    }

    /**
     * 根据ID查找用户
     */
    @Transactional(readOnly = true)
    public UserVO findById(Integer userId) {
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        return convertToVO(userOpt.get());
    }

    /**
     * 根据用户名查找用户
     */
    @Transactional(readOnly = true)
    public UserVO findByUsername(String username) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (!userOpt.isPresent()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        return convertToVO(userOpt.get());
    }

    /**
     * 获取在线用户数量
     */
    @Transactional(readOnly = true)
    public long getOnlineUserCount() {
        return userRepository.countOnlineUsers();
    }

    /**
     * 获取今日注册用户数量
     */
    @Transactional(readOnly = true)
    public long getTodayRegistrationCount() {
        return userRepository.countTodayRegistrations();
    }

    /**
     * 获取今日活跃用户数量
     */
    @Transactional(readOnly = true)
    public long getTodayActiveUserCount() {
        return userRepository.countTodayActiveUsers();
    }

    /**
     * 更新用户元宝
     */
    public void updateYuanbao(Integer userId, Integer amount) {
        logger.info("更新用户元宝: userId={}, amount={}", userId, amount);

        int updated = userRepository.updateYuanbao(userId, amount);
        if (updated == 0) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
    }

    /**
     * 更新用户积分
     */
    public void updateJifen(Integer userId, Integer amount) {
        logger.info("更新用户积分: userId={}, amount={}", userId, amount);

        int updated = userRepository.updateJifen(userId, amount);
        if (updated == 0) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
    }

    /**
     * 获取元宝排行榜
     */
    @Transactional(readOnly = true)
    @Cacheable(value = "yuanbao_ranking", key = "#limit", unless = "#result.size() == 0")
    public List<UserVO> getYuanbaoRanking(int limit) {
        List<User> users = userRepository.findYuanbaoRanking();
        return users.stream()
                .limit(limit)
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取积分排行榜
     */
    @Transactional(readOnly = true)
    public List<UserVO> getJifenRanking(int limit) {
        List<User> users = userRepository.findJifenRanking();
        return users.stream()
                .limit(limit)
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 批量更新所有用户为离线状态
     */
    public void updateAllUsersToOffline() {
        logger.info("批量更新所有用户为离线状态");
        int updated = userRepository.updateAllUsersToOffline();
        logger.info("批量更新完成，影响用户数: {}", updated);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 转换为VO对象
     */
    private UserVO convertToVO(User user) {
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);
        return userVO;
    }
}
