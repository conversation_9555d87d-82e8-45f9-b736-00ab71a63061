package com.honghuang.game.repository;

import com.honghuang.game.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface UserRepository extends JpaRepository<User, Integer> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 根据用户名和密码查找用户
     */
    Optional<User> findByUsernameAndPassword(String username, String password);

    /**
     * 根据登录状态查找用户
     */
    List<User> findByLoginState(Integer loginState);

    /**
     * 根据渠道查找用户
     */
    List<User> findByChannel(String channel);

    /**
     * 根据超级渠道查找用户
     */
    List<User> findBySuperChannel(String superChannel);

    /**
     * 查找在线用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.loginState = 1")
    long countOnlineUsers();

    /**
     * 查找指定时间段内注册的用户
     */
    @Query("SELECT u FROM User u WHERE u.createTime BETWEEN :startTime AND :endTime")
    List<User> findByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 查找指定时间段内登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginTime BETWEEN :startTime AND :endTime")
    List<User> findByLastLoginTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 更新用户登录状态
     */
    @Modifying
    @Query("UPDATE User u SET u.loginState = :loginState, u.lastLoginTime = :loginTime, u.lastLoginIp = :loginIp WHERE u.id = :userId")
    int updateLoginStatus(@Param("userId") Integer userId, 
                         @Param("loginState") Integer loginState,
                         @Param("loginTime") LocalDateTime loginTime,
                         @Param("loginIp") String loginIp);

    /**
     * 更新用户元宝数量
     */
    @Modifying
    @Query("UPDATE User u SET u.yuanbao = u.yuanbao + :amount WHERE u.id = :userId")
    int updateYuanbao(@Param("userId") Integer userId, @Param("amount") Integer amount);

    /**
     * 更新用户积分数量
     */
    @Modifying
    @Query("UPDATE User u SET u.jifen = u.jifen + :amount WHERE u.id = :userId")
    int updateJifen(@Param("userId") Integer userId, @Param("amount") Integer amount);

    /**
     * 批量更新用户登录状态为离线
     */
    @Modifying
    @Query("UPDATE User u SET u.loginState = 0 WHERE u.loginState = 1")
    int updateAllUsersToOffline();

    /**
     * 查找元宝排行榜
     */
    @Query("SELECT u FROM User u ORDER BY u.yuanbao DESC")
    List<User> findYuanbaoRanking();

    /**
     * 查找积分排行榜
     */
    @Query("SELECT u FROM User u ORDER BY u.jifen DESC")
    List<User> findJifenRanking();

    /**
     * 根据IP地址查找用户
     */
    List<User> findByLastLoginIp(String ip);

    /**
     * 查找长时间未登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginTime < :cutoffTime")
    List<User> findInactiveUsers(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计指定渠道的用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.channel = :channel")
    long countByChannel(@Param("channel") String channel);

    /**
     * 统计今日注册用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE DATE(u.createTime) = CURRENT_DATE")
    long countTodayRegistrations();

    /**
     * 统计今日活跃用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE DATE(u.lastLoginTime) = CURRENT_DATE")
    long countTodayActiveUsers();
}
