<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>装备展示 - 洪荒游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #fff;
        }

        .header {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #3498db;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .nav {
            margin-top: 15px;
        }

        .nav a {
            color: #3498db;
            text-decoration: none;
            margin: 0 15px;
            padding: 8px 16px;
            border: 1px solid #3498db;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .nav a:hover {
            background: #3498db;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .equipment-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
            color: #f39c12;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 20px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border: 1px solid #3498db;
        }

        .equipment-item {
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 2px solid #34495e;
            transition: all 0.3s;
            cursor: pointer;
        }

        .equipment-item:hover {
            border-color: #f39c12;
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        }

        .equipment-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 10px;
            border-radius: 8px;
            border: 2px solid #3498db;
            overflow: hidden;
        }

        .equipment-icon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .equipment-name {
            font-size: 0.9em;
            color: #ecf0f1;
            margin-bottom: 5px;
        }

        .equipment-type {
            font-size: 0.8em;
            color: #bdc3c7;
        }

        .role-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .role-grid {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 20px;
        }

        .role-item {
            background: rgba(52, 73, 94, 0.9);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #3498db;
            transition: all 0.3s;
        }

        .role-item:hover {
            border-color: #f39c12;
            transform: scale(1.05);
        }

        .role-avatar {
            width: 100px;
            height: 100px;
            margin: 0 auto 15px;
            border-radius: 50%;
            border: 3px solid #3498db;
            overflow: hidden;
        }

        .role-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .role-name {
            font-size: 1.2em;
            color: #f39c12;
            font-weight: bold;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s;
        }

        .back-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <a href="game.html" class="back-button">← 返回游戏</a>

    <div class="header">
        <h1>🏺 装备与角色展示</h1>
        <p>展示已迁移的游戏资源</p>
        <div class="nav">
            <a href="#roles">角色种族</a>
            <a href="#equipment">装备展示</a>
            <a href="#props">道具展示</a>
        </div>
    </div>

    <div class="container">
        <!-- 角色种族展示 -->
        <div id="roles" class="role-section">
            <h2 class="section-title">🧙‍♂️ 角色种族</h2>
            <div class="role-grid">
                <div class="role-item">
                    <div class="role-avatar">
                        <img src="/honghuang-game/images/role/wu.png" alt="巫族" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjMzQ0OTVlIi8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSIjZWNmMGYxIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lt6vmlY88L3RleHQ+Cjwvc3ZnPgo='">
                    </div>
                    <div class="role-name">巫族</div>
                    <p style="color: #bdc3c7; margin-top: 10px;">擅长近战和体魄修炼</p>
                </div>
                <div class="role-item">
                    <div class="role-avatar">
                        <img src="/honghuang-game/images/role/yao.png" alt="妖族" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjMzQ0OTVlIi8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSIjZWNmMGYxIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lpZbmlY88L3RleHQ+Cjwvc3ZnPgo='">
                    </div>
                    <div class="role-name">妖族</div>
                    <p style="color: #bdc3c7; margin-top: 10px;">擅长法术和灵力修炼</p>
                </div>
            </div>
        </div>

        <!-- 装备展示 -->
        <div id="equipment" class="equipment-section">
            <h2 class="section-title">⚔️ 装备展示</h2>
            <div class="equipment-grid" id="equipmentGrid">
                <!-- 装备将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 道具展示 -->
        <div id="props" class="equipment-section">
            <h2 class="section-title">💎 道具展示</h2>
            <div class="equipment-grid" id="propsGrid">
                <!-- 道具将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <script>
        // 装备数据
        const equipmentData = [
            { name: '金龙冠', type: '头盔', image: '001jinlongguan.png' },
            { name: '九龙袍', type: '衣服', image: '001jiulongpao.png' },
            { name: '龙虎如意', type: '武器', image: '001longhuruyi.png' },
            { name: '龙腾四海', type: '武器', image: '001longtengsihai.png' },
            { name: '圣龙盘', type: '护符', image: '001shenglongpan.png' },
            { name: '双龙戏', type: '饰品', image: '001shuanglongxi.png' },
            { name: '四海瓶', type: '法宝', image: '001sihaiping.png' },
            { name: '巫带', type: '腰带', image: '001wudai.png' },
            { name: '巫刀', type: '武器', image: '001wudao.png' },
            { name: '巫履', type: '鞋子', image: '001wulv.png' },
            { name: '巫冕', type: '头盔', image: '001wumian.png' },
            { name: '巫袍', type: '衣服', image: '001wupao.png' },
            { name: '妖带', type: '腰带', image: '001yaodai.png' },
            { name: '妖冠', type: '头盔', image: '001yaoguan.png' },
            { name: '妖甲', type: '护甲', image: '001yaojia.png' },
            { name: '妖剑', type: '武器', image: '001yaojian.png' },
            { name: '妖靴', type: '鞋子', image: '001yaoxue.png' },
            { name: '祖龙珠', type: '法宝', image: '001zulongzhu.png' }
        ];

        // 道具数据
        const propsData = [
            { name: '宝石1级', type: '宝石', image: 'baoshi01.png' },
            { name: '宝石2级', type: '宝石', image: 'baoshi02.png' },
            { name: '宝石3级', type: '宝石', image: 'baoshi03.png' },
            { name: '宝石4级', type: '宝石', image: 'baoshi04.png' },
            { name: '宝石5级', type: '宝石', image: 'baoshi05.png' },
            { name: '宝石6级', type: '宝石', image: 'baoshi06.png' },
            { name: '宝石7级', type: '宝石', image: 'baoshi07.png' },
            { name: '宝石8级', type: '宝石', image: 'baoshi08.png' },
            { name: '宝石9级', type: '宝石', image: 'baoshi09.png' },
            { name: '宝石10级', type: '宝石', image: 'baoshi10.png' },
            { name: '丹药1级', type: '丹药', image: 'danyao01.png' },
            { name: '丹药2级', type: '丹药', image: 'danyao02.png' },
            { name: '丹药3级', type: '丹药', image: 'danyao03.png' },
            { name: '丹药4级', type: '丹药', image: 'danyao04.png' },
            { name: '丹药5级', type: '丹药', image: 'danyao05.png' },
            { name: '丹药6级', type: '丹药', image: 'danyao06.png' },
            { name: '丹药7级', type: '丹药', image: 'danyao07.png' },
            { name: '丹药8级', type: '丹药', image: 'danyao08.png' },
            { name: '丹药9级', type: '丹药', image: 'danyao09.png' },
            { name: '丹药10级', type: '丹药', image: 'danyao10.png' }
        ];

        // 渲染装备
        function renderEquipment() {
            const grid = document.getElementById('equipmentGrid');
            grid.innerHTML = '';
            
            equipmentData.forEach(item => {
                const div = document.createElement('div');
                div.className = 'equipment-item';
                div.innerHTML = `
                    <div class="equipment-icon">
                        <img src="/honghuang-game/images/item/equip/${item.image}" alt="${item.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMzQ0OTVlIi8+Cjx0ZXh0IHg9IjMyIiB5PSIzNiIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmaWxsPSIjZWNmMGYxIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ijJQ8L3RleHQ+Cjwvc3ZnPgo='">
                    </div>
                    <div class="equipment-name">${item.name}</div>
                    <div class="equipment-type">${item.type}</div>
                `;
                grid.appendChild(div);
            });
        }

        // 渲染道具
        function renderProps() {
            const grid = document.getElementById('propsGrid');
            grid.innerHTML = '';
            
            propsData.forEach(item => {
                const div = document.createElement('div');
                div.className = 'equipment-item';
                div.innerHTML = `
                    <div class="equipment-icon">
                        <img src="/honghuang-game/images/item/prop/${item.image}" alt="${item.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMzQ0OTVlIi8+Cjx0ZXh0IHg9IjMyIiB5PSIzNiIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmaWxsPSIjZWNmMGYxIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7wn5OQPC90ZXh0Pgo8L3N2Zz4K'">
                    </div>
                    <div class="equipment-name">${item.name}</div>
                    <div class="equipment-type">${item.type}</div>
                `;
                grid.appendChild(div);
            });
        }

        // 页面加载完成后渲染
        document.addEventListener('DOMContentLoaded', function() {
            renderEquipment();
            renderProps();
        });

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
