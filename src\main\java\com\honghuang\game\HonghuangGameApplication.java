package com.honghuang.game;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 洪荒游戏主启动类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(exclude = {RedisRepositoriesAutoConfiguration.class})
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class HonghuangGameApplication {

    public static void main(String[] args) {
        SpringApplication.run(HonghuangGameApplication.class, args);
        System.out.println("洪荒游戏启动成功！");
    }
}
