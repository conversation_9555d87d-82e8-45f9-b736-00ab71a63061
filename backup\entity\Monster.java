package com.honghuang.game.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 怪物实体类
 * 对应原表 npc_info
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "npc_info")
public class Monster {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "npc_id")
    private Integer id;

    /**
     * 怪物名称
     */
    @Column(name = "npc_name", length = 50)
    private String name;

    /**
     * 怪物类型 1:普通怪物 2:精英怪物 3:BOSS 4:NPC
     */
    @Column(name = "npc_type")
    private Integer type = 1;

    /**
     * 怪物等级
     */
    @Column(name = "npc_level")
    private Integer level = 1;

    /**
     * 最大生命值
     */
    @Column(name = "npc_hp")
    private Integer maxHp = 100;

    /**
     * 最大法力值
     */
    @Column(name = "npc_mp")
    private Integer maxMp = 50;

    /**
     * 攻击力
     */
    @Column(name = "npc_attack")
    private Integer attack = 10;

    /**
     * 防御力
     */
    @Column(name = "npc_defense")
    private Integer defense = 5;

    /**
     * 敏捷
     */
    @Column(name = "npc_agility")
    private Integer agility = 5;

    /**
     * 智力
     */
    @Column(name = "npc_intelligence")
    private Integer intelligence = 5;

    /**
     * 体质
     */
    @Column(name = "npc_constitution")
    private Integer constitution = 5;

    /**
     * 经验值奖励
     */
    @Column(name = "exp_reward")
    private Long expReward = 10L;

    /**
     * 铜钱奖励
     */
    @Column(name = "copper_reward")
    private Long copperReward = 5L;

    /**
     * 掉落物品配置JSON
     */
    @Column(name = "drop_items", length = 1000)
    private String dropItems;

    /**
     * 技能配置JSON
     */
    @Column(name = "skills", length = 500)
    private String skills;

    /**
     * 怪物描述
     */
    @Column(name = "npc_description", length = 500)
    private String description;

    /**
     * 怪物图标
     */
    @Column(name = "npc_icon", length = 100)
    private String icon;

    /**
     * 刷新时间（秒）
     */
    @Column(name = "respawn_time")
    private Integer respawnTime = 300;

    /**
     * 移动速度
     */
    @Column(name = "move_speed")
    private Integer moveSpeed = 100;

    /**
     * 攻击范围
     */
    @Column(name = "attack_range")
    private Integer attackRange = 1;

    /**
     * 视野范围
     */
    @Column(name = "sight_range")
    private Integer sightRange = 5;

    /**
     * AI类型 1:被动 2:主动攻击 3:智能
     */
    @Column(name = "ai_type")
    private Integer aiType = 2;

    /**
     * 是否激活 0:未激活 1:激活
     */
    @Column(name = "is_active")
    private Integer isActive = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    // 业务方法

    /**
     * 是否为普通怪物
     */
    public boolean isNormalMonster() {
        return type != null && type == 1;
    }

    /**
     * 是否为精英怪物
     */
    public boolean isEliteMonster() {
        return type != null && type == 2;
    }

    /**
     * 是否为BOSS
     */
    public boolean isBoss() {
        return type != null && type == 3;
    }

    /**
     * 是否为NPC
     */
    public boolean isNpc() {
        return type != null && type == 4;
    }

    /**
     * 获取类型名称
     */
    public String getTypeName() {
        if (type == null) return "普通怪物";
        switch (type) {
            case 1: return "普通怪物";
            case 2: return "精英怪物";
            case 3: return "BOSS";
            case 4: return "NPC";
            default: return "普通怪物";
        }
    }

    /**
     * 获取AI类型名称
     */
    public String getAiTypeName() {
        if (aiType == null) return "主动攻击";
        switch (aiType) {
            case 1: return "被动";
            case 2: return "主动攻击";
            case 3: return "智能";
            default: return "主动攻击";
        }
    }

    /**
     * 计算战斗力
     */
    public int getCombatPower() {
        return (attack + defense) * level + maxHp / 10;
    }

    /**
     * 是否可以攻击
     */
    public boolean canAttack() {
        return !isNpc() && isActive == 1;
    }

    /**
     * 获取掉落经验（根据等级调整）
     */
    public long getAdjustedExpReward(int playerLevel) {
        if (playerLevel > level + 5) {
            // 等级差距太大，经验减少
            return Math.max(1, expReward / 2);
        } else if (playerLevel < level - 5) {
            // 挑战高等级怪物，经验增加
            return expReward * 2;
        }
        return expReward;
    }

    /**
     * 获取掉落铜钱（根据等级调整）
     */
    public long getAdjustedCopperReward(int playerLevel) {
        if (playerLevel > level + 5) {
            return Math.max(1, copperReward / 2);
        } else if (playerLevel < level - 5) {
            return copperReward * 2;
        }
        return copperReward;
    }
}
