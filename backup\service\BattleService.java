package com.honghuang.game.service;

import com.honghuang.game.entity.BattleRecord;
import com.honghuang.game.entity.Monster;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.GameItem;
import com.honghuang.game.repository.BattleRecordRepository;
import com.honghuang.game.repository.MonsterRepository;
import com.honghuang.game.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 战斗服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class BattleService {

    @Autowired
    private BattleRecordRepository battleRecordRepository;

    @Autowired
    private MonsterRepository monsterRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    @Autowired
    private PlayerItemService playerItemService;

    @Autowired
    private GameItemService gameItemService;

    /**
     * 开始战斗
     */
    public BattleResult startBattle(PlayerRole playerRole, Integer monsterId) {
        // 检查玩家状态
        if (!playerRole.isAlive()) {
            throw new BusinessException("角色已死亡，无法战斗");
        }

        // 获取怪物信息
        Monster monster = monsterRepository.findById(monsterId)
                .orElseThrow(() -> new BusinessException("怪物不存在"));

        if (!monster.canAttack()) {
            throw new BusinessException("该怪物无法攻击");
        }

        // 检查等级差距
        int levelDiff = Math.abs(playerRole.getGrade() - monster.getLevel());
        if (levelDiff > 10) {
            throw new BusinessException("等级差距过大，无法战斗");
        }

        // 创建战斗记录
        BattleRecord battleRecord = new BattleRecord();
        battleRecord.setPlayerRole(playerRole);
        battleRecord.setMonster(monster);
        battleRecord.setBattleType(1); // PVE
        battleRecord.setPlayerInitialHp(playerRole.getCurrentHp());
        battleRecord.setMonsterInitialHp(monster.getMaxHp());

        // 执行战斗
        BattleResult result = executeBattle(playerRole, monster, battleRecord);

        // 保存战斗记录
        battleRecord.setBattleResult(result.isVictory() ? 1 : (result.isEscape() ? 3 : 2));
        battleRecord.setPlayerRemainingHp(result.getPlayerHp());
        battleRecord.setMonsterRemainingHp(result.getMonsterHp());
        battleRecord.setBattleRounds(result.getRounds());
        battleRecord.setExpGained(result.getExpGained());
        battleRecord.setCopperGained(result.getCopperGained());
        battleRecord.setBattleDuration(result.getDuration());
        battleRecord.setBattleDetails(result.getBattleLog());

        battleRecordRepository.save(battleRecord);

        // 更新玩家状态
        updatePlayerAfterBattle(playerRole, result);

        log.info("战斗结束: 玩家={}, 怪物={}, 结果={}", 
                playerRole.getRoleName(), monster.getName(), result.isVictory() ? "胜利" : "失败");

        return result;
    }

    /**
     * 执行战斗逻辑
     */
    private BattleResult executeBattle(PlayerRole player, Monster monster, BattleRecord record) {
        BattleResult result = new BattleResult();
        List<String> battleLog = new ArrayList<>();
        
        int playerHp = player.getCurrentHp();
        int monsterHp = monster.getMaxHp();
        int rounds = 0;
        long startTime = System.currentTimeMillis();

        battleLog.add(String.format("战斗开始！%s VS %s", player.getRoleName(), monster.getName()));

        while (playerHp > 0 && monsterHp > 0 && rounds < 50) { // 最多50回合
            rounds++;
            battleLog.add(String.format("=== 第%d回合 ===", rounds));

            // 计算先手（敏捷高的先攻击）
            boolean playerFirst = player.getTotalAgility() >= monster.getAgility();

            if (playerFirst) {
                // 玩家攻击
                int playerDamage = calculateDamage(player.getTotalAttack(), monster.getDefense());
                monsterHp = Math.max(0, monsterHp - playerDamage);
                battleLog.add(String.format("%s 攻击 %s，造成 %d 点伤害", 
                        player.getRoleName(), monster.getName(), playerDamage));

                if (monsterHp <= 0) {
                    battleLog.add(String.format("%s 被击败了！", monster.getName()));
                    break;
                }

                // 怪物反击
                int monsterDamage = calculateDamage(monster.getAttack(), player.getTotalDefense());
                playerHp = Math.max(0, playerHp - monsterDamage);
                battleLog.add(String.format("%s 攻击 %s，造成 %d 点伤害", 
                        monster.getName(), player.getRoleName(), monsterDamage));

                if (playerHp <= 0) {
                    battleLog.add(String.format("%s 被击败了！", player.getRoleName()));
                    break;
                }
            } else {
                // 怪物先攻击
                int monsterDamage = calculateDamage(monster.getAttack(), player.getTotalDefense());
                playerHp = Math.max(0, playerHp - monsterDamage);
                battleLog.add(String.format("%s 攻击 %s，造成 %d 点伤害", 
                        monster.getName(), player.getRoleName(), monsterDamage));

                if (playerHp <= 0) {
                    battleLog.add(String.format("%s 被击败了！", player.getRoleName()));
                    break;
                }

                // 玩家反击
                int playerDamage = calculateDamage(player.getTotalAttack(), monster.getDefense());
                monsterHp = Math.max(0, monsterHp - playerDamage);
                battleLog.add(String.format("%s 攻击 %s，造成 %d 点伤害", 
                        player.getRoleName(), monster.getName(), playerDamage));

                if (monsterHp <= 0) {
                    battleLog.add(String.format("%s 被击败了！", monster.getName()));
                    break;
                }
            }

            // 检查是否有特殊事件（暴击、闪避等）
            if (Math.random() < 0.1) { // 10%概率触发特殊事件
                battleLog.add("发生了特殊事件！");
            }
        }

        long endTime = System.currentTimeMillis();
        int duration = (int) ((endTime - startTime) / 1000);

        // 设置战斗结果
        result.setPlayerHp(playerHp);
        result.setMonsterHp(monsterHp);
        result.setRounds(rounds);
        result.setDuration(duration);
        result.setBattleLog(String.join("\n", battleLog));

        // 计算奖励
        if (monsterHp <= 0) { // 玩家胜利
            result.setVictory(true);
            long expReward = monster.getAdjustedExpReward(player.getGrade());
            long copperReward = monster.getAdjustedCopperReward(player.getGrade());
            
            result.setExpGained(expReward);
            result.setCopperGained(copperReward);
            
            // 计算掉落物品
            List<GameItem> droppedItems = calculateDroppedItems(monster);
            result.setDroppedItems(droppedItems);
            
            battleLog.add(String.format("获得经验: %d, 铜钱: %d", expReward, copperReward));
            if (!droppedItems.isEmpty()) {
                battleLog.add("获得物品: " + droppedItems.stream()
                        .map(GameItem::getName)
                        .reduce((a, b) -> a + ", " + b)
                        .orElse(""));
            }
        } else if (playerHp <= 0) { // 玩家失败
            result.setVictory(false);
            battleLog.add("战斗失败，请提升实力后再来挑战！");
        } else { // 超时平局
            result.setVictory(false);
            battleLog.add("战斗超时，算作失败！");
        }

        result.setBattleLog(String.join("\n", battleLog));
        return result;
    }

    /**
     * 计算伤害
     */
    private int calculateDamage(int attack, int defense) {
        // 基础伤害 = 攻击力 - 防御力/2
        int baseDamage = Math.max(1, attack - defense / 2);
        
        // 随机浮动 ±20%
        double randomFactor = 0.8 + Math.random() * 0.4;
        int damage = (int) (baseDamage * randomFactor);
        
        // 暴击判定（5%概率）
        if (Math.random() < 0.05) {
            damage *= 2;
        }
        
        return Math.max(1, damage);
    }

    /**
     * 计算掉落物品
     */
    private List<GameItem> calculateDroppedItems(Monster monster) {
        List<GameItem> droppedItems = new ArrayList<>();
        
        // 简单的掉落逻辑：根据怪物等级和类型决定掉落
        double dropRate = 0.3; // 基础掉落率30%
        
        if (monster.isEliteMonster()) {
            dropRate = 0.6; // 精英怪物60%
        } else if (monster.isBoss()) {
            dropRate = 0.9; // BOSS 90%
        }
        
        if (Math.random() < dropRate) {
            // 随机掉落一个适合等级的物品
            List<GameItem> availableItems = gameItemService.findByLevelRange(
                    Math.max(1, monster.getLevel() - 2), 
                    monster.getLevel() + 2);
            
            if (!availableItems.isEmpty()) {
                GameItem randomItem = availableItems.get((int) (Math.random() * availableItems.size()));
                droppedItems.add(randomItem);
            }
        }
        
        return droppedItems;
    }

    /**
     * 更新玩家战斗后状态
     */
    private void updatePlayerAfterBattle(PlayerRole player, BattleResult result) {
        // 更新生命值
        player.setCurrentHp(result.getPlayerHp());
        
        if (result.isVictory()) {
            // 增加经验值
            player.setExperience(player.getExperience() + result.getExpGained());
            
            // 增加铜钱
            player.setCopper(player.getCopper() + result.getCopperGained());
            
            // 检查是否升级
            checkLevelUp(player);
            
            // 添加掉落物品到背包
            for (GameItem item : result.getDroppedItems()) {
                try {
                    playerItemService.addItem(player, item, 1);
                } catch (Exception e) {
                    log.warn("添加掉落物品失败: {}", e.getMessage());
                }
            }
        }
        
        // 保存玩家状态
        playerRoleService.updateRole(player);
    }

    /**
     * 检查升级
     */
    private void checkLevelUp(PlayerRole player) {
        while (player.canLevelUp()) {
            player.setGrade(player.getGrade() + 1);
            player.setExperience(player.getExperience() - player.getNextLevelExp());
            
            // 计算下一级所需经验
            long nextExp = calculateNextLevelExp(player.getGrade());
            player.setNextLevelExp(nextExp);
            
            // 升级奖励：增加属性点和生命法力上限
            player.setAttributePoints(player.getAttributePoints() + 5);
            player.setMaxHp(player.getMaxHp() + 10);
            player.setMaxMp(player.getMaxMp() + 5);
            player.setCurrentHp(player.getMaxHp()); // 升级回满血
            player.setCurrentMp(player.getMaxMp()); // 升级回满蓝
            
            log.info("玩家升级: {} 升到 {} 级", player.getRoleName(), player.getGrade());
        }
    }

    /**
     * 计算下一级所需经验
     */
    private long calculateNextLevelExp(int level) {
        return 100L * level * level + 50L * level;
    }

    /**
     * 战斗结果类
     */
    public static class BattleResult {
        private boolean victory;
        private boolean escape;
        private int playerHp;
        private int monsterHp;
        private int rounds;
        private int duration;
        private long expGained;
        private long copperGained;
        private List<GameItem> droppedItems = new ArrayList<>();
        private String battleLog;

        // Getters and Setters
        public boolean isVictory() { return victory; }
        public void setVictory(boolean victory) { this.victory = victory; }
        
        public boolean isEscape() { return escape; }
        public void setEscape(boolean escape) { this.escape = escape; }
        
        public int getPlayerHp() { return playerHp; }
        public void setPlayerHp(int playerHp) { this.playerHp = playerHp; }
        
        public int getMonsterHp() { return monsterHp; }
        public void setMonsterHp(int monsterHp) { this.monsterHp = monsterHp; }
        
        public int getRounds() { return rounds; }
        public void setRounds(int rounds) { this.rounds = rounds; }
        
        public int getDuration() { return duration; }
        public void setDuration(int duration) { this.duration = duration; }
        
        public long getExpGained() { return expGained; }
        public void setExpGained(long expGained) { this.expGained = expGained; }
        
        public long getCopperGained() { return copperGained; }
        public void setCopperGained(long copperGained) { this.copperGained = copperGained; }
        
        public List<GameItem> getDroppedItems() { return droppedItems; }
        public void setDroppedItems(List<GameItem> droppedItems) { this.droppedItems = droppedItems; }
        
        public String getBattleLog() { return battleLog; }
        public void setBattleLog(String battleLog) { this.battleLog = battleLog; }
    }
}
