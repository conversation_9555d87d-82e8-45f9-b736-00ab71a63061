package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 物品控制器
 */
@RestController
@RequestMapping("/api/item")
public class ItemController {

    @GetMapping("/active")
    public Result<List<Map<String, Object>>> getActiveItems() {
        // 模拟物品数据
        List<Map<String, Object>> items = new ArrayList<>();

        Map<String, Object> item1 = new HashMap<>();
        item1.put("id", 1001);
        item1.put("name", "新手剑");
        item1.put("description", "适合新手使用的基础武器");
        item1.put("type", 1);
        item1.put("quality", 1);
        item1.put("attack", 5);
        item1.put("defense", 0);
        item1.put("price", 100);
        item1.put("isActive", true);
        items.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("id", 2001);
        item2.put("name", "生命药水");
        item2.put("description", "恢复50点生命值");
        item2.put("type", 2);
        item2.put("quality", 1);
        item2.put("attack", 0);
        item2.put("defense", 0);
        item2.put("price", 50);
        item2.put("isActive", true);
        items.add(item2);

        return Result.success("获取激活物品成功", items);
    }

    /**
     * 使用物品
     */
    @PostMapping("/use")
    public Result<String> useItem(@RequestBody Map<String, Object> params) {
        try {
            Integer itemId = (Integer) params.get("itemId");
            Integer quantity = (Integer) params.get("quantity");

            if (itemId == null) {
                return Result.error(400, "物品ID不能为空");
            }

            if (quantity == null || quantity <= 0) {
                quantity = 1;
            }

            // 模拟物品使用逻辑
            String result = "";
            switch (itemId) {
                case 1:
                    result = "使用生命药水，恢复了100点生命值";
                    break;
                case 2:
                    result = "使用魔法药水，恢复了100点魔法值";
                    break;
                case 3:
                    result = "使用经验丹，获得了100点经验值";
                    break;
                default:
                    result = "使用了物品";
                    break;
            }

            return Result.success(result);

        } catch (Exception e) {
            return Result.error(500, "使用物品失败: " + e.getMessage());
        }
    }
}
