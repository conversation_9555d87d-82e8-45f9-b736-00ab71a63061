package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.AuctionBid;
import com.honghuang.game.entity.AuctionItem;
import com.honghuang.game.service.AuctionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 拍卖控制器
 */
@RestController
@RequestMapping("/api/auction")
public class AuctionController {

    @Autowired
    private AuctionService auctionService;

    /**
     * 创建拍卖
     */
    @PostMapping("/create")
    public Result<AuctionItem> createAuction(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer inventorySlotIndex = (Integer) request.get("inventorySlotIndex");
            Long startPrice = ((Number) request.get("startPrice")).longValue();
            Long buyoutPrice = request.get("buyoutPrice") != null ? ((Number) request.get("buyoutPrice")).longValue() : null;
            Integer durationHours = (Integer) request.get("durationHours");
            Integer currencyType = (Integer) request.get("currencyType");
            
            if (inventorySlotIndex == null || startPrice == null || durationHours == null || currencyType == null) {
                return Result.error(400, "参数不完整");
            }
            
            AuctionItem auctionItem = auctionService.createAuction(currentRoleId, inventorySlotIndex, 
                    startPrice, buyoutPrice, durationHours, currencyType);
            return Result.success("创建拍卖成功", auctionItem);
        } catch (Exception e) {
            return Result.error(500, "创建拍卖失败: " + e.getMessage());
        }
    }

    /**
     * 出价
     */
    @PostMapping("/{auctionItemId}/bid")
    public Result<AuctionBid> placeBid(@PathVariable Integer auctionItemId,
                                      @RequestBody Map<String, Object> request,
                                      HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Long bidPrice = ((Number) request.get("bidPrice")).longValue();
            
            if (bidPrice == null) {
                return Result.error(400, "出价不能为空");
            }
            
            AuctionBid bid = auctionService.placeBid(currentRoleId, auctionItemId, bidPrice);
            return Result.success("出价成功", bid);
        } catch (Exception e) {
            return Result.error(500, "出价失败: " + e.getMessage());
        }
    }

    /**
     * 一口价购买
     */
    @PostMapping("/{auctionItemId}/buyout")
    public Result<AuctionBid> buyout(@PathVariable Integer auctionItemId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            AuctionBid bid = auctionService.buyout(currentRoleId, auctionItemId);
            return Result.success("一口价购买成功", bid);
        } catch (Exception e) {
            return Result.error(500, "一口价购买失败: " + e.getMessage());
        }
    }

    /**
     * 取消拍卖
     */
    @PostMapping("/{auctionItemId}/cancel")
    public Result<Void> cancelAuction(@PathVariable Integer auctionItemId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = auctionService.cancelAuction(currentRoleId, auctionItemId);
            if (success) {
                return Result.success("取消拍卖成功");
            } else {
                return Result.error(400, "取消拍卖失败");
            }
        } catch (Exception e) {
            return Result.error(500, "取消拍卖失败: " + e.getMessage());
        }
    }

    /**
     * 获取进行中的拍卖列表
     */
    @GetMapping("/active")
    public Result<List<AuctionItem>> getActiveAuctions() {
        try {
            List<AuctionItem> auctions = auctionService.getActiveAuctions();
            return Result.success("获取拍卖列表成功", auctions);
        } catch (Exception e) {
            return Result.error(500, "获取拍卖列表失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取进行中的拍卖列表
     */
    @GetMapping("/active/page")
    public Result<Page<AuctionItem>> getActiveAuctions(@RequestParam(defaultValue = "0") int page,
                                                      @RequestParam(defaultValue = "20") int size) {
        try {
            Page<AuctionItem> auctions = auctionService.getActiveAuctions(page, size);
            return Result.success("获取拍卖列表成功", auctions);
        } catch (Exception e) {
            return Result.error(500, "获取拍卖列表失败: " + e.getMessage());
        }
    }

    /**
     * 搜索拍卖
     */
    @GetMapping("/search")
    public Result<List<AuctionItem>> searchAuctions(@RequestParam String keyword) {
        try {
            List<AuctionItem> auctions = auctionService.searchAuctions(keyword);
            return Result.success("搜索拍卖成功", auctions);
        } catch (Exception e) {
            return Result.error(500, "搜索拍卖失败: " + e.getMessage());
        }
    }

    /**
     * 根据物品ID搜索拍卖
     */
    @GetMapping("/search/item/{itemId}")
    public Result<List<AuctionItem>> searchAuctionsByItemId(@PathVariable Integer itemId) {
        try {
            List<AuctionItem> auctions = auctionService.searchAuctionsByItemId(itemId);
            return Result.success("搜索拍卖成功", auctions);
        } catch (Exception e) {
            return Result.error(500, "搜索拍卖失败: " + e.getMessage());
        }
    }

    /**
     * 获取我的拍卖列表
     */
    @GetMapping("/my/selling")
    public Result<List<AuctionItem>> getMySellingAuctions(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<AuctionItem> auctions = auctionService.getSellerAuctions(currentRoleId);
            return Result.success("获取我的拍卖列表成功", auctions);
        } catch (Exception e) {
            return Result.error(500, "获取我的拍卖列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取我的出价列表
     */
    @GetMapping("/my/bidding")
    public Result<List<AuctionBid>> getMyBids(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<AuctionBid> bids = auctionService.getBuyerBids(currentRoleId);
            return Result.success("获取我的出价列表成功", bids);
        } catch (Exception e) {
            return Result.error(500, "获取我的出价列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取拍卖的出价历史
     */
    @GetMapping("/{auctionItemId}/bids")
    public Result<List<AuctionBid>> getAuctionBids(@PathVariable Integer auctionItemId) {
        try {
            List<AuctionBid> bids = auctionService.getAuctionBids(auctionItemId);
            return Result.success("获取出价历史成功", bids);
        } catch (Exception e) {
            return Result.error(500, "获取出价历史失败: " + e.getMessage());
        }
    }

    /**
     * 处理过期拍卖
     */
    @PostMapping("/process-expired")
    public Result<Void> processExpiredAuctions() {
        try {
            auctionService.processExpiredAuctions();
            return Result.success("处理过期拍卖成功");
        } catch (Exception e) {
            return Result.error(500, "处理过期拍卖失败: " + e.getMessage());
        }
    }

    /**
     * 获取拍卖统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getAuctionStatistics() {
        try {
            Map<String, Object> stats = auctionService.getAuctionStatistics();
            return Result.success("获取拍卖统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取拍卖统计失败: " + e.getMessage());
        }
    }
}
