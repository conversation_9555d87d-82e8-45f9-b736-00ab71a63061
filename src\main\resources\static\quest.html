<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务系统 - 洪荒游戏</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f8ff;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: #2c5aa0;
            border-bottom: 2px solid #66ccff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .quest-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            color: #2c5aa0;
            border-bottom-color: #66ccff;
            background-color: #f8f9fa;
        }
        
        .tab:hover {
            background-color: #f8f9fa;
        }
        
        .quest-list {
            min-height: 300px;
        }
        
        .quest-item {
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin-bottom: 10px;
            padding: 15px;
            background-color: #fafafa;
            transition: all 0.3s;
        }
        
        .quest-item:hover {
            background-color: #f0f8ff;
            border-color: #66ccff;
        }
        
        .quest-title {
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
            cursor: pointer;
        }
        
        .quest-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .quest-actions {
            text-align: right;
        }
        
        .btn {
            padding: 5px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 5px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #2c5aa0;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1e3d6f;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .pagination {
            text-align: center;
            margin-top: 20px;
        }
        
        .pagination button {
            margin: 0 5px;
        }
        
        .empty-message {
            text-align: center;
            color: #666;
            padding: 50px;
            font-style: italic;
        }
        
        .quest-detail {
            display: none;
            border: 2px solid #66ccff;
            border-radius: 5px;
            padding: 20px;
            margin-top: 20px;
            background-color: #f8f9fa;
        }
        
        .quest-detail.show {
            display: block;
        }
        
        .navigation {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .navigation a {
            color: #2c5aa0;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .navigation a:hover {
            text-decoration: underline;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .success {
            color: #155724;
            text-align: center;
            padding: 20px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>【任务栏】</h1>
        </div>
        
        <div id="message-area"></div>
        
        <div class="quest-tabs">
            <button class="tab active" onclick="switchTab('current')">当前任务</button>
            <button class="tab" onclick="switchTab('available')">可接任务</button>
            <button class="tab" onclick="switchTab('completed')">已完成</button>
        </div>
        
        <div id="quest-content">
            <div id="loading" class="loading">加载中...</div>
            <div id="quest-list" class="quest-list" style="display: none;"></div>
            <div id="pagination" class="pagination" style="display: none;"></div>
        </div>
        
        <div id="quest-detail" class="quest-detail"></div>
        
        <div class="navigation">
            <a href="game.html">返回游戏</a>
            <a href="javascript:void(0)" onclick="refreshQuests()">刷新</a>
        </div>
    </div>

    <script>
        let currentTab = 'current';
        let currentPage = 1;
        let currentQuests = [];
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadQuests();
        });
        
        // 切换标签页
        function switchTab(tab) {
            currentTab = tab;
            currentPage = 1;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            // 隐藏任务详情
            document.getElementById('quest-detail').classList.remove('show');
            
            loadQuests();
        }
        
        // 加载任务列表
        function loadQuests() {
            showLoading();
            
            let url = '';
            switch(currentTab) {
                case 'current':
                    url = '/honghuang-game/api/quest/player';
                    break;
                case 'available':
                    url = '/honghuang-game/api/quest/available';
                    break;
                case 'completed':
                    url = '/honghuang-game/api/quest/completed';
                    break;
            }
            
            fetch(url + '?page=' + currentPage)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        displayQuests(data.data);
                    } else {
                        showError(data.message || '加载任务失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示任务列表
        function displayQuests(quests) {
            const questList = document.getElementById('quest-list');
            questList.style.display = 'block';
            
            if (!quests || quests.length === 0) {
                questList.innerHTML = '<div class="empty-message">暂无任务</div>';
                return;
            }
            
            currentQuests = quests;
            let html = '';
            
            quests.forEach((quest, index) => {
                html += `
                    <div class="quest-item">
                        <div class="quest-title" onclick="showQuestDetail(${index})">
                            ${quest.name} (${quest.requiredLevel}级)
                        </div>
                        <div class="quest-info">
                            ${quest.description || ''}
                        </div>
                        <div class="quest-actions">
                            ${getQuestActions(quest)}
                        </div>
                    </div>
                `;
            });
            
            questList.innerHTML = html;
        }
        
        // 获取任务操作按钮
        function getQuestActions(quest) {
            switch(currentTab) {
                case 'current':
                    return `
                        <button class="btn btn-success" onclick="completeQuest(${quest.id})">完成任务</button>
                        <button class="btn btn-danger" onclick="abandonQuest(${quest.id})">放弃任务</button>
                    `;
                case 'available':
                    return `<button class="btn btn-primary" onclick="acceptQuest(${quest.id})">接取任务</button>`;
                case 'completed':
                    return `<span style="color: #28a745;">已完成</span>`;
                default:
                    return '';
            }
        }
        
        // 显示任务详情
        function showQuestDetail(index) {
            const quest = currentQuests[index];
            const detailDiv = document.getElementById('quest-detail');
            
            detailDiv.innerHTML = `
                <h3>${quest.name}</h3>
                <p><strong>等级要求:</strong> ${quest.requiredLevel}级</p>
                <p><strong>任务描述:</strong> ${quest.description || '暂无描述'}</p>
                <p><strong>任务目标:</strong> ${quest.objective || '暂无目标'}</p>
                <p><strong>经验奖励:</strong> ${quest.rewardExp || 0}</p>
                <p><strong>金钱奖励:</strong> ${quest.rewardCopper || 0}铜钱</p>
                <div style="text-align: right; margin-top: 15px;">
                    <button class="btn btn-warning" onclick="hideQuestDetail()">关闭</button>
                </div>
            `;
            
            detailDiv.classList.add('show');
        }
        
        // 隐藏任务详情
        function hideQuestDetail() {
            document.getElementById('quest-detail').classList.remove('show');
        }
        
        // 接取任务
        function acceptQuest(questId) {
            fetch('/honghuang-game/api/quest/accept', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ questId: questId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('任务接取成功！');
                    loadQuests();
                } else {
                    showError(data.message || '接取任务失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 完成任务
        function completeQuest(questId) {
            fetch('/honghuang-game/api/quest/complete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ questId: questId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('任务完成！获得奖励：' + (data.data.rewardDesc || ''));
                    loadQuests();
                } else {
                    showError(data.message || '完成任务失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 放弃任务
        function abandonQuest(questId) {
            if (!confirm('确定要放弃这个任务吗？')) {
                return;
            }
            
            fetch('/honghuang-game/api/quest/abandon', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ questId: questId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('任务已放弃');
                    loadQuests();
                } else {
                    showError(data.message || '放弃任务失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 刷新任务
        function refreshQuests() {
            loadQuests();
        }
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('quest-list').style.display = 'none';
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 显示错误消息
        function showError(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="error">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="success">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
