#!/bin/bash

# 洪荒游戏完整流程测试脚本

API_BASE="http://localhost:8080/honghuang-game/api"
COOKIE_FILE="test_flow_cookies.txt"

echo "=== 洪荒游戏完整流程测试 ==="
echo

# 1. 注册用户
echo "1. 注册新用户..."
REGISTER_RESPONSE=$(curl -s -c "$COOKIE_FILE" -X POST -H "Content-Type: application/json" \
  -d '{"username":"flowtest","password":"123456","confirmPassword":"123456","channel":"web"}' \
  "$API_BASE/user/register")

echo "注册响应: $REGISTER_RESPONSE"
echo

# 2. 用户登录
echo "2. 用户登录..."
LOGIN_RESPONSE=$(curl -s -b "$COOKIE_FILE" -c "$COOKIE_FILE" -X POST -H "Content-Type: application/json" \
  -d '{"username":"flowtest","password":"123456"}' \
  "$API_BASE/user/login")

echo "登录响应: $LOGIN_RESPONSE"
echo

# 3. 获取角色列表（应该为空）
echo "3. 获取角色列表（初始应为空）..."
ROLE_LIST_RESPONSE=$(curl -s -b "$COOKIE_FILE" "$API_BASE/role/list")

echo "角色列表响应: $ROLE_LIST_RESPONSE"
echo

# 4. 创建角色
echo "4. 创建新角色..."
CREATE_ROLE_RESPONSE=$(curl -s -b "$COOKIE_FILE" -X POST -H "Content-Type: application/json" \
  -d '{"roleName":"流程测试角色","race":1,"sex":1}' \
  "$API_BASE/role/create")

echo "创建角色响应: $CREATE_ROLE_RESPONSE"
echo

# 5. 再次获取角色列表（应该有一个角色）
echo "5. 再次获取角色列表（应该有一个角色）..."
ROLE_LIST_RESPONSE2=$(curl -s -b "$COOKIE_FILE" "$API_BASE/role/list")

echo "角色列表响应: $ROLE_LIST_RESPONSE2"
echo

# 6. 选择角色
echo "6. 选择角色..."
SELECT_ROLE_RESPONSE=$(curl -s -b "$COOKIE_FILE" -X POST "$API_BASE/role/select/2")

echo "选择角色响应: $SELECT_ROLE_RESPONSE"
echo

# 7. 获取当前角色
echo "7. 获取当前角色..."
CURRENT_ROLE_RESPONSE=$(curl -s -b "$COOKIE_FILE" "$API_BASE/role/current")

echo "当前角色响应: $CURRENT_ROLE_RESPONSE"
echo

# 清理
rm -f "$COOKIE_FILE"

echo "=== 测试完成 ==="
