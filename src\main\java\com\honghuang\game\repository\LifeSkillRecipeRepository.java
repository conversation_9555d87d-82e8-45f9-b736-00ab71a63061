package com.honghuang.game.repository;

import com.honghuang.game.entity.LifeSkillRecipe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 生活技能配方数据访问层
 */
@Repository
public interface LifeSkillRecipeRepository extends JpaRepository<LifeSkillRecipe, Integer> {

    /**
     * 根据技能类型查找配方
     */
    List<LifeSkillRecipe> findBySkillTypeAndActiveOrderByRequiredSkillLevelAsc(Integer skillType, Integer active);

    /**
     * 根据需要技能等级查找配方
     */
    List<LifeSkillRecipe> findByRequiredSkillLevelAndActiveOrderBySkillTypeAsc(Integer requiredSkillLevel, Integer active);

    /**
     * 根据产出物品ID查找配方
     */
    List<LifeSkillRecipe> findByOutputItemIdAndActiveOrderByRequiredSkillLevelAsc(Integer outputItemId, Integer active);

    /**
     * 查找激活的配方
     */
    List<LifeSkillRecipe> findByActiveOrderBySkillTypeAscRequiredSkillLevelAsc(Integer active);

    /**
     * 根据技能等级范围查找配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.skillType = :skillType AND lsr.active = 1 " +
           "AND lsr.requiredSkillLevel BETWEEN :minLevel AND :maxLevel ORDER BY lsr.requiredSkillLevel ASC")
    List<LifeSkillRecipe> findBySkillTypeAndLevelRange(@Param("skillType") Integer skillType, 
                                                       @Param("minLevel") Integer minLevel, 
                                                       @Param("maxLevel") Integer maxLevel);

    /**
     * 查找角色可制作的配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.skillType = :skillType AND lsr.active = 1 " +
           "AND lsr.requiredSkillLevel <= :skillLevel ORDER BY lsr.requiredSkillLevel DESC")
    List<LifeSkillRecipe> findCraftableRecipes(@Param("skillType") Integer skillType, @Param("skillLevel") Integer skillLevel);

    /**
     * 根据材料查找配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.active = 1 AND " +
           "(lsr.material1Id = :materialId OR lsr.material2Id = :materialId OR " +
           "lsr.material3Id = :materialId OR lsr.material4Id = :materialId) " +
           "ORDER BY lsr.requiredSkillLevel ASC")
    List<LifeSkillRecipe> findByMaterial(@Param("materialId") Integer materialId);

    /**
     * 根据配方名称模糊查找
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.name LIKE %:name% AND lsr.active = 1 ORDER BY lsr.name ASC")
    List<LifeSkillRecipe> findByNameContaining(@Param("name") String name);

    /**
     * 查找高级配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.requiredSkillLevel >= :minLevel AND lsr.active = 1 ORDER BY lsr.requiredSkillLevel ASC")
    List<LifeSkillRecipe> findAdvancedRecipes(@Param("minLevel") Integer minLevel);

    /**
     * 查找新手配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.requiredSkillLevel <= :maxLevel AND lsr.active = 1 ORDER BY lsr.requiredSkillLevel ASC")
    List<LifeSkillRecipe> findBeginnerRecipes(@Param("maxLevel") Integer maxLevel);

    /**
     * 根据制作时间查找配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.craftTime <= :maxTime AND lsr.active = 1 ORDER BY lsr.craftTime ASC")
    List<LifeSkillRecipe> findQuickRecipes(@Param("maxTime") Integer maxTime);

    /**
     * 根据成功率查找配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.baseSuccessRate >= :minRate AND lsr.active = 1 ORDER BY lsr.baseSuccessRate DESC")
    List<LifeSkillRecipe> findHighSuccessRateRecipes(@Param("minRate") Double minRate);

    /**
     * 查找经验奖励高的配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.expReward >= :minExp AND lsr.active = 1 ORDER BY lsr.expReward DESC")
    List<LifeSkillRecipe> findHighExpRecipes(@Param("minExp") Long minExp);

    /**
     * 统计技能类型的配方数量
     */
    @Query("SELECT COUNT(lsr) FROM LifeSkillRecipe lsr WHERE lsr.skillType = :skillType AND lsr.active = 1")
    long countBySkillType(@Param("skillType") Integer skillType);

    /**
     * 统计指定等级要求的配方数量
     */
    @Query("SELECT COUNT(lsr) FROM LifeSkillRecipe lsr WHERE lsr.requiredSkillLevel = :level AND lsr.active = 1")
    long countByRequiredLevel(@Param("level") Integer level);

    /**
     * 查找使用特定材料组合的配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr WHERE lsr.active = 1 AND " +
           "lsr.material1Id = :material1Id AND lsr.material2Id = :material2Id")
    List<LifeSkillRecipe> findByMaterialCombination(@Param("material1Id") Integer material1Id, @Param("material2Id") Integer material2Id);

    /**
     * 查找产出指定类型物品的配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr JOIN lsr.outputItem oi WHERE lsr.active = 1 AND oi.type = :itemType ORDER BY lsr.requiredSkillLevel ASC")
    List<LifeSkillRecipe> findByOutputItemType(@Param("itemType") Integer itemType);

    /**
     * 查找产出指定品质物品的配方
     */
    @Query("SELECT lsr FROM LifeSkillRecipe lsr JOIN lsr.outputItem oi WHERE lsr.active = 1 AND oi.quality = :quality ORDER BY lsr.requiredSkillLevel ASC")
    List<LifeSkillRecipe> findByOutputItemQuality(@Param("quality") Integer quality);
}
