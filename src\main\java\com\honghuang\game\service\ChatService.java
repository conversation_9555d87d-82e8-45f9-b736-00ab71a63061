package com.honghuang.game.service;

import com.honghuang.game.entity.ChatMessage;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.ChatMessageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天服务类
 */
@Service
@Transactional
public class ChatService {

    @Autowired
    private ChatMessageRepository chatMessageRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 发送聊天消息
     */
    public ChatMessage sendMessage(Integer senderId, String content, Integer channelType, 
                                  Integer receiverId, Integer factionId) {
        System.out.println("发送聊天消息: senderId=" + senderId + ", channelType=" + channelType);

        PlayerRole sender = playerRoleService.findById(senderId);

        // 验证消息内容
        if (content == null || content.trim().isEmpty()) {
            throw new BusinessException(400, "消息内容不能为空");
        }

        if (content.length() > 200) {
            throw new BusinessException(400, "消息内容过长，最多200字符");
        }

        // 验证频道类型
        if (channelType == null || channelType < 1 || channelType > 4) {
            throw new BusinessException(400, "无效的频道类型");
        }

        // 检查发言权限
        if (!canSendMessage(sender, channelType)) {
            throw new BusinessException(400, "没有发言权限");
        }

        // 敏感词过滤
        content = filterSensitiveWords(content);

        // 创建聊天消息
        ChatMessage message = new ChatMessage();
        message.setSenderId(senderId);
        message.setSenderName(sender.getName());
        message.setContent(content);
        message.setChannelType(channelType);
        message.setSendTime(LocalDateTime.now());

        // 设置接收者（私聊）
        if (channelType == 2 && receiverId != null) {
            PlayerRole receiver = playerRoleService.findById(receiverId);
            message.setReceiverId(receiverId);
            message.setReceiverName(receiver.getName());
        }

        // 设置帮派ID（帮派频道）
        if (channelType == 3 && factionId != null) {
            message.setFactionId(factionId);
        }

        message = chatMessageRepository.save(message);

        System.out.println("聊天消息发送成功: messageId=" + message.getId());
        return message;
    }

    /**
     * 发送系统消息
     */
    public ChatMessage sendSystemMessage(String content, Integer channelType, Integer receiverId) {
        System.out.println("发送系统消息: content=" + content + ", channelType=" + channelType);

        ChatMessage message = new ChatMessage();
        message.setSenderName("系统");
        message.setContent(content);
        message.setChannelType(channelType != null ? channelType : 4);
        message.setIsSystem(1);
        message.setSendTime(LocalDateTime.now());

        if (receiverId != null) {
            PlayerRole receiver = playerRoleService.findById(receiverId);
            message.setReceiverId(receiverId);
            message.setReceiverName(receiver.getName());
        }

        message = chatMessageRepository.save(message);

        System.out.println("系统消息发送成功: messageId=" + message.getId());
        return message;
    }

    /**
     * 获取世界频道消息
     */
    @Transactional(readOnly = true)
    public List<ChatMessage> getWorldMessages(int limit) {
        List<ChatMessage> messages = chatMessageRepository.findWorldMessages();
        return messages.size() > limit ? messages.subList(0, limit) : messages;
    }

    /**
     * 获取私聊消息
     */
    @Transactional(readOnly = true)
    public List<ChatMessage> getPrivateMessages(Integer playerId, Integer friendId, int limit) {
        List<ChatMessage> messages = chatMessageRepository.findPrivateMessages(playerId, friendId);
        return messages.size() > limit ? messages.subList(0, limit) : messages;
    }

    /**
     * 获取帮派频道消息
     */
    @Transactional(readOnly = true)
    public List<ChatMessage> getFactionMessages(Integer factionId, int limit) {
        List<ChatMessage> messages = chatMessageRepository.findFactionMessages(factionId);
        return messages.size() > limit ? messages.subList(0, limit) : messages;
    }

    /**
     * 获取系统消息
     */
    @Transactional(readOnly = true)
    public List<ChatMessage> getSystemMessages(int limit) {
        List<ChatMessage> messages = chatMessageRepository.findSystemMessages();
        return messages.size() > limit ? messages.subList(0, limit) : messages;
    }

    /**
     * 获取玩家的聊天记录
     */
    @Transactional(readOnly = true)
    public List<ChatMessage> getPlayerChatHistory(Integer playerId, int limit) {
        List<ChatMessage> messages = chatMessageRepository.findBySenderIdOrderBySendTimeDesc(playerId);
        return messages.size() > limit ? messages.subList(0, limit) : messages;
    }

    /**
     * 获取最近聊天消息
     */
    @Transactional(readOnly = true)
    public List<ChatMessage> getRecentMessages(Integer channelType, int limit) {
        List<ChatMessage> messages = chatMessageRepository.findRecentMessagesByChannel(channelType);
        return messages.size() > limit ? messages.subList(0, limit) : messages;
    }

    /**
     * 删除聊天消息
     */
    public boolean deleteMessage(Integer messageId, Integer operatorId) {
        System.out.println("删除聊天消息: messageId=" + messageId + ", operatorId=" + operatorId);

        ChatMessage message = chatMessageRepository.findById(messageId.longValue())
                .orElseThrow(() -> new BusinessException(404, "消息不存在"));

        // 检查删除权限（只能删除自己的消息或管理员权限）
        if (!message.getSenderId().equals(operatorId)) {
            PlayerRole operator = playerRoleService.findById(operatorId);
            // 这里可以添加管理员权限检查
            throw new BusinessException(403, "无权限删除此消息");
        }

        chatMessageRepository.delete(message);

        System.out.println("聊天消息删除成功: messageId=" + messageId);
        return true;
    }

    /**
     * 清理过期聊天记录
     */
    @Transactional
    public void cleanupExpiredMessages() {
        System.out.println("清理过期聊天记录");

        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7); // 保留7天
        int count = chatMessageRepository.deleteExpiredMessages(cutoffTime);

        System.out.println("清理过期聊天记录完成，共清理" + count + "条消息");
    }

    /**
     * 获取聊天统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getChatStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        long totalMessages = chatMessageRepository.count();
        long worldMessages = chatMessageRepository.countByChannelType(1);
        long privateMessages = chatMessageRepository.countByChannelType(2);
        long factionMessages = chatMessageRepository.countByChannelType(3);
        long systemMessages = chatMessageRepository.countByChannelType(4);
        
        stats.put("totalMessages", totalMessages);
        stats.put("worldMessages", worldMessages);
        stats.put("privateMessages", privateMessages);
        stats.put("factionMessages", factionMessages);
        stats.put("systemMessages", systemMessages);
        
        return stats;
    }

    /**
     * 检查是否可以发言
     */
    private boolean canSendMessage(PlayerRole player, Integer channelType) {
        // 等级限制
        if (channelType == 1 && player.getLevel() < 5) { // 世界频道需要5级
            return false;
        }

        // 这里可以添加更多限制条件，如禁言状态等
        return true;
    }

    /**
     * 敏感词过滤
     */
    private String filterSensitiveWords(String content) {
        // 简单的敏感词过滤实现
        String[] sensitiveWords = {"傻逼", "操你妈", "去死", "垃圾游戏"};
        
        for (String word : sensitiveWords) {
            if (content.contains(word)) {
                content = content.replace(word, "***");
            }
        }
        
        return content;
    }

    /**
     * 广播系统公告
     */
    public void broadcastAnnouncement(String announcement) {
        System.out.println("广播系统公告: " + announcement);

        sendSystemMessage("[系统公告] " + announcement, 1, null);

        System.out.println("系统公告广播完成");
    }

    /**
     * 发送私聊消息
     */
    public ChatMessage sendPrivateMessage(Integer senderId, Integer receiverId, String content) {
        return sendMessage(senderId, content, 2, receiverId, null);
    }

    /**
     * 发送世界消息
     */
    public ChatMessage sendWorldMessage(Integer senderId, String content) {
        return sendMessage(senderId, content, 1, null, null);
    }

    /**
     * 发送帮派消息
     */
    public ChatMessage sendFactionMessage(Integer senderId, Integer factionId, String content) {
        return sendMessage(senderId, content, 3, null, factionId);
    }

    /**
     * 检查消息是否包含敏感词
     */
    public boolean containsSensitiveWords(String content) {
        String[] sensitiveWords = {"傻逼", "操你妈", "去死", "垃圾游戏"};
        
        for (String word : sensitiveWords) {
            if (content.contains(word)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取频道名称
     */
    public String getChannelName(Integer channelType) {
        switch (channelType) {
            case 1: return "世界";
            case 2: return "私聊";
            case 3: return "帮派";
            case 4: return "系统";
            default: return "未知";
        }
    }
}
