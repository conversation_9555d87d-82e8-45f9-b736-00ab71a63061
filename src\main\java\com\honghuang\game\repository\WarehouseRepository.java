package com.honghuang.game.repository;

import com.honghuang.game.entity.Warehouse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 仓库数据访问层
 */
@Repository
public interface WarehouseRepository extends JpaRepository<Warehouse, Integer> {

    /**
     * 根据角色ID查找仓库物品
     */
    List<Warehouse> findByRoleIdOrderByPageNumberAscSlotIndexAsc(Integer roleId);

    /**
     * 根据角色ID和页数查找物品
     */
    List<Warehouse> findByRoleIdAndPageNumberOrderBySlotIndexAsc(Integer roleId, Integer pageNumber);

    /**
     * 根据角色ID、页数和槽位查找物品
     */
    Optional<Warehouse> findByRoleIdAndPageNumberAndSlotIndex(Integer roleId, Integer pageNumber, Integer slotIndex);

    /**
     * 根据角色ID和物品ID查找物品
     */
    List<Warehouse> findByRoleIdAndItemIdOrderByPageNumberAscSlotIndexAsc(Integer roleId, Integer itemId);

    /**
     * 查找指定页的空槽位
     */
    @Query("SELECT w.slotIndex FROM Warehouse w WHERE w.roleId = :roleId AND w.pageNumber = :pageNumber " +
           "AND (w.itemId IS NULL OR w.quantity <= 0) ORDER BY w.slotIndex ASC")
    List<Integer> findEmptySlots(@Param("roleId") Integer roleId, @Param("pageNumber") Integer pageNumber);

    /**
     * 查找第一个空槽位
     */
    @Query("SELECT w FROM Warehouse w WHERE w.roleId = :roleId AND (w.itemId IS NULL OR w.quantity <= 0) " +
           "ORDER BY w.pageNumber ASC, w.slotIndex ASC")
    Optional<Warehouse> findFirstEmptySlot(@Param("roleId") Integer roleId);

    /**
     * 查找可堆叠的同类物品
     */
    @Query("SELECT w FROM Warehouse w JOIN w.item item WHERE w.roleId = :roleId AND w.itemId = :itemId " +
           "AND item.stackable = 1 AND w.quantity < item.maxStack ORDER BY w.pageNumber ASC, w.slotIndex ASC")
    List<Warehouse> findStackableItems(@Param("roleId") Integer roleId, @Param("itemId") Integer itemId);

    /**
     * 统计仓库使用槽位数
     */
    @Query("SELECT COUNT(w) FROM Warehouse w WHERE w.roleId = :roleId AND w.itemId IS NOT NULL AND w.quantity > 0")
    long countUsedSlots(@Param("roleId") Integer roleId);

    /**
     * 统计指定页使用槽位数
     */
    @Query("SELECT COUNT(w) FROM Warehouse w WHERE w.roleId = :roleId AND w.pageNumber = :pageNumber " +
           "AND w.itemId IS NOT NULL AND w.quantity > 0")
    long countUsedSlotsInPage(@Param("roleId") Integer roleId, @Param("pageNumber") Integer pageNumber);

    /**
     * 统计仓库空槽位数
     */
    @Query("SELECT COUNT(w) FROM Warehouse w WHERE w.roleId = :roleId AND (w.itemId IS NULL OR w.quantity <= 0)")
    long countEmptySlots(@Param("roleId") Integer roleId);

    /**
     * 根据物品类型查找仓库物品
     */
    @Query("SELECT w FROM Warehouse w JOIN w.item item WHERE w.roleId = :roleId AND item.type = :itemType " +
           "AND w.quantity > 0 ORDER BY w.pageNumber ASC, w.slotIndex ASC")
    List<Warehouse> findByItemType(@Param("roleId") Integer roleId, @Param("itemType") Integer itemType);

    /**
     * 查找绑定物品
     */
    @Query("SELECT w FROM Warehouse w WHERE w.roleId = :roleId AND w.bound = 1 AND w.quantity > 0 " +
           "ORDER BY w.pageNumber ASC, w.slotIndex ASC")
    List<Warehouse> findBoundItems(@Param("roleId") Integer roleId);

    /**
     * 更新物品数量
     */
    @Modifying
    @Query("UPDATE Warehouse w SET w.quantity = :quantity WHERE w.id = :warehouseId")
    int updateQuantity(@Param("warehouseId") Integer warehouseId, @Param("quantity") Integer quantity);

    /**
     * 更新物品位置
     */
    @Modifying
    @Query("UPDATE Warehouse w SET w.pageNumber = :pageNumber, w.slotIndex = :slotIndex WHERE w.id = :warehouseId")
    int updateLocation(@Param("warehouseId") Integer warehouseId, @Param("pageNumber") Integer pageNumber, @Param("slotIndex") Integer slotIndex);

    /**
     * 绑定物品
     */
    @Modifying
    @Query("UPDATE Warehouse w SET w.bound = 1 WHERE w.id = :warehouseId")
    int bindItem(@Param("warehouseId") Integer warehouseId);

    /**
     * 清空槽位
     */
    @Modifying
    @Query("UPDATE Warehouse w SET w.itemId = NULL, w.quantity = 0 WHERE w.id = :warehouseId")
    int clearSlot(@Param("warehouseId") Integer warehouseId);

    /**
     * 删除角色的所有仓库物品
     */
    @Modifying
    @Query("DELETE FROM Warehouse w WHERE w.roleId = :roleId")
    int deleteByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找长期未访问的物品
     */
    @Query("SELECT w FROM Warehouse w WHERE w.roleId = :roleId AND w.lastAccessTime <= CURRENT_DATE - 30 AND w.quantity > 0")
    List<Warehouse> findLongTimeUnaccessedItems(@Param("roleId") Integer roleId);

    /**
     * 统计指定物品数量
     */
    @Query("SELECT COALESCE(SUM(w.quantity), 0) FROM Warehouse w WHERE w.roleId = :roleId AND w.itemId = :itemId")
    long countItemQuantity(@Param("roleId") Integer roleId, @Param("itemId") Integer itemId);

    /**
     * 查找仓库价值最高的物品
     */
    @Query("SELECT w FROM Warehouse w JOIN w.item item WHERE w.roleId = :roleId AND w.quantity > 0 " +
           "ORDER BY (item.sellPrice * w.quantity) DESC")
    List<Warehouse> findMostValuableItems(@Param("roleId") Integer roleId);

    /**
     * 统计角色已开启的仓库页数
     */
    @Query("SELECT COUNT(DISTINCT w.pageNumber) FROM Warehouse w WHERE w.roleId = :roleId")
    long countOpenedPages(@Param("roleId") Integer roleId);

    /**
     * 查找角色使用的最大页数
     */
    @Query("SELECT MAX(w.pageNumber) FROM Warehouse w WHERE w.roleId = :roleId AND w.itemId IS NOT NULL")
    Optional<Integer> findMaxUsedPage(@Param("roleId") Integer roleId);
}
