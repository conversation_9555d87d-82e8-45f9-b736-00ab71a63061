package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.constant.RankingConstants;
import com.honghuang.game.dto.RankingDTO;
import com.honghuang.game.service.RankingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 排行榜控制器
 */
@Slf4j
@Controller
@RequestMapping("/ranking")
public class RankingController {

    @Autowired
    private RankingService rankingService;

    /**
     * 排行榜测试页
     */
    @GetMapping("/test")
    public String test() {
        return "ranking/test";
    }

    /**
     * 排行榜主页
     */
    @GetMapping("/index")
    public String index(Model model, HttpServletRequest request) {
        try {
            // 获取所有排行榜分类
            Map<Integer, List<Map<String, Object>>> categories = rankingService.getAllRankingCategories();
            model.addAttribute("categories", categories);
            
            // 添加分类常量
            model.addAttribute("CATEGORY_PERSONAL", RankingConstants.CATEGORY_PERSONAL);
            model.addAttribute("CATEGORY_BATTLE", RankingConstants.CATEGORY_BATTLE);
            model.addAttribute("CATEGORY_PET", RankingConstants.CATEGORY_PET);
            model.addAttribute("CATEGORY_EQUIPMENT", RankingConstants.CATEGORY_EQUIPMENT);
            
            return "ranking/index";
        } catch (Exception e) {
            log.error("访问排行榜主页失败", e);
            model.addAttribute("error", "系统错误，请稍后重试");
            return "error";
        }
    }

    /**
     * 排行榜详情页
     */
    @GetMapping("/detail")
    public String detail(@RequestParam("type") String type,
                        @RequestParam(value = "limit", defaultValue = "10") int limit,
                        Model model, HttpServletRequest request) {
        try {
            // 验证排行榜类型
            if (!isValidRankType(type)) {
                model.addAttribute("error", "无效的排行榜类型");
                return "error";
            }
            
            // 获取排行榜数据
            List<RankingDTO> rankingList = rankingService.getRankingList(type, limit);
            
            // 获取当前玩家排名（如果已登录）
            Integer playerRank = 0;
            Integer roleId = getCurrentRoleId(request);
            if (roleId != null) {
                playerRank = rankingService.getPlayerRank(roleId, type);
            }
            
            model.addAttribute("rankingList", rankingList);
            model.addAttribute("playerRank", playerRank);
            model.addAttribute("type", type);
            model.addAttribute("typeName", RankingConstants.getRankName(type));
            model.addAttribute("description", RankingConstants.getRankDescription(type));
            model.addAttribute("unit", RankingConstants.getRankUnit(type));
            model.addAttribute("limit", limit);
            
            return "ranking/detail";
        } catch (Exception e) {
            log.error("访问排行榜详情页失败, type: {}", type, e);
            model.addAttribute("error", "系统错误，请稍后重试");
            return "error";
        }
    }

    /**
     * 获取排行榜数据API
     */
    @GetMapping("/api/list")
    @ResponseBody
    public Result<List<RankingDTO>> getRankingList(
            @RequestParam("type") String type,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        try {
            if (!isValidRankType(type)) {
                return Result.error(400, "无效的排行榜类型");
            }
            
            List<RankingDTO> rankingList = rankingService.getRankingList(type, limit);
            return Result.success("获取排行榜数据成功", rankingList);
        } catch (Exception e) {
            log.error("获取排行榜数据失败, type: {}, limit: {}", type, limit, e);
            return Result.error(500, "获取排行榜数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取玩家排名API
     */
    @GetMapping("/api/player-rank")
    @ResponseBody
    public Result<Integer> getPlayerRank(
            @RequestParam("roleId") Integer roleId,
            @RequestParam("type") String type) {
        try {
            if (!isValidRankType(type)) {
                return Result.error(400, "无效的排行榜类型");
            }
            
            Integer rank = rankingService.getPlayerRank(roleId, type);
            return Result.success("获取玩家排名成功", rank);
        } catch (Exception e) {
            log.error("获取玩家排名失败, roleId: {}, type: {}", roleId, type, e);
            return Result.error(500, "获取玩家排名失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有排行榜分类API
     */
    @GetMapping("/api/categories")
    @ResponseBody
    public Result<Map<Integer, List<Map<String, Object>>>> getAllCategories() {
        try {
            Map<Integer, List<Map<String, Object>>> categories = rankingService.getAllRankingCategories();
            return Result.success("获取排行榜分类成功", categories);
        } catch (Exception e) {
            log.error("获取排行榜分类失败", e);
            return Result.error(500, "获取排行榜分类失败: " + e.getMessage());
        }
    }

    /**
     * 更新排行榜数据API（内部接口）
     */
    @PostMapping("/api/update")
    @ResponseBody
    public Result<Void> updateRankingData(
            @RequestParam("roleId") Integer roleId,
            @RequestParam("type") String type,
            @RequestParam("value") Long value) {
        try {
            if (!isValidRankType(type)) {
                return Result.error(400, "无效的排行榜类型");
            }
            
            rankingService.updateRankingData(roleId, type, value);
            return Result.success("更新排行榜数据成功");
        } catch (Exception e) {
            log.error("更新排行榜数据失败, roleId: {}, type: {}, value: {}", roleId, type, value, e);
            return Result.error(500, "更新排行榜数据失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新排行榜数据API（内部接口）
     */
    @PostMapping("/api/batch-update")
    @ResponseBody
    public Result<Void> batchUpdateRankingData(
            @RequestParam("roleId") Integer roleId,
            @RequestBody Map<String, Long> dataMap) {
        try {
            rankingService.batchUpdateRankingData(roleId, dataMap);
            return Result.success("批量更新排行榜数据成功");
        } catch (Exception e) {
            log.error("批量更新排行榜数据失败, roleId: {}, dataMap: {}", roleId, dataMap, e);
            return Result.error(500, "批量更新排行榜数据失败: " + e.getMessage());
        }
    }

    /**
     * 验证排行榜类型是否有效
     */
    private boolean isValidRankType(String type) {
        return RankingConstants.RANK_NAMES.containsKey(type);
    }

    /**
     * 获取当前登录角色ID
     */
    private Integer getCurrentRoleId(HttpServletRequest request) {
        // TODO: 从session或token中获取当前登录的角色ID
        // 这里暂时返回null，需要根据实际的认证机制实现
        return null;
    }
}
