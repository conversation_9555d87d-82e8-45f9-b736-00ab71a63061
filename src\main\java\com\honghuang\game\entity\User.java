package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"password", "roles"})
@Entity
@Table(name = "user_info")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户登录名
     */
    @Column(name = "username", unique = true, length = 20)
    private String username;

    /**
     * 用户登录密码
     */
    @Column(name = "password", length = 60)
    private String password;

    /**
     * 登录状态 1为登录 0为未登录
     */
    @Column(name = "login_state")
    private Integer loginState = 0;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 最后登录IP地址
     */
    @Column(name = "last_login_ip", length = 20)
    private String lastLoginIp;

    /**
     * 最后一次登录时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 元宝数量
     */
    @Column(name = "yuanbao")
    private Integer yuanbao = 0;

    /**
     * 商城积分数量
     */
    @Column(name = "jifen")
    private Integer jifen = 0;

    /**
     * 超级渠道
     */
    @Column(name = "super_channel")
    private String superChannel;

    /**
     * 渠道来源
     */
    @Column(name = "channel")
    private String channel;

    /**
     * 用户角色列表
     */
    @OneToMany(mappedBy = "userId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PlayerRole> roles;

    // 业务方法

    /**
     * 是否在线
     */
    public boolean isOnline() {
        return loginState == 1;
    }

    /**
     * 设置在线状态
     */
    public void setOnline(boolean online) {
        this.loginState = online ? 1 : 0;
    }

    /**
     * 增加元宝
     */
    public boolean addYuanbao(Integer amount) {
        if (amount <= 0) return false;
        this.yuanbao += amount;
        return true;
    }

    /**
     * 消费元宝
     */
    public boolean spendYuanbao(Integer amount) {
        if (amount <= 0 || this.yuanbao < amount) return false;
        this.yuanbao -= amount;
        return true;
    }

    /**
     * 增加积分
     */
    public boolean addJifen(Integer amount) {
        if (amount <= 0) return false;
        this.jifen += amount;
        return true;
    }

    /**
     * 消费积分
     */
    public boolean spendJifen(Integer amount) {
        if (amount <= 0 || this.jifen < amount) return false;
        this.jifen -= amount;
        return true;
    }

    /**
     * 获取用户等级（基于最高角色等级）
     */
    public Integer getUserLevel() {
        if (roles == null || roles.isEmpty()) {
            return 1;
        }
        return roles.stream()
                .mapToInt(PlayerRole::getLevel)
                .max()
                .orElse(1);
    }

    /**
     * 获取角色数量
     */
    public int getRoleCount() {
        return roles != null ? roles.size() : 0;
    }

    /**
     * 检查是否为新用户
     */
    public boolean isNewUser() {
        return createTime != null && 
               createTime.isAfter(LocalDateTime.now().minusDays(7));
    }

    /**
     * 检查是否为活跃用户
     */
    public boolean isActiveUser() {
        return lastLoginTime != null && 
               lastLoginTime.isAfter(LocalDateTime.now().minusDays(3));
    }

    /**
     * 获取注册天数
     */
    public long getRegistrationDays() {
        if (createTime == null) return 0;
        return java.time.Duration.between(createTime, LocalDateTime.now()).toDays();
    }

    /**
     * 获取最后登录经过时间（小时）
     */
    public long getLastLoginHours() {
        if (lastLoginTime == null) return Long.MAX_VALUE;
        return java.time.Duration.between(lastLoginTime, LocalDateTime.now()).toHours();
    }

    /**
     * 更新登录信息
     */
    public void updateLoginInfo(String ip) {
        this.loginState = 1;
        this.lastLoginIp = ip;
        this.lastLoginTime = LocalDateTime.now();
    }

    /**
     * 登出
     */
    public void logout() {
        this.loginState = 0;
    }

    /**
     * 获取用户显示名称
     */
    public String getDisplayName() {
        return username;
    }

    /**
     * 获取用户状态描述
     */
    public String getStatusDescription() {
        if (isOnline()) {
            return "在线";
        } else if (lastLoginTime != null) {
            long hours = getLastLoginHours();
            if (hours < 24) {
                return hours + "小时前在线";
            } else {
                long days = hours / 24;
                return days + "天前在线";
            }
        } else {
            return "从未登录";
        }
    }

    /**
     * 获取用户完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("用户: ").append(username);
        sb.append(" [").append(getStatusDescription()).append("]");
        sb.append(" 元宝: ").append(yuanbao);
        sb.append(" 积分: ").append(jifen);
        sb.append(" 角色数: ").append(getRoleCount());
        return sb.toString();
    }

    // 自定义构造函数
    public User(String username, String password) {
        this.username = username;
        this.password = password;
    }

    public User(String username, String password, String channel) {
        this.username = username;
        this.password = password;
        this.channel = channel;
    }
}
