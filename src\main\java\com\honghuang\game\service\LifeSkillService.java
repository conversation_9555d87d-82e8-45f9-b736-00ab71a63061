package com.honghuang.game.service;

import com.honghuang.game.entity.LifeSkill;
import com.honghuang.game.entity.LifeSkillRecipe;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.LifeSkillRecipeRepository;
import com.honghuang.game.repository.LifeSkillRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 生活技能服务类
 */
@Service
@Transactional
public class LifeSkillService {

    @Autowired
    private LifeSkillRepository lifeSkillRepository;

    @Autowired
    private LifeSkillRecipeRepository recipeRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    @Autowired
    private InventoryService inventoryService;

    /**
     * 学习生活技能
     */
    public LifeSkill learnSkill(Integer roleId, Integer skillType) {
        System.out.println("学习生活技能: roleId=" + roleId + ", skillType=" + skillType);

        PlayerRole role = playerRoleService.findById(roleId);

        // 检查是否已学习该技能
        Optional<LifeSkill> existingSkill = lifeSkillRepository.findByRoleIdAndSkillType(roleId, skillType);
        if (existingSkill.isPresent()) {
            throw new BusinessException(400, "已学习该技能");
        }

        // 检查技能类型是否有效
        if (skillType < 1 || skillType > 6) {
            throw new BusinessException(400, "无效的技能类型");
        }

        // 检查学习条件
        if (role.getLevel() < 10) {
            throw new BusinessException(400, "角色等级不足，需要10级以上才能学习生活技能");
        }

        // 创建技能
        LifeSkill skill = new LifeSkill();
        skill.setRoleId(roleId);
        skill.setSkillType(skillType);
        skill.setSkillLevel(1);
        skill.setExperience(0L);
        skill.setProficiency(0);
        skill.setMaxProficiency(100);
        skill.setStatus(1);

        skill = lifeSkillRepository.save(skill);

        System.out.println("生活技能学习成功: skillId=" + skill.getId());
        return skill;
    }

    /**
     * 使用配方制作物品
     */
    public boolean craftItem(Integer roleId, Integer recipeId, Integer quantity) {
        System.out.println("制作物品: roleId=" + roleId + ", recipeId=" + recipeId + ", quantity=" + quantity);

        if (quantity <= 0 || quantity > 10) {
            throw new BusinessException(400, "制作数量必须在1-10之间");
        }

        PlayerRole role = playerRoleService.findById(roleId);
        LifeSkillRecipe recipe = recipeRepository.findById(recipeId)
                .orElseThrow(() -> new BusinessException(404, "配方不存在"));

        // 获取对应技能
        LifeSkill skill = lifeSkillRepository.findByRoleIdAndSkillType(roleId, recipe.getSkillType())
                .orElseThrow(() -> new BusinessException(404, "未学习对应技能"));

        // 检查制作条件
        if (!recipe.canCraft(skill)) {
            throw new BusinessException(400, "不满足制作条件");
        }

        // 检查材料
        Map<Integer, Integer> materials = recipe.getMaterialRequirements();
        for (Map.Entry<Integer, Integer> entry : materials.entrySet()) {
            Integer itemId = entry.getKey();
            Integer requiredQuantity = entry.getValue() * quantity;
            
            if (!inventoryService.hasItem(roleId, itemId, requiredQuantity)) {
                throw new BusinessException(400, "材料不足");
            }
        }

        // 消耗材料
        for (Map.Entry<Integer, Integer> entry : materials.entrySet()) {
            Integer itemId = entry.getKey();
            Integer requiredQuantity = entry.getValue() * quantity;
            inventoryService.removeItem(roleId, itemId, requiredQuantity);
        }

        // 执行制作
        int successCount = 0;
        for (int i = 0; i < quantity; i++) {
            if (performCraft(skill, recipe)) {
                successCount++;
                // 给予产出物品
                inventoryService.addItem(roleId, recipe.getOutputItemId(), recipe.getOutputQuantity());
            }
        }

        // 使用技能（设置冷却）
        skill.useSkill();
        lifeSkillRepository.save(skill);

        // 给予经验和熟练度
        Long expReward = recipe.calculateExpReward(skill) * successCount;
        Integer proficiencyReward = recipe.getProficiencyReward() * successCount;
        
        skill.addExperience(expReward);
        skill.addProficiency(proficiencyReward);
        lifeSkillRepository.save(skill);

        System.out.println("制作完成: 成功" + successCount + "/" + quantity + "次");
        return successCount > 0;
    }

    /**
     * 采集资源
     */
    public boolean gatherResource(Integer roleId, Integer resourceItemId, Integer gatherTime) {
        System.out.println("采集资源: roleId=" + roleId + ", resourceItemId=" + resourceItemId);

        // 获取采集技能
        LifeSkill skill = lifeSkillRepository.findByRoleIdAndSkillType(roleId, 1)
                .orElseThrow(() -> new BusinessException(404, "未学习采集技能"));

        if (!skill.isAvailable() || !skill.isCooldownFinished()) {
            throw new BusinessException(400, "技能不可用或在冷却中");
        }

        // 计算采集成功率
        double successRate = skill.getSuccessRate();
        boolean success = Math.random() * 100 < successRate;

        if (success) {
            // 计算采集数量
            int baseQuantity = 1;
            int bonusQuantity = 0;
            
            // 技能等级加成
            if (skill.getSkillLevel() >= 20) bonusQuantity++;
            if (skill.getSkillLevel() >= 50) bonusQuantity++;
            if (skill.getSkillLevel() >= 80) bonusQuantity++;
            
            // 暴击判断
            if (Math.random() * 100 < skill.getCriticalRate()) {
                bonusQuantity += 2; // 暴击额外获得2个
            }

            int totalQuantity = baseQuantity + bonusQuantity;
            
            // 给予采集物品
            inventoryService.addItem(roleId, resourceItemId, totalQuantity);
            
            // 给予经验和熟练度
            Long expReward = (long) (10 + skill.getSkillLevel() * 2);
            skill.addExperience(expReward);
            skill.addProficiency(1);
        }

        // 使用技能
        skill.useSkill();
        lifeSkillRepository.save(skill);

        System.out.println("采集结果: " + (success ? "成功" : "失败"));
        return success;
    }

    /**
     * 获取角色的生活技能列表
     */
    @Transactional(readOnly = true)
    public List<LifeSkill> getPlayerSkills(Integer roleId) {
        return lifeSkillRepository.findByRoleIdOrderBySkillTypeAsc(roleId);
    }

    /**
     * 获取指定技能类型的配方列表
     */
    @Transactional(readOnly = true)
    public List<LifeSkillRecipe> getRecipesBySkillType(Integer skillType) {
        return recipeRepository.findBySkillTypeAndActiveOrderByRequiredSkillLevelAsc(skillType, 1);
    }

    /**
     * 获取角色可制作的配方
     */
    @Transactional(readOnly = true)
    public List<LifeSkillRecipe> getCraftableRecipes(Integer roleId, Integer skillType) {
        LifeSkill skill = lifeSkillRepository.findByRoleIdAndSkillType(roleId, skillType)
                .orElseThrow(() -> new BusinessException(404, "未学习对应技能"));

        return recipeRepository.findCraftableRecipes(skillType, skill.getSkillLevel());
    }

    /**
     * 搜索配方
     */
    @Transactional(readOnly = true)
    public List<LifeSkillRecipe> searchRecipes(String keyword) {
        return recipeRepository.findByNameContaining(keyword);
    }

    /**
     * 重置技能冷却
     */
    public boolean resetSkillCooldown(Integer roleId, Integer skillType) {
        System.out.println("重置技能冷却: roleId=" + roleId + ", skillType=" + skillType);

        LifeSkill skill = lifeSkillRepository.findByRoleIdAndSkillType(roleId, skillType)
                .orElseThrow(() -> new BusinessException(404, "技能不存在"));

        if (!skill.isOnCooldown()) {
            throw new BusinessException(400, "技能不在冷却中");
        }

        skill.resetCooldown();
        lifeSkillRepository.save(skill);

        System.out.println("技能冷却重置成功");
        return true;
    }

    /**
     * 处理过期的技能冷却
     */
    @Transactional
    public void processExpiredCooldowns() {
        System.out.println("处理过期的技能冷却");

        LocalDateTime cutoffTime = LocalDateTime.now();
        List<LifeSkill> expiredSkills = lifeSkillRepository.findSkillsToResetCooldown(cutoffTime);

        for (LifeSkill skill : expiredSkills) {
            if (skill.isCooldownFinished()) {
                skill.resetCooldown();
                lifeSkillRepository.save(skill);
            }
        }

        System.out.println("处理过期冷却完成，共处理" + expiredSkills.size() + "个技能");
    }

    /**
     * 获取技能统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getSkillStatistics(Integer roleId) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        List<LifeSkill> skills = getPlayerSkills(roleId);
        long totalSkills = skills.size();
        Integer maxLevel = lifeSkillRepository.findMaxSkillLevel(roleId);
        Double avgLevel = lifeSkillRepository.findAverageSkillLevel(roleId);
        
        stats.put("totalSkills", totalSkills);
        stats.put("maxSkillLevel", maxLevel != null ? maxLevel : 0);
        stats.put("averageSkillLevel", avgLevel != null ? avgLevel : 0.0);
        
        // 统计各技能类型
        for (LifeSkill skill : skills) {
            String skillName = skill.getSkillTypeName();
            stats.put(skillName + "Level", skill.getSkillLevel());
            stats.put(skillName + "Experience", skill.getExperience());
            stats.put(skillName + "Proficiency", skill.getProficiency());
        }
        
        return stats;
    }

    /**
     * 执行制作过程
     */
    private boolean performCraft(LifeSkill skill, LifeSkillRecipe recipe) {
        double successRate = recipe.calculateSuccessRate(skill);
        return Math.random() * 100 < successRate;
    }

    /**
     * 获取技能排行榜
     */
    @Transactional(readOnly = true)
    public List<LifeSkill> getSkillRanking(Integer skillType) {
        return lifeSkillRepository.findSkillRanking(skillType);
    }

    /**
     * 获取配方详情
     */
    @Transactional(readOnly = true)
    public LifeSkillRecipe getRecipeDetail(Integer recipeId) {
        return recipeRepository.findById(recipeId)
                .orElseThrow(() -> new BusinessException(404, "配方不存在"));
    }
}
