package com.honghuang.game.entity;

import javax.persistence.*;

/**
 * 怪物实体类
 */
@Entity
@Table(name = "npc_info")
public class Monster {
    
    @Id
    @Column(name = "npc_id")
    private Integer id;
    
    @Column(name = "npc_name")
    private String name;
    
    @Column(name = "npc_hp")
    private Integer maxHp;
    
    @Column(name = "npc_level")
    private Integer level;
    
    @Column(name = "npc_type")
    private Integer type;
    
    @Column(name = "npc_attack")
    private Integer attack;
    
    @Column(name = "npc_defense")
    private Integer defense;
    
    @Column(name = "npc_agility")
    private Integer agility;
    
    @Column(name = "exp_reward")
    private Long expReward;
    
    @Column(name = "copper_reward")
    private Long copperReward;
    
    @Column(name = "npc_description")
    private String description;
    
    @Column(name = "is_active")
    private Integer isActive;
    
    // 构造函数
    public Monster() {}
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Integer getMaxHp() {
        return maxHp;
    }
    
    public void setMaxHp(Integer maxHp) {
        this.maxHp = maxHp;
    }
    
    public Integer getLevel() {
        return level;
    }
    
    public void setLevel(Integer level) {
        this.level = level;
    }
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    public Integer getAttack() {
        return attack;
    }
    
    public void setAttack(Integer attack) {
        this.attack = attack;
    }
    
    public Integer getDefense() {
        return defense;
    }
    
    public void setDefense(Integer defense) {
        this.defense = defense;
    }
    
    public Integer getAgility() {
        return agility;
    }
    
    public void setAgility(Integer agility) {
        this.agility = agility;
    }
    
    public Long getExpReward() {
        return expReward;
    }
    
    public void setExpReward(Long expReward) {
        this.expReward = expReward;
    }
    
    public Long getCopperReward() {
        return copperReward;
    }
    
    public void setCopperReward(Long copperReward) {
        this.copperReward = copperReward;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }
    
    // 业务方法
    public boolean canAttack() {
        return isActive != null && isActive == 1 && type != null && type != 4; // type 4 是NPC
    }
    
    public boolean isNpc() {
        return type != null && type == 4;
    }
}
