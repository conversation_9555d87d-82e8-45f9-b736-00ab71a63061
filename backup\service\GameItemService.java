package com.honghuang.game.service;

import com.honghuang.game.entity.GameItem;
import com.honghuang.game.repository.GameItemRepository;
import com.honghuang.game.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 游戏物品服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class GameItemService {

    @Autowired
    private GameItemRepository gameItemRepository;

    /**
     * 根据ID查找物品
     */
    public GameItem findById(Integer id) {
        return gameItemRepository.findById(id)
                .orElseThrow(() -> new BusinessException("物品不存在"));
    }

    /**
     * 根据名称查找物品
     */
    public GameItem findByName(String name) {
        return gameItemRepository.findByName(name)
                .orElseThrow(() -> new BusinessException("物品不存在"));
    }

    /**
     * 查找所有激活的物品
     */
    public List<GameItem> findAllActive() {
        return gameItemRepository.findByIsActive(1);
    }

    /**
     * 根据类型查找物品
     */
    public List<GameItem> findByType(Integer type) {
        return gameItemRepository.findByType(type);
    }

    /**
     * 根据类型和子类型查找物品
     */
    public List<GameItem> findByTypeAndSubType(Integer type, Integer subType) {
        return gameItemRepository.findByTypeAndSubType(type, subType);
    }

    /**
     * 根据品质查找物品
     */
    public List<GameItem> findByQuality(Integer quality) {
        return gameItemRepository.findByQuality(quality);
    }

    /**
     * 根据等级范围查找物品
     */
    public List<GameItem> findByLevelRange(Integer minLevel, Integer maxLevel) {
        return gameItemRepository.findByLevelBetween(minLevel, maxLevel);
    }

    /**
     * 根据名称模糊查找物品
     */
    public List<GameItem> findByNameContaining(String name) {
        return gameItemRepository.findByNameContaining(name);
    }

    /**
     * 根据多个条件查找物品
     */
    public Page<GameItem> findByConditions(Integer type, Integer subType, Integer quality,
                                          Integer minLevel, Integer maxLevel, Pageable pageable) {
        return gameItemRepository.findByConditions(type, subType, quality, minLevel, maxLevel, pageable);
    }

    /**
     * 查找装备类物品
     */
    public List<GameItem> findEquipments() {
        return gameItemRepository.findEquipments();
    }

    /**
     * 查找消耗品
     */
    public List<GameItem> findConsumables() {
        return gameItemRepository.findConsumables();
    }

    /**
     * 查找材料
     */
    public List<GameItem> findMaterials() {
        return gameItemRepository.findMaterials();
    }

    /**
     * 查找任务物品
     */
    public List<GameItem> findQuestItems() {
        return gameItemRepository.findQuestItems();
    }

    /**
     * 根据价格范围查找物品
     */
    public List<GameItem> findByPriceRange(Long minPrice, Long maxPrice) {
        return gameItemRepository.findByPriceRange(minPrice, maxPrice);
    }

    /**
     * 查找可交易的物品
     */
    public List<GameItem> findTradeableItems() {
        return gameItemRepository.findTradeableItems();
    }

    /**
     * 查找可堆叠的物品
     */
    public List<GameItem> findStackableItems() {
        return gameItemRepository.findStackableItems();
    }

    /**
     * 根据使用等级限制查找物品
     */
    public List<GameItem> findByUseLevelLimit(Integer level) {
        return gameItemRepository.findByUseLevelLimit(level);
    }

    /**
     * 根据性别限制查找物品
     */
    public List<GameItem> findBySexLimit(Integer sex) {
        return gameItemRepository.findBySexLimit(sex);
    }

    /**
     * 根据种族限制查找物品
     */
    public List<GameItem> findByRaceLimit(Integer race) {
        return gameItemRepository.findByRaceLimit(race);
    }

    /**
     * 查找有属性加成的装备
     */
    public List<GameItem> findItemsWithBonus() {
        return gameItemRepository.findItemsWithBonus();
    }

    /**
     * 创建新物品
     */
    public GameItem createItem(GameItem gameItem) {
        // 验证物品名称唯一性
        Optional<GameItem> existingItem = gameItemRepository.findByName(gameItem.getName());
        if (existingItem.isPresent()) {
            throw new BusinessException("物品名称已存在");
        }

        // 设置默认值
        if (gameItem.getIsActive() == null) {
            gameItem.setIsActive(1);
        }
        if (gameItem.getLevel() == null) {
            gameItem.setLevel(1);
        }
        if (gameItem.getQuality() == null) {
            gameItem.setQuality(1);
        }

        log.info("创建新物品: {}", gameItem.getName());
        return gameItemRepository.save(gameItem);
    }

    /**
     * 更新物品信息
     */
    public GameItem updateItem(GameItem gameItem) {
        GameItem existingItem = findById(gameItem.getId());
        
        // 更新字段
        existingItem.setName(gameItem.getName());
        existingItem.setType(gameItem.getType());
        existingItem.setSubType(gameItem.getSubType());
        existingItem.setLevel(gameItem.getLevel());
        existingItem.setQuality(gameItem.getQuality());
        existingItem.setDescription(gameItem.getDescription());
        existingItem.setIcon(gameItem.getIcon());
        existingItem.setIsStackable(gameItem.getIsStackable());
        existingItem.setMaxStack(gameItem.getMaxStack());
        existingItem.setIsTradeable(gameItem.getIsTradeable());
        existingItem.setIsDroppable(gameItem.getIsDroppable());
        existingItem.setBuyPrice(gameItem.getBuyPrice());
        existingItem.setSellPrice(gameItem.getSellPrice());
        existingItem.setUseLevelLimit(gameItem.getUseLevelLimit());
        existingItem.setSexLimit(gameItem.getSexLimit());
        existingItem.setRaceLimit(gameItem.getRaceLimit());
        existingItem.setProfessionLimit(gameItem.getProfessionLimit());
        existingItem.setAttackBonus(gameItem.getAttackBonus());
        existingItem.setDefenseBonus(gameItem.getDefenseBonus());
        existingItem.setHpBonus(gameItem.getHpBonus());
        existingItem.setMpBonus(gameItem.getMpBonus());
        existingItem.setForceBonus(gameItem.getForceBonus());
        existingItem.setAgileBonus(gameItem.getAgileBonus());
        existingItem.setPhysiqueBonus(gameItem.getPhysiqueBonus());
        existingItem.setSavvyBonus(gameItem.getSavvyBonus());
        existingItem.setUseEffect(gameItem.getUseEffect());
        existingItem.setCooldown(gameItem.getCooldown());
        existingItem.setDuration(gameItem.getDuration());

        log.info("更新物品信息: {}", existingItem.getName());
        return gameItemRepository.save(existingItem);
    }

    /**
     * 删除物品（软删除）
     */
    public void deleteItem(Integer id) {
        GameItem gameItem = findById(id);
        gameItem.setIsActive(0);
        gameItemRepository.save(gameItem);
        log.info("删除物品: {}", gameItem.getName());
    }

    /**
     * 激活物品
     */
    public void activateItem(Integer id) {
        GameItem gameItem = findById(id);
        gameItem.setIsActive(1);
        gameItemRepository.save(gameItem);
        log.info("激活物品: {}", gameItem.getName());
    }

    /**
     * 统计各类型物品数量
     */
    public List<Object[]> countByType() {
        return gameItemRepository.countByType();
    }

    /**
     * 统计各品质物品数量
     */
    public List<Object[]> countByQuality() {
        return gameItemRepository.countByQuality();
    }

    /**
     * 验证物品是否可以被角色使用
     */
    public boolean canUseItem(GameItem item, Integer playerLevel, Integer playerSex, Integer playerRace) {
        // 检查等级限制
        if (item.getUseLevelLimit() != null && playerLevel < item.getUseLevelLimit()) {
            return false;
        }

        // 检查性别限制
        if (item.getSexLimit() != null && item.getSexLimit() != 0 && !item.getSexLimit().equals(playerSex)) {
            return false;
        }

        // 检查种族限制
        if (item.getRaceLimit() != null && item.getRaceLimit() != 0 && !item.getRaceLimit().equals(playerRace)) {
            return false;
        }

        // 检查是否激活
        return item.getIsActive() != null && item.getIsActive() == 1;
    }
}
