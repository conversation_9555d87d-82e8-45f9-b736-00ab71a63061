package com.honghuang.game.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 帮派成员实体类
 */
@Entity
@Table(name = "faction_member")
public class FactionMember {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "faction_id")
    private Integer factionId;
    
    @Column(name = "player_id")
    private Integer playerId;
    
    @Column(name = "player_name")
    private String playerName;
    
    @Column(name = "position")
    private Integer position; // 职位：1-帮主 2-副帮主 3-长老 4-精英 5-普通成员
    
    @Column(name = "join_time")
    private LocalDateTime joinTime;
    
    @Column(name = "contribution")
    private Integer contribution; // 贡献度
    
    @Column(name = "title")
    private String title; // 称号
    
    @Column(name = "last_active_time")
    private LocalDateTime lastActiveTime;
    
    @Column(name = "is_online")
    private Integer isOnline;
    
    @Column(name = "player_level")
    private Integer playerLevel;
    
    @Column(name = "weekly_contribution")
    private Integer weeklyContribution; // 本周贡献
    
    @Column(name = "total_contribution")
    private Integer totalContribution; // 总贡献
    
    // 构造函数
    public FactionMember() {
        this.joinTime = LocalDateTime.now();
        this.lastActiveTime = LocalDateTime.now();
        this.contribution = 0;
        this.weeklyContribution = 0;
        this.totalContribution = 0;
        this.position = 5; // 默认普通成员
        this.isOnline = 0;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getFactionId() {
        return factionId;
    }
    
    public void setFactionId(Integer factionId) {
        this.factionId = factionId;
    }
    
    public Integer getPlayerId() {
        return playerId;
    }
    
    public void setPlayerId(Integer playerId) {
        this.playerId = playerId;
    }
    
    public String getPlayerName() {
        return playerName;
    }
    
    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }
    
    public Integer getPosition() {
        return position;
    }
    
    public void setPosition(Integer position) {
        this.position = position;
    }
    
    public LocalDateTime getJoinTime() {
        return joinTime;
    }
    
    public void setJoinTime(LocalDateTime joinTime) {
        this.joinTime = joinTime;
    }
    
    public Integer getContribution() {
        return contribution;
    }
    
    public void setContribution(Integer contribution) {
        this.contribution = contribution;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public LocalDateTime getLastActiveTime() {
        return lastActiveTime;
    }
    
    public void setLastActiveTime(LocalDateTime lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }
    
    public Integer getIsOnline() {
        return isOnline;
    }
    
    public void setIsOnline(Integer isOnline) {
        this.isOnline = isOnline;
    }
    
    public Integer getPlayerLevel() {
        return playerLevel;
    }
    
    public void setPlayerLevel(Integer playerLevel) {
        this.playerLevel = playerLevel;
    }
    
    public Integer getWeeklyContribution() {
        return weeklyContribution;
    }
    
    public void setWeeklyContribution(Integer weeklyContribution) {
        this.weeklyContribution = weeklyContribution;
    }
    
    public Integer getTotalContribution() {
        return totalContribution;
    }
    
    public void setTotalContribution(Integer totalContribution) {
        this.totalContribution = totalContribution;
    }
    
    // 业务方法
    public boolean isLeader() {
        return position != null && position == 1;
    }
    
    public boolean isViceLeader() {
        return position != null && position == 2;
    }
    
    public boolean isElder() {
        return position != null && position == 3;
    }
    
    public boolean isElite() {
        return position != null && position == 4;
    }
    
    public boolean isMember() {
        return position != null && position == 5;
    }
    
    public boolean isOnline() {
        return isOnline != null && isOnline == 1;
    }
    
    public boolean hasManagePermission() {
        return position != null && position <= 3; // 帮主、副帮主、长老有管理权限
    }
    
    public String getPositionName() {
        if (position == null) return "未知";
        switch (position) {
            case 1: return "帮主";
            case 2: return "副帮主";
            case 3: return "长老";
            case 4: return "精英";
            case 5: return "成员";
            default: return "未知";
        }
    }
}
