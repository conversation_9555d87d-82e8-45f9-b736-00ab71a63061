package com.honghuang.game.repository;

import com.honghuang.game.entity.Npc;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * NPC数据访问层
 */
@Repository
public interface NpcRepository extends JpaRepository<Npc, Integer> {

    /**
     * 查找激活的NPC
     */
    List<Npc> findByActiveOrderBySceneIdAscTypeAsc(Integer active);

    /**
     * 根据场景ID查找NPC
     */
    List<Npc> findBySceneIdAndActiveOrderByTypeAsc(Integer sceneId, Integer active);

    /**
     * 根据类型查找NPC
     */
    List<Npc> findByTypeAndActiveOrderBySceneIdAsc(Integer type, Integer active);

    /**
     * 根据类型和子类型查找NPC
     */
    List<Npc> findByTypeAndSubTypeAndActiveOrderBySceneIdAsc(Integer type, Integer subType, Integer active);

    /**
     * 根据名称模糊查找NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.name LIKE %:name% AND n.active = 1 ORDER BY n.name ASC")
    List<Npc> findByNameContaining(@Param("name") String name);

    /**
     * 查找商店NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.type = 1 AND n.active = 1 ORDER BY n.sceneId ASC, n.subType ASC")
    List<Npc> findShopNpcs();

    /**
     * 查找任务NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.type = 2 AND n.active = 1 ORDER BY n.sceneId ASC, n.level ASC")
    List<Npc> findQuestNpcs();

    /**
     * 查找功能NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.type = 3 AND n.active = 1 ORDER BY n.sceneId ASC, n.subType ASC")
    List<Npc> findFunctionNpcs();

    /**
     * 查找可交互的NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.interactive = 1 AND n.active = 1 ORDER BY n.sceneId ASC, n.type ASC")
    List<Npc> findInteractiveNpcs();

    /**
     * 根据位置范围查找NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.sceneId = :sceneId AND n.active = 1 " +
           "AND ABS(n.positionX - :x) + ABS(n.positionY - :y) <= :range " +
           "ORDER BY (ABS(n.positionX - :x) + ABS(n.positionY - :y)) ASC")
    List<Npc> findNpcsInRange(@Param("sceneId") Integer sceneId, @Param("x") Integer x, 
                             @Param("y") Integer y, @Param("range") Integer range);

    /**
     * 查找最近的NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.sceneId = :sceneId AND n.active = 1 " +
           "ORDER BY (ABS(n.positionX - :x) + ABS(n.positionY - :y)) ASC")
    List<Npc> findNearestNpcs(@Param("sceneId") Integer sceneId, @Param("x") Integer x, @Param("y") Integer y);

    /**
     * 根据等级范围查找NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.level BETWEEN :minLevel AND :maxLevel AND n.active = 1 ORDER BY n.level ASC")
    List<Npc> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 统计场景中的NPC数量
     */
    @Query("SELECT COUNT(n) FROM Npc n WHERE n.sceneId = :sceneId AND n.active = 1")
    long countBySceneId(@Param("sceneId") Integer sceneId);

    /**
     * 统计指定类型的NPC数量
     */
    @Query("SELECT COUNT(n) FROM Npc n WHERE n.type = :type AND n.active = 1")
    long countByType(@Param("type") Integer type);

    /**
     * 查找指定场景的商店NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.sceneId = :sceneId AND n.type = 1 AND n.active = 1 ORDER BY n.subType ASC")
    List<Npc> findShopNpcsByScene(@Param("sceneId") Integer sceneId);

    /**
     * 查找指定场景的任务NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.sceneId = :sceneId AND n.type = 2 AND n.active = 1 ORDER BY n.level ASC")
    List<Npc> findQuestNpcsByScene(@Param("sceneId") Integer sceneId);

    /**
     * 查找指定场景的功能NPC
     */
    @Query("SELECT n FROM Npc n WHERE n.sceneId = :sceneId AND n.type = 3 AND n.active = 1 ORDER BY n.subType ASC")
    List<Npc> findFunctionNpcsByScene(@Param("sceneId") Integer sceneId);
}
