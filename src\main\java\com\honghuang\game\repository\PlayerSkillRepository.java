package com.honghuang.game.repository;

import com.honghuang.game.entity.PlayerSkill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 玩家技能数据访问层
 */
@Repository
public interface PlayerSkillRepository extends JpaRepository<PlayerSkill, Integer> {

    /**
     * 根据角色ID查找技能列表
     */
    List<PlayerSkill> findByRoleIdOrderBySkillIdAsc(Integer roleId);

    /**
     * 根据角色ID和技能ID查找技能
     */
    Optional<PlayerSkill> findByRoleIdAndSkillId(Integer roleId, Integer skillId);

    /**
     * 检查角色是否已学习某技能
     */
    boolean existsByRoleIdAndSkillId(Integer roleId, Integer skillId);

    /**
     * 根据角色ID和技能类型查找技能
     */
    @Query("SELECT ps FROM PlayerSkill ps JOIN ps.skill s WHERE ps.roleId = :roleId AND s.type = :type ORDER BY ps.skillLevel DESC")
    List<PlayerSkill> findByRoleIdAndSkillType(@Param("roleId") Integer roleId, @Param("type") Integer type);

    /**
     * 根据角色ID和技能分类查找技能
     */
    @Query("SELECT ps FROM PlayerSkill ps JOIN ps.skill s WHERE ps.roleId = :roleId AND s.category = :category ORDER BY ps.skillLevel DESC")
    List<PlayerSkill> findByRoleIdAndSkillCategory(@Param("roleId") Integer roleId, @Param("category") Integer category);

    /**
     * 查找角色的攻击技能
     */
    @Query("SELECT ps FROM PlayerSkill ps JOIN ps.skill s WHERE ps.roleId = :roleId AND s.type = 1 ORDER BY ps.skillLevel DESC")
    List<PlayerSkill> findAttackSkillsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找角色的防御技能
     */
    @Query("SELECT ps FROM PlayerSkill ps JOIN ps.skill s WHERE ps.roleId = :roleId AND s.type = 2 ORDER BY ps.skillLevel DESC")
    List<PlayerSkill> findDefenseSkillsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找角色的治疗技能
     */
    @Query("SELECT ps FROM PlayerSkill ps JOIN ps.skill s WHERE ps.roleId = :roleId AND s.type = 3 ORDER BY ps.skillLevel DESC")
    List<PlayerSkill> findHealSkillsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找角色的被动技能
     */
    @Query("SELECT ps FROM PlayerSkill ps JOIN ps.skill s WHERE ps.roleId = :roleId AND s.category = 3 ORDER BY ps.skillLevel DESC")
    List<PlayerSkill> findPassiveSkillsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 根据快捷键查找技能
     */
    Optional<PlayerSkill> findByRoleIdAndShortcutKey(Integer roleId, Integer shortcutKey);

    /**
     * 查找设置了快捷键的技能
     */
    @Query("SELECT ps FROM PlayerSkill ps WHERE ps.roleId = :roleId AND ps.shortcutKey > 0 ORDER BY ps.shortcutKey ASC")
    List<PlayerSkill> findShortcutSkillsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 更新技能等级和经验
     */
    @Modifying
    @Query("UPDATE PlayerSkill ps SET ps.skillLevel = :level, ps.skillExp = :exp WHERE ps.id = :id")
    int updateLevelAndExp(@Param("id") Integer id, @Param("level") Integer level, @Param("exp") Long exp);

    /**
     * 更新技能经验
     */
    @Modifying
    @Query("UPDATE PlayerSkill ps SET ps.skillExp = :exp WHERE ps.id = :id")
    int updateExp(@Param("id") Integer id, @Param("exp") Long exp);

    /**
     * 更新快捷键
     */
    @Modifying
    @Query("UPDATE PlayerSkill ps SET ps.shortcutKey = :shortcutKey WHERE ps.id = :id")
    int updateShortcutKey(@Param("id") Integer id, @Param("shortcutKey") Integer shortcutKey);

    /**
     * 清除指定快捷键位置的技能
     */
    @Modifying
    @Query("UPDATE PlayerSkill ps SET ps.shortcutKey = 0 WHERE ps.roleId = :roleId AND ps.shortcutKey = :shortcutKey")
    int clearShortcutKey(@Param("roleId") Integer roleId, @Param("shortcutKey") Integer shortcutKey);

    /**
     * 更新最后使用时间
     */
    @Modifying
    @Query("UPDATE PlayerSkill ps SET ps.lastUseTime = :useTime WHERE ps.id = :id")
    int updateLastUseTime(@Param("id") Integer id, @Param("useTime") LocalDateTime useTime);

    /**
     * 统计角色技能数量
     */
    @Query("SELECT COUNT(ps) FROM PlayerSkill ps WHERE ps.roleId = :roleId")
    long countByRoleId(@Param("roleId") Integer roleId);

    /**
     * 统计角色指定类型技能数量
     */
    @Query("SELECT COUNT(ps) FROM PlayerSkill ps JOIN ps.skill s WHERE ps.roleId = :roleId AND s.type = :type")
    long countByRoleIdAndSkillType(@Param("roleId") Integer roleId, @Param("type") Integer type);

    /**
     * 查找角色最高等级的技能
     */
    @Query("SELECT ps FROM PlayerSkill ps WHERE ps.roleId = :roleId ORDER BY ps.skillLevel DESC")
    List<PlayerSkill> findTopLevelSkillsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找可以升级的技能
     */
    @Query("SELECT ps FROM PlayerSkill ps JOIN ps.skill s WHERE ps.roleId = :roleId AND ps.skillLevel < s.maxLevel ORDER BY ps.skillLevel DESC")
    List<PlayerSkill> findUpgradeableSkillsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 删除角色的所有技能
     */
    @Modifying
    @Query("DELETE FROM PlayerSkill ps WHERE ps.roleId = :roleId")
    int deleteByRoleId(@Param("roleId") Integer roleId);
}
