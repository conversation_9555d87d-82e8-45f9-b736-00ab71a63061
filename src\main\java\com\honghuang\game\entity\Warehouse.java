package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 仓库物品实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "warehouse")
public class Warehouse {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 角色ID
     */
    @Column(name = "role_id")
    private Integer roleId;

    /**
     * 物品ID
     */
    @Column(name = "item_id")
    private Integer itemId;

    /**
     * 仓库页数 1-10（每页50格）
     */
    @Column(name = "page_number")
    private Integer pageNumber = 1;

    /**
     * 页内位置索引 0-49
     */
    @Column(name = "slot_index")
    private Integer slotIndex;

    /**
     * 物品数量
     */
    @Column(name = "quantity")
    private Integer quantity = 1;

    /**
     * 是否绑定 0未绑定 1已绑定
     */
    @Column(name = "bound")
    private Integer bound = 0;

    /**
     * 存入时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "store_time")
    private LocalDateTime storeTime;

    /**
     * 最后访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_access_time")
    private LocalDateTime lastAccessTime;

    /**
     * 物品信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "item_id", insertable = false, updatable = false)
    private Item item;

    // 业务方法

    /**
     * 是否绑定
     */
    public boolean isBound() {
        return bound == 1;
    }

    /**
     * 绑定物品
     */
    public void bind() {
        this.bound = 1;
    }

    /**
     * 是否可以堆叠
     */
    public boolean canStack() {
        return item != null && item.isStackable();
    }

    /**
     * 是否可以增加数量
     */
    public boolean canAddQuantity(Integer amount) {
        if (!canStack()) {
            return false;
        }
        return quantity + amount <= item.getMaxStack();
    }

    /**
     * 增加数量
     */
    public boolean addQuantity(Integer amount) {
        if (!canAddQuantity(amount)) {
            return false;
        }
        this.quantity += amount;
        updateAccessTime();
        return true;
    }

    /**
     * 减少数量
     */
    public boolean reduceQuantity(Integer amount) {
        if (quantity < amount) {
            return false;
        }
        this.quantity -= amount;
        updateAccessTime();
        return true;
    }

    /**
     * 更新访问时间
     */
    public void updateAccessTime() {
        this.lastAccessTime = LocalDateTime.now();
    }

    /**
     * 获取绝对位置索引
     */
    public Integer getAbsoluteIndex() {
        return (pageNumber - 1) * 50 + slotIndex;
    }

    /**
     * 移动到新位置
     */
    public void moveTo(Integer newPageNumber, Integer newSlotIndex) {
        this.pageNumber = newPageNumber;
        this.slotIndex = newSlotIndex;
        updateAccessTime();
    }

    /**
     * 是否为空槽位
     */
    public boolean isEmpty() {
        return item == null || quantity <= 0;
    }

    /**
     * 清空槽位
     */
    public void clear() {
        this.itemId = null;
        this.quantity = 0;
        this.item = null;
        updateAccessTime();
    }

    /**
     * 获取物品完整名称
     */
    public String getFullName() {
        if (item == null) {
            return "未知物品";
        }
        String name = item.getFullName();
        if (quantity > 1) {
            name += " x" + quantity;
        }
        if (isBound()) {
            name += " (绑定)";
        }
        return name;
    }

    /**
     * 获取物品总价值
     */
    public Long getTotalValue() {
        if (item == null) {
            return 0L;
        }
        return item.getSellPrice() * quantity;
    }

    /**
     * 分割物品
     */
    public Warehouse split(Integer splitQuantity) {
        if (!canStack() || quantity <= splitQuantity) {
            return null;
        }

        Warehouse newWarehouse = new Warehouse();
        newWarehouse.setRoleId(this.roleId);
        newWarehouse.setItemId(this.itemId);
        newWarehouse.setQuantity(splitQuantity);
        newWarehouse.setBound(this.bound);
        newWarehouse.setStoreTime(LocalDateTime.now());
        newWarehouse.setLastAccessTime(LocalDateTime.now());

        this.quantity -= splitQuantity;
        updateAccessTime();

        return newWarehouse;
    }

    /**
     * 合并物品
     */
    public boolean merge(Warehouse other) {
        if (other == null || !this.itemId.equals(other.itemId) || !canStack()) {
            return false;
        }

        if (!canAddQuantity(other.quantity)) {
            return false;
        }

        this.quantity += other.quantity;
        updateAccessTime();
        return true;
    }

    /**
     * 获取存储天数
     */
    public Long getStoreDays() {
        if (storeTime == null) {
            return 0L;
        }
        return java.time.Duration.between(storeTime, LocalDateTime.now()).toDays();
    }

    /**
     * 获取上次访问天数
     */
    public Long getLastAccessDays() {
        if (lastAccessTime == null) {
            return getStoreDays();
        }
        return java.time.Duration.between(lastAccessTime, LocalDateTime.now()).toDays();
    }

    /**
     * 是否长期未访问
     */
    public boolean isLongTimeUnaccessed() {
        return getLastAccessDays() > 30; // 30天未访问
    }

    /**
     * 获取位置描述
     */
    public String getLocationDescription() {
        return String.format("第%d页 第%d格", pageNumber, slotIndex + 1);
    }

    /**
     * 是否可以取出到背包
     */
    public boolean canWithdraw() {
        // 检查物品是否存在且数量大于0
        return !isEmpty();
    }

    /**
     * 检查是否在指定页面
     */
    public boolean isInPage(Integer page) {
        return pageNumber.equals(page);
    }
}
