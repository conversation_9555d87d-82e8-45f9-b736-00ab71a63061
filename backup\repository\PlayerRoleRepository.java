package com.honghuang.game.repository;

import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 玩家角色数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface PlayerRoleRepository extends JpaRepository<PlayerRole, Integer> {

    /**
     * 根据角色名查找角色
     */
    Optional<PlayerRole> findByName(String name);

    /**
     * 检查角色名是否存在
     */
    boolean existsByName(String name);

    /**
     * 根据用户查找角色列表
     */
    List<PlayerRole> findByUser(User user);

    /**
     * 根据用户ID查找角色列表
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.user.id = :userId AND pr.deleteFlag = 0")
    List<PlayerRole> findByUserId(@Param("userId") Integer userId);

    /**
     * 根据等级范围查找角色
     */
    List<PlayerRole> findByGradeBetween(Integer minGrade, Integer maxGrade);

    /**
     * 根据门派查找角色
     */
    List<PlayerRole> findBySchool(String school);

    /**
     * 根据帮派查找角色
     */
    List<PlayerRole> findByTong(Integer tong);

    /**
     * 根据登录状态查找角色
     */
    List<PlayerRole> findByLoginState(Integer loginState);

    /**
     * 查找在线角色数量
     */
    @Query("SELECT COUNT(pr) FROM PlayerRole pr WHERE pr.loginState = 1 AND pr.deleteFlag = 0")
    long countOnlineRoles();

    /**
     * 等级排行榜
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.deleteFlag = 0 ORDER BY pr.grade DESC, pr.experience DESC")
    Page<PlayerRole> findGradeRanking(Pageable pageable);

    /**
     * PK值排行榜
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.deleteFlag = 0 ORDER BY pr.pkValue DESC")
    Page<PlayerRole> findPkRanking(Pageable pageable);

    /**
     * 杀人数排行榜
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.deleteFlag = 0 ORDER BY pr.killNum DESC")
    Page<PlayerRole> findKillRanking(Pageable pageable);

    /**
     * 根据场景查找角色
     */
    List<PlayerRole> findByMap(String map);

    /**
     * 查找指定场景的在线角色
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.map = :map AND pr.loginState = 1 AND pr.deleteFlag = 0")
    List<PlayerRole> findOnlineRolesByMap(@Param("map") String map);

    /**
     * 更新角色登录状态
     */
    @Modifying
    @Query("UPDATE PlayerRole pr SET pr.loginState = :loginState WHERE pr.id = :roleId")
    int updateLoginStatus(@Param("roleId") Integer roleId, @Param("loginState") Integer loginState);

    /**
     * 更新角色位置
     */
    @Modifying
    @Query("UPDATE PlayerRole pr SET pr.map = :map WHERE pr.id = :roleId")
    int updateLocation(@Param("roleId") Integer roleId, @Param("map") String map);

    /**
     * 更新角色等级和经验
     */
    @Modifying
    @Query("UPDATE PlayerRole pr SET pr.grade = :grade, pr.experience = :experience WHERE pr.id = :roleId")
    int updateGradeAndExp(@Param("roleId") Integer roleId, @Param("grade") Integer grade, @Param("experience") String experience);

    /**
     * 更新角色HP和MP
     */
    @Modifying
    @Query("UPDATE PlayerRole pr SET pr.hp = :hp, pr.mp = :mp WHERE pr.id = :roleId")
    int updateHpAndMp(@Param("roleId") Integer roleId, @Param("hp") Integer hp, @Param("mp") Integer mp);

    /**
     * 更新角色金钱
     */
    @Modifying
    @Query("UPDATE PlayerRole pr SET pr.silver = :silver, pr.copper = :copper WHERE pr.id = :roleId")
    int updateMoney(@Param("roleId") Integer roleId, @Param("silver") String silver, @Param("copper") String copper);

    /**
     * 软删除角色
     */
    @Modifying
    @Query("UPDATE PlayerRole pr SET pr.deleteFlag = 1, pr.deleteTime = :deleteTime WHERE pr.id = :roleId")
    int softDeleteRole(@Param("roleId") Integer roleId, @Param("deleteTime") LocalDateTime deleteTime);

    /**
     * 恢复删除的角色
     */
    @Modifying
    @Query("UPDATE PlayerRole pr SET pr.deleteFlag = 0, pr.deleteTime = null WHERE pr.id = :roleId")
    int restoreRole(@Param("roleId") Integer roleId);

    /**
     * 查找待删除的角色
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.deleteFlag = 1 AND pr.deleteTime < :cutoffTime")
    List<PlayerRole> findRolesToDelete(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找新手角色
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.playerStateByNew IN (1, 11) AND pr.deleteFlag = 0")
    List<PlayerRole> findNewbieRoles();

    /**
     * 查找指定师父的徒弟
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.teacher = :teacherId AND pr.teacherType = 2 AND pr.deleteFlag = 0")
    List<PlayerRole> findStudentsByTeacher(@Param("teacherId") Integer teacherId);

    /**
     * 查找师父
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.id = :teacherId AND pr.teacherType = 1 AND pr.deleteFlag = 0")
    Optional<PlayerRole> findTeacher(@Param("teacherId") Integer teacherId);

    /**
     * 查找已婚角色的伴侣
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.id = :fereId AND pr.harness = 2 AND pr.deleteFlag = 0")
    Optional<PlayerRole> findSpouse(@Param("fereId") Integer fereId);

    /**
     * 统计指定门派的角色数量
     */
    @Query("SELECT COUNT(pr) FROM PlayerRole pr WHERE pr.school = :school AND pr.deleteFlag = 0")
    long countBySchool(@Param("school") String school);

    /**
     * 统计指定帮派的角色数量
     */
    @Query("SELECT COUNT(pr) FROM PlayerRole pr WHERE pr.tong = :tong AND pr.deleteFlag = 0")
    long countByTong(@Param("tong") Integer tong);

    /**
     * 查找今日创建的角色
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE DATE(pr.createTime) = CURRENT_DATE AND pr.deleteFlag = 0")
    List<PlayerRole> findTodayCreatedRoles();

    /**
     * 统计今日创建的角色数量
     */
    @Query("SELECT COUNT(pr) FROM PlayerRole pr WHERE DATE(pr.createTime) = CURRENT_DATE AND pr.deleteFlag = 0")
    long countTodayCreatedRoles();

    /**
     * 查找活跃角色（最近登录）
     */
    @Query("SELECT pr FROM PlayerRole pr WHERE pr.createTime > :cutoffTime AND pr.deleteFlag = 0 ORDER BY pr.createTime DESC")
    List<PlayerRole> findActiveRoles(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计在线玩家数量
     */
    Long countByLoginState(Integer loginState);

    /**
     * 统计今日注册玩家数量
     */
    @Query("SELECT COUNT(pr) FROM PlayerRole pr WHERE DATE(pr.createTime) = CURRENT_DATE")
    Long countTodayRegistrations();
}
