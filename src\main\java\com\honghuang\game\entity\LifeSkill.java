package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 生活技能实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "life_skill")
public class LifeSkill {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 角色ID
     */
    @Column(name = "role_id")
    private Integer roleId;

    /**
     * 技能类型 1采集 2制造 3炼药 4锻造 5烹饪 6附魔
     */
    @Column(name = "skill_type")
    private Integer skillType;

    /**
     * 技能等级
     */
    @Column(name = "skill_level")
    private Integer skillLevel = 1;

    /**
     * 当前经验值
     */
    @Column(name = "experience")
    private Long experience = 0L;

    /**
     * 熟练度
     */
    @Column(name = "proficiency")
    private Integer proficiency = 0;

    /**
     * 最大熟练度
     */
    @Column(name = "max_proficiency")
    private Integer maxProficiency = 100;

    /**
     * 技能状态 1正常 2冷却中 3禁用
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 上次使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_use_time")
    private LocalDateTime lastUseTime;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 角色信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", insertable = false, updatable = false)
    private PlayerRole role;

    // 业务方法

    /**
     * 获取技能类型名称
     */
    public String getSkillTypeName() {
        switch (skillType) {
            case 1: return "采集";
            case 2: return "制造";
            case 3: return "炼药";
            case 4: return "锻造";
            case 5: return "烹饪";
            case 6: return "附魔";
            default: return "未知";
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        switch (status) {
            case 1: return "正常";
            case 2: return "冷却中";
            case 3: return "禁用";
            default: return "未知";
        }
    }

    /**
     * 是否为采集技能
     */
    public boolean isGatheringSkill() {
        return skillType == 1;
    }

    /**
     * 是否为制造技能
     */
    public boolean isCraftingSkill() {
        return skillType >= 2 && skillType <= 6;
    }

    /**
     * 是否可用
     */
    public boolean isAvailable() {
        return status == 1;
    }

    /**
     * 是否在冷却中
     */
    public boolean isOnCooldown() {
        return status == 2;
    }

    /**
     * 获取升级所需经验
     */
    public Long getRequiredExpForNextLevel() {
        return (long) (skillLevel * 1000 + Math.pow(skillLevel, 2) * 100);
    }

    /**
     * 获取经验百分比
     */
    public Double getExpPercentage() {
        Long required = getRequiredExpForNextLevel();
        if (required == 0) return 100.0;
        return (double) experience / required * 100;
    }

    /**
     * 增加经验
     */
    public boolean addExperience(Long exp) {
        if (exp <= 0) return false;
        
        this.experience += exp;
        
        // 检查是否可以升级
        while (this.experience >= getRequiredExpForNextLevel() && this.skillLevel < 100) {
            this.experience -= getRequiredExpForNextLevel();
            this.skillLevel++;
            this.maxProficiency = Math.min(1000, this.maxProficiency + 50);
        }
        
        return true;
    }

    /**
     * 增加熟练度
     */
    public boolean addProficiency(Integer prof) {
        if (prof <= 0) return false;
        
        this.proficiency = Math.min(this.maxProficiency, this.proficiency + prof);
        return true;
    }

    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        double baseRate = 50.0; // 基础成功率50%
        double levelBonus = skillLevel * 2.0; // 等级加成
        double proficiencyBonus = (double) proficiency / maxProficiency * 30.0; // 熟练度加成
        
        return Math.min(95.0, baseRate + levelBonus + proficiencyBonus);
    }

    /**
     * 获取暴击率
     */
    public Double getCriticalRate() {
        double baseRate = 5.0; // 基础暴击率5%
        double levelBonus = skillLevel * 0.5; // 等级加成
        double proficiencyBonus = (double) proficiency / maxProficiency * 10.0; // 熟练度加成
        
        return Math.min(50.0, baseRate + levelBonus + proficiencyBonus);
    }

    /**
     * 使用技能
     */
    public void useSkill() {
        this.lastUseTime = LocalDateTime.now();
        
        // 根据技能类型设置冷却时间
        if (isCraftingSkill()) {
            this.status = 2; // 设置冷却状态
        }
    }

    /**
     * 检查冷却是否结束
     */
    public boolean isCooldownFinished() {
        if (!isOnCooldown() || lastUseTime == null) {
            return true;
        }
        
        int cooldownSeconds = getCooldownSeconds();
        return LocalDateTime.now().isAfter(lastUseTime.plusSeconds(cooldownSeconds));
    }

    /**
     * 获取冷却时间（秒）
     */
    public int getCooldownSeconds() {
        switch (skillType) {
            case 1: return 5; // 采集5秒
            case 2: return 30; // 制造30秒
            case 3: return 60; // 炼药60秒
            case 4: return 120; // 锻造120秒
            case 5: return 45; // 烹饪45秒
            case 6: return 90; // 附魔90秒
            default: return 10;
        }
    }

    /**
     * 重置冷却
     */
    public void resetCooldown() {
        this.status = 1;
        this.lastUseTime = null;
    }

    /**
     * 获取剩余冷却时间（秒）
     */
    public Long getRemainingCooldownSeconds() {
        if (!isOnCooldown() || lastUseTime == null) {
            return 0L;
        }
        
        LocalDateTime cooldownEnd = lastUseTime.plusSeconds(getCooldownSeconds());
        LocalDateTime now = LocalDateTime.now();
        
        if (now.isAfter(cooldownEnd)) {
            return 0L;
        }
        
        return java.time.Duration.between(now, cooldownEnd).getSeconds();
    }

    /**
     * 获取技能完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(getSkillTypeName()).append(" Lv.").append(skillLevel);
        sb.append(" (").append(String.format("%.1f", getExpPercentage())).append("%)");
        sb.append(" 熟练度: ").append(proficiency).append("/").append(maxProficiency);
        sb.append(" 成功率: ").append(String.format("%.1f", getSuccessRate())).append("%");
        
        if (isOnCooldown()) {
            sb.append(" [冷却中: ").append(getRemainingCooldownSeconds()).append("秒]");
        }
        
        return sb.toString();
    }

    /**
     * 检查是否可以制作指定等级的物品
     */
    public boolean canCraft(Integer itemLevel) {
        return skillLevel >= itemLevel && isAvailable() && isCooldownFinished();
    }

    /**
     * 获取制作物品的品质加成
     */
    public Integer getQualityBonus() {
        if (skillLevel >= 80) return 3; // 高级技能有机会制作史诗品质
        if (skillLevel >= 60) return 2; // 中级技能有机会制作稀有品质
        if (skillLevel >= 40) return 1; // 初级技能有机会制作优秀品质
        return 0; // 只能制作普通品质
    }

    /**
     * 计算制作消耗的材料数量
     */
    public Double getMaterialCostMultiplier() {
        // 技能等级越高，材料消耗越少
        double reduction = skillLevel * 0.005; // 每级减少0.5%
        return Math.max(0.5, 1.0 - reduction); // 最多减少50%
    }
}
