package com.honghuang.game.repository;

import com.honghuang.game.entity.PlayerItem;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.GameItem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 玩家物品Repository
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface PlayerItemRepository extends JpaRepository<PlayerItem, Integer> {

    /**
     * 根据玩家角色查找所有物品
     */
    List<PlayerItem> findByPlayerRole(PlayerRole playerRole);

    /**
     * 根据玩家角色和位置查找物品
     */
    List<PlayerItem> findByPlayerRoleAndPosition(PlayerRole playerRole, Integer position);

    /**
     * 根据玩家角色查找背包物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.position = 1")
    List<PlayerItem> findBagItems(@Param("playerRole") PlayerRole playerRole);

    /**
     * 根据玩家角色查找装备物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.position = 2")
    List<PlayerItem> findEquippedItems(@Param("playerRole") PlayerRole playerRole);

    /**
     * 根据玩家角色查找仓库物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.position = 3")
    List<PlayerItem> findWarehouseItems(@Param("playerRole") PlayerRole playerRole);

    /**
     * 根据玩家角色和物品ID查找
     */
    List<PlayerItem> findByPlayerRoleAndGameItem(PlayerRole playerRole, GameItem gameItem);

    /**
     * 根据玩家角色、物品ID和位置查找
     */
    Optional<PlayerItem> findByPlayerRoleAndGameItemAndPosition(PlayerRole playerRole, GameItem gameItem, Integer position);

    /**
     * 根据玩家角色和位置索引查找
     */
    Optional<PlayerItem> findByPlayerRoleAndPositionAndPositionIndex(PlayerRole playerRole, Integer position, Integer positionIndex);

    /**
     * 查找玩家背包中的空位
     */
    @Query("SELECT pi.positionIndex FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.position = 1")
    List<Integer> findUsedBagSlots(@Param("playerRole") PlayerRole playerRole);

    /**
     * 查找玩家装备栏中的空位
     */
    @Query("SELECT pi.positionIndex FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.position = 2")
    List<Integer> findUsedEquipSlots(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计玩家背包物品数量
     */
    @Query("SELECT COUNT(pi) FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.position = 1")
    Long countBagItems(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计玩家装备数量
     */
    @Query("SELECT COUNT(pi) FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.position = 2")
    Long countEquippedItems(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计玩家仓库物品数量
     */
    @Query("SELECT COUNT(pi) FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.position = 3")
    Long countWarehouseItems(@Param("playerRole") PlayerRole playerRole);

    /**
     * 查找过期物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.expireTime IS NOT NULL AND pi.expireTime < :now")
    List<PlayerItem> findExpiredItems(@Param("now") LocalDateTime now);

    /**
     * 查找绑定物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.isBound = 1")
    List<PlayerItem> findBoundItems(@Param("playerRole") PlayerRole playerRole);

    /**
     * 查找可交易物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.isBound = 0 AND pi.gameItem.isTradeable = 1")
    List<PlayerItem> findTradeableItems(@Param("playerRole") PlayerRole playerRole);

    /**
     * 根据物品类型查找玩家物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.gameItem.type = :type")
    List<PlayerItem> findByPlayerRoleAndItemType(@Param("playerRole") PlayerRole playerRole, @Param("type") Integer type);

    /**
     * 根据物品品质查找玩家物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.gameItem.quality = :quality")
    List<PlayerItem> findByPlayerRoleAndItemQuality(@Param("playerRole") PlayerRole playerRole, @Param("quality") Integer quality);

    /**
     * 查找可堆叠的同类物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.gameItem = :gameItem AND " +
           "pi.position = :position AND pi.isBound = :isBound AND pi.gameItem.isStackable = 1 AND " +
           "pi.quantity < pi.gameItem.maxStack")
    List<PlayerItem> findStackableItems(@Param("playerRole") PlayerRole playerRole,
                                       @Param("gameItem") GameItem gameItem,
                                       @Param("position") Integer position,
                                       @Param("isBound") Integer isBound);

    /**
     * 查找强化等级大于指定值的装备
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.enhanceLevel > :level")
    List<PlayerItem> findEnhancedItems(@Param("playerRole") PlayerRole playerRole, @Param("level") Integer level);

    /**
     * 查找镶嵌了宝石的装备
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND " +
           "(pi.gem1Id IS NOT NULL OR pi.gem2Id IS NOT NULL OR pi.gem3Id IS NOT NULL OR pi.gem4Id IS NOT NULL)")
    List<PlayerItem> findItemsWithGems(@Param("playerRole") PlayerRole playerRole);

    /**
     * 根据耐久度查找装备
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND pi.durability IS NOT NULL AND " +
           "pi.durability <= :threshold")
    List<PlayerItem> findLowDurabilityItems(@Param("playerRole") PlayerRole playerRole, @Param("threshold") Integer threshold);

    /**
     * 分页查找玩家物品
     */
    @Query("SELECT pi FROM PlayerItem pi WHERE pi.playerRole = :playerRole AND " +
           "(:position IS NULL OR pi.position = :position)")
    Page<PlayerItem> findByPlayerRoleAndPosition(@Param("playerRole") PlayerRole playerRole,
                                                @Param("position") Integer position,
                                                Pageable pageable);

    /**
     * 删除过期物品
     */
    @Query("DELETE FROM PlayerItem pi WHERE pi.expireTime IS NOT NULL AND pi.expireTime < :now")
    void deleteExpiredItems(@Param("now") LocalDateTime now);

    /**
     * 统计玩家各类型物品数量
     */
    @Query("SELECT pi.gameItem.type, COUNT(pi) FROM PlayerItem pi WHERE pi.playerRole = :playerRole GROUP BY pi.gameItem.type")
    List<Object[]> countByItemType(@Param("playerRole") PlayerRole playerRole);

    /**
     * 统计玩家各品质物品数量
     */
    @Query("SELECT pi.gameItem.quality, COUNT(pi) FROM PlayerItem pi WHERE pi.playerRole = :playerRole GROUP BY pi.gameItem.quality")
    List<Object[]> countByItemQuality(@Param("playerRole") PlayerRole playerRole);
}
