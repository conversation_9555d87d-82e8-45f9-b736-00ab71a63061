package com.honghuang.game.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 游戏物品实体类
 * 对应原表 goods_info
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "goods_info")
public class GameItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "goods_id")
    private Integer id;

    /**
     * 物品名称
     */
    @Column(name = "goods_name", length = 50)
    private String name;

    /**
     * 物品类型 1:武器 2:防具 3:饰品 4:消耗品 5:材料 6:任务物品 7:其他
     */
    @Column(name = "goods_type")
    private Integer type;

    /**
     * 物品子类型
     */
    @Column(name = "goods_sub_type")
    private Integer subType;

    /**
     * 物品等级
     */
    @Column(name = "goods_level")
    private Integer level = 1;

    /**
     * 物品品质 1:白色 2:绿色 3:蓝色 4:紫色 5:橙色 6:红色
     */
    @Column(name = "goods_quality")
    private Integer quality = 1;

    /**
     * 物品描述
     */
    @Column(name = "goods_description", length = 500)
    private String description;

    /**
     * 物品图标
     */
    @Column(name = "goods_icon", length = 100)
    private String icon;

    /**
     * 是否可堆叠 0:不可堆叠 1:可堆叠
     */
    @Column(name = "is_stackable")
    private Integer isStackable = 0;

    /**
     * 最大堆叠数量
     */
    @Column(name = "max_stack")
    private Integer maxStack = 1;

    /**
     * 是否可交易 0:不可交易 1:可交易
     */
    @Column(name = "is_tradeable")
    private Integer isTradeable = 1;

    /**
     * 是否可丢弃 0:不可丢弃 1:可丢弃
     */
    @Column(name = "is_droppable")
    private Integer isDroppable = 1;

    /**
     * 购买价格（铜钱）
     */
    @Column(name = "buy_price")
    private Long buyPrice = 0L;

    /**
     * 出售价格（铜钱）
     */
    @Column(name = "sell_price")
    private Long sellPrice = 0L;

    /**
     * 使用等级限制
     */
    @Column(name = "use_level_limit")
    private Integer useLevelLimit = 1;

    /**
     * 性别限制 0:无限制 1:男性 2:女性
     */
    @Column(name = "sex_limit")
    private Integer sexLimit = 0;

    /**
     * 种族限制 0:无限制 1:人族 2:妖族
     */
    @Column(name = "race_limit")
    private Integer raceLimit = 0;

    /**
     * 职业限制
     */
    @Column(name = "profession_limit", length = 100)
    private String professionLimit;

    /**
     * 攻击力加成
     */
    @Column(name = "attack_bonus")
    private Integer attackBonus = 0;

    /**
     * 防御力加成
     */
    @Column(name = "defense_bonus")
    private Integer defenseBonus = 0;

    /**
     * 生命值加成
     */
    @Column(name = "hp_bonus")
    private Integer hpBonus = 0;

    /**
     * 法力值加成
     */
    @Column(name = "mp_bonus")
    private Integer mpBonus = 0;

    /**
     * 力量加成
     */
    @Column(name = "force_bonus")
    private Integer forceBonus = 0;

    /**
     * 敏捷加成
     */
    @Column(name = "agile_bonus")
    private Integer agileBonus = 0;

    /**
     * 体魄加成
     */
    @Column(name = "physique_bonus")
    private Integer physiqueBonus = 0;

    /**
     * 悟性加成
     */
    @Column(name = "savvy_bonus")
    private Integer savvyBonus = 0;

    /**
     * 使用效果描述
     */
    @Column(name = "use_effect", length = 200)
    private String useEffect;

    /**
     * 冷却时间（秒）
     */
    @Column(name = "cooldown")
    private Integer cooldown = 0;

    /**
     * 持续时间（秒）
     */
    @Column(name = "duration")
    private Integer duration = 0;

    /**
     * 是否激活 0:未激活 1:激活
     */
    @Column(name = "is_active")
    private Integer isActive = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    // 业务方法

    /**
     * 是否为装备
     */
    public boolean isEquipment() {
        return type != null && (type == 1 || type == 2 || type == 3);
    }

    /**
     * 是否为消耗品
     */
    public boolean isConsumable() {
        return type != null && type == 4;
    }

    /**
     * 是否为材料
     */
    public boolean isMaterial() {
        return type != null && type == 5;
    }

    /**
     * 是否为任务物品
     */
    public boolean isQuestItem() {
        return type != null && type == 6;
    }

    /**
     * 获取品质颜色
     */
    public String getQualityColor() {
        if (quality == null) return "#FFFFFF";
        switch (quality) {
            case 1: return "#FFFFFF"; // 白色
            case 2: return "#00FF00"; // 绿色
            case 3: return "#0080FF"; // 蓝色
            case 4: return "#8000FF"; // 紫色
            case 5: return "#FF8000"; // 橙色
            case 6: return "#FF0000"; // 红色
            default: return "#FFFFFF";
        }
    }

    /**
     * 获取品质名称
     */
    public String getQualityName() {
        if (quality == null) return "普通";
        switch (quality) {
            case 1: return "普通";
            case 2: return "优秀";
            case 3: return "精良";
            case 4: return "史诗";
            case 5: return "传说";
            case 6: return "神器";
            default: return "普通";
        }
    }

    /**
     * 获取类型名称
     */
    public String getTypeName() {
        if (type == null) return "其他";
        switch (type) {
            case 1: return "武器";
            case 2: return "防具";
            case 3: return "饰品";
            case 4: return "消耗品";
            case 5: return "材料";
            case 6: return "任务物品";
            default: return "其他";
        }
    }
}
