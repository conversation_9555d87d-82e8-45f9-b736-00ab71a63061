package com.honghuang.game.service;

import com.honghuang.game.entity.PlayerPosition;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.Scene;
import com.honghuang.game.entity.TeleportPoint;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.PlayerPositionRepository;
import com.honghuang.game.repository.SceneRepository;
import com.honghuang.game.repository.TeleportPointRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 场景服务类
 */
@Service
@Transactional
public class SceneService {

    @Autowired
    private SceneRepository sceneRepository;

    @Autowired
    private PlayerPositionRepository playerPositionRepository;

    @Autowired
    private TeleportPointRepository teleportPointRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 获取所有激活的场景
     */
    @Transactional(readOnly = true)
    public List<Scene> getAllActiveScenes() {
        return sceneRepository.findByActiveOrderByTypeAscRequiredLevelAsc(1);
    }

    /**
     * 根据类型获取场景
     */
    @Transactional(readOnly = true)
    public List<Scene> getScenesByType(Integer type) {
        return sceneRepository.findByTypeAndActiveOrderByRequiredLevelAsc(type, 1);
    }

    /**
     * 获取适合角色等级的场景
     */
    @Transactional(readOnly = true)
    public List<Scene> getSuitableScenes(Integer level) {
        return sceneRepository.findSuitableScenes(level);
    }

    /**
     * 获取场景详情
     */
    @Transactional(readOnly = true)
    public Scene getSceneDetail(Integer sceneId) {
        return sceneRepository.findById(sceneId)
                .orElseThrow(() -> new BusinessException(404, "场景不存在"));
    }

    /**
     * 玩家进入场景
     */
    public boolean enterScene(Integer roleId, Integer sceneId, Integer x, Integer y) {
        System.out.println("玩家进入场景: roleId=" + roleId + ", sceneId=" + sceneId + ", pos=(" + x + "," + y + ")");

        PlayerRole role = playerRoleService.findById(roleId);
        Scene scene = getSceneDetail(sceneId);

        // 检查是否可以进入场景
        if (!scene.canEnter(role)) {
            throw new BusinessException(400, "无法进入该场景");
        }

        // 检查坐标是否有效
        if (!scene.isValidPosition(x, y)) {
            // 使用默认出生点
            x = scene.getSpawnX();
            y = scene.getSpawnY();
        }

        // 获取或创建玩家位置记录
        Optional<PlayerPosition> positionOpt = playerPositionRepository.findByRoleId(roleId);
        PlayerPosition position;

        if (positionOpt.isPresent()) {
            position = positionOpt.get();
            
            // 如果在不同场景，更新场景人数
            if (!sceneId.equals(position.getSceneId())) {
                // 离开原场景
                if (position.getSceneId() != null) {
                    sceneRepository.decrementPlayerCount(position.getSceneId());
                }
                // 进入新场景
                sceneRepository.incrementPlayerCount(sceneId);
            }
            
            position.moveToScene(sceneId, x, y);
        } else {
            // 创建新的位置记录
            position = new PlayerPosition();
            position.setRoleId(roleId);
            position.setSceneId(sceneId);
            position.setPositionX(x);
            position.setPositionY(y);
            position.setDirection(2); // 默认朝南
            
            // 进入场景
            sceneRepository.incrementPlayerCount(sceneId);
        }

        position.setOnlineStatus(true);
        playerPositionRepository.save(position);

        System.out.println("玩家进入场景成功: roleId=" + roleId + ", sceneId=" + sceneId);
        return true;
    }

    /**
     * 玩家移动
     */
    public boolean movePlayer(Integer roleId, Integer x, Integer y, Integer direction) {
        System.out.println("玩家移动: roleId=" + roleId + ", pos=(" + x + "," + y + "), direction=" + direction);

        PlayerPosition position = playerPositionRepository.findByRoleId(roleId)
                .orElseThrow(() -> new BusinessException(404, "玩家位置不存在"));

        Scene scene = getSceneDetail(position.getSceneId());

        // 检查移动是否有效
        if (!position.isValidMove(x, y, scene)) {
            throw new BusinessException(400, "无效的移动");
        }

        // 更新位置
        position.moveTo(x, y);
        if (direction != null) {
            position.setDirection(direction);
        }

        playerPositionRepository.save(position);

        System.out.println("玩家移动成功: roleId=" + roleId);
        return true;
    }

    /**
     * 使用传送点
     */
    public boolean useTeleportPoint(Integer roleId, Integer teleportPointId) {
        System.out.println("使用传送点: roleId=" + roleId + ", teleportPointId=" + teleportPointId);

        PlayerRole role = playerRoleService.findById(roleId);
        TeleportPoint teleportPoint = teleportPointRepository.findById(teleportPointId)
                .orElseThrow(() -> new BusinessException(404, "传送点不存在"));

        PlayerPosition position = playerPositionRepository.findByRoleId(roleId)
                .orElseThrow(() -> new BusinessException(404, "玩家位置不存在"));

        // 检查是否可以使用传送点
        if (!teleportPoint.canUse(role)) {
            throw new BusinessException(400, "无法使用该传送点");
        }

        // 检查是否在传送点范围内
        if (!teleportPoint.isInRange(position.getPositionX(), position.getPositionY(), 5)) {
            throw new BusinessException(400, "不在传送点范围内");
        }

        // 检查费用
        if (!teleportPoint.hasEnoughCurrency(role)) {
            throw new BusinessException(400, "传送费用不足");
        }

        // 扣除费用
        if (!teleportPoint.chargeCost(role)) {
            throw new BusinessException(400, "扣除传送费用失败");
        }

        // 传送到目标位置
        enterScene(roleId, teleportPoint.getTargetSceneId(), teleportPoint.getTargetX(), teleportPoint.getTargetY());

        // 保存角色信息（费用变化）
        playerRoleService.save(role);

        System.out.println("传送成功: roleId=" + roleId + ", 目标场景=" + teleportPoint.getTargetSceneId());
        return true;
    }

    /**
     * 玩家下线
     */
    public boolean playerOffline(Integer roleId) {
        System.out.println("玩家下线: roleId=" + roleId);

        Optional<PlayerPosition> positionOpt = playerPositionRepository.findByRoleId(roleId);
        if (positionOpt.isPresent()) {
            PlayerPosition position = positionOpt.get();
            position.setOnlineStatus(false);
            playerPositionRepository.save(position);

            // 减少场景人数
            if (position.getSceneId() != null) {
                sceneRepository.decrementPlayerCount(position.getSceneId());
            }
        }

        System.out.println("玩家下线处理完成: roleId=" + roleId);
        return true;
    }

    /**
     * 获取玩家当前位置
     */
    @Transactional(readOnly = true)
    public PlayerPosition getPlayerPosition(Integer roleId) {
        return playerPositionRepository.findByRoleId(roleId)
                .orElseThrow(() -> new BusinessException(404, "玩家位置不存在"));
    }

    /**
     * 获取场景中的在线玩家
     */
    @Transactional(readOnly = true)
    public List<PlayerPosition> getOnlinePlayersInScene(Integer sceneId) {
        return playerPositionRepository.findBySceneIdAndOnlineOrderByUpdateTimeDesc(sceneId, 1);
    }

    /**
     * 获取附近的玩家
     */
    @Transactional(readOnly = true)
    public List<PlayerPosition> getNearbyPlayers(Integer roleId, Integer range) {
        PlayerPosition position = getPlayerPosition(roleId);
        return playerPositionRepository.findPlayersInRange(position.getSceneId(), 
                position.getPositionX(), position.getPositionY(), range);
    }

    /**
     * 获取场景的传送点
     */
    @Transactional(readOnly = true)
    public List<TeleportPoint> getSceneTeleportPoints(Integer sceneId) {
        return teleportPointRepository.findBySceneIdAndActiveOrderByNameAsc(sceneId, 1);
    }

    /**
     * 获取附近的传送点
     */
    @Transactional(readOnly = true)
    public List<TeleportPoint> getNearbyTeleportPoints(Integer roleId, Integer range) {
        PlayerPosition position = getPlayerPosition(roleId);
        return teleportPointRepository.findTeleportPointsInRange(position.getSceneId(), 
                position.getPositionX(), position.getPositionY(), range);
    }

    /**
     * 回到上次位置
     */
    public boolean returnToLastPosition(Integer roleId) {
        System.out.println("回到上次位置: roleId=" + roleId);

        PlayerPosition position = getPlayerPosition(roleId);
        
        if (!position.returnToLastPosition()) {
            throw new BusinessException(400, "没有上次位置记录");
        }

        // 检查目标场景是否有效
        Scene targetScene = getSceneDetail(position.getSceneId());
        PlayerRole role = playerRoleService.findById(roleId);

        if (!targetScene.canEnter(role)) {
            throw new BusinessException(400, "无法进入目标场景");
        }

        playerPositionRepository.save(position);

        System.out.println("回到上次位置成功: roleId=" + roleId);
        return true;
    }

    /**
     * 随机传送
     */
    public boolean randomTeleport(Integer roleId, Integer sceneId) {
        System.out.println("随机传送: roleId=" + roleId + ", sceneId=" + sceneId);

        Scene scene = getSceneDetail(sceneId);
        PlayerRole role = playerRoleService.findById(roleId);

        if (!scene.canEnter(role)) {
            throw new BusinessException(400, "无法进入该场景");
        }

        // 获取随机位置
        int[] randomPos = scene.getRandomPosition();
        
        return enterScene(roleId, sceneId, randomPos[0], randomPos[1]);
    }

    /**
     * 获取场景统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getSceneStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        long totalScenes = sceneRepository.countActiveScenes();
        long newbieVillages = sceneRepository.countByType(1);
        long towns = sceneRepository.countByType(2);
        long wilderness = sceneRepository.countByType(3);
        long dungeons = sceneRepository.countByType(4);
        long specialScenes = sceneRepository.countByType(5);
        long totalOnlinePlayers = playerPositionRepository.countTotalOnlinePlayers();
        
        stats.put("totalScenes", totalScenes);
        stats.put("newbieVillages", newbieVillages);
        stats.put("towns", towns);
        stats.put("wilderness", wilderness);
        stats.put("dungeons", dungeons);
        stats.put("specialScenes", specialScenes);
        stats.put("totalOnlinePlayers", totalOnlinePlayers);
        
        return stats;
    }

    /**
     * 搜索场景
     */
    @Transactional(readOnly = true)
    public List<Scene> searchScenes(String keyword) {
        return sceneRepository.findByNameContaining(keyword);
    }

    /**
     * 获取热门场景
     */
    @Transactional(readOnly = true)
    public List<Scene> getPopularScenes() {
        return sceneRepository.findPopularScenes();
    }

    /**
     * 同步场景在线人数
     */
    @Transactional
    public void syncScenePlayerCounts() {
        System.out.println("同步场景在线人数");

        List<Scene> scenes = getAllActiveScenes();
        for (Scene scene : scenes) {
            long actualCount = playerPositionRepository.countOnlinePlayersInScene(scene.getId());
            sceneRepository.updatePlayerCount(scene.getId(), (int) actualCount);
        }

        System.out.println("场景在线人数同步完成");
    }
}
