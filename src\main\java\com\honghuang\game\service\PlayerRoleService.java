package com.honghuang.game.service;

import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.PlayerRoleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 玩家角色服务类
 */
@Slf4j
@Service
@Transactional
public class PlayerRoleService {

    @Autowired
    private PlayerRoleRepository playerRoleRepository;

    /**
     * 创建角色
     */
    public PlayerRole createRole(Integer userId, String name, Integer sex, Integer race) {
        log.info("创建角色: userId={}, name={}, sex={}, race={}", userId, name, sex, race);

        // 检查角色名是否已存在
        if (playerRoleRepository.existsByNameAndDeleted(name, 0)) {
            throw new BusinessException(400, "角色名已存在");
        }

        // 检查用户角色数量限制（最多3个角色）
        long roleCount = playerRoleRepository.countByUserId(userId);
        if (roleCount >= 3) {
            throw new BusinessException(400, "每个用户最多只能创建3个角色");
        }

        // 创建角色
        PlayerRole role = new PlayerRole();
        role.setUserId(userId);
        role.setName(name);
        role.setSex(sex);
        role.setRace(race);

        // 设置初始属性
        initializeRoleAttributes(role);

        role = playerRoleRepository.save(role);
        log.info("角色创建成功: roleId={}, name={}", role.getId(), role.getName());

        return role;
    }

    /**
     * 根据ID查找角色
     */
    @Transactional(readOnly = true)
    public PlayerRole findById(Integer roleId) {
        return playerRoleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException(404, "角色不存在"));
    }

    /**
     * 根据角色名查找角色
     */
    @Transactional(readOnly = true)
    public PlayerRole findByName(String name) {
        Optional<PlayerRole> role = playerRoleRepository.findByNameAndDeleted(name, 0);
        return role.orElse(null);
    }

    /**
     * 保存角色
     */
    public PlayerRole save(PlayerRole role) {
        return playerRoleRepository.save(role);
    }

    /**
     * 获取用户的主角色
     */
    @Transactional(readOnly = true)
    public PlayerRole getMainRoleByUserId(Integer userId) {
        List<PlayerRole> roles = playerRoleRepository.findByUserIdAndDeletedOrderByCreateTimeAsc(userId, 0);
        return roles.isEmpty() ? null : roles.get(0);
    }

    /**
     * 获取用户的所有角色
     */
    @Transactional(readOnly = true)
    public List<PlayerRole> getRolesByUserId(Integer userId) {
        return playerRoleRepository.findByUserIdAndDeletedOrderByCreateTimeAsc(userId, 0);
    }



    /**
     * 选择角色
     */
    @Transactional(readOnly = true)
    public PlayerRole selectRole(Integer userId, Integer roleId) {
        PlayerRole role = playerRoleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException(404, "角色不存在"));

        if (!role.getUserId().equals(userId)) {
            throw new BusinessException(403, "无权访问此角色");
        }

        if (role.getDeleted() == 1) {
            throw new BusinessException(400, "角色已被删除");
        }

        return role;
    }

    /**
     * 删除角色
     */
    @Transactional
    public void deleteRole(Integer userId, Integer roleId) {
        PlayerRole role = playerRoleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException(404, "角色不存在"));

        if (!role.getUserId().equals(userId)) {
            throw new BusinessException(403, "无权删除此角色");
        }

        role.setDeleted(1);
        playerRoleRepository.save(role);
    }

    /**
     * 获取角色统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getRoleStatistics() {
        Map<String, Object> stats = new HashMap<>();

        long totalRoles = playerRoleRepository.countByDeleted(0);
        long onlineRoles = 0; // 暂时设为0，后续可以通过其他方式统计在线角色

        stats.put("totalRoles", totalRoles);
        stats.put("onlineRoles", onlineRoles);
        stats.put("averageLevel", getAverageLevel());
        stats.put("maxLevel", getMaxLevel());

        return stats;
    }

    /**
     * 获取等级排行榜
     */
    @Transactional(readOnly = true)
    public List<PlayerRole> getLevelRanking(int limit) {
        return playerRoleRepository.findTop10ByDeletedOrderByLevelDescCreateTimeAsc(0)
                .stream()
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * 获取财富排行榜
     */
    @Transactional(readOnly = true)
    public List<PlayerRole> getGoldRanking(int limit) {
        return playerRoleRepository.findTop10ByDeletedOrderByGoldDescCreateTimeAsc(0)
                .stream()
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID查找角色列表
     */
    @Transactional(readOnly = true)
    public List<PlayerRole> findByUserId(Integer userId) {
        return playerRoleRepository.findByUserIdAndDeleted(userId, 0);
    }

    /**
     * 根据用户ID和角色ID查找角色
     */
    @Transactional(readOnly = true)
    public PlayerRole findByUserIdAndRoleId(Integer userId, Integer roleId) {
        return playerRoleRepository.findByIdAndUserIdAndDeleted(roleId, userId, 0)
                .orElseThrow(() -> new BusinessException(404, "角色不存在或无权限访问"));
    }

    /**
     * 角色升级
     */
    public boolean levelUp(Integer roleId) {
        log.info("角色升级: roleId={}", roleId);

        PlayerRole role = findById(roleId);
        
        if (!role.canLevelUp()) {
            log.warn("角色经验不足，无法升级: roleId={}, currentExp={}, needExp={}", 
                    roleId, role.getExperience(), role.getNextLevelExp());
            return false;
        }

        // 升级
        int newLevel = role.getLevel() + 1;
        Long remainingExp = role.getExperience() - role.getNextLevelExp();
        
        // 计算属性提升
        int hpIncrease = calculateHpIncrease(role);
        int mpIncrease = calculateMpIncrease(role);
        int attributePointsGain = 5; // 每级获得5个属性点

        // 更新角色属性
        role.setLevel(newLevel);
        role.setExperience(remainingExp);
        role.setMaxHp(role.getMaxHp() + hpIncrease);
        role.setMaxMp(role.getMaxMp() + mpIncrease);
        role.setCurrentHp(role.getMaxHp()); // 升级时恢复满血
        role.setCurrentMp(role.getMaxMp()); // 升级时恢复满蓝
        role.setAttributePoints(role.getAttributePoints() + attributePointsGain);

        playerRoleRepository.save(role);
        
        log.info("角色升级成功: roleId={}, newLevel={}, hpIncrease={}, mpIncrease={}, attributePoints={}", 
                roleId, newLevel, hpIncrease, mpIncrease, attributePointsGain);
        
        return true;
    }

    /**
     * 分配属性点
     */
    public boolean allocateAttributePoints(Integer roleId, Integer attack, Integer defense, 
                                         Integer agility, Integer intelligence, Integer constitution) {
        log.info("分配属性点: roleId={}, attack={}, defense={}, agility={}, intelligence={}, constitution={}", 
                roleId, attack, defense, agility, intelligence, constitution);

        PlayerRole role = findById(roleId);
        
        int totalPoints = attack + defense + agility + intelligence + constitution;
        if (totalPoints > role.getAttributePoints()) {
            throw new BusinessException(400, "属性点不足");
        }

        // 更新属性
        role.setBaseAttack(role.getBaseAttack() + attack);
        role.setBaseDefense(role.getBaseDefense() + defense);
        role.setBaseAgility(role.getBaseAgility() + agility);
        role.setBaseIntelligence(role.getBaseIntelligence() + intelligence);
        role.setBaseConstitution(role.getBaseConstitution() + constitution);
        role.setAttributePoints(role.getAttributePoints() - totalPoints);

        // 体质影响生命值和法力值
        if (constitution > 0) {
            int hpIncrease = constitution * 10;
            int mpIncrease = constitution * 5;
            role.setMaxHp(role.getMaxHp() + hpIncrease);
            role.setMaxMp(role.getMaxMp() + mpIncrease);
        }

        playerRoleRepository.save(role);
        
        log.info("属性点分配成功: roleId={}", roleId);
        return true;
    }

    /**
     * 增加经验值
     */
    public void addExperience(Integer roleId, Long exp) {
        log.info("增加经验值: roleId={}, exp={}", roleId, exp);

        PlayerRole role = findById(roleId);
        role.addExperience(exp);
        
        // 检查是否可以升级
        while (role.canLevelUp()) {
            levelUp(roleId);
            role = findById(roleId); // 重新获取最新数据
        }
        
        playerRoleRepository.save(role);
    }

    /**
     * 更新角色位置
     */
    public void updateLocation(Integer roleId, Integer sceneId, String sceneName) {
        log.info("更新角色位置: roleId={}, sceneId={}, sceneName={}", roleId, sceneId, sceneName);
        
        int updated = playerRoleRepository.updateLocation(roleId, sceneId, sceneName);
        if (updated == 0) {
            throw new BusinessException(404, "角色不存在");
        }
    }

    /**
     * 更新角色在线状态
     */
    public void updateOnlineStatus(Integer roleId, Integer status) {
        log.info("更新角色在线状态: roleId={}, status={}", roleId, status);
        
        int updated = playerRoleRepository.updateOnlineStatus(roleId, status, LocalDateTime.now());
        if (updated == 0) {
            throw new BusinessException(404, "角色不存在");
        }
    }

    /**
     * 恢复角色生命值和法力值
     */
    public void restoreHpMp(Integer roleId, Integer hp, Integer mp) {
        PlayerRole role = findById(roleId);
        
        if (hp != null) {
            role.restoreHp(hp);
        }
        if (mp != null) {
            role.restoreMp(mp);
        }
        
        playerRoleRepository.save(role);
    }

    /**
     * 扣除角色生命值
     */
    public void takeDamage(Integer roleId, Integer damage) {
        PlayerRole role = findById(roleId);
        role.takeDamage(damage);
        playerRoleRepository.save(role);
    }

    /**
     * 获取等级排行榜
     */
    @Transactional(readOnly = true)
    public Page<PlayerRole> getLevelRanking(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return playerRoleRepository.findLevelRanking(pageable);
    }



    /**
     * 初始化角色属性
     */
    private void initializeRoleAttributes(PlayerRole role) {
        // 根据性别设置初始属性
        if (role.getSex() == 1) { // 男性
            role.setBaseAttack(role.getBaseAttack() + 2);
            role.setBaseConstitution(role.getBaseConstitution() + 1);
        } else { // 女性
            role.setBaseAgility(role.getBaseAgility() + 2);
            role.setBaseIntelligence(role.getBaseIntelligence() + 1);
        }

        // 根据种族设置属性
        if (role.getRace() == 2) { // 妖族
            role.setBaseIntelligence(role.getBaseIntelligence() + 2);
            role.setBaseAgility(role.getBaseAgility() + 1);
        } else { // 巫族
            role.setBaseAttack(role.getBaseAttack() + 1);
            role.setBaseConstitution(role.getBaseConstitution() + 2);
        }

        // 根据体质计算初始生命值和法力值
        int constitution = role.getBaseConstitution();
        role.setMaxHp(100 + constitution * 10);
        role.setMaxMp(50 + constitution * 5);
        role.setCurrentHp(role.getMaxHp());
        role.setCurrentMp(role.getMaxMp());
    }

    /**
     * 计算升级时生命值增长
     */
    private int calculateHpIncrease(PlayerRole role) {
        return 20 + role.getBaseConstitution() * 2;
    }

    /**
     * 计算升级时法力值增长
     */
    private int calculateMpIncrease(PlayerRole role) {
        return 10 + role.getBaseIntelligence() * 2;
    }

    /**
     * 获取平均等级
     */
    private Double getAverageLevel() {
        List<PlayerRole> roles = playerRoleRepository.findByDeleted(0);
        if (roles.isEmpty()) {
            return 0.0;
        }
        return roles.stream()
                .mapToInt(PlayerRole::getLevel)
                .average()
                .orElse(0.0);
    }

    /**
     * 获取最高等级
     */
    private Integer getMaxLevel() {
        List<PlayerRole> roles = playerRoleRepository.findByDeleted(0);
        if (roles.isEmpty()) {
            return 0;
        }
        return roles.stream()
                .mapToInt(PlayerRole::getLevel)
                .max()
                .orElse(0);
    }

    /**
     * 角色升级
     */
    @Transactional
    public PlayerRole levelUp(Integer userId, Integer roleId) {
        PlayerRole role = playerRoleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException(404, "角色不存在"));

        if (!role.getUserId().equals(userId)) {
            throw new BusinessException(403, "无权操作此角色");
        }

        // 简单的升级逻辑
        role.setLevel(role.getLevel() + 1);
        role.setMaxHp(role.getMaxHp() + 20);
        role.setMaxMp(role.getMaxMp() + 10);
        role.setCurrentHp(role.getMaxHp());
        role.setCurrentMp(role.getMaxMp());

        return playerRoleRepository.save(role);
    }

    /**
     * 恢复角色状态
     */
    @Transactional
    public PlayerRole recoverRole(Integer userId, Integer roleId) {
        PlayerRole role = playerRoleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException(404, "角色不存在"));

        if (!role.getUserId().equals(userId)) {
            throw new BusinessException(403, "无权操作此角色");
        }

        // 恢复血量和法力
        role.setCurrentHp(role.getMaxHp());
        role.setCurrentMp(role.getMaxMp());

        return playerRoleRepository.save(role);
    }

    /**
     * 获取角色详细信息
     */
    @Transactional(readOnly = true)
    public PlayerRole getRoleDetail(Integer userId, Integer roleId) {
        PlayerRole role = playerRoleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException(404, "角色不存在"));

        if (!role.getUserId().equals(userId)) {
            throw new BusinessException(403, "无权访问此角色");
        }

        return role;
    }
}
