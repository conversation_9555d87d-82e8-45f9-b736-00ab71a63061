package com.honghuang.game.repository;

import com.honghuang.game.entity.Guild;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 帮派数据访问层
 */
@Repository
public interface GuildRepository extends JpaRepository<Guild, Integer> {

    /**
     * 根据名称查找帮派
     */
    Optional<Guild> findByNameAndStatus(String name, Integer status);

    /**
     * 检查帮派名是否存在
     */
    boolean existsByNameAndStatus(String name, Integer status);

    /**
     * 查找正常状态的帮派
     */
    List<Guild> findByStatusOrderByLevelDescExperienceDesc(Integer status);

    /**
     * 根据帮主ID查找帮派
     */
    Optional<Guild> findByLeaderIdAndStatus(Integer leaderId, Integer status);

    /**
     * 根据等级范围查找帮派
     */
    @Query("SELECT g FROM Guild g WHERE g.level BETWEEN :minLevel AND :maxLevel AND g.status = 1 ORDER BY g.level DESC")
    List<Guild> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 查找可加入的帮派
     */
    @Query("SELECT g FROM Guild g WHERE g.status = 1 AND g.memberCount < g.maxMembers AND g.joinType != 3 ORDER BY g.level DESC")
    List<Guild> findJoinableGuilds();

    /**
     * 查找需要审核的帮派
     */
    @Query("SELECT g FROM Guild g WHERE g.status = 1 AND g.joinType = 2 ORDER BY g.level DESC")
    List<Guild> findGuildsNeedingApproval();

    /**
     * 获取帮派等级排行榜
     */
    @Query("SELECT g FROM Guild g WHERE g.status = 1 ORDER BY g.level DESC, g.experience DESC")
    Page<Guild> findLevelRanking(Pageable pageable);

    /**
     * 获取帮派成员数排行榜
     */
    @Query("SELECT g FROM Guild g WHERE g.status = 1 ORDER BY g.memberCount DESC, g.level DESC")
    Page<Guild> findMemberCountRanking(Pageable pageable);

    /**
     * 获取帮派资金排行榜
     */
    @Query("SELECT g FROM Guild g WHERE g.status = 1 ORDER BY g.funds DESC, g.level DESC")
    Page<Guild> findFundsRanking(Pageable pageable);

    /**
     * 更新帮派成员数
     */
    @Modifying
    @Query("UPDATE Guild g SET g.memberCount = :memberCount WHERE g.id = :guildId")
    int updateMemberCount(@Param("guildId") Integer guildId, @Param("memberCount") Integer memberCount);

    /**
     * 更新帮派等级和经验
     */
    @Modifying
    @Query("UPDATE Guild g SET g.level = :level, g.experience = :experience WHERE g.id = :guildId")
    int updateLevelAndExp(@Param("guildId") Integer guildId, @Param("level") Integer level, @Param("experience") Long experience);

    /**
     * 更新帮派资金
     */
    @Modifying
    @Query("UPDATE Guild g SET g.funds = :funds WHERE g.id = :guildId")
    int updateFunds(@Param("guildId") Integer guildId, @Param("funds") Long funds);

    /**
     * 更新帮派公告
     */
    @Modifying
    @Query("UPDATE Guild g SET g.notice = :notice WHERE g.id = :guildId")
    int updateNotice(@Param("guildId") Integer guildId, @Param("notice") String notice);

    /**
     * 更新帮派活跃时间
     */
    @Modifying
    @Query("UPDATE Guild g SET g.lastActiveTime = :activeTime WHERE g.id = :guildId")
    int updateActiveTime(@Param("guildId") Integer guildId, @Param("activeTime") LocalDateTime activeTime);

    /**
     * 更新帮主信息
     */
    @Modifying
    @Query("UPDATE Guild g SET g.leaderId = :leaderId, g.leaderName = :leaderName WHERE g.id = :guildId")
    int updateLeader(@Param("guildId") Integer guildId, @Param("leaderId") Integer leaderId, @Param("leaderName") String leaderName);

    /**
     * 解散帮派
     */
    @Modifying
    @Query("UPDATE Guild g SET g.status = 3 WHERE g.id = :guildId")
    int dissolveGuild(@Param("guildId") Integer guildId);

    /**
     * 根据名称模糊查找帮派
     */
    @Query("SELECT g FROM Guild g WHERE g.name LIKE %:name% AND g.status = 1 ORDER BY g.level DESC")
    List<Guild> findByNameContaining(@Param("name") String name);

    /**
     * 查找长期不活跃的帮派
     */
    @Query("SELECT g FROM Guild g WHERE g.status = 1 AND g.lastActiveTime <= :cutoffTime")
    List<Guild> findInactiveGuilds(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计正常帮派数量
     */
    @Query("SELECT COUNT(g) FROM Guild g WHERE g.status = 1")
    long countActiveGuilds();

    /**
     * 查找指定等级以上的帮派
     */
    @Query("SELECT g FROM Guild g WHERE g.level >= :minLevel AND g.status = 1 ORDER BY g.level DESC")
    List<Guild> findByMinLevel(@Param("minLevel") Integer minLevel);

    /**
     * 查找有空位的帮派
     */
    @Query("SELECT g FROM Guild g WHERE g.memberCount < g.maxMembers AND g.status = 1 ORDER BY g.level DESC")
    List<Guild> findGuildsWithVacancy();
}
