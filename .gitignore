# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

# STS
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

# IntelliJ IDEA
.idea
*.iws
*.iml
*.ipr

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

# VS Code
.vscode/

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log
*.log.*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp

# Application specific
application-local.yml
application-dev.yml

# Database files
*.db
*.mv.db
*.trace.db
data/

# Backup files
backup/
*.sql
backup_*

# Python files (for utility scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Shell scripts (if they contain sensitive info)
# Uncomment if needed
# *.sh

# Analysis and documentation files (optional - uncomment if you don't want to track them)
# *.md
# !README.md

# Cookies and session files
cookies.txt

# Performance and analysis files
*-analysis.md
*报告.md
