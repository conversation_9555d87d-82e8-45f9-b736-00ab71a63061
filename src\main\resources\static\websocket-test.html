<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket测试 - 洪荒游戏</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 20px;
            background-color: #f0f8ff;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .controls input, .controls button {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .controls button {
            background-color: #2c5aa0;
            color: white;
            cursor: pointer;
        }
        
        .controls button:hover {
            background-color: #1e3d6f;
        }
        
        .controls button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .messages {
            border: 1px solid #ddd;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            background-color: #fafafa;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            background-color: white;
            border-left: 3px solid #66ccff;
        }
        
        .message.sent {
            border-left-color: #28a745;
        }
        
        .message.received {
            border-left-color: #007bff;
        }
        
        .message.error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        
        .message-time {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .message-content {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接测试</h1>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="controls">
            <label>角色ID: </label>
            <input type="number" id="roleId" value="1" min="1">
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            <br><br>
            <label>消息: </label>
            <input type="text" id="messageInput" placeholder="输入测试消息" disabled>
            <button id="sendBtn" onclick="sendMessage()" disabled>发送</button>
            <button onclick="sendHeartbeat()" disabled id="heartbeatBtn">心跳</button>
        </div>
        
        <div class="messages" id="messages"></div>
        
        <div style="margin-top: 20px;">
            <button onclick="clearMessages()">清空消息</button>
            <button onclick="window.location.href='game.html'">返回游戏</button>
        </div>
    </div>

    <script>
        let websocket = null;
        let roleId = 1;
        
        function connect() {
            roleId = document.getElementById('roleId').value;
            if (!roleId) {
                addMessage('error', '请输入角色ID');
                return;
            }
            
            updateStatus('connecting', '连接中...');
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/honghuang-game/websocket/game/${roleId}`;
            
            try {
                websocket = new WebSocket(wsUrl);
                
                websocket.onopen = function(event) {
                    updateStatus('connected', '已连接');
                    addMessage('received', 'WebSocket连接成功');
                    
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('messageInput').disabled = false;
                    document.getElementById('sendBtn').disabled = false;
                    document.getElementById('heartbeatBtn').disabled = false;
                    document.getElementById('roleId').disabled = true;
                };
                
                websocket.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        addMessage('received', `收到消息: ${JSON.stringify(message, null, 2)}`);
                    } catch (e) {
                        addMessage('received', `收到消息: ${event.data}`);
                    }
                };
                
                websocket.onclose = function(event) {
                    updateStatus('disconnected', '连接已断开');
                    addMessage('error', `连接关闭: ${event.code} - ${event.reason}`);
                    
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    document.getElementById('messageInput').disabled = true;
                    document.getElementById('sendBtn').disabled = true;
                    document.getElementById('heartbeatBtn').disabled = true;
                    document.getElementById('roleId').disabled = false;
                };
                
                websocket.onerror = function(error) {
                    addMessage('error', `WebSocket错误: ${error}`);
                    updateStatus('disconnected', '连接错误');
                };
                
            } catch (error) {
                addMessage('error', `连接失败: ${error.message}`);
                updateStatus('disconnected', '连接失败');
            }
        }
        
        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const content = input.value.trim();
            
            if (!content) {
                addMessage('error', '请输入消息内容');
                return;
            }
            
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                addMessage('error', 'WebSocket未连接');
                return;
            }
            
            const message = {
                type: 'chat',
                content: content,
                channel: 'world'
            };
            
            try {
                websocket.send(JSON.stringify(message));
                addMessage('sent', `发送消息: ${content}`);
                input.value = '';
            } catch (error) {
                addMessage('error', `发送失败: ${error.message}`);
            }
        }
        
        function sendHeartbeat() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                addMessage('error', 'WebSocket未连接');
                return;
            }
            
            const message = {
                type: 'heartbeat',
                content: 'ping'
            };
            
            try {
                websocket.send(JSON.stringify(message));
                addMessage('sent', '发送心跳: ping');
            } catch (error) {
                addMessage('error', `心跳发送失败: ${error.message}`);
            }
        }
        
        function updateStatus(type, text) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = text;
        }
        
        function addMessage(type, content) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const time = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <div class="message-time">${time}</div>
                <div class="message-content">${content}</div>
            `;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
        
        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            if (websocket) {
                websocket.close();
            }
        });
    </script>
</body>
</html>
