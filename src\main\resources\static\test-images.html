<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片测试 - 洪荒游戏</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #fff;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #3498db;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
            border: 1px solid #34495e;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
        }
        .image-item img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #3498db;
        }
        .image-name {
            margin-top: 10px;
            font-size: 14px;
            color: #ecf0f1;
        }
        .status {
            margin-top: 10px;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
        }
        .success { background: #2ecc71; }
        .error { background: #e74c3c; }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 图片路径测试</h1>
        <p>测试游戏中各种图片资源的加载情况</p>
        
        <div class="test-section">
            <h3>🧙‍♂️ 角色种族图片</h3>
            <button class="btn" onclick="testRoleImages()">测试角色图片</button>
            <div class="image-grid" id="roleImages">
                <!-- 角色图片将在这里显示 -->
            </div>
        </div>

        <div class="test-section">
            <h3>⚔️ 装备图片</h3>
            <button class="btn" onclick="testEquipmentImages()">测试装备图片</button>
            <div class="image-grid" id="equipmentImages">
                <!-- 装备图片将在这里显示 -->
            </div>
        </div>

        <div class="test-section">
            <h3>💎 道具图片</h3>
            <button class="btn" onclick="testPropImages()">测试道具图片</button>
            <div class="image-grid" id="propImages">
                <!-- 道具图片将在这里显示 -->
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="testAllImages()">测试所有图片</button>
            <button class="btn" onclick="window.location.href='/honghuang-game/equipment.html'">返回装备展示</button>
        </div>
    </div>

    <script>
        // 角色数据
        const roleData = [
            { name: '巫族', image: 'wu.png' },
            { name: '妖族', image: 'yao.png' }
        ];

        // 装备数据
        const equipmentData = [
            { name: '金龙冠', image: '001jinlongguan.png' },
            { name: '九龙袍', image: '001jiulongpao.png' },
            { name: '龙虎如意', image: '001longhuruyi.png' },
            { name: '龙腾四海', image: '001longtengsihai.png' }
        ];

        // 道具数据
        const propData = [
            { name: '宝石01', image: 'baoshi01.png' },
            { name: '宝石02', image: 'baoshi02.png' },
            { name: '丹药01', image: 'danyao01.png' },
            { name: '丹药02', image: 'danyao02.png' }
        ];

        // 测试角色图片
        function testRoleImages() {
            const container = document.getElementById('roleImages');
            container.innerHTML = '';
            
            roleData.forEach(role => {
                const div = document.createElement('div');
                div.className = 'image-item';
                
                const img = document.createElement('img');
                img.src = `/honghuang-game/images/role/${role.image}`;
                img.alt = role.name;
                
                const name = document.createElement('div');
                name.className = 'image-name';
                name.textContent = role.name;
                
                const status = document.createElement('div');
                status.className = 'status';
                status.textContent = '加载中...';
                
                img.onload = function() {
                    status.textContent = '✅ 加载成功';
                    status.className = 'status success';
                };
                
                img.onerror = function() {
                    status.textContent = '❌ 加载失败';
                    status.className = 'status error';
                    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjZTc0YzNjIi8+Cjx0ZXh0IHg9IjQwIiB5PSI0NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7plJnor688L3RleHQ+Cjwvc3ZnPgo=';
                };
                
                div.appendChild(img);
                div.appendChild(name);
                div.appendChild(status);
                container.appendChild(div);
            });
        }

        // 测试装备图片
        function testEquipmentImages() {
            const container = document.getElementById('equipmentImages');
            container.innerHTML = '';
            
            equipmentData.forEach(item => {
                const div = document.createElement('div');
                div.className = 'image-item';
                
                const img = document.createElement('img');
                img.src = `/honghuang-game/images/item/equip/${item.image}`;
                img.alt = item.name;
                
                const name = document.createElement('div');
                name.className = 'image-name';
                name.textContent = item.name;
                
                const status = document.createElement('div');
                status.className = 'status';
                status.textContent = '加载中...';
                
                img.onload = function() {
                    status.textContent = '✅ 加载成功';
                    status.className = 'status success';
                };
                
                img.onerror = function() {
                    status.textContent = '❌ 加载失败';
                    status.className = 'status error';
                    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjZTc0YzNjIi8+Cjx0ZXh0IHg9IjQwIiB5PSI0NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7plJnor688L3RleHQ+Cjwvc3ZnPgo=';
                };
                
                div.appendChild(img);
                div.appendChild(name);
                div.appendChild(status);
                container.appendChild(div);
            });
        }

        // 测试道具图片
        function testPropImages() {
            const container = document.getElementById('propImages');
            container.innerHTML = '';
            
            propData.forEach(item => {
                const div = document.createElement('div');
                div.className = 'image-item';
                
                const img = document.createElement('img');
                img.src = `/honghuang-game/images/item/prop/${item.image}`;
                img.alt = item.name;
                
                const name = document.createElement('div');
                name.className = 'image-name';
                name.textContent = item.name;
                
                const status = document.createElement('div');
                status.className = 'status';
                status.textContent = '加载中...';
                
                img.onload = function() {
                    status.textContent = '✅ 加载成功';
                    status.className = 'status success';
                };
                
                img.onerror = function() {
                    status.textContent = '❌ 加载失败';
                    status.className = 'status error';
                    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjZTc0YzNjIi8+Cjx0ZXh0IHg9IjQwIiB5PSI0NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7plJnor688L3RleHQ+Cjwvc3ZnPgo=';
                };
                
                div.appendChild(img);
                div.appendChild(name);
                div.appendChild(status);
                container.appendChild(div);
            });
        }

        // 测试所有图片
        function testAllImages() {
            testRoleImages();
            testEquipmentImages();
            testPropImages();
        }

        // 页面加载时自动测试
        window.onload = function() {
            testAllImages();
        };
    </script>
</body>
</html>
