package com.honghuang.game.service;

import com.honghuang.game.entity.Inventory;
import com.honghuang.game.entity.Item;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.InventoryRepository;
import com.honghuang.game.repository.ItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 背包服务类
 */
@Service
@Transactional
public class InventoryService {

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    private static final int MAX_INVENTORY_SLOTS = 100; // 背包最大100格

    /**
     * 获取角色背包物品列表
     */
    @Transactional(readOnly = true)
    public List<Inventory> getInventory(Integer roleId) {
        return inventoryRepository.findByRoleIdOrderBySlotIndexAsc(roleId);
    }

    /**
     * 获取指定槽位的物品
     */
    @Transactional(readOnly = true)
    public Optional<Inventory> getInventorySlot(Integer roleId, Integer slotIndex) {
        return inventoryRepository.findByRoleIdAndSlotIndex(roleId, slotIndex);
    }

    /**
     * 添加物品到背包
     */
    public boolean addItem(Integer roleId, Integer itemId, Integer quantity) {
        System.out.println("添加物品到背包: roleId=" + roleId + ", itemId=" + itemId + ", quantity=" + quantity);

        if (quantity <= 0) {
            throw new BusinessException(400, "物品数量必须大于0");
        }

        Item item = itemRepository.findById(itemId)
                .orElseThrow(() -> new BusinessException(404, "物品不存在"));

        int remainingQuantity = quantity;

        // 如果物品可堆叠，先尝试堆叠到现有物品上
        if (item.isStackable()) {
            List<Inventory> stackableItems = inventoryRepository.findStackableItems(roleId, itemId);
            for (Inventory inventory : stackableItems) {
                if (remainingQuantity <= 0) break;

                int canAdd = item.getMaxStack() - inventory.getQuantity();
                int toAdd = Math.min(canAdd, remainingQuantity);

                if (toAdd > 0) {
                    inventory.addQuantity(toAdd);
                    inventoryRepository.save(inventory);
                    remainingQuantity -= toAdd;
                }
            }
        }

        // 如果还有剩余数量，创建新的背包条目
        while (remainingQuantity > 0) {
            Optional<Integer> emptySlot = findEmptySlot(roleId);
            if (!emptySlot.isPresent()) {
                throw new BusinessException(400, "背包已满");
            }

            int toAdd = item.isStackable() ? Math.min(remainingQuantity, item.getMaxStack()) : 1;

            Inventory inventory = new Inventory();
            inventory.setRoleId(roleId);
            inventory.setItemId(itemId);
            inventory.setSlotIndex(emptySlot.get());
            inventory.setQuantity(toAdd);
            inventory.setObtainTime(LocalDateTime.now());

            inventoryRepository.save(inventory);
            remainingQuantity -= toAdd;
        }

        System.out.println("物品添加成功: roleId=" + roleId + ", itemId=" + itemId + ", quantity=" + quantity);
        return true;
    }

    /**
     * 移除背包物品
     */
    public boolean removeItem(Integer roleId, Integer itemId, Integer quantity) {
        System.out.println("移除背包物品: roleId=" + roleId + ", itemId=" + itemId + ", quantity=" + quantity);

        if (quantity <= 0) {
            throw new BusinessException(400, "移除数量必须大于0");
        }

        List<Inventory> items = inventoryRepository.findByRoleIdAndItemIdOrderBySlotIndexAsc(roleId, itemId);
        int remainingToRemove = quantity;

        for (Inventory inventory : items) {
            if (remainingToRemove <= 0) break;

            int toRemove = Math.min(inventory.getQuantity(), remainingToRemove);
            inventory.reduceQuantity(toRemove);
            remainingToRemove -= toRemove;

            if (inventory.getQuantity() <= 0) {
                inventory.clear();
            }
            inventoryRepository.save(inventory);
        }

        if (remainingToRemove > 0) {
            throw new BusinessException(400, "背包中物品数量不足");
        }

        System.out.println("物品移除成功: roleId=" + roleId + ", itemId=" + itemId + ", quantity=" + quantity);
        return true;
    }

    /**
     * 使用物品
     */
    public boolean useItem(Integer roleId, Integer slotIndex) {
        System.out.println("使用物品: roleId=" + roleId + ", slotIndex=" + slotIndex);

        Inventory inventory = inventoryRepository.findByRoleIdAndSlotIndex(roleId, slotIndex)
                .orElseThrow(() -> new BusinessException(404, "槽位为空"));

        if (inventory.isEmpty()) {
            throw new BusinessException(400, "槽位为空");
        }

        PlayerRole role = playerRoleService.findById(roleId);
        if (!inventory.canUse(role)) {
            throw new BusinessException(400, "无法使用该物品");
        }

        // 执行物品效果
        executeItemEffect(role, inventory.getItem(), inventory);

        // 使用物品
        inventory.use();
        if (inventory.getQuantity() <= 0) {
            inventory.clear();
        }
        inventoryRepository.save(inventory);

        System.out.println("物品使用成功: roleId=" + roleId + ", itemId=" + inventory.getItemId());
        return true;
    }

    /**
     * 移动物品位置
     */
    public boolean moveItem(Integer roleId, Integer fromSlot, Integer toSlot) {
        System.out.println("移动物品: roleId=" + roleId + ", from=" + fromSlot + ", to=" + toSlot);

        if (fromSlot.equals(toSlot)) {
            return true; // 位置相同，无需移动
        }

        Optional<Inventory> fromInventory = inventoryRepository.findByRoleIdAndSlotIndex(roleId, fromSlot);
        Optional<Inventory> toInventory = inventoryRepository.findByRoleIdAndSlotIndex(roleId, toSlot);

        if (!fromInventory.isPresent() || fromInventory.get().isEmpty()) {
            throw new BusinessException(400, "源槽位为空");
        }

        Inventory from = fromInventory.get();

        if (!toInventory.isPresent() || toInventory.get().isEmpty()) {
            // 目标槽位为空，直接移动
            from.moveTo(toSlot);
            inventoryRepository.save(from);
        } else {
            // 目标槽位有物品，交换位置
            Inventory to = toInventory.get();
            
            // 检查是否可以合并
            if (from.getItemId().equals(to.getItemId()) && from.canStack()) {
                if (to.canAddQuantity(from.getQuantity())) {
                    // 可以合并
                    to.addQuantity(from.getQuantity());
                    from.clear();
                    inventoryRepository.save(from);
                    inventoryRepository.save(to);
                } else {
                    throw new BusinessException(400, "无法合并，超出堆叠上限");
                }
            } else {
                // 交换位置
                from.moveTo(toSlot);
                to.moveTo(fromSlot);
                inventoryRepository.save(from);
                inventoryRepository.save(to);
            }
        }

        System.out.println("物品移动成功: roleId=" + roleId);
        return true;
    }

    /**
     * 分割物品
     */
    public boolean splitItem(Integer roleId, Integer slotIndex, Integer splitQuantity) {
        System.out.println("分割物品: roleId=" + roleId + ", slotIndex=" + slotIndex + ", splitQuantity=" + splitQuantity);

        Inventory inventory = inventoryRepository.findByRoleIdAndSlotIndex(roleId, slotIndex)
                .orElseThrow(() -> new BusinessException(404, "槽位为空"));

        if (!inventory.canStack() || inventory.getQuantity() <= splitQuantity) {
            throw new BusinessException(400, "无法分割该物品");
        }

        Optional<Integer> emptySlot = findEmptySlot(roleId);
        if (!emptySlot.isPresent()) {
            throw new BusinessException(400, "背包已满，无法分割");
        }

        Inventory newInventory = inventory.split(splitQuantity);
        if (newInventory == null) {
            throw new BusinessException(400, "分割失败");
        }

        newInventory.setSlotIndex(emptySlot.get());
        inventoryRepository.save(inventory);
        inventoryRepository.save(newInventory);

        System.out.println("物品分割成功: roleId=" + roleId);
        return true;
    }

    /**
     * 整理背包
     */
    public boolean organizeInventory(Integer roleId) {
        System.out.println("整理背包: roleId=" + roleId);

        List<Inventory> items = inventoryRepository.findByRoleIdOrderBySlotIndexAsc(roleId);
        
        // 先合并可堆叠的物品
        mergeStackableItems(roleId);
        
        // 重新排列物品，去除空隙
        items = inventoryRepository.findByRoleIdOrderBySlotIndexAsc(roleId);
        int newSlotIndex = 0;
        
        for (Inventory inventory : items) {
            if (!inventory.isEmpty()) {
                if (inventory.getSlotIndex() != newSlotIndex) {
                    inventory.setSlotIndex(newSlotIndex);
                    inventoryRepository.save(inventory);
                }
                newSlotIndex++;
            }
        }

        System.out.println("背包整理完成: roleId=" + roleId);
        return true;
    }

    /**
     * 获取背包统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getInventoryStatistics(Integer roleId) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        long usedSlots = inventoryRepository.countUsedSlots(roleId);
        long emptySlots = MAX_INVENTORY_SLOTS - usedSlots;
        
        stats.put("totalSlots", MAX_INVENTORY_SLOTS);
        stats.put("usedSlots", usedSlots);
        stats.put("emptySlots", emptySlots);
        stats.put("usageRate", (double) usedSlots / MAX_INVENTORY_SLOTS * 100);
        
        return stats;
    }

    /**
     * 检查是否有指定物品
     */
    @Transactional(readOnly = true)
    public boolean hasItem(Integer roleId, Integer itemId, Integer quantity) {
        long totalQuantity = inventoryRepository.countItemQuantity(roleId, itemId);
        return totalQuantity >= quantity;
    }

    /**
     * 获取指定物品的总数量
     */
    @Transactional(readOnly = true)
    public long getItemQuantity(Integer roleId, Integer itemId) {
        return inventoryRepository.countItemQuantity(roleId, itemId);
    }

    /**
     * 查找空槽位
     */
    private Optional<Integer> findEmptySlot(Integer roleId) {
        for (int i = 0; i < MAX_INVENTORY_SLOTS; i++) {
            Optional<Inventory> slot = inventoryRepository.findByRoleIdAndSlotIndex(roleId, i);
            if (!slot.isPresent() || slot.get().isEmpty()) {
                return Optional.of(i);
            }
        }
        return Optional.empty();
    }

    /**
     * 合并可堆叠的物品
     */
    private void mergeStackableItems(Integer roleId) {
        List<Inventory> items = inventoryRepository.findByRoleIdOrderBySlotIndexAsc(roleId);
        
        for (int i = 0; i < items.size(); i++) {
            Inventory current = items.get(i);
            if (current.isEmpty() || !current.canStack()) continue;
            
            for (int j = i + 1; j < items.size(); j++) {
                Inventory other = items.get(j);
                if (other.isEmpty() || !current.getItemId().equals(other.getItemId())) continue;
                
                if (current.canAddQuantity(other.getQuantity())) {
                    current.addQuantity(other.getQuantity());
                    other.clear();
                    inventoryRepository.save(current);
                    inventoryRepository.save(other);
                }
            }
        }
    }

    /**
     * 执行物品使用效果
     */
    private void executeItemEffect(PlayerRole role, Item item, Inventory inventory) {
        if (!item.hasEffect()) return;

        switch (item.getEffectType()) {
            case 1: // 恢复HP
                role.restoreHp(item.getEffectValue());
                System.out.println("恢复生命值: " + item.getEffectValue());
                break;
            case 2: // 恢复MP
                role.restoreMp(item.getEffectValue());
                System.out.println("恢复法力值: " + item.getEffectValue());
                break;
            case 3: // 增加经验
                playerRoleService.addExperience(role.getId(), item.getEffectValue().longValue());
                System.out.println("增加经验值: " + item.getEffectValue());
                break;
            case 4: // 学习技能
                System.out.println("学习技能效果: " + item.getEffectValue());
                // TODO: 实现技能学习逻辑
                break;
            case 5: // 其他效果
                System.out.println("特殊效果: " + item.getEffectValue());
                break;
        }
    }
}
