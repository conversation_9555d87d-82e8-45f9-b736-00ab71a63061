package com.honghuang.game.service;

import com.honghuang.game.entity.Equipment;
import com.honghuang.game.entity.PlayerEquipment;
import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.EquipmentRepository;
import com.honghuang.game.repository.PlayerEquipmentRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 装备服务类
 */
@Slf4j
@Service
public class EquipmentService {

    @Autowired
    private PlayerEquipmentRepository playerEquipmentRepository;
    
    @Autowired
    private EquipmentRepository equipmentRepository;
    
    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 获取角色当前装备
     */
    public Map<String, PlayerEquipment> getCurrentEquipment(Integer roleId) {
        List<PlayerEquipment> equippedItems = playerEquipmentRepository.findByRoleIdAndPositionGreaterThan(roleId, 0);
        
        Map<String, PlayerEquipment> equipmentMap = new HashMap<>();
        for (PlayerEquipment equipment : equippedItems) {
            equipmentMap.put(equipment.getPositionName(), equipment);
        }
        
        return equipmentMap;
    }

    /**
     * 获取背包中的装备
     */
    public List<PlayerEquipment> getBagEquipment(Integer roleId) {
        return playerEquipmentRepository.findByRoleIdAndPosition(roleId, 0);
    }

    /**
     * 装备物品
     */
    @Transactional
    public boolean equipItem(PlayerRole role, Integer equipmentId) {
        // 查找玩家装备
        PlayerEquipment playerEquipment = playerEquipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new BusinessException("装备不存在"));
        
        // 验证所有权
        if (!playerEquipment.getRoleId().equals(role.getId())) {
            throw new BusinessException("无权操作此装备");
        }
        
        // 检查是否在背包中
        if (!playerEquipment.isInBag()) {
            throw new BusinessException("装备不在背包中");
        }
        
        // 检查装备条件
        if (!playerEquipment.canEquip(role)) {
            throw new BusinessException("不满足装备条件");
        }
        
        Equipment equipment = playerEquipment.getEquipment();
        Integer targetPosition = equipment.getType(); // 装备类型对应装备位置
        
        // 检查该位置是否已有装备
        Optional<PlayerEquipment> existingEquipment = playerEquipmentRepository
                .findByRoleIdAndPosition(role.getId(), targetPosition)
                .stream().findFirst();
        
        if (existingEquipment.isPresent()) {
            // 卸下原有装备
            unequipToFirstAvailableSlot(role, existingEquipment.get());
        }
        
        // 装备新物品
        playerEquipment.equipTo(targetPosition);
        playerEquipmentRepository.save(playerEquipment);
        
        // 更新角色属性
        updateRoleStats(role);
        
        log.info("角色 {} 装备了 {}", role.getName(), equipment.getName());
        return true;
    }

    /**
     * 卸下装备
     */
    @Transactional
    public boolean unequipItem(PlayerRole role, Integer position) {
        // 查找指定位置的装备
        Optional<PlayerEquipment> equipmentOpt = playerEquipmentRepository
                .findByRoleIdAndPosition(role.getId(), position)
                .stream().findFirst();
        
        if (!equipmentOpt.isPresent()) {
            throw new BusinessException("该位置没有装备");
        }
        
        PlayerEquipment equipment = equipmentOpt.get();
        
        // 卸下到背包
        unequipToFirstAvailableSlot(role, equipment);
        
        // 更新角色属性
        updateRoleStats(role);
        
        log.info("角色 {} 卸下了 {}", role.getName(), equipment.getEquipment().getName());
        return true;
    }

    /**
     * 卸下装备到第一个可用背包位置
     */
    private void unequipToFirstAvailableSlot(PlayerRole role, PlayerEquipment equipment) {
        // 查找第一个可用的背包位置
        Integer availableSlot = findFirstAvailableBagSlot(role.getId());
        if (availableSlot == null) {
            throw new BusinessException("背包空间不足");
        }
        
        equipment.unequipToBag(availableSlot);
        playerEquipmentRepository.save(equipment);
    }

    /**
     * 查找第一个可用的背包位置
     */
    private Integer findFirstAvailableBagSlot(Integer roleId) {
        List<PlayerEquipment> bagItems = playerEquipmentRepository.findByRoleIdAndPosition(roleId, 0);
        Set<Integer> usedSlots = new HashSet<>();
        
        for (PlayerEquipment item : bagItems) {
            if (item.getBagIndex() != null) {
                usedSlots.add(item.getBagIndex());
            }
        }
        
        // 背包最大容量假设为50个位置
        for (int i = 1; i <= 50; i++) {
            if (!usedSlots.contains(i)) {
                return i;
            }
        }
        
        return null; // 背包已满
    }

    /**
     * 强化装备
     */
    @Transactional
    public Map<String, Object> enhanceEquipment(PlayerRole role, Integer equipmentId) {
        PlayerEquipment playerEquipment = playerEquipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new BusinessException("装备不存在"));
        
        // 验证所有权
        if (!playerEquipment.getRoleId().equals(role.getId())) {
            throw new BusinessException("无权操作此装备");
        }
        
        // 计算强化费用
        long cost = playerEquipment.getEnhanceCost();
        if (role.getCopper() < cost) {
            throw new BusinessException("铜钱不足，需要 " + cost + " 铜钱");
        }
        
        // 扣除费用
        role.setCopper(role.getCopper() - cost);
        playerRoleService.save(role);
        
        // 尝试强化
        boolean success = playerEquipment.enhance();
        playerEquipmentRepository.save(playerEquipment);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("newLevel", playerEquipment.getEnhanceLevel());
        result.put("cost", cost);
        
        if (success) {
            // 更新角色属性
            updateRoleStats(role);
            log.info("角色 {} 强化 {} 成功，等级: {}", role.getName(), 
                    playerEquipment.getEquipment().getName(), playerEquipment.getEnhanceLevel());
        } else {
            log.info("角色 {} 强化 {} 失败", role.getName(), playerEquipment.getEquipment().getName());
        }
        
        return result;
    }

    /**
     * 修理装备
     */
    @Transactional
    public long repairEquipment(PlayerRole role, Integer equipmentId) {
        PlayerEquipment playerEquipment = playerEquipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new BusinessException("装备不存在"));
        
        // 验证所有权
        if (!playerEquipment.getRoleId().equals(role.getId())) {
            throw new BusinessException("无权操作此装备");
        }
        
        if (!playerEquipment.isDamaged()) {
            throw new BusinessException("装备无需修理");
        }
        
        // 计算修理费用（基于装备价值和损坏程度）
        long cost = calculateRepairCost(playerEquipment);
        if (role.getCopper() < cost) {
            throw new BusinessException("铜钱不足，需要 " + cost + " 铜钱");
        }
        
        // 扣除费用并修理
        role.setCopper(role.getCopper() - cost);
        playerRoleService.save(role);
        
        playerEquipment.repair();
        playerEquipmentRepository.save(playerEquipment);
        
        log.info("角色 {} 修理了 {}，花费 {} 铜钱", role.getName(), 
                playerEquipment.getEquipment().getName(), cost);
        
        return cost;
    }

    /**
     * 批量修理所有装备
     */
    @Transactional
    public long repairAllEquipment(PlayerRole role) {
        List<PlayerEquipment> allEquipment = playerEquipmentRepository.findByRoleId(role.getId());
        long totalCost = 0;
        
        for (PlayerEquipment equipment : allEquipment) {
            if (equipment.isDamaged()) {
                long cost = calculateRepairCost(equipment);
                totalCost += cost;
            }
        }
        
        if (role.getCopper() < totalCost) {
            throw new BusinessException("铜钱不足，需要 " + totalCost + " 铜钱");
        }
        
        // 扣除费用
        role.setCopper(role.getCopper() - totalCost);
        playerRoleService.save(role);
        
        // 修理所有装备
        for (PlayerEquipment equipment : allEquipment) {
            if (equipment.isDamaged()) {
                equipment.repair();
                playerEquipmentRepository.save(equipment);
            }
        }
        
        log.info("角色 {} 修理了所有装备，总花费 {} 铜钱", role.getName(), totalCost);
        return totalCost;
    }

    /**
     * 计算修理费用
     */
    private long calculateRepairCost(PlayerEquipment equipment) {
        Equipment equipmentTemplate = equipment.getEquipment();
        double damageRatio = 1.0 - (double) equipment.getCurrentDurability() / equipment.getMaxDurability();
        return (long) (equipmentTemplate.getBuyPrice() * 0.1 * damageRatio);
    }

    /**
     * 获取装备详情
     */
    public PlayerEquipment getEquipmentDetail(Integer roleId, Integer equipmentId) {
        PlayerEquipment equipment = playerEquipmentRepository.findById(equipmentId).orElse(null);
        if (equipment != null && equipment.getRoleId().equals(roleId)) {
            return equipment;
        }
        return null;
    }

    /**
     * 计算角色总属性（包含装备加成）
     */
    public Map<String, Object> calculateTotalStats(Integer roleId) {
        PlayerRole role = playerRoleService.findById(roleId);
        List<PlayerEquipment> equippedItems = playerEquipmentRepository.findByRoleIdAndPositionGreaterThan(roleId, 0);
        
        Map<String, Object> stats = new HashMap<>();
        
        // 基础属性
        int totalAttack = role.getBaseAttack();
        int totalDefense = role.getBaseDefense();
        int totalAgility = role.getBaseAgility();
        int totalIntelligence = role.getBaseIntelligence();
        int totalConstitution = role.getBaseConstitution();
        int totalHp = role.getMaxHp();
        int totalMp = role.getMaxMp();
        
        // 装备加成
        for (PlayerEquipment equipment : equippedItems) {
            totalAttack += equipment.getEnhancedAttackBonus();
            totalDefense += equipment.getEnhancedDefenseBonus();
            totalAgility += equipment.getEnhancedAgilityBonus();
            totalIntelligence += equipment.getEnhancedIntelligenceBonus();
            totalConstitution += equipment.getEnhancedConstitutionBonus();
            totalHp += equipment.getEnhancedHpBonus();
            totalMp += equipment.getEnhancedMpBonus();
        }
        
        stats.put("attack", totalAttack);
        stats.put("defense", totalDefense);
        stats.put("agility", totalAgility);
        stats.put("intelligence", totalIntelligence);
        stats.put("constitution", totalConstitution);
        stats.put("hp", totalHp);
        stats.put("mp", totalMp);
        stats.put("equipmentCount", equippedItems.size());
        
        return stats;
    }

    /**
     * 获取强化预览
     */
    public Map<String, Object> getEnhancePreview(Integer roleId, Integer equipmentId) {
        PlayerEquipment equipment = playerEquipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new BusinessException("装备不存在"));
        
        if (!equipment.getRoleId().equals(roleId)) {
            throw new BusinessException("无权操作此装备");
        }
        
        Map<String, Object> preview = new HashMap<>();
        preview.put("currentLevel", equipment.getEnhanceLevel());
        preview.put("nextLevel", equipment.getEnhanceLevel() + 1);
        preview.put("successRate", equipment.getEnhanceSuccessRate());
        preview.put("cost", equipment.getEnhanceCost());
        
        // 当前属性
        Map<String, Integer> currentStats = new HashMap<>();
        currentStats.put("attack", equipment.getEnhancedAttackBonus());
        currentStats.put("defense", equipment.getEnhancedDefenseBonus());
        currentStats.put("agility", equipment.getEnhancedAgilityBonus());
        currentStats.put("intelligence", equipment.getEnhancedIntelligenceBonus());
        currentStats.put("constitution", equipment.getEnhancedConstitutionBonus());
        currentStats.put("hp", equipment.getEnhancedHpBonus());
        currentStats.put("mp", equipment.getEnhancedMpBonus());
        
        // 下一级属性
        Map<String, Integer> nextStats = new HashMap<>();
        Equipment template = equipment.getEquipment();
        int nextLevel = equipment.getEnhanceLevel() + 1;
        nextStats.put("attack", template.getAttackBonus() + nextLevel * 2);
        nextStats.put("defense", template.getDefenseBonus() + nextLevel * 2);
        nextStats.put("agility", template.getAgilityBonus() + nextLevel);
        nextStats.put("intelligence", template.getIntelligenceBonus() + nextLevel);
        nextStats.put("constitution", template.getConstitutionBonus() + nextLevel);
        nextStats.put("hp", template.getHpBonus() + nextLevel * 10);
        nextStats.put("mp", template.getMpBonus() + nextLevel * 5);
        
        preview.put("currentStats", currentStats);
        preview.put("nextStats", nextStats);
        
        return preview;
    }

    /**
     * 更新角色属性（重新计算装备加成）
     */
    private void updateRoleStats(PlayerRole role) {
        Map<String, Object> totalStats = calculateTotalStats(role.getId());
        
        // 更新角色的最大生命值和魔法值
        role.setMaxHp((Integer) totalStats.get("hp"));
        role.setMaxMp((Integer) totalStats.get("mp"));
        
        // 如果当前生命值/魔法值超过最大值，则调整
        if (role.getCurrentHp() > role.getMaxHp()) {
            role.setCurrentHp(role.getMaxHp());
        }
        if (role.getCurrentMp() > role.getMaxMp()) {
            role.setCurrentMp(role.getMaxMp());
        }
        
        playerRoleService.save(role);
    }
}
