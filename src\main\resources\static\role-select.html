<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洪荒Online - 角色选择</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 800px;
            width: 90%;
        }

        .logo {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        .role-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .role-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .role-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .role-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            background: rgba(255, 255, 255, 0.2);
        }

        .role-name {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .role-info {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .create-role-card {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px dashed #4caf50;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }

        .create-role-card:hover {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
        }

        .create-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }

        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* 创建角色表单 */
        .create-form {
            display: none;
            text-align: left;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .race-options,
        .sex-options {
            display: flex;
            gap: 10px;
        }

        .race-option,
        .sex-option {
            flex: 1;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .race-option.selected,
        .sex-option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .form-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">洪荒Online</div>
        <div class="subtitle">选择你的角色，开始修仙之旅</div>

        <div class="message" id="message"></div>

        <div class="role-list" id="roleList">
            <!-- 角色列表将在这里动态生成 -->
        </div>

        <div class="buttons">
            <button class="btn btn-primary" id="enterGameBtn" disabled onclick="enterGame()">进入游戏</button>
            <button class="btn btn-secondary" onclick="showCreateForm()">创建角色</button>
            <a href="/honghuang-game/" class="btn" style="background: #6c757d; color: white;">返回登录</a>
        </div>

        <!-- 创建角色表单 -->
        <div class="create-form" id="createForm">
            <h3 style="text-align: center; margin-bottom: 20px;">创建新角色</h3>
            
            <div class="form-group">
                <label for="roleName">角色名称</label>
                <input type="text" id="roleName" placeholder="请输入角色名称" maxlength="20">
            </div>

            <div class="form-group">
                <label>选择种族</label>
                <div class="race-options">
                    <div class="race-option" data-race="1" onclick="selectRace(1)">
                        <div style="font-size: 2em;">⚔️</div>
                        <div>巫族</div>
                        <div style="font-size: 0.8em; opacity: 0.7;">力量型，高攻击高血量</div>
                    </div>
                    <div class="race-option" data-race="2" onclick="selectRace(2)">
                        <div style="font-size: 2em;">🔮</div>
                        <div>妖族</div>
                        <div style="font-size: 0.8em; opacity: 0.7;">敏捷型，高法力高敏捷</div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>选择性别</label>
                <div class="sex-options">
                    <div class="sex-option" data-sex="1" onclick="selectSex(1)">
                        <div style="font-size: 2em;">👨</div>
                        <div>男</div>
                    </div>
                    <div class="sex-option" data-sex="2" onclick="selectSex(2)">
                        <div style="font-size: 2em;">👩</div>
                        <div>女</div>
                    </div>
                </div>
            </div>

            <div class="form-buttons">
                <button class="btn btn-primary" onclick="createRole()">创建角色</button>
                <button class="btn" style="background: #6c757d; color: white;" onclick="hideCreateForm()">取消</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/honghuang-game/api';
        let selectedRoleId = null;
        let selectedRace = 1;
        let selectedSex = 1;

        // 页面加载时检查登录状态并加载角色列表
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 检查登录状态
        function checkLoginStatus() {
            fetch(`${API_BASE}/user/current`, {
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        loadRoles();
                    } else {
                        window.location.href = '/honghuang-game/';
                    }
                })
                .catch(error => {
                    console.error('检查登录状态失败:', error);
                    window.location.href = '/honghuang-game/';
                });
        }

        // 加载角色列表
        function loadRoles() {
            console.log('开始加载角色列表...');
            fetch(`${API_BASE}/role/list`, {
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    console.log('角色列表API响应:', data);
                    if (data.code === 200) {
                        console.log('角色数据:', data.data);
                        displayRoles(data.data || []);
                    } else {
                        console.log('API返回错误:', data.message);
                        displayRoles([]);
                    }
                })
                .catch(error => {
                    console.error('加载角色列表失败:', error);
                    displayRoles([]);
                });
        }

        // 显示角色列表
        function displayRoles(roles) {
            console.log('显示角色列表，角色数量:', roles.length);
            const roleList = document.getElementById('roleList');
            roleList.innerHTML = '';

            // 显示现有角色
            roles.forEach(role => {
                console.log('创建角色卡片:', role.name);
                const roleCard = createRoleCard(role);
                roleList.appendChild(roleCard);
            });

            // 如果角色数量少于3个，显示创建角色卡片
            if (roles.length < 3) {
                console.log('添加创建角色卡片');
                const createCard = createCreateRoleCard();
                roleList.appendChild(createCard);
            }

            // 如果有角色，默认选择第一个
            if (roles.length > 0) {
                console.log('默认选择第一个角色:', roles[0].name);
                selectRole(roles[0].id);
            }
        }

        // 创建角色卡片
        function createRoleCard(role) {
            const card = document.createElement('div');
            card.className = 'role-card';
            card.setAttribute('data-role-id', role.id);
            card.onclick = () => selectRole(role.id, card);

            const raceIcon = role.race === 1 ? '⚔️' : '🔮';
            const raceName = role.race === 1 ? '巫族' : '妖族';
            const sexName = role.sex === 1 ? '男' : '女';

            card.innerHTML = `
                <div class="role-avatar">${raceIcon}</div>
                <div class="role-name">${role.name}</div>
                <div class="role-info">
                    ${raceName} ${sexName} | 等级 ${role.level}<br>
                    场景: ${role.sceneName}
                </div>
            `;

            return card;
        }

        // 创建"创建角色"卡片
        function createCreateRoleCard() {
            const card = document.createElement('div');
            card.className = 'role-card create-role-card';
            card.onclick = showCreateForm;
            
            card.innerHTML = `
                <div class="create-icon">➕</div>
                <div>创建新角色</div>
            `;
            
            return card;
        }

        // 选择角色
        function selectRole(roleId, cardElement) {
            selectedRoleId = roleId;

            // 更新UI
            document.querySelectorAll('.role-card').forEach(card => {
                card.classList.remove('selected');
            });

            if (cardElement) {
                cardElement.classList.add('selected');
            } else {
                // 如果没有传入元素，通过roleId查找
                const targetCard = document.querySelector(`[data-role-id="${roleId}"]`);
                if (targetCard) {
                    targetCard.classList.add('selected');
                }
            }
            document.getElementById('enterGameBtn').disabled = false;
        }

        // 进入游戏
        function enterGame() {
            if (!selectedRoleId) {
                showMessage('请选择一个角色', 'error');
                return;
            }

            // 选择角色并进入游戏
            fetch(`${API_BASE}/role/select/${selectedRoleId}`, {
                method: 'POST',
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        window.location.href = '/honghuang-game/game.html';
                    } else {
                        showMessage(data.message || '进入游戏失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('进入游戏失败:', error);
                    showMessage('网络错误，请稍后重试', 'error');
                });
        }

        // 显示创建角色表单
        function showCreateForm() {
            document.getElementById('createForm').style.display = 'block';
            // 默认选择巫族男性
            selectRace(1);
            selectSex(1);
        }

        // 隐藏创建角色表单
        function hideCreateForm() {
            document.getElementById('createForm').style.display = 'none';
            document.getElementById('roleName').value = '';
        }

        // 选择种族
        function selectRace(race) {
            selectedRace = race;
            document.querySelectorAll('.race-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-race="${race}"]`).classList.add('selected');
        }

        // 选择性别
        function selectSex(sex) {
            selectedSex = sex;
            document.querySelectorAll('.sex-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-sex="${sex}"]`).classList.add('selected');
        }

        // 创建角色
        function createRole() {
            const roleName = document.getElementById('roleName').value.trim();
            
            if (!roleName) {
                showMessage('请输入角色名称', 'error');
                return;
            }

            const roleData = {
                roleName: roleName,
                race: selectedRace,
                sex: selectedSex
            };

            fetch(`${API_BASE}/role/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(roleData),
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        showMessage('角色创建成功！', 'success');
                        hideCreateForm();
                        loadRoles(); // 重新加载角色列表
                    } else {
                        showMessage(data.message || '创建角色失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('创建角色失败:', error);
                    showMessage('网络错误，请稍后重试', 'error');
                });
        }

        // 显示消息
        function showMessage(text, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
