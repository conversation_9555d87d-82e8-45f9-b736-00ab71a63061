package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 玩家任务实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "player_quest")
public class PlayerQuest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 玩家角色ID
     */
    @Column(name = "role_id")
    private Integer roleId;

    /**
     * 任务ID
     */
    @Column(name = "quest_id")
    private Integer questId;

    /**
     * 任务状态 1进行中 2已完成 3已放弃
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 当前进度
     */
    @Column(name = "progress")
    private Integer progress = 0;

    /**
     * 接取时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "accept_time")
    private LocalDateTime acceptTime;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "complete_time")
    private LocalDateTime completeTime;

    /**
     * 放弃时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "abandon_time")
    private LocalDateTime abandonTime;

    /**
     * 上次完成时间（用于可重复任务）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_complete_time")
    private LocalDateTime lastCompleteTime;

    /**
     * 任务信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "quest_id", insertable = false, updatable = false)
    private Quest quest;

    // 业务方法

    /**
     * 是否进行中
     */
    public boolean isInProgress() {
        return status == 1;
    }

    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return status == 2;
    }

    /**
     * 是否已放弃
     */
    public boolean isAbandoned() {
        return status == 3;
    }

    /**
     * 是否可以完成
     */
    public boolean canComplete() {
        if (quest == null) return false;
        return isInProgress() && progress >= quest.getObjectiveCount();
    }

    /**
     * 获取进度百分比
     */
    public double getProgressPercentage() {
        if (quest == null || quest.getObjectiveCount() == 0) {
            return 0.0;
        }
        return Math.min(100.0, (double) progress / quest.getObjectiveCount() * 100.0);
    }

    /**
     * 增加进度
     */
    public void addProgress(Integer amount) {
        if (amount > 0) {
            this.progress += amount;
            if (quest != null) {
                this.progress = Math.min(this.progress, quest.getObjectiveCount());
            }
        }
    }

    /**
     * 完成任务
     */
    public void complete() {
        this.status = 2;
        this.completeTime = LocalDateTime.now();
        this.lastCompleteTime = LocalDateTime.now();
    }

    /**
     * 放弃任务
     */
    public void abandon() {
        this.status = 3;
        this.abandonTime = LocalDateTime.now();
    }

    /**
     * 重置任务（用于可重复任务）
     */
    public void reset() {
        this.status = 1;
        this.progress = 0;
        this.acceptTime = LocalDateTime.now();
        this.completeTime = null;
        this.abandonTime = null;
    }

    /**
     * 检查是否可以重新接取（可重复任务）
     */
    public boolean canReaccept() {
        if (quest == null || quest.getRepeatable() == 0) {
            return false;
        }
        
        if (lastCompleteTime == null) {
            return true;
        }
        
        LocalDateTime nextAvailableTime = lastCompleteTime.plusHours(quest.getRepeatInterval());
        return LocalDateTime.now().isAfter(nextAvailableTime);
    }

    /**
     * 获取下次可接取时间
     */
    public LocalDateTime getNextAvailableTime() {
        if (quest == null || quest.getRepeatable() == 0 || lastCompleteTime == null) {
            return null;
        }
        return lastCompleteTime.plusHours(quest.getRepeatInterval());
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        switch (status) {
            case 1: return "进行中";
            case 2: return "已完成";
            case 3: return "已放弃";
            default: return "未知";
        }
    }

    /**
     * 获取进度描述
     */
    public String getProgressDescription() {
        if (quest == null) {
            return "0/0";
        }
        return progress + "/" + quest.getObjectiveCount();
    }

    /**
     * 获取任务完整名称（包含状态）
     */
    public String getFullName() {
        if (quest == null) return "未知任务";
        return quest.getName() + " [" + getStatusName() + "]";
    }

    /**
     * 检查目标是否匹配
     */
    public boolean isObjectiveMatch(Integer objectiveType, Integer objectiveId) {
        if (quest == null) return false;
        return quest.getObjectiveType().equals(objectiveType) && 
               quest.getObjectiveId().equals(objectiveId);
    }

    /**
     * 检查是否为杀怪任务且怪物匹配
     */
    public boolean isKillQuestMatch(Integer monsterId) {
        return quest != null && quest.isKillQuest() && 
               quest.getObjectiveId().equals(monsterId);
    }

    /**
     * 检查是否为收集任务且物品匹配
     */
    public boolean isCollectQuestMatch(Integer itemId) {
        return quest != null && quest.isCollectQuest() && 
               quest.getObjectiveId().equals(itemId);
    }

    /**
     * 检查是否为对话任务且NPC匹配
     */
    public boolean isTalkQuestMatch(Integer npcId) {
        return quest != null && quest.isTalkQuest() && 
               quest.getObjectiveId().equals(npcId);
    }

    /**
     * 检查是否为探索任务且场景匹配
     */
    public boolean isExploreQuestMatch(Integer sceneId) {
        return quest != null && quest.isExploreQuest() && 
               quest.getObjectiveSceneId().equals(sceneId);
    }

    /**
     * 获取剩余需要完成的数量
     */
    public Integer getRemainingCount() {
        if (quest == null) return 0;
        return Math.max(0, quest.getObjectiveCount() - progress);
    }

    /**
     * 是否已达到目标数量
     */
    public boolean isObjectiveReached() {
        if (quest == null) return false;
        return progress >= quest.getObjectiveCount();
    }

    /**
     * 计算任务完成用时（分钟）
     */
    public Long getCompletionTimeMinutes() {
        if (acceptTime == null || completeTime == null) {
            return null;
        }
        return java.time.Duration.between(acceptTime, completeTime).toMinutes();
    }

    /**
     * 获取任务接取后的经过时间（分钟）
     */
    public Long getElapsedTimeMinutes() {
        if (acceptTime == null) {
            return null;
        }
        LocalDateTime endTime = completeTime != null ? completeTime : LocalDateTime.now();
        return java.time.Duration.between(acceptTime, endTime).toMinutes();
    }
}
