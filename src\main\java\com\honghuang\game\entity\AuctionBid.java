package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 拍卖出价记录实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "auction_bid")
public class AuctionBid {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 拍卖物品ID
     */
    @Column(name = "auction_item_id")
    private Integer auctionItemId;

    /**
     * 出价者角色ID
     */
    @Column(name = "bidder_id")
    private Integer bidderId;

    /**
     * 出价者角色名称
     */
    @Column(name = "bidder_name", length = 50)
    private String bidderName;

    /**
     * 出价金额
     */
    @Column(name = "bid_price")
    private Long bidPrice;

    /**
     * 出价类型 1普通出价 2一口价
     */
    @Column(name = "bid_type")
    private Integer bidType = 1;

    /**
     * 是否为当前最高价 0否 1是
     */
    @Column(name = "is_highest")
    private Integer isHighest = 1;

    /**
     * 出价时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "bid_time")
    private LocalDateTime bidTime;

    /**
     * 拍卖物品信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "auction_item_id", insertable = false, updatable = false)
    private AuctionItem auctionItem;

    /**
     * 出价者信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bidder_id", insertable = false, updatable = false)
    private PlayerRole bidder;

    // 业务方法

    /**
     * 获取出价类型名称
     */
    public String getBidTypeName() {
        switch (bidType) {
            case 1: return "普通出价";
            case 2: return "一口价";
            default: return "未知";
        }
    }

    /**
     * 是否为普通出价
     */
    public boolean isNormalBid() {
        return bidType == 1;
    }

    /**
     * 是否为一口价
     */
    public boolean isBuyoutBid() {
        return bidType == 2;
    }

    /**
     * 是否为当前最高价
     */
    public boolean isCurrentHighest() {
        return isHighest == 1;
    }

    /**
     * 设置为最高价
     */
    public void setAsHighest() {
        this.isHighest = 1;
    }

    /**
     * 取消最高价状态
     */
    public void removeHighestStatus() {
        this.isHighest = 0;
    }

    /**
     * 获取出价经过时间（分钟）
     */
    public Long getElapsedMinutes() {
        if (bidTime == null) {
            return null;
        }
        return java.time.Duration.between(bidTime, LocalDateTime.now()).toMinutes();
    }

    /**
     * 获取格式化的出价时间
     */
    public String getFormattedBidTime() {
        if (bidTime == null) return "";
        
        LocalDateTime now = LocalDateTime.now();
        long minutes = java.time.Duration.between(bidTime, now).toMinutes();
        
        if (minutes < 1) {
            return "刚刚";
        } else if (minutes < 60) {
            return minutes + "分钟前";
        } else if (minutes < 1440) { // 24小时
            return (minutes / 60) + "小时前";
        } else {
            return bidTime.toLocalDate().toString();
        }
    }

    /**
     * 获取出价完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        
        sb.append(bidderName).append(" 出价 ").append(bidPrice);
        
        if (auctionItem != null) {
            sb.append(auctionItem.getCurrencyTypeName());
        }
        
        sb.append(" (").append(getBidTypeName()).append(")");
        
        if (isCurrentHighest()) {
            sb.append(" [当前最高]");
        }
        
        sb.append(" - ").append(getFormattedBidTime());
        
        return sb.toString();
    }

    /**
     * 检查是否为最近的出价
     */
    public boolean isRecentBid() {
        return getElapsedMinutes() != null && getElapsedMinutes() <= 30; // 30分钟内算最近
    }

    /**
     * 比较出价高低
     */
    public int compareTo(AuctionBid other) {
        if (other == null) return 1;
        
        // 先比较价格
        int priceCompare = this.bidPrice.compareTo(other.bidPrice);
        if (priceCompare != 0) {
            return priceCompare;
        }
        
        // 价格相同时，比较时间（早出价的优先）
        if (this.bidTime != null && other.bidTime != null) {
            return this.bidTime.compareTo(other.bidTime);
        }
        
        return 0;
    }

    /**
     * 创建普通出价记录
     */
    public static AuctionBid createNormalBid(Integer auctionItemId, Integer bidderId, String bidderName, Long bidPrice) {
        AuctionBid bid = new AuctionBid();
        bid.setAuctionItemId(auctionItemId);
        bid.setBidderId(bidderId);
        bid.setBidderName(bidderName);
        bid.setBidPrice(bidPrice);
        bid.setBidType(1);
        bid.setIsHighest(1);
        return bid;
    }

    /**
     * 创建一口价出价记录
     */
    public static AuctionBid createBuyoutBid(Integer auctionItemId, Integer bidderId, String bidderName, Long bidPrice) {
        AuctionBid bid = new AuctionBid();
        bid.setAuctionItemId(auctionItemId);
        bid.setBidderId(bidderId);
        bid.setBidderName(bidderName);
        bid.setBidPrice(bidPrice);
        bid.setBidType(2);
        bid.setIsHighest(1);
        return bid;
    }
}
