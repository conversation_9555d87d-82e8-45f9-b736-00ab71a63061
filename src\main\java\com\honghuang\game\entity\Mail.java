package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 邮件实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "mail")
public class Mail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 发送者角色ID
     */
    @Column(name = "sender_id")
    private Integer senderId;

    /**
     * 发送者角色名称
     */
    @Column(name = "sender_name", length = 50)
    private String senderName;

    /**
     * 接收者角色ID
     */
    @Column(name = "receiver_id")
    private Integer receiverId;

    /**
     * 接收者角色名称
     */
    @Column(name = "receiver_name", length = 50)
    private String receiverName;

    /**
     * 邮件标题
     */
    @Column(name = "title", length = 100)
    private String title;

    /**
     * 邮件内容
     */
    @Column(name = "content", length = 2000)
    private String content;

    /**
     * 邮件类型 1普通邮件 2系统邮件 3奖励邮件 4拍卖邮件
     */
    @Column(name = "mail_type")
    private Integer mailType = 1;

    /**
     * 是否已读 0未读 1已读
     */
    @Column(name = "is_read")
    private Integer isRead = 0;

    /**
     * 是否有附件 0无 1有
     */
    @Column(name = "has_attachment")
    private Integer hasAttachment = 0;

    /**
     * 是否已领取附件 0未领取 1已领取
     */
    @Column(name = "attachment_claimed")
    private Integer attachmentClaimed = 0;

    /**
     * 附件金币
     */
    @Column(name = "attachment_gold")
    private Long attachmentGold = 0L;

    /**
     * 附件银币
     */
    @Column(name = "attachment_silver")
    private Long attachmentSilver = 0L;

    /**
     * 附件铜币
     */
    @Column(name = "attachment_copper")
    private Long attachmentCopper = 0L;

    /**
     * 邮件状态 1正常 2发送者删除 3接收者删除 4系统删除
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 发送时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "send_time")
    private LocalDateTime sendTime;

    /**
     * 阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "read_time")
    private LocalDateTime readTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    /**
     * 发送者信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sender_id", insertable = false, updatable = false)
    private PlayerRole sender;

    /**
     * 接收者信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "receiver_id", insertable = false, updatable = false)
    private PlayerRole receiver;

    // 业务方法

    /**
     * 获取邮件类型名称
     */
    public String getMailTypeName() {
        switch (mailType) {
            case 1: return "普通邮件";
            case 2: return "系统邮件";
            case 3: return "奖励邮件";
            case 4: return "拍卖邮件";
            default: return "未知";
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        switch (status) {
            case 1: return "正常";
            case 2: return "发送者删除";
            case 3: return "接收者删除";
            case 4: return "系统删除";
            default: return "未知";
        }
    }

    /**
     * 是否为普通邮件
     */
    public boolean isNormalMail() {
        return mailType == 1;
    }

    /**
     * 是否为系统邮件
     */
    public boolean isSystemMail() {
        return mailType == 2;
    }

    /**
     * 是否为奖励邮件
     */
    public boolean isRewardMail() {
        return mailType == 3;
    }

    /**
     * 是否为拍卖邮件
     */
    public boolean isAuctionMail() {
        return mailType == 4;
    }

    /**
     * 是否已读
     */
    public boolean isRead() {
        return isRead == 1;
    }

    /**
     * 是否有附件
     */
    public boolean hasAttachment() {
        return hasAttachment == 1;
    }

    /**
     * 是否已领取附件
     */
    public boolean isAttachmentClaimed() {
        return attachmentClaimed == 1;
    }

    /**
     * 是否正常状态
     */
    public boolean isNormal() {
        return status == 1;
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 标记为已读
     */
    public void markAsRead() {
        this.isRead = 1;
        this.readTime = LocalDateTime.now();
    }

    /**
     * 领取附件
     */
    public void claimAttachment() {
        if (!hasAttachment()) {
            throw new RuntimeException("邮件没有附件");
        }
        if (isAttachmentClaimed()) {
            throw new RuntimeException("附件已领取");
        }
        this.attachmentClaimed = 1;
    }

    /**
     * 设置附件金币
     */
    public void setAttachmentMoney(Long gold, Long silver, Long copper) {
        this.attachmentGold = gold != null ? gold : 0L;
        this.attachmentSilver = silver != null ? silver : 0L;
        this.attachmentCopper = copper != null ? copper : 0L;
        
        if (this.attachmentGold > 0 || this.attachmentSilver > 0 || this.attachmentCopper > 0) {
            this.hasAttachment = 1;
        }
    }

    /**
     * 获取附件金币总价值（转换为铜币）
     */
    public Long getTotalAttachmentValue() {
        return attachmentGold * 10000 + attachmentSilver * 100 + attachmentCopper;
    }

    /**
     * 发送者删除
     */
    public void deleteBySender() {
        if (status == 3) {
            // 接收者已删除，彻底删除
            this.status = 4;
        } else {
            this.status = 2;
        }
    }

    /**
     * 接收者删除
     */
    public void deleteByReceiver() {
        if (status == 2) {
            // 发送者已删除，彻底删除
            this.status = 4;
        } else {
            this.status = 3;
        }
    }

    /**
     * 系统删除
     */
    public void deleteBySystem() {
        this.status = 4;
    }

    /**
     * 获取邮件经过时间（小时）
     */
    public Long getElapsedHours() {
        if (sendTime == null) {
            return null;
        }
        return java.time.Duration.between(sendTime, LocalDateTime.now()).toHours();
    }

    /**
     * 获取剩余有效时间（小时）
     */
    public Long getRemainingHours() {
        if (expireTime == null) {
            return null;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(expireTime)) {
            return 0L;
        }
        
        return java.time.Duration.between(now, expireTime).toHours();
    }

    /**
     * 获取格式化的发送时间
     */
    public String getFormattedSendTime() {
        if (sendTime == null) return "";
        
        LocalDateTime now = LocalDateTime.now();
        long hours = java.time.Duration.between(sendTime, now).toHours();
        
        if (hours < 1) {
            return "刚刚";
        } else if (hours < 24) {
            return hours + "小时前";
        } else {
            long days = hours / 24;
            return days + "天前";
        }
    }

    /**
     * 获取邮件摘要
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        
        if (isSystemMail()) {
            sb.append("[系统] ");
        } else {
            sb.append("[").append(senderName).append("] ");
        }
        
        sb.append(title);
        
        if (hasAttachment() && !isAttachmentClaimed()) {
            sb.append(" [有附件]");
        }
        
        if (!isRead()) {
            sb.append(" [未读]");
        }
        
        return sb.toString();
    }

    /**
     * 获取附件描述
     */
    public String getAttachmentDescription() {
        if (!hasAttachment()) {
            return "无附件";
        }
        
        StringBuilder sb = new StringBuilder();
        boolean hasContent = false;
        
        if (attachmentGold > 0) {
            sb.append(attachmentGold).append("仙晶");
            hasContent = true;
        }
        
        if (attachmentSilver > 0) {
            if (hasContent) sb.append(" ");
            sb.append(attachmentSilver).append("灵石");
            hasContent = true;
        }
        
        if (attachmentCopper > 0) {
            if (hasContent) sb.append(" ");
            sb.append(attachmentCopper).append("铜钱");
            hasContent = true;
        }
        
        if (!hasContent) {
            return "物品附件";
        }
        
        return sb.toString();
    }

    /**
     * 检查是否可以删除
     */
    public boolean canDelete(Integer roleId) {
        if (!isNormal()) {
            return false;
        }
        
        // 有未领取的附件不能删除
        if (hasAttachment() && !isAttachmentClaimed() && roleId.equals(receiverId)) {
            return false;
        }
        
        return roleId.equals(senderId) || roleId.equals(receiverId);
    }

    /**
     * 设置过期时间（默认30天）
     */
    public void setDefaultExpireTime() {
        this.expireTime = LocalDateTime.now().plusDays(30);
    }
}
