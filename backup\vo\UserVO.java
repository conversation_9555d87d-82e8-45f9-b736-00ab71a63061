package com.honghuang.game.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@ApiModel(description = "用户信息")
public class UserVO {

    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "用户名", example = "testuser")
    private String username;

    @ApiModelProperty(value = "登录状态", example = "1", notes = "1-在线，0-离线")
    private Integer loginState;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "最后登录IP", example = "***********")
    private String lastLoginIp;

    @ApiModelProperty(value = "最后登录时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    @ApiModelProperty(value = "元宝数量", example = "1000")
    private Integer yuanbao;

    @ApiModelProperty(value = "积分数量", example = "500")
    private Integer jifen;

    @ApiModelProperty(value = "超级渠道", example = "official")
    private String superChannel;

    @ApiModelProperty(value = "渠道来源", example = "web")
    private String channel;

    @ApiModelProperty(value = "角色列表")
    private List<PlayerRoleVO> roles;
}
