package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 商店商品实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "shop_item")
public class ShopItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 商店ID
     */
    @Column(name = "shop_id")
    private Integer shopId;

    /**
     * 商品类型 1装备 2技能 3消耗品 4材料
     */
    @Column(name = "item_type")
    private Integer itemType;

    /**
     * 商品ID（对应装备ID、技能ID等）
     */
    @Column(name = "item_id")
    private Integer itemId;

    /**
     * 商品名称
     */
    @Column(name = "item_name", length = 100)
    private String itemName;

    /**
     * 商品描述
     */
    @Column(name = "item_description", length = 500)
    private String itemDescription;

    /**
     * 商品图标
     */
    @Column(name = "item_icon", length = 100)
    private String itemIcon;

    /**
     * 商品品质 1白色 2绿色 3蓝色 4紫色 5橙色 6红色
     */
    @Column(name = "item_quality")
    private Integer itemQuality = 1;

    /**
     * 售价（铜钱）
     */
    @Column(name = "price_copper")
    private Long priceCopper = 0L;

    /**
     * 售价（银两）
     */
    @Column(name = "price_silver")
    private Long priceSilver = 0L;

    /**
     * 售价（元宝）
     */
    @Column(name = "price_gold")
    private Long priceGold = 0L;

    /**
     * 库存数量 -1表示无限
     */
    @Column(name = "stock")
    private Integer stock = -1;

    /**
     * 已售数量
     */
    @Column(name = "sold_count")
    private Integer soldCount = 0;

    /**
     * 每次购买限制数量
     */
    @Column(name = "buy_limit")
    private Integer buyLimit = 1;

    /**
     * 等级要求
     */
    @Column(name = "required_level")
    private Integer requiredLevel = 1;

    /**
     * 种族要求 0无要求 1巫族 2妖族
     */
    @Column(name = "required_race")
    private Integer requiredRace = 0;

    /**
     * 性别要求 0无要求 1男性 2女性
     */
    @Column(name = "required_sex")
    private Integer requiredSex = 0;

    /**
     * 折扣百分比 100表示无折扣
     */
    @Column(name = "discount")
    private Integer discount = 100;

    /**
     * 是否限时商品 0否 1是
     */
    @Column(name = "is_limited_time")
    private Integer isLimitedTime = 0;

    /**
     * 限时开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "limited_start_time")
    private LocalDateTime limitedStartTime;

    /**
     * 限时结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "limited_end_time")
    private LocalDateTime limitedEndTime;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 商店信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shop_id", insertable = false, updatable = false)
    private Shop shop;

    // 业务方法

    /**
     * 获取商品类型名称
     */
    public String getItemTypeName() {
        switch (itemType) {
            case 1: return "装备";
            case 2: return "技能";
            case 3: return "消耗品";
            case 4: return "材料";
            default: return "未知";
        }
    }

    /**
     * 获取品质名称
     */
    public String getQualityName() {
        switch (itemQuality) {
            case 1: return "白色";
            case 2: return "绿色";
            case 3: return "蓝色";
            case 4: return "紫色";
            case 5: return "橙色";
            case 6: return "红色";
            default: return "未知";
        }
    }

    /**
     * 获取品质颜色代码
     */
    public String getQualityColor() {
        switch (itemQuality) {
            case 1: return "#FFFFFF"; // 白色
            case 2: return "#00FF00"; // 绿色
            case 3: return "#0080FF"; // 蓝色
            case 4: return "#8000FF"; // 紫色
            case 5: return "#FF8000"; // 橙色
            case 6: return "#FF0000"; // 红色
            default: return "#FFFFFF";
        }
    }

    /**
     * 检查角色是否可以购买此商品
     */
    public boolean canBuy(PlayerRole role) {
        // 检查等级要求
        if (role.getLevel() < requiredLevel) {
            return false;
        }
        
        // 检查种族要求
        if (requiredRace != 0 && !role.getRace().equals(requiredRace)) {
            return false;
        }
        
        // 检查性别要求
        if (requiredSex != 0 && !role.getSex().equals(requiredSex)) {
            return false;
        }
        
        // 检查库存
        if (stock != -1 && stock <= 0) {
            return false;
        }
        
        // 检查限时
        if (isLimitedTime == 1) {
            LocalDateTime now = LocalDateTime.now();
            if (limitedStartTime != null && now.isBefore(limitedStartTime)) {
                return false;
            }
            if (limitedEndTime != null && now.isAfter(limitedEndTime)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 计算折扣后的价格（铜钱）
     */
    public Long getDiscountedCopperPrice() {
        return priceCopper * discount / 100;
    }

    /**
     * 计算折扣后的价格（银两）
     */
    public Long getDiscountedSilverPrice() {
        return priceSilver * discount / 100;
    }

    /**
     * 计算折扣后的价格（元宝）
     */
    public Long getDiscountedGoldPrice() {
        return priceGold * discount / 100;
    }

    /**
     * 计算总价格（转换为铜钱）
     */
    public Long getTotalCopperPrice() {
        return getDiscountedCopperPrice() + getDiscountedSilverPrice() * 100 + getDiscountedGoldPrice() * 10000;
    }

    /**
     * 是否有库存
     */
    public boolean hasStock() {
        return stock == -1 || stock > 0;
    }

    /**
     * 是否无限库存
     */
    public boolean isUnlimitedStock() {
        return stock == -1;
    }

    /**
     * 是否有折扣
     */
    public boolean hasDiscount() {
        return discount < 100;
    }

    /**
     * 获取折扣百分比
     */
    public Integer getDiscountPercentage() {
        return 100 - discount;
    }

    /**
     * 是否在限时销售期间
     */
    public boolean isInLimitedTimePeriod() {
        if (isLimitedTime != 1) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        if (limitedStartTime != null && now.isBefore(limitedStartTime)) {
            return false;
        }
        
        if (limitedEndTime != null && now.isAfter(limitedEndTime)) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取限时剩余时间（分钟）
     */
    public Long getRemainingLimitedTimeMinutes() {
        if (isLimitedTime != 1 || limitedEndTime == null) {
            return null;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(limitedEndTime)) {
            return 0L;
        }
        
        return java.time.Duration.between(now, limitedEndTime).toMinutes();
    }

    /**
     * 购买商品（减少库存）
     */
    public boolean purchase(Integer quantity) {
        if (quantity <= 0) {
            return false;
        }
        
        if (quantity > buyLimit) {
            return false;
        }
        
        if (stock != -1) {
            if (stock < quantity) {
                return false;
            }
            stock -= quantity;
        }
        
        soldCount += quantity;
        return true;
    }

    /**
     * 获取商品完整名称（包含品质）
     */
    public String getFullName() {
        return "[" + getQualityName() + "] " + itemName;
    }

    /**
     * 获取价格描述
     */
    public String getPriceDescription() {
        StringBuilder sb = new StringBuilder();
        
        if (getDiscountedGoldPrice() > 0) {
            sb.append(getDiscountedGoldPrice()).append("元宝");
        }
        
        if (getDiscountedSilverPrice() > 0) {
            if (sb.length() > 0) sb.append(" + ");
            sb.append(getDiscountedSilverPrice()).append("银两");
        }
        
        if (getDiscountedCopperPrice() > 0) {
            if (sb.length() > 0) sb.append(" + ");
            sb.append(getDiscountedCopperPrice()).append("铜钱");
        }
        
        if (sb.length() == 0) {
            sb.append("免费");
        }
        
        return sb.toString();
    }
}
