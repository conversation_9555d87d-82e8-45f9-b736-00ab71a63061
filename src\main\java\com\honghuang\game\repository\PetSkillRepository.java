package com.honghuang.game.repository;

import com.honghuang.game.entity.PetSkill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 宠物技能数据访问接口
 */
@Repository
public interface PetSkillRepository extends JpaRepository<PetSkill, Integer> {
    
    /**
     * 根据技能类型查找
     */
    List<PetSkill> findByType(Integer type);
    
    /**
     * 根据技能等级查找
     */
    List<PetSkill> findByLevel(Integer level);
    
    /**
     * 根据稀有度查找
     */
    List<PetSkill> findByRarity(Integer rarity);
    
    /**
     * 查找激活的技能
     */
    @Query("SELECT ps FROM PetSkill ps WHERE ps.isActive = 1")
    List<PetSkill> findActiveSkills();
    
    /**
     * 根据学习等级要求查找技能
     */
    @Query("SELECT ps FROM PetSkill ps WHERE ps.learnLevel <= :petLevel AND ps.isActive = 1")
    List<PetSkill> findLearnableSkills(@Param("petLevel") Integer petLevel);
    
    /**
     * 根据技能名称模糊查找
     */
    List<PetSkill> findByNameContaining(String name);
    
    /**
     * 查找攻击类技能
     */
    @Query("SELECT ps FROM PetSkill ps WHERE ps.type = 1 AND ps.isActive = 1")
    List<PetSkill> findAttackSkills();
    
    /**
     * 查找治疗类技能
     */
    @Query("SELECT ps FROM PetSkill ps WHERE ps.type = 4 AND ps.isActive = 1")
    List<PetSkill> findHealSkills();
    
    /**
     * 查找辅助类技能
     */
    @Query("SELECT ps FROM PetSkill ps WHERE ps.type = 3 AND ps.isActive = 1")
    List<PetSkill> findBuffSkills();
    
    /**
     * 根据学习费用范围查找
     */
    @Query("SELECT ps FROM PetSkill ps WHERE ps.learnCost BETWEEN :minCost AND :maxCost AND ps.isActive = 1")
    List<PetSkill> findByLearnCostRange(@Param("minCost") Integer minCost, @Param("maxCost") Integer maxCost);
}
