package com.honghuang.game.dto;

import lombok.Data;

/**
 * 排行榜DTO
 */
@Data
public class RankingDTO {
    
    /**
     * 排名
     */
    private Integer rank;
    
    /**
     * 角色ID
     */
    private Integer roleId;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 角色种族 1-巫族 2-妖族
     */
    private Integer race;
    
    /**
     * 角色种族名称
     */
    private String raceName;
    
    /**
     * 角色等级
     */
    private Integer level;
    
    /**
     * 排行榜数值
     */
    private Long value;
    
    /**
     * 排行榜数值字符串（格式化后）
     */
    private String valueStr;
    
    /**
     * 排行榜类型
     */
    private String type;
    
    /**
     * 排行榜名称
     */
    private String typeName;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 是否是第一名
     */
    private Boolean isFirst;
    
    /**
     * 第一名称号
     */
    private String firstTitle;
    
    /**
     * 获取种族名称
     */
    public String getRaceName() {
        if (race == null) {
            return "";
        }
        switch (race) {
            case 1:
                return "巫族";
            case 2:
                return "妖族";
            default:
                return "";
        }
    }
    
    /**
     * 获取格式化的数值字符串
     */
    public String getValueStr() {
        if (value == null) {
            return "0";
        }
        
        // 根据类型格式化数值
        if ("copper".equals(type)) {
            // 铜钱格式化
            if (value >= 100000000) {
                return String.format("%.1f亿", value / 100000000.0);
            } else if (value >= 10000) {
                return String.format("%.1f万", value / 10000.0);
            } else {
                return value.toString();
            }
        } else if ("experience".equals(type)) {
            // 经验格式化
            if (value >= 100000000) {
                return String.format("%.1f亿", value / 100000000.0);
            } else if (value >= 10000) {
                return String.format("%.1f万", value / 10000.0);
            } else {
                return value.toString();
            }
        } else if ("onlineTime".equals(type)) {
            // 在线时长格式化（分钟转换为小时）
            if (value >= 60) {
                long hours = value / 60;
                long minutes = value % 60;
                if (minutes > 0) {
                    return hours + "小时" + minutes + "分钟";
                } else {
                    return hours + "小时";
                }
            } else {
                return value + "分钟";
            }
        } else {
            return value.toString();
        }
    }
}
