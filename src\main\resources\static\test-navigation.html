<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航测试 - 洪荒游戏</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #fff;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #3498db;
        }
        .nav-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
            border: 1px solid #34495e;
        }
        .nav-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
        }
        .nav-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.3);
        }
        .success { color: #2ecc71; }
        .error { color: #e74c3c; }
        .info { color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧭 洪荒游戏导航测试</h1>
        <p>测试所有页面的访问权限和链接功能</p>
        
        <div class="nav-section">
            <h3>🏠 主要页面</h3>
            <a href="/honghuang-game/index.html" class="nav-btn" target="_blank">🏠 首页</a>
            <a href="/honghuang-game/game.html" class="nav-btn" target="_blank">🎮 游戏主界面</a>
            <div class="status">
                <span class="info">ℹ️ 这些是游戏的主要入口页面</span>
            </div>
        </div>

        <div class="nav-section">
            <h3>🎯 功能页面</h3>
            <a href="/honghuang-game/battle.html" class="nav-btn" target="_blank">⚔️ 战斗系统</a>
            <a href="/honghuang-game/equipment.html" class="nav-btn" target="_blank">🏺 装备展示</a>
            <a href="/honghuang-game/pet.html" class="nav-btn" target="_blank">🐾 宠物系统</a>
            <div class="status">
                <span class="success">✅ 所有功能页面现在都可以正常访问</span>
            </div>
        </div>

        <div class="nav-section">
            <h3>🔧 测试页面</h3>
            <a href="/honghuang-game/test-session.html" class="nav-btn" target="_blank">🔧 Session测试</a>
            <a href="/honghuang-game/test-navigation.html" class="nav-btn" target="_blank">🧭 导航测试</a>
            <div class="status">
                <span class="info">ℹ️ 这些是开发和调试用的测试页面</span>
            </div>
        </div>

        <div class="nav-section">
            <h3>🔗 API测试</h3>
            <button class="nav-btn" onclick="testAPI('/honghuang-game/api/server/status', 'serverStatus')">📊 服务器状态</button>
            <button class="nav-btn" onclick="testAPI('/honghuang-game/api/game/info', 'gameInfo')">🎮 游戏信息</button>
            <button class="nav-btn" onclick="testAPI('/honghuang-game/api/scene/list', 'sceneList')">🗺️ 场景列表</button>
            <div id="apiResults" class="status">
                <span class="info">ℹ️ 点击按钮测试API接口</span>
            </div>
        </div>

        <div class="nav-section">
            <h3>📋 修复记录</h3>
            <div class="status">
                <div class="success">✅ 修复了装备展示页面的访问问题</div>
                <div class="success">✅ 修复了战斗记录功能</div>
                <div class="success">✅ 更新了Spring Security配置，允许所有HTML页面访问</div>
                <div class="info">ℹ️ 现在所有页面都可以正常访问，不会跳转到登录页</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/honghuang-game/game.html" class="nav-btn">🎮 返回游戏主界面</a>
        </div>
    </div>

    <script>
        // 测试API接口
        function testAPI(url, resultId) {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = `<span class="info">正在测试 ${url}...</span>`;

            fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultsDiv.innerHTML = `<span class="success">✅ ${url} - 成功</span><br><pre style="font-size: 12px; margin-top: 10px;">${JSON.stringify(data, null, 2).substring(0, 200)}...</pre>`;
                } else {
                    resultsDiv.innerHTML = `<span class="error">❌ ${url} - 失败: ${data.message}</span>`;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `<span class="error">❌ ${url} - 网络错误: ${error.message}</span>`;
            });
        }

        // 页面加载时显示欢迎信息
        window.onload = function() {
            console.log('🎉 洪荒游戏导航测试页面加载完成！');
            console.log('📝 修复记录:');
            console.log('  ✅ 装备展示页面访问问题已修复');
            console.log('  ✅ 战斗记录功能已恢复');
            console.log('  ✅ Spring Security配置已更新');
        };
    </script>
</body>
</html>
