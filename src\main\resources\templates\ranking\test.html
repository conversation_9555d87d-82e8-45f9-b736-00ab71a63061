<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洪荒世界 - 排行榜系统测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #fff;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 10px;
        }

        .test-section {
            background: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .test-title {
            font-size: 1.5em;
            color: #ffd700;
            margin-bottom: 15px;
            border-bottom: 2px solid rgba(255,215,0,0.3);
            padding-bottom: 10px;
        }

        .api-test {
            margin-bottom: 15px;
        }

        .api-url {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin-bottom: 10px;
            word-break: break-all;
        }

        .test-btn {
            background: linear-gradient(145deg, #ffd700, #ffed4e);
            color: #1e3c72;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,215,0,0.3);
        }

        .result-area {
            background: rgba(0,0,0,0.5);
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }

        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }

        .nav-link {
            display: inline-block;
            background: rgba(255,215,0,0.2);
            border: 2px solid #ffd700;
            color: #ffd700;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: #ffd700;
            color: #1e3c72;
        }

        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-left: 10px;
        }

        .status.success {
            background: #4caf50;
            color: white;
        }

        .status.error {
            background: #f44336;
            color: white;
        }

        .status.loading {
            background: #ff9800;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 排行榜系统测试</h1>
            <p>洪荒世界排行榜功能完整测试</p>
        </div>

        <div class="nav-links">
            <a href="/honghuang-game/ranking/index" class="nav-link">📊 排行榜主页</a>
            <a href="/honghuang-game/ranking/detail?type=level" class="nav-link">🎯 等级榜</a>
            <a href="/honghuang-game/ranking/detail?type=copper" class="nav-link">💰 财富榜</a>
            <a href="/honghuang-game/" class="nav-link">🏠 返回游戏</a>
        </div>

        <!-- API测试 -->
        <div class="test-section">
            <h2 class="test-title">🔧 API接口测试</h2>
            
            <div class="api-test">
                <h3>1. 获取等级排行榜 <span id="level-status" class="status">待测试</span></h3>
                <div class="api-url">GET /honghuang-game/ranking/api/list?type=level&limit=5</div>
                <button class="test-btn" onclick="testAPI('level', 5, 'level-status', 'level-result')">测试等级榜</button>
                <div id="level-result" class="result-area" style="display: none;"></div>
            </div>

            <div class="api-test">
                <h3>2. 获取财富排行榜 <span id="copper-status" class="status">待测试</span></h3>
                <div class="api-url">GET /honghuang-game/ranking/api/list?type=copper&limit=3</div>
                <button class="test-btn" onclick="testAPI('copper', 3, 'copper-status', 'copper-result')">测试财富榜</button>
                <div id="copper-result" class="result-area" style="display: none;"></div>
            </div>

            <div class="api-test">
                <h3>3. 获取攻击力排行榜 <span id="attack-status" class="status">待测试</span></h3>
                <div class="api-url">GET /honghuang-game/ranking/api/list?type=attack&limit=3</div>
                <button class="test-btn" onclick="testAPI('attack', 3, 'attack-status', 'attack-result')">测试攻击榜</button>
                <div id="attack-result" class="result-area" style="display: none;"></div>
            </div>

            <div class="api-test">
                <h3>4. 获取排行榜分类 <span id="categories-status" class="status">待测试</span></h3>
                <div class="api-url">GET /honghuang-game/ranking/api/categories</div>
                <button class="test-btn" onclick="testCategories('categories-status', 'categories-result')">测试分类API</button>
                <div id="categories-result" class="result-area" style="display: none;"></div>
            </div>

            <div class="api-test">
                <h3>5. 批量测试所有排行榜 <span id="batch-status" class="status">待测试</span></h3>
                <button class="test-btn" onclick="testAllRankings()">批量测试</button>
                <div id="batch-result" class="result-area" style="display: none;"></div>
            </div>
        </div>

        <!-- 功能测试 -->
        <div class="test-section">
            <h2 class="test-title">⚡ 功能特性测试</h2>
            
            <div class="api-test">
                <h3>✅ 已实现功能</h3>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    <li>✅ 13种排行榜类型（等级、经验、财富、攻击、防御、敏捷、杀怪、PK、在线、宠物等级、宠物攻击、装备、综合）</li>
                    <li>✅ 4个排行榜分类（个人榜、战斗榜、宠物榜、装备榜）</li>
                    <li>✅ 响应式手机端适配界面</li>
                    <li>✅ 数值格式化显示（万、亿、时间格式）</li>
                    <li>✅ 第一名特殊称号系统</li>
                    <li>✅ 种族标识（巫族、妖族）</li>
                    <li>✅ RESTful API接口</li>
                    <li>✅ 定时任务调度器</li>
                    <li>✅ 数据库表结构和测试数据</li>
                    <li>✅ MyBatis Plus集成</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // API测试函数
        async function testAPI(type, limit, statusId, resultId) {
            const statusEl = document.getElementById(statusId);
            const resultEl = document.getElementById(resultId);
            
            statusEl.textContent = '测试中...';
            statusEl.className = 'status loading';
            resultEl.style.display = 'block';
            resultEl.textContent = '正在请求数据...';
            
            try {
                const response = await fetch(`/honghuang-game/ranking/api/list?type=${type}&limit=${limit}`);
                const data = await response.json();
                
                if (data.code === 200) {
                    statusEl.textContent = '成功';
                    statusEl.className = 'status success';
                    resultEl.textContent = JSON.stringify(data, null, 2);
                } else {
                    statusEl.textContent = '失败';
                    statusEl.className = 'status error';
                    resultEl.textContent = `错误: ${data.message}`;
                }
            } catch (error) {
                statusEl.textContent = '失败';
                statusEl.className = 'status error';
                resultEl.textContent = `网络错误: ${error.message}`;
            }
        }

        // 测试分类API
        async function testCategories(statusId, resultId) {
            const statusEl = document.getElementById(statusId);
            const resultEl = document.getElementById(resultId);
            
            statusEl.textContent = '测试中...';
            statusEl.className = 'status loading';
            resultEl.style.display = 'block';
            resultEl.textContent = '正在请求数据...';
            
            try {
                const response = await fetch('/honghuang-game/ranking/api/categories');
                const data = await response.json();
                
                if (data.code === 200) {
                    statusEl.textContent = '成功';
                    statusEl.className = 'status success';
                    resultEl.textContent = JSON.stringify(data, null, 2);
                } else {
                    statusEl.textContent = '失败';
                    statusEl.className = 'status error';
                    resultEl.textContent = `错误: ${data.message}`;
                }
            } catch (error) {
                statusEl.textContent = '失败';
                statusEl.className = 'status error';
                resultEl.textContent = `网络错误: ${error.message}`;
            }
        }

        // 批量测试所有排行榜
        async function testAllRankings() {
            const statusEl = document.getElementById('batch-status');
            const resultEl = document.getElementById('batch-result');
            
            statusEl.textContent = '测试中...';
            statusEl.className = 'status loading';
            resultEl.style.display = 'block';
            resultEl.textContent = '开始批量测试...\n';
            
            const rankTypes = ['level', 'experience', 'copper', 'attack', 'defense', 'agility', 
                              'killMonster', 'pkWin', 'onlineTime', 'petLevel', 'petAttack', 
                              'equipScore', 'totalScore'];
            
            let successCount = 0;
            let totalCount = rankTypes.length;
            
            for (const type of rankTypes) {
                try {
                    resultEl.textContent += `测试 ${type} 排行榜...\n`;
                    const response = await fetch(`/honghuang-game/ranking/api/list?type=${type}&limit=3`);
                    const data = await response.json();
                    
                    if (data.code === 200) {
                        resultEl.textContent += `✅ ${type}: 成功 (${data.data.length} 条记录)\n`;
                        successCount++;
                    } else {
                        resultEl.textContent += `❌ ${type}: 失败 - ${data.message}\n`;
                    }
                } catch (error) {
                    resultEl.textContent += `❌ ${type}: 网络错误 - ${error.message}\n`;
                }
                
                // 滚动到底部
                resultEl.scrollTop = resultEl.scrollHeight;
                
                // 短暂延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            resultEl.textContent += `\n测试完成！成功: ${successCount}/${totalCount}\n`;
            
            if (successCount === totalCount) {
                statusEl.textContent = '全部成功';
                statusEl.className = 'status success';
            } else {
                statusEl.textContent = `部分成功 (${successCount}/${totalCount})`;
                statusEl.className = 'status error';
            }
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('排行榜系统测试页面加载完成');
        });
    </script>
</body>
</html>
