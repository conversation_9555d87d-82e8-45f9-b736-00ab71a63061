package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.Pet;
import com.honghuang.game.entity.PetSkill;
import com.honghuang.game.service.PetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 宠物控制器
 */
@RestController
@RequestMapping("/api/pet")
public class PetController {
    
    @Autowired
    private PetService petService;
    
    /**
     * 获取玩家的所有宠物
     */
    @GetMapping("/list")
    public Result<List<Pet>> getPlayerPets(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");
            
            if (userId == null) {
                return Result.error(401, "用户未登录");
            }
            
            List<Pet> pets = petService.getPlayerPets(userId);
            return Result.success("获取宠物列表成功", pets);
            
        } catch (Exception e) {
            return Result.error(500, "获取宠物列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取出战宠物
     */
    @GetMapping("/active")
    public Result<Pet> getActivePet(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");
            
            if (userId == null) {
                return Result.error(401, "用户未登录");
            }
            
            Optional<Pet> activePet = petService.getActivePet(userId);
            if (activePet.isPresent()) {
                return Result.success("获取出战宠物成功", activePet.get());
            } else {
                return Result.error(404, "没有出战宠物");
            }
            
        } catch (Exception e) {
            return Result.error(500, "获取出战宠物失败: " + e.getMessage());
        }
    }
    
    /**
     * 捕获宠物
     */
    @PostMapping("/capture")
    public Result<Pet> capturePet(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer userId = (Integer) session.getAttribute("userId");
            
            if (userId == null) {
                return Result.error(401, "用户未登录");
            }
            
            String petName = (String) request.get("petName");
            Integer petType = (Integer) request.get("petType");
            
            if (petName == null || petType == null) {
                return Result.error(400, "宠物名称和类型不能为空");
            }
            
            Pet pet = petService.capturePet(userId, petName, petType);
            return Result.success("捕获宠物成功", pet);
            
        } catch (Exception e) {
            return Result.error(500, "捕获宠物失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置出战宠物
     */
    @PostMapping("/setActive")
    public Result<Void> setActivePet(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer userId = (Integer) session.getAttribute("userId");
            
            if (userId == null) {
                return Result.error(401, "用户未登录");
            }
            
            Integer petId = (Integer) request.get("petId");
            if (petId == null) {
                return Result.error(400, "宠物ID不能为空");
            }
            
            petService.setActivePet(userId, petId);
            return Result.success("设置出战宠物成功");
            
        } catch (Exception e) {
            return Result.error(500, "设置出战宠物失败: " + e.getMessage());
        }
    }
    
    /**
     * 宠物升级
     */
    @PostMapping("/levelUp")
    public Result<Void> levelUpPet(@RequestBody Map<String, Object> request) {
        try {
            Integer petId = (Integer) request.get("petId");
            if (petId == null) {
                return Result.error(400, "宠物ID不能为空");
            }
            
            boolean success = petService.levelUpPet(petId);
            if (success) {
                return Result.success("宠物升级成功");
            } else {
                return Result.error(400, "宠物升级失败，可能是经验不足");
            }
            
        } catch (Exception e) {
            return Result.error(500, "宠物升级失败: " + e.getMessage());
        }
    }
    
    /**
     * 喂养宠物
     */
    @PostMapping("/feed")
    public Result<Void> feedPet(@RequestBody Map<String, Object> request) {
        try {
            Integer petId = (Integer) request.get("petId");
            Integer foodValue = (Integer) request.get("foodValue");
            
            if (petId == null || foodValue == null) {
                return Result.error(400, "宠物ID和食物价值不能为空");
            }
            
            petService.feedPet(petId, foodValue);
            return Result.success("喂养宠物成功");
            
        } catch (Exception e) {
            return Result.error(500, "喂养宠物失败: " + e.getMessage());
        }
    }
    
    /**
     * 宠物休息
     */
    @PostMapping("/rest")
    public Result<Void> restPet(@RequestBody Map<String, Object> request) {
        try {
            Integer petId = (Integer) request.get("petId");
            if (petId == null) {
                return Result.error(400, "宠物ID不能为空");
            }
            
            petService.restPet(petId);
            return Result.success("宠物休息成功");
            
        } catch (Exception e) {
            return Result.error(500, "宠物休息失败: " + e.getMessage());
        }
    }
    
    /**
     * 学习技能
     */
    @PostMapping("/learnSkill")
    public Result<Void> learnSkill(@RequestBody Map<String, Object> request) {
        try {
            Integer petId = (Integer) request.get("petId");
            Integer skillId = (Integer) request.get("skillId");
            
            if (petId == null || skillId == null) {
                return Result.error(400, "宠物ID和技能ID不能为空");
            }
            
            boolean success = petService.learnSkill(petId, skillId);
            if (success) {
                return Result.success("学习技能成功");
            } else {
                return Result.error(400, "学习技能失败，可能是等级不足或技能位已满");
            }
            
        } catch (Exception e) {
            return Result.error(500, "学习技能失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取宠物技能
     */
    @GetMapping("/{petId}/skills")
    public Result<List<PetSkill>> getPetSkills(@PathVariable Integer petId) {
        try {
            List<PetSkill> skills = petService.getPetSkills(petId);
            return Result.success("获取宠物技能成功", skills);
            
        } catch (Exception e) {
            return Result.error(500, "获取宠物技能失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取可学习的技能
     */
    @GetMapping("/{petId}/learnableSkills")
    public Result<List<PetSkill>> getLearnableSkills(@PathVariable Integer petId) {
        try {
            List<PetSkill> skills = petService.getLearnableSkills(petId);
            return Result.success("获取可学习技能成功", skills);
            
        } catch (Exception e) {
            return Result.error(500, "获取可学习技能失败: " + e.getMessage());
        }
    }
    
    /**
     * 释放宠物
     */
    @DeleteMapping("/{petId}")
    public Result<Void> releasePet(@PathVariable Integer petId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer userId = (Integer) session.getAttribute("userId");
            
            if (userId == null) {
                return Result.error(401, "用户未登录");
            }
            
            petService.releasePet(petId, userId);
            return Result.success("释放宠物成功");
            
        } catch (Exception e) {
            return Result.error(500, "释放宠物失败: " + e.getMessage());
        }
    }
    
    /**
     * 重命名宠物
     */
    @PostMapping("/rename")
    public Result<Void> renamePet(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer userId = (Integer) session.getAttribute("userId");
            
            if (userId == null) {
                return Result.error(401, "用户未登录");
            }
            
            Integer petId = (Integer) request.get("petId");
            String newNickname = (String) request.get("newNickname");
            
            if (petId == null || newNickname == null || newNickname.trim().isEmpty()) {
                return Result.error(400, "宠物ID和新昵称不能为空");
            }
            
            petService.renamePet(petId, userId, newNickname.trim());
            return Result.success("重命名宠物成功");
            
        } catch (Exception e) {
            return Result.error(500, "重命名宠物失败: " + e.getMessage());
        }
    }
}
