package com.honghuang.game.service;

import com.honghuang.game.entity.User;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.UserRepository;
import com.honghuang.game.util.PasswordUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 用户服务类
 */
@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    /**
     * 用户注册
     */
    public User register(String username, String password, String confirmPassword, String channel) {
        System.out.println("用户注册: username=" + username);

        // 验证密码一致性
        if (!password.equals(confirmPassword)) {
            throw new BusinessException(400, "两次输入的密码不一致");
        }

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(username)) {
            throw new BusinessException(400, "用户名已存在");
        }

        // 创建用户
        User user = new User();
        user.setUsername(username);
        user.setPassword(PasswordUtil.encode(password));
        user.setChannel(channel != null ? channel : "web");
        user.setLoginState(0);

        user = userRepository.save(user);
        System.out.println("用户注册成功: userId=" + user.getId() + ", username=" + user.getUsername());

        return user;
    }

    /**
     * 用户登录
     */
    public User login(String username, String password, HttpServletRequest request) {
        System.out.println("用户登录: username=" + username);

        // 查找用户
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (!userOpt.isPresent()) {
            throw new BusinessException(404, "用户不存在");
        }

        User user = userOpt.get();

        // 验证密码
        if (!PasswordUtil.matches(password, user.getPassword())) {
            throw new BusinessException(401, "密码错误");
        }

        // 更新登录信息
        String clientIp = getClientIp(request);
        user.updateLoginInfo(clientIp);
        userRepository.save(user);

        System.out.println("用户登录成功: userId=" + user.getId() + ", username=" + user.getUsername() + ", ip=" + clientIp);

        return user;
    }

    /**
     * 用户登出
     */
    public void logout(Integer userId) {
        System.out.println("用户登出: userId=" + userId);

        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.logout();
            userRepository.save(user);
        }

        System.out.println("用户登出成功: userId=" + userId);
    }

    /**
     * 根据ID查找用户
     */
    @Transactional(readOnly = true)
    public User findById(Integer userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(404, "用户不存在"));
    }

    /**
     * 根据用户名查找用户
     */
    @Transactional(readOnly = true)
    public User findByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new BusinessException(404, "用户不存在"));
    }

    /**
     * 获取在线用户数量
     */
    @Transactional(readOnly = true)
    public long getOnlineUserCount() {
        return userRepository.countOnlineUsers();
    }

    /**
     * 获取用户统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getUserStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        long totalUsers = userRepository.countTotalUsers();
        long onlineUsers = userRepository.countOnlineUsers();
        long todayRegistrations = userRepository.countTodayRegistrations();
        long todayActiveUsers = userRepository.countTodayActiveUsers();
        
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
        
        long newUsers = userRepository.findNewUsers(sevenDaysAgo).size();
        long activeUsers = userRepository.findActiveUsers(threeDaysAgo).size();
        
        stats.put("totalUsers", totalUsers);
        stats.put("onlineUsers", onlineUsers);
        stats.put("todayRegistrations", todayRegistrations);
        stats.put("todayActiveUsers", todayActiveUsers);
        stats.put("newUsers", newUsers);
        stats.put("activeUsers", activeUsers);
        
        return stats;
    }

    /**
     * 更新用户元宝
     */
    public void updateYuanbao(Integer userId, Integer amount) {
        System.out.println("更新用户元宝: userId=" + userId + ", amount=" + amount);

        int updated = userRepository.updateYuanbao(userId, amount);
        if (updated == 0) {
            throw new BusinessException(404, "用户不存在");
        }
    }

    /**
     * 更新用户积分
     */
    public void updateJifen(Integer userId, Integer amount) {
        System.out.println("更新用户积分: userId=" + userId + ", amount=" + amount);

        int updated = userRepository.updateJifen(userId, amount);
        if (updated == 0) {
            throw new BusinessException(404, "用户不存在");
        }
    }

    /**
     * 获取元宝排行榜
     */
    @Transactional(readOnly = true)
    public List<User> getYuanbaoRanking(int limit) {
        List<User> users = userRepository.findYuanbaoRanking();
        return users.size() > limit ? users.subList(0, limit) : users;
    }

    /**
     * 获取积分排行榜
     */
    @Transactional(readOnly = true)
    public List<User> getJifenRanking(int limit) {
        List<User> users = userRepository.findJifenRanking();
        return users.size() > limit ? users.subList(0, limit) : users;
    }

    /**
     * 搜索用户
     */
    @Transactional(readOnly = true)
    public List<User> searchUsers(String keyword) {
        return userRepository.findByUsernameContaining(keyword);
    }

    /**
     * 获取VIP用户列表
     */
    @Transactional(readOnly = true)
    public List<User> getVipUsers(Integer minYuanbao) {
        return userRepository.findVipUsers(minYuanbao != null ? minYuanbao : 1000);
    }

    /**
     * 获取新用户列表
     */
    @Transactional(readOnly = true)
    public List<User> getNewUsers() {
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        return userRepository.findNewUsers(sevenDaysAgo);
    }

    /**
     * 获取活跃用户列表
     */
    @Transactional(readOnly = true)
    public List<User> getActiveUsers() {
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
        return userRepository.findActiveUsers(threeDaysAgo);
    }

    /**
     * 批量下线所有用户
     */
    public void logoutAllUsers() {
        System.out.println("批量下线所有用户");

        int count = userRepository.updateAllUsersToOffline();

        System.out.println("批量下线完成，共下线" + count + "个用户");
    }

    /**
     * 清理不活跃用户
     */
    @Transactional
    public void cleanupInactiveUsers(int inactiveDays) {
        System.out.println("清理不活跃用户: inactiveDays=" + inactiveDays);

        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(inactiveDays);
        List<User> inactiveUsers = userRepository.findInactiveUsers(cutoffTime);

        System.out.println("发现" + inactiveUsers.size() + "个不活跃用户");
        // 这里可以添加具体的清理逻辑，比如发送邮件通知等
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 验证用户权限
     */
    public boolean hasPermission(Integer userId, String permission) {
        // 简化的权限验证，实际项目中可以扩展
        User user = findById(userId);
        return user.isOnline();
    }

    /**
     * 检查用户是否为管理员
     */
    public boolean isAdmin(Integer userId) {
        User user = findById(userId);
        return "admin".equals(user.getUsername());
    }
}
