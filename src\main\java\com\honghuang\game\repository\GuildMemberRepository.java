package com.honghuang.game.repository;

import com.honghuang.game.entity.GuildMember;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 帮派成员数据访问层
 */
@Repository
public interface GuildMemberRepository extends JpaRepository<GuildMember, Integer> {

    /**
     * 根据帮派ID查找成员列表
     */
    List<GuildMember> findByGuildIdOrderByPositionAscContributionDesc(Integer guildId);

    /**
     * 根据角色ID查找帮派成员信息
     */
    Optional<GuildMember> findByRoleId(Integer roleId);

    /**
     * 根据帮派ID和角色ID查找成员
     */
    Optional<GuildMember> findByGuildIdAndRoleId(Integer guildId, Integer roleId);

    /**
     * 根据帮派ID和职位查找成员
     */
    List<GuildMember> findByGuildIdAndPositionOrderByContributionDesc(Integer guildId, Integer position);

    /**
     * 查找帮派的帮主
     */
    Optional<GuildMember> findByGuildIdAndPosition(Integer guildId, Integer position);

    /**
     * 查找帮派的管理层成员
     */
    @Query("SELECT gm FROM GuildMember gm WHERE gm.guildId = :guildId AND gm.position <= 3 ORDER BY gm.position ASC")
    List<GuildMember> findManagers(@Param("guildId") Integer guildId);

    /**
     * 统计帮派成员数量
     */
    @Query("SELECT COUNT(gm) FROM GuildMember gm WHERE gm.guildId = :guildId")
    long countByGuildId(@Param("guildId") Integer guildId);

    /**
     * 统计指定职位成员数量
     */
    @Query("SELECT COUNT(gm) FROM GuildMember gm WHERE gm.guildId = :guildId AND gm.position = :position")
    long countByGuildIdAndPosition(@Param("guildId") Integer guildId, @Param("position") Integer position);

    /**
     * 查找贡献度排行榜
     */
    @Query("SELECT gm FROM GuildMember gm WHERE gm.guildId = :guildId ORDER BY gm.contribution DESC, gm.position ASC")
    List<GuildMember> findContributionRanking(@Param("guildId") Integer guildId);

    /**
     * 查找在线成员
     */
    @Query("SELECT gm FROM GuildMember gm WHERE gm.guildId = :guildId AND gm.lastOnlineTime >= :cutoffTime ORDER BY gm.position ASC")
    List<GuildMember> findOnlineMembers(@Param("guildId") Integer guildId, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找长期离线成员
     */
    @Query("SELECT gm FROM GuildMember gm WHERE gm.guildId = :guildId AND gm.lastOnlineTime <= :cutoffTime ORDER BY gm.lastOnlineTime ASC")
    List<GuildMember> findOfflineMembers(@Param("guildId") Integer guildId, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 更新成员职位
     */
    @Modifying
    @Query("UPDATE GuildMember gm SET gm.position = :position WHERE gm.id = :memberId")
    int updatePosition(@Param("memberId") Integer memberId, @Param("position") Integer position);

    /**
     * 更新成员贡献度
     */
    @Modifying
    @Query("UPDATE GuildMember gm SET gm.contribution = :contribution WHERE gm.id = :memberId")
    int updateContribution(@Param("memberId") Integer memberId, @Param("contribution") Long contribution);

    /**
     * 增加成员贡献度
     */
    @Modifying
    @Query("UPDATE GuildMember gm SET gm.contribution = gm.contribution + :amount WHERE gm.id = :memberId")
    int addContribution(@Param("memberId") Integer memberId, @Param("amount") Long amount);

    /**
     * 更新成员在线时间
     */
    @Modifying
    @Query("UPDATE GuildMember gm SET gm.lastOnlineTime = :onlineTime WHERE gm.roleId = :roleId")
    int updateOnlineTime(@Param("roleId") Integer roleId, @Param("onlineTime") LocalDateTime onlineTime);

    /**
     * 更新成员等级
     */
    @Modifying
    @Query("UPDATE GuildMember gm SET gm.roleLevel = :level WHERE gm.roleId = :roleId")
    int updateRoleLevel(@Param("roleId") Integer roleId, @Param("level") Integer level);

    /**
     * 删除帮派的所有成员
     */
    @Modifying
    @Query("DELETE FROM GuildMember gm WHERE gm.guildId = :guildId")
    int deleteByGuildId(@Param("guildId") Integer guildId);

    /**
     * 查找指定等级范围的成员
     */
    @Query("SELECT gm FROM GuildMember gm WHERE gm.guildId = :guildId AND gm.roleLevel BETWEEN :minLevel AND :maxLevel ORDER BY gm.roleLevel DESC")
    List<GuildMember> findByLevelRange(@Param("guildId") Integer guildId, @Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 查找新加入的成员
     */
    @Query("SELECT gm FROM GuildMember gm WHERE gm.guildId = :guildId AND gm.joinTime >= :cutoffTime ORDER BY gm.joinTime DESC")
    List<GuildMember> findNewMembers(@Param("guildId") Integer guildId, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计帮派总贡献度
     */
    @Query("SELECT COALESCE(SUM(gm.contribution), 0) FROM GuildMember gm WHERE gm.guildId = :guildId")
    long sumContributionByGuildId(@Param("guildId") Integer guildId);
}
