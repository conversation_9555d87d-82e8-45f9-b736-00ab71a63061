package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 师徒关系实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "mentor_relation")
public class MentorRelation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 师父角色ID
     */
    @Column(name = "mentor_id")
    private Integer mentorId;

    /**
     * 师父角色名称
     */
    @Column(name = "mentor_name", length = 50)
    private String mentorName;

    /**
     * 师父等级
     */
    @Column(name = "mentor_level")
    private Integer mentorLevel;

    /**
     * 徒弟角色ID
     */
    @Column(name = "apprentice_id")
    private Integer apprenticeId;

    /**
     * 徒弟角色名称
     */
    @Column(name = "apprentice_name", length = 50)
    private String apprenticeName;

    /**
     * 徒弟等级
     */
    @Column(name = "apprentice_level")
    private Integer apprenticeLevel;

    /**
     * 师徒关系状态 1正常 2师父解除 3徒弟解除 4系统解除
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 师父贡献度
     */
    @Column(name = "mentor_contribution")
    private Long mentorContribution = 0L;

    /**
     * 徒弟贡献度
     */
    @Column(name = "apprentice_contribution")
    private Long apprenticeContribution = 0L;

    /**
     * 建立关系时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "establish_time")
    private LocalDateTime establishTime;

    /**
     * 解除关系时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "dissolve_time")
    private LocalDateTime dissolveTime;

    /**
     * 最后互动时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_interaction_time")
    private LocalDateTime lastInteractionTime;

    /**
     * 师父信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mentor_id", insertable = false, updatable = false)
    private PlayerRole mentor;

    /**
     * 徒弟信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "apprentice_id", insertable = false, updatable = false)
    private PlayerRole apprentice;

    // 业务方法

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        switch (status) {
            case 1: return "正常";
            case 2: return "师父解除";
            case 3: return "徒弟解除";
            case 4: return "系统解除";
            default: return "未知";
        }
    }

    /**
     * 是否为正常状态
     */
    public boolean isActive() {
        return status == 1;
    }

    /**
     * 是否已解除
     */
    public boolean isDissolved() {
        return status > 1;
    }

    /**
     * 师父解除关系
     */
    public void dissolveByMentor() {
        this.status = 2;
        this.dissolveTime = LocalDateTime.now();
    }

    /**
     * 徒弟解除关系
     */
    public void dissolveByApprentice() {
        this.status = 3;
        this.dissolveTime = LocalDateTime.now();
    }

    /**
     * 系统解除关系
     */
    public void dissolveBySystem() {
        this.status = 4;
        this.dissolveTime = LocalDateTime.now();
    }

    /**
     * 更新互动时间
     */
    public void updateInteractionTime() {
        this.lastInteractionTime = LocalDateTime.now();
    }

    /**
     * 增加师父贡献度
     */
    public void addMentorContribution(Long amount) {
        this.mentorContribution += amount;
        updateInteractionTime();
    }

    /**
     * 增加徒弟贡献度
     */
    public void addApprenticeContribution(Long amount) {
        this.apprenticeContribution += amount;
        updateInteractionTime();
    }

    /**
     * 获取师徒关系持续天数
     */
    public Long getRelationDays() {
        if (establishTime == null) {
            return 0L;
        }
        
        LocalDateTime endTime = isDissolved() ? dissolveTime : LocalDateTime.now();
        return java.time.Duration.between(establishTime, endTime).toDays();
    }

    /**
     * 获取上次互动天数
     */
    public Long getLastInteractionDays() {
        if (lastInteractionTime == null) {
            return getRelationDays();
        }
        return java.time.Duration.between(lastInteractionTime, LocalDateTime.now()).toDays();
    }

    /**
     * 是否长期未互动
     */
    public boolean isLongTimeNoInteraction() {
        return getLastInteractionDays() > 7; // 7天未互动
    }

    /**
     * 获取等级差距
     */
    public Integer getLevelGap() {
        if (mentorLevel == null || apprenticeLevel == null) {
            return 0;
        }
        return mentorLevel - apprenticeLevel;
    }

    /**
     * 检查等级差距是否合适
     */
    public boolean isLevelGapAppropriate() {
        int gap = getLevelGap();
        return gap >= 5 && gap <= 50; // 师父比徒弟高5-50级比较合适
    }

    /**
     * 获取师徒关系完整信息
     */
    public String getFullInfo() {
        return String.format("师父: %s(Lv.%d) - 徒弟: %s(Lv.%d) [%s]", 
                mentorName, mentorLevel, apprenticeName, apprenticeLevel, getStatusName());
    }

    /**
     * 计算师父奖励系数
     */
    public Double getMentorRewardMultiplier() {
        // 根据徒弟等级和师徒关系时间计算奖励系数
        long days = getRelationDays();
        double baseMultiplier = 1.0;
        
        if (days >= 30) {
            baseMultiplier += 0.5; // 30天以上额外50%奖励
        } else if (days >= 7) {
            baseMultiplier += 0.2; // 7天以上额外20%奖励
        }
        
        // 根据等级差距调整
        int levelGap = getLevelGap();
        if (levelGap >= 20) {
            baseMultiplier += 0.3;
        } else if (levelGap >= 10) {
            baseMultiplier += 0.1;
        }
        
        return baseMultiplier;
    }

    /**
     * 计算徒弟奖励系数
     */
    public Double getApprenticeRewardMultiplier() {
        // 根据师父等级和师徒关系时间计算奖励系数
        long days = getRelationDays();
        double baseMultiplier = 1.2; // 徒弟基础奖励20%
        
        if (days >= 30) {
            baseMultiplier += 0.3; // 30天以上额外30%奖励
        } else if (days >= 7) {
            baseMultiplier += 0.1; // 7天以上额外10%奖励
        }
        
        return baseMultiplier;
    }

    /**
     * 检查是否可以出师
     */
    public boolean canGraduate() {
        // 徒弟等级达到师父等级的80%且关系持续30天以上
        if (mentorLevel == null || apprenticeLevel == null) {
            return false;
        }
        
        boolean levelRequirement = apprenticeLevel >= (mentorLevel * 0.8);
        boolean timeRequirement = getRelationDays() >= 30;
        
        return levelRequirement && timeRequirement;
    }

    /**
     * 出师
     */
    public void graduate() {
        if (!canGraduate()) {
            throw new RuntimeException("不满足出师条件");
        }
        
        // 给予出师奖励
        addMentorContribution(1000L); // 师父获得1000贡献度
        addApprenticeContribution(500L); // 徒弟获得500贡献度
        
        // 解除师徒关系
        dissolveBySystem();
    }
}
