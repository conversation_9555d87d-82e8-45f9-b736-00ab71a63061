package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 传送点实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "teleport_point")
public class TeleportPoint {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 传送点名称
     */
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 所在场景ID
     */
    @Column(name = "scene_id")
    private Integer sceneId;

    /**
     * 目标场景ID
     */
    @Column(name = "target_scene_id")
    private Integer targetSceneId;

    /**
     * 传送点X坐标
     */
    @Column(name = "position_x")
    private Integer positionX;

    /**
     * 传送点Y坐标
     */
    @Column(name = "position_y")
    private Integer positionY;

    /**
     * 目标X坐标
     */
    @Column(name = "target_x")
    private Integer targetX;

    /**
     * 目标Y坐标
     */
    @Column(name = "target_y")
    private Integer targetY;

    /**
     * 传送类型 1双向传送 2单向传送 3付费传送 4任务传送
     */
    @Column(name = "teleport_type")
    private Integer teleportType = 1;

    /**
     * 等级要求
     */
    @Column(name = "required_level")
    private Integer requiredLevel = 1;

    /**
     * 传送费用
     */
    @Column(name = "cost")
    private Long cost = 0L;

    /**
     * 货币类型 1铜钱 2灵石 3仙晶
     */
    @Column(name = "currency_type")
    private Integer currencyType = 1;

    /**
     * 冷却时间（秒）
     */
    @Column(name = "cooldown")
    private Integer cooldown = 0;

    /**
     * 是否需要任务完成
     */
    @Column(name = "required_quest_id")
    private Integer requiredQuestId;

    /**
     * 传送点描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 所在场景信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "scene_id", insertable = false, updatable = false)
    private Scene scene;

    /**
     * 目标场景信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_scene_id", insertable = false, updatable = false)
    private Scene targetScene;

    // 业务方法

    /**
     * 获取传送类型名称
     */
    public String getTeleportTypeName() {
        switch (teleportType) {
            case 1: return "双向传送";
            case 2: return "单向传送";
            case 3: return "付费传送";
            case 4: return "任务传送";
            default: return "未知";
        }
    }

    /**
     * 获取货币类型名称
     */
    public String getCurrencyTypeName() {
        switch (currencyType) {
            case 1: return "铜钱";
            case 2: return "灵石";
            case 3: return "仙晶";
            default: return "未知";
        }
    }

    /**
     * 是否为双向传送
     */
    public boolean isBidirectional() {
        return teleportType == 1;
    }

    /**
     * 是否为单向传送
     */
    public boolean isUnidirectional() {
        return teleportType == 2;
    }

    /**
     * 是否为付费传送
     */
    public boolean isPaidTeleport() {
        return teleportType == 3;
    }

    /**
     * 是否为任务传送
     */
    public boolean isQuestTeleport() {
        return teleportType == 4;
    }

    /**
     * 是否激活
     */
    public boolean isActive() {
        return active == 1;
    }

    /**
     * 是否需要费用
     */
    public boolean hasCost() {
        return cost != null && cost > 0;
    }

    /**
     * 是否有冷却时间
     */
    public boolean hasCooldown() {
        return cooldown != null && cooldown > 0;
    }

    /**
     * 是否需要任务
     */
    public boolean requiresQuest() {
        return requiredQuestId != null;
    }

    /**
     * 检查角色是否可以使用传送点
     */
    public boolean canUse(PlayerRole role) {
        if (!isActive()) {
            return false;
        }
        
        if (role.getLevel() < requiredLevel) {
            return false;
        }
        
        // 检查任务要求
        if (requiresQuest()) {
            // TODO: 检查角色是否完成了指定任务
            // return questService.isQuestCompleted(role.getId(), requiredQuestId);
        }
        
        return true;
    }

    /**
     * 检查角色是否有足够的传送费用
     */
    public boolean hasEnoughCurrency(PlayerRole role) {
        if (!hasCost()) {
            return true;
        }
        
        switch (currencyType) {
            case 1: return role.getCopper() >= cost;
            case 2: return role.getSilver() >= cost;
            case 3: return role.getGold() >= cost;
            default: return false;
        }
    }

    /**
     * 扣除传送费用
     */
    public boolean chargeCost(PlayerRole role) {
        if (!hasCost()) {
            return true;
        }
        
        switch (currencyType) {
            case 1: return role.spendCopper(cost);
            case 2: return role.spendSilver(cost);
            case 3: return role.spendGold(cost);
            default: return false;
        }
    }

    /**
     * 检查是否在传送点范围内
     */
    public boolean isInRange(Integer x, Integer y, Integer range) {
        if (positionX == null || positionY == null) {
            return false;
        }
        
        int distance = Math.abs(positionX - x) + Math.abs(positionY - y);
        return distance <= range;
    }

    /**
     * 计算与指定位置的距离
     */
    public Integer getDistanceTo(Integer x, Integer y) {
        if (positionX == null || positionY == null) {
            return Integer.MAX_VALUE;
        }
        
        return Math.abs(positionX - x) + Math.abs(positionY - y);
    }

    /**
     * 获取传送点完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(name).append(" [").append(getTeleportTypeName()).append("]");
        
        if (scene != null && targetScene != null) {
            sb.append(" (").append(scene.getName()).append(" -> ").append(targetScene.getName()).append(")");
        }
        
        if (hasCost()) {
            sb.append(" 费用: ").append(cost).append(getCurrencyTypeName());
        }
        
        if (requiredLevel > 1) {
            sb.append(" 要求等级: ").append(requiredLevel);
        }
        
        return sb.toString();
    }

    /**
     * 获取位置描述
     */
    public String getPositionDescription() {
        return String.format("(%d, %d)", positionX, positionY);
    }

    /**
     * 获取目标位置描述
     */
    public String getTargetPositionDescription() {
        return String.format("(%d, %d)", targetX, targetY);
    }

    /**
     * 获取费用描述
     */
    public String getCostDescription() {
        if (!hasCost()) {
            return "免费";
        }
        return cost + getCurrencyTypeName();
    }

    /**
     * 获取冷却时间描述
     */
    public String getCooldownDescription() {
        if (!hasCooldown()) {
            return "无冷却";
        }
        
        if (cooldown < 60) {
            return cooldown + "秒";
        } else if (cooldown < 3600) {
            return (cooldown / 60) + "分钟";
        } else {
            return (cooldown / 3600) + "小时";
        }
    }

    /**
     * 创建反向传送点
     */
    public TeleportPoint createReverseTeleportPoint() {
        if (!isBidirectional()) {
            return null;
        }
        
        TeleportPoint reverse = new TeleportPoint();
        reverse.setName("返回 " + (scene != null ? scene.getName() : "原点"));
        reverse.setSceneId(this.targetSceneId);
        reverse.setTargetSceneId(this.sceneId);
        reverse.setPositionX(this.targetX);
        reverse.setPositionY(this.targetY);
        reverse.setTargetX(this.positionX);
        reverse.setTargetY(this.positionY);
        reverse.setTeleportType(this.teleportType);
        reverse.setRequiredLevel(this.requiredLevel);
        reverse.setCost(this.cost);
        reverse.setCurrencyType(this.currencyType);
        reverse.setCooldown(this.cooldown);
        reverse.setActive(this.active);
        
        return reverse;
    }
}
