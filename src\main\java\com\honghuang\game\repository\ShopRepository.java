package com.honghuang.game.repository;

import com.honghuang.game.entity.Shop;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商店数据访问层
 */
@Repository
public interface ShopRepository extends JpaRepository<Shop, Integer> {

    /**
     * 查找激活的商店
     */
    List<Shop> findByActiveOrderByTypeAscNameAsc(Integer active);

    /**
     * 根据类型查找商店
     */
    List<Shop> findByTypeAndActiveOrderByNameAsc(Integer type, Integer active);

    /**
     * 根据场景ID查找商店
     */
    List<Shop> findBySceneIdAndActiveOrderByTypeAsc(Integer sceneId, Integer active);

    /**
     * 根据NPC ID查找商店
     */
    List<Shop> findByNpcIdAndActiveOrderByTypeAsc(Integer npcId, Integer active);

    /**
     * 查找适合角色的商店
     */
    @Query("SELECT s FROM Shop s WHERE s.requiredLevel <= :level " +
           "AND (s.requiredRace = 0 OR s.requiredRace = :race) " +
           "AND (s.requiredSex = 0 OR s.requiredSex = :sex) " +
           "AND s.active = 1 ORDER BY s.type ASC, s.name ASC")
    List<Shop> findSuitableShops(@Param("level") Integer level, @Param("race") Integer race, @Param("sex") Integer sex);

    /**
     * 查找需要刷新的商店
     */
    @Query("SELECT s FROM Shop s WHERE s.active = 1 AND " +
           "(s.lastRefreshTime IS NULL OR s.lastRefreshTime <= :cutoffTime)")
    List<Shop> findShopsNeedingRefresh(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 更新商店刷新时间
     */
    @Modifying
    @Query("UPDATE Shop s SET s.lastRefreshTime = :refreshTime WHERE s.id = :shopId")
    int updateRefreshTime(@Param("shopId") Integer shopId, @Param("refreshTime") LocalDateTime refreshTime);

    /**
     * 根据名称模糊查找商店
     */
    @Query("SELECT s FROM Shop s WHERE s.name LIKE %:name% AND s.active = 1 ORDER BY s.name ASC")
    List<Shop> findByNameContaining(@Param("name") String name);

    /**
     * 查找装备商店
     */
    @Query("SELECT s FROM Shop s WHERE s.type = 1 AND s.active = 1 ORDER BY s.name ASC")
    List<Shop> findEquipmentShops();

    /**
     * 查找药品商店
     */
    @Query("SELECT s FROM Shop s WHERE s.type = 2 AND s.active = 1 ORDER BY s.name ASC")
    List<Shop> findPotionShops();

    /**
     * 查找杂货商店
     */
    @Query("SELECT s FROM Shop s WHERE s.type = 3 AND s.active = 1 ORDER BY s.name ASC")
    List<Shop> findGeneralShops();

    /**
     * 查找技能商店
     */
    @Query("SELECT s FROM Shop s WHERE s.type = 4 AND s.active = 1 ORDER BY s.name ASC")
    List<Shop> findSkillShops();

    /**
     * 查找特殊商店
     */
    @Query("SELECT s FROM Shop s WHERE s.type = 5 AND s.active = 1 ORDER BY s.name ASC")
    List<Shop> findSpecialShops();
}
