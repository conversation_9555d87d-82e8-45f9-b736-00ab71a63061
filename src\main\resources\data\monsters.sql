-- 怪物数据初始化
-- 插入基础怪物数据

-- 新手区域怪物 (1-5级)
INSERT INTO npc_info (npc_id, npc_name, npc_type, npc_level, npc_hp, npc_attack, npc_defense, npc_agility, exp_reward, copper_reward, npc_description, is_active) VALUES (1, '野兔', 1, 1, 30, 5, 2, 8, 5, 2, '温顺的野兔，不会主动攻击，适合新手练习。', 1);

INSERT INTO npc_info (npc_id, npc_name, npc_type, npc_level, npc_hp, npc_attack, npc_defense, npc_agility, exp_reward, copper_reward, npc_description, is_active) VALUES (2, '野狼', 1, 2, 60, 12, 5, 6, 15, 8, '凶猛的野狼，会主动攻击靠近的冒险者。', 1);

INSERT INTO npc_info (npc_id, npc_name, npc_type, npc_level, npc_hp, npc_attack, npc_defense, npc_agility, exp_reward, copper_reward, npc_description, is_active) VALUES (3, '田鼠', 1, 1, 25, 4, 1, 10, 3, 1, '胆小的田鼠，速度很快。', 1);

INSERT INTO npc_info (npc_id, npc_name, npc_type, npc_level, npc_hp, npc_attack, npc_defense, npc_agility, exp_reward, copper_reward, npc_description, is_active) VALUES (4, '毒蛇', 1, 2, 40, 8, 3, 12, 10, 5, '危险的毒蛇，攻击带有毒性。', 1);

INSERT INTO npc_info (npc_id, npc_name, npc_type, npc_level, npc_hp, npc_attack, npc_defense, npc_agility, exp_reward, copper_reward, npc_description, is_active) VALUES (5, '土匪', 1, 3, 80, 15, 8, 5, 25, 15, '山路上的土匪，装备简陋但经验丰富。', 1);
