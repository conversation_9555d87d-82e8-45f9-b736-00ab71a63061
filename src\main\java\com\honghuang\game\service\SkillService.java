package com.honghuang.game.service;

import com.honghuang.game.entity.PlayerRole;
import com.honghuang.game.entity.PlayerSkill;
import com.honghuang.game.entity.Skill;
import com.honghuang.game.exception.BusinessException;
import com.honghuang.game.repository.PlayerSkillRepository;
import com.honghuang.game.repository.SkillRepository;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 技能服务类
 */
@Slf4j
@Service
@Transactional
public class SkillService {

    @Autowired
    private SkillRepository skillRepository;

    @Autowired
    private PlayerSkillRepository playerSkillRepository;

    @Autowired
    private PlayerRoleService playerRoleService;

    /**
     * 获取所有可用技能
     */
    @Transactional(readOnly = true)
    public List<Skill> getAllSkills() {
        return skillRepository.findByActiveOrderByTypeAscRequiredLevelAsc(1);
    }

    /**
     * 根据角色获取可学习的技能
     */
    @Transactional(readOnly = true)
    public List<Skill> getLearnableSkills(Integer roleId) {
        PlayerRole role = playerRoleService.findById(roleId);
        return skillRepository.findSuitableSkills(role.getLevel(), role.getRace(), role.getSex());
    }

    /**
     * 获取角色已学习的技能
     */
    @Transactional(readOnly = true)
    public List<PlayerSkill> getPlayerSkills(Integer roleId) {
        return playerSkillRepository.findByRoleIdOrderBySkillIdAsc(roleId);
    }

    /**
     * 学习技能
     */
    public PlayerSkill learnSkill(Integer roleId, Integer skillId) {
        System.out.println("学习技能: roleId=" + roleId + ", skillId=" + skillId);

        PlayerRole role = playerRoleService.findById(roleId);
        Skill skill = skillRepository.findById(skillId)
                .orElseThrow(() -> new BusinessException(404, "技能不存在"));

        // 检查是否已学习
        if (playerSkillRepository.existsByRoleIdAndSkillId(roleId, skillId)) {
            throw new BusinessException(400, "已经学习过该技能");
        }

        // 检查学习条件
        if (!skill.canLearn(role)) {
            throw new BusinessException(400, "不满足学习条件");
        }

        // 检查前置技能
        if (skill.getPrerequisiteSkillId() != null) {
            Optional<PlayerSkill> prerequisite = playerSkillRepository.findByRoleIdAndSkillId(
                    roleId, skill.getPrerequisiteSkillId());
            if (!prerequisite.isPresent() || 
                prerequisite.get().getSkillLevel() < skill.getPrerequisiteSkillLevel()) {
                throw new BusinessException(400, "前置技能等级不足");
            }
        }

        // 检查学习费用
        if (!role.spendCopper(skill.getLearnCost())) {
            throw new BusinessException(400, "金钱不足");
        }

        // 创建玩家技能
        PlayerSkill playerSkill = new PlayerSkill();
        playerSkill.setRoleId(roleId);
        playerSkill.setSkillId(skillId);
        playerSkill.setSkillLevel(1);
        playerSkill.setSkillExp(0L);

        playerSkill = playerSkillRepository.save(playerSkill);
        playerRoleService.findById(roleId); // 更新角色金钱

        log.info("技能学习成功: roleId={}, skillId={}, skillName={}", 
                roleId, skillId, skill.getName());

        return playerSkill;
    }

    /**
     * 升级技能
     */
    public boolean upgradeSkill(Integer roleId, Integer skillId) {
        log.info("升级技能: roleId={}, skillId={}", roleId, skillId);

        PlayerRole role = playerRoleService.findById(roleId);
        PlayerSkill playerSkill = playerSkillRepository.findByRoleIdAndSkillId(roleId, skillId)
                .orElseThrow(() -> new BusinessException(404, "未学习该技能"));

        if (!playerSkill.canUpgrade()) {
            throw new BusinessException(400, "技能经验不足或已达最高等级");
        }

        // 检查升级费用
        Long upgradeCost = playerSkill.getUpgradeCost();
        if (!role.spendCopper(upgradeCost)) {
            throw new BusinessException(400, "金钱不足");
        }

        // 升级技能
        boolean success = playerSkill.upgrade();
        if (success) {
            playerSkillRepository.save(playerSkill);
            log.info("技能升级成功: roleId={}, skillId={}, newLevel={}", 
                    roleId, skillId, playerSkill.getSkillLevel());
        }

        return success;
    }

    /**
     * 使用技能
     */
    public boolean useSkill(Integer roleId, Integer skillId, Integer targetId) {
        log.info("使用技能: roleId={}, skillId={}, targetId={}", roleId, skillId, targetId);

        PlayerRole role = playerRoleService.findById(roleId);
        PlayerSkill playerSkill = playerSkillRepository.findByRoleIdAndSkillId(roleId, skillId)
                .orElseThrow(() -> new BusinessException(404, "未学习该技能"));

        // 检查使用条件
        if (!playerSkill.canUse(role)) {
            throw new BusinessException(400, "技能冷却中或MP不足");
        }

        // 消耗MP
        int mpCost = playerSkill.getCurrentMpCost();
        role.consumeMp(mpCost);

        // 标记技能使用
        playerSkill.use();
        playerSkillRepository.save(playerSkill);

        // 增加技能经验
        addSkillExp(playerSkill, 10L);

        // 根据技能类型执行效果
        executeSkillEffect(playerSkill, role, targetId);

        log.info("技能使用成功: roleId={}, skillId={}, mpCost={}", roleId, skillId, mpCost);
        return true;
    }

    /**
     * 设置技能快捷键
     */
    public void setSkillShortcut(Integer roleId, Integer skillId, Integer shortcutKey) {
        log.info("设置技能快捷键: roleId={}, skillId={}, shortcutKey={}", roleId, skillId, shortcutKey);

        if (shortcutKey < 0 || shortcutKey > 9) {
            throw new BusinessException(400, "快捷键范围为0-9");
        }

        PlayerSkill playerSkill = playerSkillRepository.findByRoleIdAndSkillId(roleId, skillId)
                .orElseThrow(() -> new BusinessException(404, "未学习该技能"));

        // 清除该快捷键位置的其他技能
        if (shortcutKey > 0) {
            playerSkillRepository.clearShortcutKey(roleId, shortcutKey);
        }

        // 设置新的快捷键
        playerSkill.setShortcut(shortcutKey);
        playerSkillRepository.save(playerSkill);

        log.info("技能快捷键设置成功: roleId={}, skillId={}, shortcutKey={}", roleId, skillId, shortcutKey);
    }

    /**
     * 获取角色的快捷键技能
     */
    @Transactional(readOnly = true)
    public List<PlayerSkill> getShortcutSkills(Integer roleId) {
        return playerSkillRepository.findShortcutSkillsByRoleId(roleId);
    }

    /**
     * 根据技能类型获取角色技能
     */
    @Transactional(readOnly = true)
    public List<PlayerSkill> getSkillsByType(Integer roleId, Integer type) {
        return playerSkillRepository.findByRoleIdAndSkillType(roleId, type);
    }

    /**
     * 增加技能经验
     */
    public void addSkillExp(PlayerSkill playerSkill, Long exp) {
        playerSkill.addExp(exp);
        
        // 检查是否可以自动升级
        while (playerSkill.canUpgrade()) {
            playerSkill.upgrade();
            log.info("技能自动升级: skillId={}, newLevel={}", 
                    playerSkill.getSkillId(), playerSkill.getSkillLevel());
        }
        
        playerSkillRepository.save(playerSkill);
    }

    /**
     * 执行技能效果
     */
    private void executeSkillEffect(PlayerSkill playerSkill, PlayerRole caster, Integer targetId) {
        Skill skill = playerSkill.getSkill();
        if (skill == null) return;

        switch (skill.getType()) {
            case 1: // 攻击技能
                executeAttackSkill(playerSkill, caster, targetId);
                break;
            case 2: // 防御技能
                executeDefenseSkill(playerSkill, caster);
                break;
            case 3: // 治疗技能
                executeHealSkill(playerSkill, caster, targetId);
                break;
            case 4: // 辅助技能
                executeSupportSkill(playerSkill, caster, targetId);
                break;
            case 5: // 生活技能
                executeLifeSkill(playerSkill, caster);
                break;
        }
    }

    /**
     * 执行攻击技能
     */
    private void executeAttackSkill(PlayerSkill playerSkill, PlayerRole caster, Integer targetId) {
        int damage = playerSkill.calculateDamage(caster);
        log.info("攻击技能造成伤害: damage={}", damage);
        // TODO: 实现对目标造成伤害的逻辑
    }

    /**
     * 执行防御技能
     */
    private void executeDefenseSkill(PlayerSkill playerSkill, PlayerRole caster) {
        int defenseBonus = playerSkill.getCurrentValue();
        log.info("防御技能提升防御: defenseBonus={}", defenseBonus);
        // TODO: 实现临时提升防御力的逻辑
    }

    /**
     * 执行治疗技能
     */
    private void executeHealSkill(PlayerSkill playerSkill, PlayerRole caster, Integer targetId) {
        int healAmount = playerSkill.calculateHeal(caster);
        
        if (targetId == null || targetId.equals(caster.getId())) {
            // 治疗自己
            caster.restoreHp(healAmount);
            playerRoleService.findById(caster.getId()); // 保存角色状态
            log.info("治疗技能恢复生命值: healAmount={}", healAmount);
        } else {
            // TODO: 实现治疗其他玩家的逻辑
            log.info("治疗其他玩家: targetId={}, healAmount={}", targetId, healAmount);
        }
    }

    /**
     * 执行辅助技能
     */
    private void executeSupportSkill(PlayerSkill playerSkill, PlayerRole caster, Integer targetId) {
        int supportValue = playerSkill.getCurrentValue();
        log.info("辅助技能效果: supportValue={}", supportValue);
        // TODO: 实现各种辅助效果的逻辑
    }

    /**
     * 执行生活技能
     */
    private void executeLifeSkill(PlayerSkill playerSkill, PlayerRole caster) {
        int lifeValue = playerSkill.getCurrentValue();
        log.info("生活技能效果: lifeValue={}", lifeValue);
        // TODO: 实现生活技能的逻辑（如制作、采集等）
    }
}
