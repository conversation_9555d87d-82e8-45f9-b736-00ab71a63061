package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.Npc;
import com.honghuang.game.entity.NpcDialogue;
import com.honghuang.game.service.NpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * NPC控制器
 */
@RestController
@RequestMapping("/api/npc")
public class NpcController {

    @Autowired
    private NpcService npcService;

    /**
     * 获取所有激活的NPC
     */
    @GetMapping("/list")
    public Result<List<Npc>> getAllActiveNpcs() {
        try {
            List<Npc> npcs = npcService.getAllActiveNpcs();
            return Result.success("获取NPC列表成功", npcs);
        } catch (Exception e) {
            return Result.error(500, "获取NPC列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据场景ID获取NPC列表
     */
    @GetMapping("/scene/{sceneId}")
    public Result<List<Npc>> getNpcsByScene(@PathVariable Integer sceneId) {
        try {
            List<Npc> npcs = npcService.getNpcsByScene(sceneId);
            return Result.success("获取场景NPC列表成功", npcs);
        } catch (Exception e) {
            return Result.error(500, "获取场景NPC列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取NPC列表
     */
    @GetMapping("/type/{type}")
    public Result<List<Npc>> getNpcsByType(@PathVariable Integer type) {
        try {
            List<Npc> npcs = npcService.getNpcsByType(type);
            return Result.success("获取类型NPC列表成功", npcs);
        } catch (Exception e) {
            return Result.error(500, "获取类型NPC列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取NPC详情
     */
    @GetMapping("/{npcId}")
    public Result<Npc> getNpcDetail(@PathVariable Integer npcId) {
        try {
            Npc npc = npcService.getNpcDetail(npcId);
            return Result.success("获取NPC详情成功", npc);
        } catch (Exception e) {
            return Result.error(500, "获取NPC详情失败: " + e.getMessage());
        }
    }

    /**
     * 查找范围内的NPC
     */
    @GetMapping("/range")
    public Result<List<Npc>> getNpcsInRange(@RequestParam Integer sceneId,
                                           @RequestParam Integer x,
                                           @RequestParam Integer y,
                                           @RequestParam Integer range) {
        try {
            List<Npc> npcs = npcService.getNpcsInRange(sceneId, x, y, range);
            return Result.success("获取范围内NPC成功", npcs);
        } catch (Exception e) {
            return Result.error(500, "获取范围内NPC失败: " + e.getMessage());
        }
    }

    /**
     * 查找最近的NPC
     */
    @GetMapping("/nearest")
    public Result<List<Npc>> getNearestNpcs(@RequestParam Integer sceneId,
                                           @RequestParam Integer x,
                                           @RequestParam Integer y) {
        try {
            List<Npc> npcs = npcService.getNearestNpcs(sceneId, x, y);
            return Result.success("获取最近NPC成功", npcs);
        } catch (Exception e) {
            return Result.error(500, "获取最近NPC失败: " + e.getMessage());
        }
    }

    /**
     * 与NPC对话
     */
    @PostMapping("/{npcId}/talk")
    public Result<NpcDialogue> talkToNpc(@PathVariable Integer npcId,
                                        @RequestBody Map<String, Object> request,
                                        HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer dialogueType = (Integer) request.get("dialogueType");
            String message = (String) request.get("message");
            
            if (dialogueType == null) {
                dialogueType = 1; // 默认普通对话
            }
            
            NpcDialogue dialogue = npcService.talkToNpc(currentRoleId, npcId, dialogueType, message);
            return Result.success("对话成功", dialogue);
        } catch (Exception e) {
            return Result.error(500, "对话失败: " + e.getMessage());
        }
    }

    /**
     * 选择对话选项
     */
    @PostMapping("/dialogue/{dialogueId}/select")
    public Result<Void> selectDialogueOption(@PathVariable Integer dialogueId,
                                            @RequestBody Map<String, Integer> request,
                                            HttpServletRequest httpRequest) {
        try {
            HttpSession session = httpRequest.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Integer optionIndex = request.get("optionIndex");
            
            if (optionIndex == null) {
                return Result.error(400, "选项索引不能为空");
            }
            
            boolean success = npcService.selectDialogueOption(currentRoleId, dialogueId, optionIndex);
            if (success) {
                return Result.success("选择选项成功");
            } else {
                return Result.error(400, "选择选项失败");
            }
        } catch (Exception e) {
            return Result.error(500, "选择选项失败: " + e.getMessage());
        }
    }

    /**
     * 获取对话历史
     */
    @GetMapping("/dialogue/history")
    public Result<List<NpcDialogue>> getDialogueHistory(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<NpcDialogue> dialogues = npcService.getDialogueHistory(currentRoleId);
            return Result.success("获取对话历史成功", dialogues);
        } catch (Exception e) {
            return Result.error(500, "获取对话历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取与指定NPC的对话历史
     */
    @GetMapping("/{npcId}/dialogue/history")
    public Result<List<NpcDialogue>> getDialogueHistoryWithNpc(@PathVariable Integer npcId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<NpcDialogue> dialogues = npcService.getDialogueHistoryWithNpc(currentRoleId, npcId);
            return Result.success("获取NPC对话历史成功", dialogues);
        } catch (Exception e) {
            return Result.error(500, "获取NPC对话历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的对话记录
     */
    @GetMapping("/dialogue/recent")
    public Result<List<NpcDialogue>> getRecentDialogues(@RequestParam(defaultValue = "24") Integer hours,
                                                       HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<NpcDialogue> dialogues = npcService.getRecentDialogues(currentRoleId, hours);
            return Result.success("获取最近对话记录成功", dialogues);
        } catch (Exception e) {
            return Result.error(500, "获取最近对话记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取商店NPC列表
     */
    @GetMapping("/shop")
    public Result<List<Npc>> getShopNpcs() {
        try {
            List<Npc> npcs = npcService.getShopNpcs();
            return Result.success("获取商店NPC列表成功", npcs);
        } catch (Exception e) {
            return Result.error(500, "获取商店NPC列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务NPC列表
     */
    @GetMapping("/quest")
    public Result<List<Npc>> getQuestNpcs() {
        try {
            List<Npc> npcs = npcService.getQuestNpcs();
            return Result.success("获取任务NPC列表成功", npcs);
        } catch (Exception e) {
            return Result.error(500, "获取任务NPC列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取功能NPC列表
     */
    @GetMapping("/function")
    public Result<List<Npc>> getFunctionNpcs() {
        try {
            List<Npc> npcs = npcService.getFunctionNpcs();
            return Result.success("获取功能NPC列表成功", npcs);
        } catch (Exception e) {
            return Result.error(500, "获取功能NPC列表失败: " + e.getMessage());
        }
    }

    /**
     * 搜索NPC
     */
    @GetMapping("/search")
    public Result<List<Npc>> searchNpcs(@RequestParam String keyword) {
        try {
            List<Npc> npcs = npcService.searchNpcs(keyword);
            return Result.success("搜索NPC成功", npcs);
        } catch (Exception e) {
            return Result.error(500, "搜索NPC失败: " + e.getMessage());
        }
    }

    /**
     * 获取NPC统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getNpcStatistics() {
        try {
            Map<String, Object> stats = npcService.getNpcStatistics();
            return Result.success("获取NPC统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取NPC统计失败: " + e.getMessage());
        }
    }
}
