package com.honghuang.game.repository;

import com.honghuang.game.entity.Quest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 任务数据访问层
 */
@Repository
public interface QuestRepository extends JpaRepository<Quest, Integer> {

    /**
     * 查找激活的任务
     */
    List<Quest> findByActiveOrderByRequiredLevelAscTypeAsc(Integer active);

    /**
     * 根据类型查找任务
     */
    List<Quest> findByTypeAndActiveOrderByRequiredLevelAsc(Integer type, Integer active);

    /**
     * 根据等级范围查找任务
     */
    @Query("SELECT q FROM Quest q WHERE q.requiredLevel BETWEEN :minLevel AND :maxLevel AND q.active = 1 ORDER BY q.requiredLevel ASC")
    List<Quest> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 查找适合角色的任务
     */
    @Query("SELECT q FROM Quest q WHERE q.requiredLevel <= :level " +
           "AND (q.requiredRace = 0 OR q.requiredRace = :race) " +
           "AND (q.requiredSex = 0 OR q.requiredSex = :sex) " +
           "AND q.active = 1 ORDER BY q.requiredLevel ASC, q.type ASC")
    List<Quest> findSuitableQuests(@Param("level") Integer level, @Param("race") Integer race, @Param("sex") Integer sex);

    /**
     * 根据前置任务查找任务
     */
    List<Quest> findByPrerequisiteQuestIdAndActiveOrderByRequiredLevelAsc(Integer prerequisiteQuestId, Integer active);

    /**
     * 查找无前置任务的任务
     */
    @Query("SELECT q FROM Quest q WHERE q.prerequisiteQuestId IS NULL AND q.active = 1 ORDER BY q.requiredLevel ASC, q.type ASC")
    List<Quest> findBasicQuests();

    /**
     * 根据NPC查找任务
     */
    List<Quest> findByGiverNpcIdAndActiveOrderByRequiredLevelAsc(Integer giverNpcId, Integer active);

    /**
     * 根据完成NPC查找任务
     */
    List<Quest> findByFinisherNpcIdAndActiveOrderByRequiredLevelAsc(Integer finisherNpcId, Integer active);

    /**
     * 查找杀怪任务
     */
    @Query("SELECT q FROM Quest q WHERE q.type = 1 AND q.active = 1 ORDER BY q.requiredLevel ASC")
    List<Quest> findKillQuests();

    /**
     * 查找收集任务
     */
    @Query("SELECT q FROM Quest q WHERE q.type = 2 AND q.active = 1 ORDER BY q.requiredLevel ASC")
    List<Quest> findCollectQuests();

    /**
     * 查找对话任务
     */
    @Query("SELECT q FROM Quest q WHERE q.type = 3 AND q.active = 1 ORDER BY q.requiredLevel ASC")
    List<Quest> findTalkQuests();

    /**
     * 查找护送任务
     */
    @Query("SELECT q FROM Quest q WHERE q.type = 4 AND q.active = 1 ORDER BY q.requiredLevel ASC")
    List<Quest> findEscortQuests();

    /**
     * 查找探索任务
     */
    @Query("SELECT q FROM Quest q WHERE q.type = 5 AND q.active = 1 ORDER BY q.requiredLevel ASC")
    List<Quest> findExploreQuests();

    /**
     * 查找可重复任务
     */
    @Query("SELECT q FROM Quest q WHERE q.repeatable = 1 AND q.active = 1 ORDER BY q.requiredLevel ASC")
    List<Quest> findRepeatableQuests();

    /**
     * 根据目标类型和目标ID查找任务
     */
    @Query("SELECT q FROM Quest q WHERE q.objectiveType = :objectiveType AND q.objectiveId = :objectiveId AND q.active = 1")
    List<Quest> findByObjective(@Param("objectiveType") Integer objectiveType, @Param("objectiveId") Integer objectiveId);

    /**
     * 根据名称模糊查找任务
     */
    @Query("SELECT q FROM Quest q WHERE q.name LIKE %:name% AND q.active = 1 ORDER BY q.requiredLevel ASC")
    List<Quest> findByNameContaining(@Param("name") String name);

    /**
     * 查找指定等级的任务
     */
    List<Quest> findByRequiredLevelAndActiveOrderByTypeAsc(Integer requiredLevel, Integer active);

    /**
     * 查找低于指定等级的任务
     */
    @Query("SELECT q FROM Quest q WHERE q.requiredLevel < :level AND q.active = 1 ORDER BY q.requiredLevel DESC")
    List<Quest> findByLevelLessThan(@Param("level") Integer level);

    /**
     * 查找高于指定等级的任务
     */
    @Query("SELECT q FROM Quest q WHERE q.requiredLevel > :level AND q.active = 1 ORDER BY q.requiredLevel ASC")
    List<Quest> findByLevelGreaterThan(@Param("level") Integer level);
}
