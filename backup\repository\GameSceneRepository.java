package com.honghuang.game.repository;

import com.honghuang.game.entity.GameScene;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 游戏场景Repository
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface GameSceneRepository extends JpaRepository<GameScene, String> {

    /**
     * 根据场景名称查找
     */
    Optional<GameScene> findByName(String name);

    /**
     * 根据场景类型查找
     */
    List<GameScene> findByType(Integer type);

    /**
     * 根据是否激活查找
     */
    List<GameScene> findByIsActive(Integer isActive);

    /**
     * 查找激活的场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.isActive = 1")
    List<GameScene> findActiveScenes();

    /**
     * 根据等级限制查找适合的场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.levelLimit <= :level AND gs.isActive = 1")
    List<GameScene> findSuitableScenes(@Param("level") Integer level);

    /**
     * 查找城镇场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.type = 3 AND gs.isActive = 1")
    List<GameScene> findTowns();

    /**
     * 查找副本场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.type = 2 AND gs.isActive = 1")
    List<GameScene> findDungeons();

    /**
     * 查找野外场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.type = 4 AND gs.isActive = 1")
    List<GameScene> findWilderness();

    /**
     * 查找安全区域
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.isSafeZone = 1 AND gs.isActive = 1")
    List<GameScene> findSafeZones();

    /**
     * 查找允许PK的场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.allowPk = 1 AND gs.isActive = 1")
    List<GameScene> findPkScenes();

    /**
     * 查找连接到指定场景的场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE " +
           "(gs.northScene = :sceneId OR gs.southScene = :sceneId OR " +
           "gs.eastScene = :sceneId OR gs.westScene = :sceneId OR " +
           "gs.upScene = :sceneId OR gs.downScene = :sceneId) AND gs.isActive = 1")
    List<GameScene> findConnectedScenes(@Param("sceneId") String sceneId);

    /**
     * 根据场景名称模糊查找
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.name LIKE %:name% AND gs.isActive = 1")
    List<GameScene> findByNameContaining(@Param("name") String name);

    /**
     * 根据等级范围查找场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.levelLimit BETWEEN :minLevel AND :maxLevel AND gs.isActive = 1")
    List<GameScene> findByLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 查找有怪物的场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.monsterConfig IS NOT NULL AND gs.monsterConfig != '' AND gs.isActive = 1")
    List<GameScene> findScenesWithMonsters();

    /**
     * 查找有NPC的场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.npcConfig IS NOT NULL AND gs.npcConfig != '' AND gs.isActive = 1")
    List<GameScene> findScenesWithNpcs();

    /**
     * 查找有传送点的场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.teleportConfig IS NOT NULL AND gs.teleportConfig != '' AND gs.isActive = 1")
    List<GameScene> findScenesWithTeleports();

    /**
     * 查找有特殊事件的场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.eventConfig IS NOT NULL AND gs.eventConfig != '' AND gs.isActive = 1")
    List<GameScene> findScenesWithEvents();

    /**
     * 根据天气类型查找场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.weatherType = :weatherType AND gs.isActive = 1")
    List<GameScene> findByWeatherType(@Param("weatherType") Integer weatherType);

    /**
     * 根据时间类型查找场景
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.timeType = :timeType AND gs.isActive = 1")
    List<GameScene> findByTimeType(@Param("timeType") Integer timeType);

    /**
     * 统计各类型场景数量
     */
    @Query("SELECT gs.type, COUNT(gs) FROM GameScene gs WHERE gs.isActive = 1 GROUP BY gs.type")
    List<Object[]> countByType();

    /**
     * 查找新手场景（等级限制较低）
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.levelLimit <= 10 AND gs.isActive = 1 ORDER BY gs.levelLimit")
    List<GameScene> findNewbieScenes();

    /**
     * 查找高级场景（等级限制较高）
     */
    @Query("SELECT gs FROM GameScene gs WHERE gs.levelLimit >= 50 AND gs.isActive = 1 ORDER BY gs.levelLimit DESC")
    List<GameScene> findHighLevelScenes();

    /**
     * 根据激活状态统计场景数量
     */
    List<GameScene> countByIsActive(Integer isActive);
}
