package com.honghuang.game.repository;

import com.honghuang.game.entity.TeleportPoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 传送点数据访问层
 */
@Repository
public interface TeleportPointRepository extends JpaRepository<TeleportPoint, Integer> {

    /**
     * 根据场景ID查找传送点
     */
    List<TeleportPoint> findBySceneIdAndActiveOrderByNameAsc(Integer sceneId, Integer active);

    /**
     * 根据目标场景ID查找传送点
     */
    List<TeleportPoint> findByTargetSceneIdAndActiveOrderByNameAsc(Integer targetSceneId, Integer active);

    /**
     * 根据传送类型查找传送点
     */
    List<TeleportPoint> findByTeleportTypeAndActiveOrderBySceneIdAsc(Integer teleportType, Integer active);

    /**
     * 查找激活的传送点
     */
    List<TeleportPoint> findByActiveOrderBySceneIdAscNameAsc(Integer active);

    /**
     * 查找双向传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.teleportType = 1 AND tp.active = 1 ORDER BY tp.sceneId ASC")
    List<TeleportPoint> findBidirectionalTeleportPoints();

    /**
     * 查找付费传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.teleportType = 3 AND tp.active = 1 ORDER BY tp.cost ASC")
    List<TeleportPoint> findPaidTeleportPoints();

    /**
     * 查找任务传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.teleportType = 4 AND tp.active = 1 ORDER BY tp.requiredLevel ASC")
    List<TeleportPoint> findQuestTeleportPoints();

    /**
     * 查找适合角色等级的传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.requiredLevel <= :level AND tp.active = 1 ORDER BY tp.sceneId ASC")
    List<TeleportPoint> findSuitableTeleportPoints(@Param("level") Integer level);

    /**
     * 根据位置范围查找传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.sceneId = :sceneId AND tp.active = 1 " +
           "AND ABS(tp.positionX - :x) + ABS(tp.positionY - :y) <= :range " +
           "ORDER BY (ABS(tp.positionX - :x) + ABS(tp.positionY - :y)) ASC")
    List<TeleportPoint> findTeleportPointsInRange(@Param("sceneId") Integer sceneId, @Param("x") Integer x, 
                                                  @Param("y") Integer y, @Param("range") Integer range);

    /**
     * 查找最近的传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.sceneId = :sceneId AND tp.active = 1 " +
           "ORDER BY (ABS(tp.positionX - :x) + ABS(tp.positionY - :y)) ASC")
    List<TeleportPoint> findNearestTeleportPoints(@Param("sceneId") Integer sceneId, @Param("x") Integer x, @Param("y") Integer y);

    /**
     * 根据费用范围查找传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.cost BETWEEN :minCost AND :maxCost AND tp.active = 1 ORDER BY tp.cost ASC")
    List<TeleportPoint> findByFeeRange(@Param("minCost") Long minCost, @Param("maxCost") Long maxCost);

    /**
     * 查找免费传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE (tp.cost IS NULL OR tp.cost = 0) AND tp.active = 1 ORDER BY tp.sceneId ASC")
    List<TeleportPoint> findFreeTeleportPoints();

    /**
     * 根据货币类型查找传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.currencyType = :currencyType AND tp.cost > 0 AND tp.active = 1 ORDER BY tp.cost ASC")
    List<TeleportPoint> findByCurrencyType(@Param("currencyType") Integer currencyType);

    /**
     * 查找有冷却时间的传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.cooldown > 0 AND tp.active = 1 ORDER BY tp.cooldown ASC")
    List<TeleportPoint> findTeleportPointsWithCooldown();

    /**
     * 查找无冷却时间的传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE (tp.cooldown IS NULL OR tp.cooldown = 0) AND tp.active = 1 ORDER BY tp.sceneId ASC")
    List<TeleportPoint> findTeleportPointsWithoutCooldown();

    /**
     * 根据名称模糊查找传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.name LIKE %:name% AND tp.active = 1 ORDER BY tp.name ASC")
    List<TeleportPoint> findByNameContaining(@Param("name") String name);

    /**
     * 查找连接两个场景的传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.sceneId = :sceneId AND tp.targetSceneId = :targetSceneId AND tp.active = 1")
    List<TeleportPoint> findConnectionBetweenScenes(@Param("sceneId") Integer sceneId, @Param("targetSceneId") Integer targetSceneId);

    /**
     * 统计场景的传送点数量
     */
    @Query("SELECT COUNT(tp) FROM TeleportPoint tp WHERE tp.sceneId = :sceneId AND tp.active = 1")
    long countBySceneId(@Param("sceneId") Integer sceneId);

    /**
     * 统计指定类型的传送点数量
     */
    @Query("SELECT COUNT(tp) FROM TeleportPoint tp WHERE tp.teleportType = :teleportType AND tp.active = 1")
    long countByTeleportType(@Param("teleportType") Integer teleportType);

    /**
     * 查找需要特定任务的传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.requiredQuestId = :questId AND tp.active = 1")
    List<TeleportPoint> findByRequiredQuest(@Param("questId") Integer questId);

    /**
     * 查找不需要任务的传送点
     */
    @Query("SELECT tp FROM TeleportPoint tp WHERE tp.requiredQuestId IS NULL AND tp.active = 1 ORDER BY tp.sceneId ASC")
    List<TeleportPoint> findTeleportPointsWithoutQuestRequirement();
}
