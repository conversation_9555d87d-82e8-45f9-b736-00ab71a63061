package com.honghuang.game.controller;

import com.honghuang.game.common.Result;
import com.honghuang.game.entity.PlayerQuest;
import com.honghuang.game.entity.Quest;
import com.honghuang.game.service.QuestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * 任务控制器
 */
@RestController
@RequestMapping("/api/quest")
public class QuestController {

    @Autowired
    private QuestService questService;

    /**
     * 获取所有可用任务
     */
    @GetMapping("/all")
    public Result<List<Quest>> getAllQuests() {
        try {
            List<Quest> quests = questService.getAllQuests();
            return Result.success("获取任务列表成功", quests);
        } catch (Exception e) {
            return Result.error(500, "获取任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色可接取的任务
     */
    @GetMapping("/available")
    public Result<List<Quest>> getAvailableQuests(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<Quest> quests = questService.getAvailableQuests(currentRoleId);
            return Result.success("获取可接取任务成功", quests);
        } catch (Exception e) {
            return Result.error(500, "获取可接取任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色的任务列表
     */
    @GetMapping("/my")
    public Result<List<PlayerQuest>> getPlayerQuests(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<PlayerQuest> quests = questService.getPlayerQuests(currentRoleId);
            return Result.success("获取任务列表成功", quests);
        } catch (Exception e) {
            return Result.error(500, "获取任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色进行中的任务
     */
    @GetMapping("/inprogress")
    public Result<List<PlayerQuest>> getInProgressQuests(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<PlayerQuest> quests = questService.getInProgressQuests(currentRoleId);
            return Result.success("获取进行中任务成功", quests);
        } catch (Exception e) {
            return Result.error(500, "获取进行中任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取角色已完成的任务
     */
    @GetMapping("/completed")
    public Result<List<PlayerQuest>> getCompletedQuests(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<PlayerQuest> quests = questService.getCompletedQuests(currentRoleId);
            return Result.success("获取已完成任务成功", quests);
        } catch (Exception e) {
            return Result.error(500, "获取已完成任务失败: " + e.getMessage());
        }
    }

    /**
     * 接取任务
     */
    @PostMapping("/accept/{questId}")
    public Result<PlayerQuest> acceptQuest(@PathVariable Integer questId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            PlayerQuest playerQuest = questService.acceptQuest(currentRoleId, questId);
            return Result.success("接取任务成功", playerQuest);
        } catch (Exception e) {
            return Result.error(500, "接取任务失败: " + e.getMessage());
        }
    }

    /**
     * 完成任务
     */
    @PostMapping("/complete/{questId}")
    public Result<Void> completeQuest(@PathVariable Integer questId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = questService.completeQuest(currentRoleId, questId);
            if (success) {
                return Result.success("完成任务成功");
            } else {
                return Result.error(400, "完成任务失败");
            }
        } catch (Exception e) {
            return Result.error(500, "完成任务失败: " + e.getMessage());
        }
    }

    /**
     * 放弃任务
     */
    @PostMapping("/abandon/{questId}")
    public Result<Void> abandonQuest(@PathVariable Integer questId, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            boolean success = questService.abandonQuest(currentRoleId, questId);
            if (success) {
                return Result.success("放弃任务成功");
            } else {
                return Result.error(400, "放弃任务失败");
            }
        } catch (Exception e) {
            return Result.error(500, "放弃任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取可完成的任务
     */
    @GetMapping("/completable")
    public Result<List<PlayerQuest>> getCompletableQuests(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<PlayerQuest> quests = questService.getCompletableQuests(currentRoleId);
            return Result.success("获取可完成任务成功", quests);
        } catch (Exception e) {
            return Result.error(500, "获取可完成任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取任务
     */
    @GetMapping("/type/{type}")
    public Result<List<PlayerQuest>> getQuestsByType(@PathVariable Integer type, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            List<PlayerQuest> quests = questService.getQuestsByType(currentRoleId, type);
            return Result.success("获取指定类型任务成功", quests);
        } catch (Exception e) {
            return Result.error(500, "获取指定类型任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getQuestStatistics(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            Integer currentRoleId = (Integer) session.getAttribute("currentRoleId");
            
            if (currentRoleId == null) {
                return Result.error(401, "未选择角色");
            }
            
            Map<String, Object> stats = questService.getQuestStatistics(currentRoleId);
            return Result.success("获取任务统计成功", stats);
        } catch (Exception e) {
            return Result.error(500, "获取任务统计失败: " + e.getMessage());
        }
    }
}
