package com.honghuang.game.repository;

import com.honghuang.game.entity.PlayerQuest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 玩家任务数据访问层
 */
@Repository
public interface PlayerQuestRepository extends JpaRepository<PlayerQuest, Integer> {

    /**
     * 根据角色ID查找任务列表
     */
    List<PlayerQuest> findByRoleIdOrderByAcceptTimeDesc(Integer roleId);

    /**
     * 根据角色ID和任务ID查找任务
     */
    Optional<PlayerQuest> findByRoleIdAndQuestId(Integer roleId, Integer questId);

    /**
     * 检查角色是否已接取某任务
     */
    boolean existsByRoleIdAndQuestId(Integer roleId, Integer questId);

    /**
     * 根据角色ID和状态查找任务
     */
    List<PlayerQuest> findByRoleIdAndStatusOrderByAcceptTimeDesc(Integer roleId, Integer status);

    /**
     * 查找角色进行中的任务
     */
    @Query("SELECT pq FROM PlayerQuest pq WHERE pq.roleId = :roleId AND pq.status = 1 ORDER BY pq.acceptTime DESC")
    List<PlayerQuest> findInProgressQuestsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找角色已完成的任务
     */
    @Query("SELECT pq FROM PlayerQuest pq WHERE pq.roleId = :roleId AND pq.status = 2 ORDER BY pq.completeTime DESC")
    List<PlayerQuest> findCompletedQuestsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找角色已放弃的任务
     */
    @Query("SELECT pq FROM PlayerQuest pq WHERE pq.roleId = :roleId AND pq.status = 3 ORDER BY pq.abandonTime DESC")
    List<PlayerQuest> findAbandonedQuestsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找可以完成的任务
     */
    @Query("SELECT pq FROM PlayerQuest pq JOIN pq.quest q WHERE pq.roleId = :roleId AND pq.status = 1 AND pq.progress >= q.objectiveCount")
    List<PlayerQuest> findCompletableQuestsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 根据任务类型查找角色任务
     */
    @Query("SELECT pq FROM PlayerQuest pq JOIN pq.quest q WHERE pq.roleId = :roleId AND q.type = :type ORDER BY pq.acceptTime DESC")
    List<PlayerQuest> findByRoleIdAndQuestType(@Param("roleId") Integer roleId, @Param("type") Integer type);

    /**
     * 查找角色的杀怪任务
     */
    @Query("SELECT pq FROM PlayerQuest pq JOIN pq.quest q WHERE pq.roleId = :roleId AND q.type = 1 AND pq.status = 1")
    List<PlayerQuest> findKillQuestsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找角色的收集任务
     */
    @Query("SELECT pq FROM PlayerQuest pq JOIN pq.quest q WHERE pq.roleId = :roleId AND q.type = 2 AND pq.status = 1")
    List<PlayerQuest> findCollectQuestsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找角色的对话任务
     */
    @Query("SELECT pq FROM PlayerQuest pq JOIN pq.quest q WHERE pq.roleId = :roleId AND q.type = 3 AND pq.status = 1")
    List<PlayerQuest> findTalkQuestsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找角色的探索任务
     */
    @Query("SELECT pq FROM PlayerQuest pq JOIN pq.quest q WHERE pq.roleId = :roleId AND q.type = 5 AND pq.status = 1")
    List<PlayerQuest> findExploreQuestsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找匹配目标的进行中任务
     */
    @Query("SELECT pq FROM PlayerQuest pq JOIN pq.quest q WHERE pq.roleId = :roleId AND pq.status = 1 " +
           "AND q.objectiveType = :objectiveType AND q.objectiveId = :objectiveId")
    List<PlayerQuest> findMatchingQuests(@Param("roleId") Integer roleId, 
                                        @Param("objectiveType") Integer objectiveType, 
                                        @Param("objectiveId") Integer objectiveId);

    /**
     * 更新任务进度
     */
    @Modifying
    @Query("UPDATE PlayerQuest pq SET pq.progress = :progress WHERE pq.id = :id")
    int updateProgress(@Param("id") Integer id, @Param("progress") Integer progress);

    /**
     * 更新任务状态
     */
    @Modifying
    @Query("UPDATE PlayerQuest pq SET pq.status = :status WHERE pq.id = :id")
    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);

    /**
     * 完成任务
     */
    @Modifying
    @Query("UPDATE PlayerQuest pq SET pq.status = 2, pq.completeTime = :completeTime, pq.lastCompleteTime = :completeTime WHERE pq.id = :id")
    int completeQuest(@Param("id") Integer id, @Param("completeTime") LocalDateTime completeTime);

    /**
     * 放弃任务
     */
    @Modifying
    @Query("UPDATE PlayerQuest pq SET pq.status = 3, pq.abandonTime = :abandonTime WHERE pq.id = :id")
    int abandonQuest(@Param("id") Integer id, @Param("abandonTime") LocalDateTime abandonTime);

    /**
     * 重置任务（用于可重复任务）
     */
    @Modifying
    @Query("UPDATE PlayerQuest pq SET pq.status = 1, pq.progress = 0, pq.acceptTime = :acceptTime, " +
           "pq.completeTime = NULL, pq.abandonTime = NULL WHERE pq.id = :id")
    int resetQuest(@Param("id") Integer id, @Param("acceptTime") LocalDateTime acceptTime);

    /**
     * 统计角色任务数量
     */
    @Query("SELECT COUNT(pq) FROM PlayerQuest pq WHERE pq.roleId = :roleId")
    long countByRoleId(@Param("roleId") Integer roleId);

    /**
     * 统计角色指定状态任务数量
     */
    @Query("SELECT COUNT(pq) FROM PlayerQuest pq WHERE pq.roleId = :roleId AND pq.status = :status")
    long countByRoleIdAndStatus(@Param("roleId") Integer roleId, @Param("status") Integer status);

    /**
     * 统计角色完成的任务数量
     */
    @Query("SELECT COUNT(pq) FROM PlayerQuest pq WHERE pq.roleId = :roleId AND pq.status = 2")
    long countCompletedQuestsByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找角色最近完成的任务
     */
    @Query("SELECT pq FROM PlayerQuest pq WHERE pq.roleId = :roleId AND pq.status = 2 ORDER BY pq.completeTime DESC")
    List<PlayerQuest> findRecentCompletedQuests(@Param("roleId") Integer roleId);

    /**
     * 查找可重新接取的任务
     */
    @Query("SELECT pq FROM PlayerQuest pq JOIN pq.quest q WHERE pq.roleId = :roleId AND pq.status = 2 " +
           "AND q.repeatable = 1 AND (pq.lastCompleteTime IS NULL OR " +
           "pq.lastCompleteTime <= :cutoffTime)")
    List<PlayerQuest> findReacceptableQuests(@Param("roleId") Integer roleId, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除角色的所有任务
     */
    @Modifying
    @Query("DELETE FROM PlayerQuest pq WHERE pq.roleId = :roleId")
    int deleteByRoleId(@Param("roleId") Integer roleId);

    /**
     * 查找超时的任务（可选功能）
     */
    @Query("SELECT pq FROM PlayerQuest pq WHERE pq.status = 1 AND pq.acceptTime <= :timeoutTime")
    List<PlayerQuest> findTimeoutQuests(@Param("timeoutTime") LocalDateTime timeoutTime);
}
