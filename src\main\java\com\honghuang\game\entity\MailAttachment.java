package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 邮件附件实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "mail_attachment")
public class MailAttachment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 邮件ID
     */
    @Column(name = "mail_id")
    private Integer mailId;

    /**
     * 物品ID
     */
    @Column(name = "item_id")
    private Integer itemId;

    /**
     * 物品数量
     */
    @Column(name = "quantity")
    private Integer quantity;

    /**
     * 是否已领取 0未领取 1已领取
     */
    @Column(name = "claimed")
    private Integer claimed = 0;

    /**
     * 领取时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "claim_time")
    private LocalDateTime claimTime;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 邮件信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mail_id", insertable = false, updatable = false)
    private Mail mail;

    /**
     * 物品信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "item_id", insertable = false, updatable = false)
    private Item item;

    // 业务方法

    /**
     * 是否已领取
     */
    public boolean isClaimed() {
        return claimed == 1;
    }

    /**
     * 领取附件
     */
    public void claim() {
        this.claimed = 1;
        this.claimTime = LocalDateTime.now();
    }

    /**
     * 获取附件描述
     */
    public String getDescription() {
        StringBuilder sb = new StringBuilder();
        
        if (item != null) {
            sb.append(item.getName());
        } else {
            sb.append("物品").append(itemId);
        }
        
        if (quantity > 1) {
            sb.append(" x").append(quantity);
        }
        
        if (isClaimed()) {
            sb.append(" [已领取]");
        }
        
        return sb.toString();
    }

    /**
     * 获取物品完整信息
     */
    public String getFullItemInfo() {
        if (item == null) {
            return "未知物品 x" + quantity;
        }
        
        return item.getFullName() + " x" + quantity;
    }

    /**
     * 检查是否可以领取
     */
    public boolean canClaim() {
        return !isClaimed() && mail != null && mail.isNormal() && !mail.isExpired();
    }

    /**
     * 获取领取经过时间（小时）
     */
    public Long getClaimElapsedHours() {
        if (claimTime == null) {
            return null;
        }
        return java.time.Duration.between(claimTime, LocalDateTime.now()).toHours();
    }

    /**
     * 获取格式化的领取时间
     */
    public String getFormattedClaimTime() {
        if (claimTime == null) return "未领取";
        
        LocalDateTime now = LocalDateTime.now();
        long hours = java.time.Duration.between(claimTime, now).toHours();
        
        if (hours < 1) {
            return "刚刚领取";
        } else if (hours < 24) {
            return hours + "小时前领取";
        } else {
            long days = hours / 24;
            return days + "天前领取";
        }
    }

    /**
     * 创建邮件附件
     */
    public static MailAttachment create(Integer mailId, Integer itemId, Integer quantity) {
        MailAttachment attachment = new MailAttachment();
        attachment.setMailId(mailId);
        attachment.setItemId(itemId);
        attachment.setQuantity(quantity);
        attachment.setClaimed(0);
        return attachment;
    }
}
