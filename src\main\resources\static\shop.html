<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商店系统 - 洪荒游戏</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f8ff;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: #2c5aa0;
            border-bottom: 2px solid #66ccff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .shop-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            flex-wrap: wrap;
        }
        
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            margin-right: 5px;
        }
        
        .tab.active {
            color: #2c5aa0;
            border-bottom-color: #66ccff;
            background-color: #f8f9fa;
        }
        
        .tab:hover {
            background-color: #f8f9fa;
        }
        
        .player-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .currency-info {
            display: flex;
            gap: 20px;
        }
        
        .currency-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .currency-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
        
        .copper { background-color: #cd7f32; }
        .silver { background-color: #c0c0c0; }
        .gold { background-color: #ffd700; }
        .yuanbao { background-color: #ff6b6b; }
        
        .item-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
            min-height: 300px;
        }
        
        .item-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .item-card:hover {
            background-color: #f0f8ff;
            border-color: #66ccff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .item-name {
            font-weight: bold;
            color: #2c5aa0;
            font-size: 16px;
        }
        
        .item-quality {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            color: white;
        }
        
        .quality-1 { background-color: #6c757d; } /* 白色 */
        .quality-2 { background-color: #28a745; } /* 绿色 */
        .quality-3 { background-color: #007bff; } /* 蓝色 */
        .quality-4 { background-color: #6f42c1; } /* 紫色 */
        .quality-5 { background-color: #fd7e14; } /* 橙色 */
        .quality-6 { background-color: #dc3545; } /* 红色 */
        
        .item-description {
            color: #666;
            font-size: 13px;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .item-stats {
            font-size: 12px;
            color: #888;
            margin-bottom: 10px;
        }
        
        .item-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .price-info {
            display: flex;
            align-items: center;
            gap: 5px;
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .item-actions {
            text-align: right;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 5px;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #2c5aa0;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1e3d6f;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .empty-message {
            text-align: center;
            color: #666;
            padding: 50px;
            font-style: italic;
            grid-column: 1 / -1;
        }
        
        .item-detail {
            display: none;
            border: 2px solid #66ccff;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            background-color: #f8f9fa;
        }
        
        .item-detail.show {
            display: block;
        }
        
        .item-detail h3 {
            color: #2c5aa0;
            margin-top: 0;
        }
        
        .item-detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        
        .item-detail-item {
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .item-detail-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }
        
        .item-detail-value {
            color: #333;
        }
        
        .purchase-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .purchase-modal.show {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .purchase-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 400px;
            width: 90%;
        }
        
        .purchase-content h3 {
            color: #2c5aa0;
            margin-top: 0;
            text-align: center;
        }
        
        .quantity-input {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        .quantity-input input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }
        
        .quantity-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .quantity-btn:hover {
            background-color: #e9ecef;
        }
        
        .total-price {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #2c5aa0;
            margin: 20px 0;
        }
        
        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .navigation {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .navigation a {
            color: #2c5aa0;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .navigation a:hover {
            text-decoration: underline;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .success {
            color: #155724;
            text-align: center;
            padding: 20px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>【商店系统】</h1>
        </div>
        
        <div id="message-area"></div>
        
        <div class="player-info">
            <div>
                <strong>当前角色:</strong> <span id="player-name">加载中...</span>
            </div>
            <div class="currency-info">
                <div class="currency-item">
                    <div class="currency-icon copper"></div>
                    <span id="copper-amount">0</span> 铜钱
                </div>
                <div class="currency-item">
                    <div class="currency-icon silver"></div>
                    <span id="silver-amount">0</span> 银两
                </div>
                <div class="currency-item">
                    <div class="currency-icon gold"></div>
                    <span id="gold-amount">0</span> 金币
                </div>
                <div class="currency-item">
                    <div class="currency-icon yuanbao"></div>
                    <span id="yuanbao-amount">0</span> 元宝
                </div>
            </div>
        </div>
        
        <div class="shop-tabs">
            <button class="tab active" onclick="switchShop(1)">新手商店</button>
            <button class="tab" onclick="switchShop(2)">武器店</button>
            <button class="tab" onclick="switchShop(3)">药剂店</button>
            <button class="tab" onclick="switchShop(4)">杂货店</button>
        </div>
        
        <div id="shop-content">
            <div id="loading" class="loading">加载中...</div>
            <div id="item-grid" class="item-grid" style="display: none;"></div>
        </div>
        
        <div id="item-detail" class="item-detail"></div>
        
        <div class="navigation">
            <a href="game.html">返回游戏</a>
            <a href="javascript:void(0)" onclick="refreshShop()">刷新</a>
        </div>
    </div>

    <!-- 购买确认模态框 -->
    <div id="purchase-modal" class="purchase-modal">
        <div class="purchase-content">
            <h3>购买确认</h3>
            <div id="purchase-item-info"></div>
            <div class="quantity-input">
                <button class="quantity-btn" onclick="changeQuantity(-1)">-</button>
                <input type="number" id="purchase-quantity" value="1" min="1" onchange="updateTotalPrice()">
                <button class="quantity-btn" onclick="changeQuantity(1)">+</button>
            </div>
            <div class="total-price" id="total-price">总价: 0 铜钱</div>
            <div class="modal-actions">
                <button class="btn btn-success" onclick="confirmPurchase()">确认购买</button>
                <button class="btn btn-primary" onclick="closePurchaseModal()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let currentShopId = 1;
        let currentItems = [];
        let currentPurchaseItem = null;
        let playerInfo = {};
        
        // 物品品质映射
        const qualityMap = {
            1: { name: '普通', class: 'quality-1' },
            2: { name: '优秀', class: 'quality-2' },
            3: { name: '精良', class: 'quality-3' },
            4: { name: '史诗', class: 'quality-4' },
            5: { name: '传说', class: 'quality-5' },
            6: { name: '神话', class: 'quality-6' }
        };
        
        // 货币类型映射
        const currencyMap = {
            1: { name: '铜钱', icon: 'copper' },
            2: { name: '银两', icon: 'silver' },
            3: { name: '金币', icon: 'gold' },
            4: { name: '元宝', icon: 'yuanbao' }
        };
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPlayerInfo();
            loadShopItems();
        });
        
        // 加载玩家信息
        function loadPlayerInfo() {
            fetch('/honghuang-game/api/role/current')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        playerInfo = data.data;
                        updatePlayerInfo();
                    }
                })
                .catch(error => {
                    console.error('加载玩家信息失败:', error);
                });
        }
        
        // 更新玩家信息显示
        function updatePlayerInfo() {
            document.getElementById('player-name').textContent = playerInfo.name || '未知';
            document.getElementById('copper-amount').textContent = playerInfo.copper || 0;
            document.getElementById('silver-amount').textContent = playerInfo.silver || 0;
            document.getElementById('gold-amount').textContent = playerInfo.gold || 0;
            document.getElementById('yuanbao-amount').textContent = playerInfo.yuanbao || 0;
        }
        
        // 切换商店
        function switchShop(shopId) {
            currentShopId = shopId;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            // 隐藏物品详情
            document.getElementById('item-detail').classList.remove('show');
            
            loadShopItems();
        }
        
        // 加载商店物品
        function loadShopItems() {
            showLoading();
            
            fetch(`/honghuang-game/api/shop/${currentShopId}/items`)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.code === 200) {
                        displayItems(data.data);
                    } else {
                        showError(data.message || '加载商店物品失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误: ' + error.message);
                });
        }
        
        // 显示物品列表
        function displayItems(items) {
            const itemGrid = document.getElementById('item-grid');
            itemGrid.style.display = 'grid';
            
            if (!items || items.length === 0) {
                itemGrid.innerHTML = '<div class="empty-message">商店暂无商品</div>';
                return;
            }
            
            currentItems = items;
            let html = '';
            
            items.forEach((item, index) => {
                const quality = qualityMap[item.quality] || { name: '普通', class: 'quality-1' };
                const currency = currencyMap[item.currencyType] || { name: '铜钱', icon: 'copper' };
                const canAfford = checkCanAfford(item);
                
                html += `
                    <div class="item-card" onclick="showItemDetail(${index})">
                        <div class="item-header">
                            <div class="item-name">${item.name}</div>
                            <div class="item-quality ${quality.class}">${quality.name}</div>
                        </div>
                        <div class="item-description">${item.description || '暂无描述'}</div>
                        ${getItemStats(item)}
                        <div class="item-price">
                            <div class="price-info">
                                <div class="currency-icon ${currency.icon}"></div>
                                ${item.price} ${currency.name}
                            </div>
                            ${item.stock > 0 ? `<span style="color: #28a745;">库存: ${item.stock}</span>` : ''}
                        </div>
                        <div class="item-actions">
                            <button class="btn btn-primary" onclick="openPurchaseModal(${index}); event.stopPropagation();" 
                                ${!canAfford || item.stock === 0 ? 'disabled' : ''}>
                                ${item.stock === 0 ? '缺货' : (canAfford ? '购买' : '金钱不足')}
                            </button>
                        </div>
                    </div>
                `;
            });
            
            itemGrid.innerHTML = html;
        }
        
        // 检查是否能够购买
        function checkCanAfford(item) {
            switch(item.currencyType) {
                case 1: return (playerInfo.copper || 0) >= item.price;
                case 2: return (playerInfo.silver || 0) >= item.price;
                case 3: return (playerInfo.gold || 0) >= item.price;
                case 4: return (playerInfo.yuanbao || 0) >= item.price;
                default: return false;
            }
        }
        
        // 获取物品属性信息
        function getItemStats(item) {
            let stats = [];
            if (item.requiredLevel > 1) stats.push(`需要等级: ${item.requiredLevel}`);
            if (item.attackBonus > 0) stats.push(`攻击力: +${item.attackBonus}`);
            if (item.defenseBonus > 0) stats.push(`防御力: +${item.defenseBonus}`);
            if (item.hpBonus > 0) stats.push(`生命值: +${item.hpBonus}`);
            if (item.mpBonus > 0) stats.push(`魔法值: +${item.mpBonus}`);
            
            return stats.length > 0 ? `<div class="item-stats">${stats.join(' | ')}</div>` : '';
        }
        
        // 显示物品详情
        function showItemDetail(index) {
            const item = currentItems[index];
            const detailDiv = document.getElementById('item-detail');
            const quality = qualityMap[item.quality] || { name: '普通', class: 'quality-1' };
            const currency = currencyMap[item.currencyType] || { name: '铜钱', icon: 'copper' };
            
            detailDiv.innerHTML = `
                <h3>${item.name} <span class="item-quality ${quality.class}">${quality.name}</span></h3>
                <div class="item-detail-grid">
                    <div class="item-detail-item">
                        <div class="item-detail-label">物品类型</div>
                        <div class="item-detail-value">${getItemTypeName(item.type)}</div>
                    </div>
                    <div class="item-detail-item">
                        <div class="item-detail-label">品质等级</div>
                        <div class="item-detail-value">${quality.name}</div>
                    </div>
                    <div class="item-detail-item">
                        <div class="item-detail-label">价格</div>
                        <div class="item-detail-value">${item.price} ${currency.name}</div>
                    </div>
                    <div class="item-detail-item">
                        <div class="item-detail-label">库存</div>
                        <div class="item-detail-value">${item.stock > 0 ? item.stock : '无限'}</div>
                    </div>
                    ${item.requiredLevel > 1 ? `
                    <div class="item-detail-item">
                        <div class="item-detail-label">等级要求</div>
                        <div class="item-detail-value">${item.requiredLevel}级</div>
                    </div>
                    ` : ''}
                    ${item.attackBonus > 0 ? `
                    <div class="item-detail-item">
                        <div class="item-detail-label">攻击力加成</div>
                        <div class="item-detail-value">+${item.attackBonus}</div>
                    </div>
                    ` : ''}
                    ${item.defenseBonus > 0 ? `
                    <div class="item-detail-item">
                        <div class="item-detail-label">防御力加成</div>
                        <div class="item-detail-value">+${item.defenseBonus}</div>
                    </div>
                    ` : ''}
                    ${item.hpBonus > 0 ? `
                    <div class="item-detail-item">
                        <div class="item-detail-label">生命值加成</div>
                        <div class="item-detail-value">+${item.hpBonus}</div>
                    </div>
                    ` : ''}
                    ${item.mpBonus > 0 ? `
                    <div class="item-detail-item">
                        <div class="item-detail-label">魔法值加成</div>
                        <div class="item-detail-value">+${item.mpBonus}</div>
                    </div>
                    ` : ''}
                </div>
                <p><strong>物品描述:</strong> ${item.description || '暂无描述'}</p>
                <div style="text-align: right; margin-top: 15px;">
                    <button class="btn btn-primary" onclick="hideItemDetail()">关闭</button>
                </div>
            `;
            
            detailDiv.classList.add('show');
        }
        
        // 获取物品类型名称
        function getItemTypeName(type) {
            const typeMap = {
                1: '装备',
                2: '消耗品',
                3: '材料',
                4: '任务物品',
                5: '宝石',
                6: '书籍',
                7: '其他'
            };
            return typeMap[type] || '未知';
        }
        
        // 隐藏物品详情
        function hideItemDetail() {
            document.getElementById('item-detail').classList.remove('show');
        }
        
        // 打开购买模态框
        function openPurchaseModal(index) {
            const item = currentItems[index];
            currentPurchaseItem = item;
            
            const currency = currencyMap[item.currencyType] || { name: '铜钱', icon: 'copper' };
            
            document.getElementById('purchase-item-info').innerHTML = `
                <p><strong>物品:</strong> ${item.name}</p>
                <p><strong>单价:</strong> ${item.price} ${currency.name}</p>
            `;
            
            document.getElementById('purchase-quantity').value = 1;
            updateTotalPrice();
            
            document.getElementById('purchase-modal').classList.add('show');
        }
        
        // 关闭购买模态框
        function closePurchaseModal() {
            document.getElementById('purchase-modal').classList.remove('show');
            currentPurchaseItem = null;
        }
        
        // 改变购买数量
        function changeQuantity(delta) {
            const input = document.getElementById('purchase-quantity');
            const newValue = parseInt(input.value) + delta;
            if (newValue >= 1) {
                input.value = newValue;
                updateTotalPrice();
            }
        }
        
        // 更新总价
        function updateTotalPrice() {
            if (!currentPurchaseItem) return;
            
            const quantity = parseInt(document.getElementById('purchase-quantity').value) || 1;
            const totalPrice = currentPurchaseItem.price * quantity;
            const currency = currencyMap[currentPurchaseItem.currencyType] || { name: '铜钱', icon: 'copper' };
            
            document.getElementById('total-price').textContent = `总价: ${totalPrice} ${currency.name}`;
        }
        
        // 确认购买
        function confirmPurchase() {
            if (!currentPurchaseItem) return;
            
            const quantity = parseInt(document.getElementById('purchase-quantity').value) || 1;
            
            fetch('/honghuang-game/api/shop/buy', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    shopId: currentShopId,
                    itemId: currentPurchaseItem.id,
                    quantity: quantity
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('购买成功！');
                    closePurchaseModal();
                    loadPlayerInfo(); // 刷新玩家信息
                    loadShopItems(); // 刷新商店物品
                } else {
                    showError(data.message || '购买失败');
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            });
        }
        
        // 刷新商店
        function refreshShop() {
            loadPlayerInfo();
            loadShopItems();
        }
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('item-grid').style.display = 'none';
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 显示错误消息
        function showError(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="error">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="success">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
