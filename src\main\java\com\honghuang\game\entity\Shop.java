package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 商店实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "shop")
public class Shop {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 商店名称
     */
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 商店类型 1装备商店 2药品商店 3杂货商店 4技能商店 5特殊商店
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 商店描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 商店NPC ID
     */
    @Column(name = "npc_id")
    private Integer npcId;

    /**
     * 商店NPC名称
     */
    @Column(name = "npc_name", length = 50)
    private String npcName;

    /**
     * 所在场景ID
     */
    @Column(name = "scene_id")
    private Integer sceneId;

    /**
     * 场景名称
     */
    @Column(name = "scene_name", length = 100)
    private String sceneName;

    /**
     * 等级要求
     */
    @Column(name = "required_level")
    private Integer requiredLevel = 1;

    /**
     * 种族要求 0无要求 1巫族 2妖族
     */
    @Column(name = "required_race")
    private Integer requiredRace = 0;

    /**
     * 性别要求 0无要求 1男性 2女性
     */
    @Column(name = "required_sex")
    private Integer requiredSex = 0;

    /**
     * 刷新间隔（分钟）
     */
    @Column(name = "refresh_interval")
    private Integer refreshInterval = 60;

    /**
     * 上次刷新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_refresh_time")
    private LocalDateTime lastRefreshTime;

    /**
     * 是否激活 0未激活 1已激活
     */
    @Column(name = "active")
    private Integer active = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    // 业务方法

    /**
     * 获取商店类型名称
     */
    public String getTypeName() {
        switch (type) {
            case 1: return "装备商店";
            case 2: return "药品商店";
            case 3: return "杂货商店";
            case 4: return "技能商店";
            case 5: return "特殊商店";
            default: return "未知";
        }
    }

    /**
     * 检查角色是否可以访问此商店
     */
    public boolean canAccess(PlayerRole role) {
        // 检查等级要求
        if (role.getLevel() < requiredLevel) {
            return false;
        }
        
        // 检查种族要求
        if (requiredRace != 0 && !role.getRace().equals(requiredRace)) {
            return false;
        }
        
        // 检查性别要求
        if (requiredSex != 0 && !role.getSex().equals(requiredSex)) {
            return false;
        }
        
        return true;
    }

    /**
     * 是否需要刷新
     */
    public boolean needsRefresh() {
        if (lastRefreshTime == null) {
            return true;
        }
        
        LocalDateTime nextRefreshTime = lastRefreshTime.plusMinutes(refreshInterval);
        return LocalDateTime.now().isAfter(nextRefreshTime);
    }

    /**
     * 获取下次刷新时间
     */
    public LocalDateTime getNextRefreshTime() {
        if (lastRefreshTime == null) {
            return LocalDateTime.now();
        }
        return lastRefreshTime.plusMinutes(refreshInterval);
    }

    /**
     * 获取距离下次刷新的分钟数
     */
    public Long getMinutesToNextRefresh() {
        LocalDateTime nextRefresh = getNextRefreshTime();
        LocalDateTime now = LocalDateTime.now();
        
        if (now.isAfter(nextRefresh)) {
            return 0L;
        }
        
        return java.time.Duration.between(now, nextRefresh).toMinutes();
    }

    /**
     * 刷新商店
     */
    public void refresh() {
        this.lastRefreshTime = LocalDateTime.now();
    }

    /**
     * 是否为装备商店
     */
    public boolean isEquipmentShop() {
        return type == 1;
    }

    /**
     * 是否为药品商店
     */
    public boolean isPotionShop() {
        return type == 2;
    }

    /**
     * 是否为杂货商店
     */
    public boolean isGeneralShop() {
        return type == 3;
    }

    /**
     * 是否为技能商店
     */
    public boolean isSkillShop() {
        return type == 4;
    }

    /**
     * 是否为特殊商店
     */
    public boolean isSpecialShop() {
        return type == 5;
    }

    /**
     * 获取种族要求名称
     */
    public String getRequiredRaceName() {
        switch (requiredRace) {
            case 0: return "无要求";
            case 1: return "巫族";
            case 2: return "妖族";
            default: return "未知";
        }
    }

    /**
     * 获取性别要求名称
     */
    public String getRequiredSexName() {
        switch (requiredSex) {
            case 0: return "无要求";
            case 1: return "男性";
            case 2: return "女性";
            default: return "未知";
        }
    }

    /**
     * 获取商店完整信息
     */
    public String getFullInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(name).append(" (").append(getTypeName()).append(")");
        if (sceneName != null) {
            sb.append(" - ").append(sceneName);
        }
        if (npcName != null) {
            sb.append(" [").append(npcName).append("]");
        }
        return sb.toString();
    }
}
