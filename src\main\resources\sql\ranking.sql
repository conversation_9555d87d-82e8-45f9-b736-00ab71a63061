-- 排行榜表结构
CREATE TABLE IF NOT EXISTS `ranking` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `race` tinyint(1) NOT NULL DEFAULT '1' COMMENT '角色种族 1-巫族 2-妖族',
  `level` int(11) NOT NULL DEFAULT '1' COMMENT '角色等级',
  `experience` bigint(20) NOT NULL DEFAULT '0' COMMENT '经验值',
  `experience_time` datetime DEFAULT NULL COMMENT '经验更新时间',
  `copper` bigint(20) NOT NULL DEFAULT '0' COMMENT '铜钱数量',
  `copper_time` datetime DEFAULT NULL COMMENT '铜钱更新时间',
  `attack` int(11) NOT NULL DEFAULT '0' COMMENT '攻击力',
  `attack_time` datetime DEFAULT NULL COMMENT '攻击力更新时间',
  `defense` int(11) NOT NULL DEFAULT '0' COMMENT '防御力',
  `defense_time` datetime DEFAULT NULL COMMENT '防御力更新时间',
  `agility` int(11) NOT NULL DEFAULT '0' COMMENT '敏捷',
  `agility_time` datetime DEFAULT NULL COMMENT '敏捷更新时间',
  `kill_monster` int(11) NOT NULL DEFAULT '0' COMMENT '杀怪数量',
  `kill_monster_time` datetime DEFAULT NULL COMMENT '杀怪更新时间',
  `pk_win` int(11) NOT NULL DEFAULT '0' COMMENT 'PK胜利次数',
  `pk_win_time` datetime DEFAULT NULL COMMENT 'PK胜利更新时间',
  `online_time` int(11) NOT NULL DEFAULT '0' COMMENT '在线时长（分钟）',
  `online_time_update` datetime DEFAULT NULL COMMENT '在线时长更新时间',
  `pet_level` int(11) NOT NULL DEFAULT '0' COMMENT '宠物等级',
  `pet_level_time` datetime DEFAULT NULL COMMENT '宠物等级更新时间',
  `pet_attack` int(11) NOT NULL DEFAULT '0' COMMENT '宠物攻击力',
  `pet_attack_time` datetime DEFAULT NULL COMMENT '宠物攻击力更新时间',
  `equip_score` int(11) NOT NULL DEFAULT '0' COMMENT '装备评分',
  `equip_score_time` datetime DEFAULT NULL COMMENT '装备评分更新时间',
  `total_score` int(11) NOT NULL DEFAULT '0' COMMENT '综合实力评分',
  `total_score_time` datetime DEFAULT NULL COMMENT '综合实力评分更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_id` (`role_id`),
  KEY `idx_level` (`level`),
  KEY `idx_experience` (`experience`),
  KEY `idx_copper` (`copper`),
  KEY `idx_attack` (`attack`),
  KEY `idx_defense` (`defense`),
  KEY `idx_agility` (`agility`),
  KEY `idx_kill_monster` (`kill_monster`),
  KEY `idx_pk_win` (`pk_win`),
  KEY `idx_online_time` (`online_time`),
  KEY `idx_pet_level` (`pet_level`),
  KEY `idx_pet_attack` (`pet_attack`),
  KEY `idx_equip_score` (`equip_score`),
  KEY `idx_total_score` (`total_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜表';

-- 插入测试数据
INSERT INTO `ranking` (`role_id`, `role_name`, `race`, `level`, `experience`, `experience_time`, `copper`, `copper_time`, `attack`, `attack_time`, `defense`, `defense_time`, `agility`, `agility_time`, `kill_monster`, `kill_monster_time`, `pk_win`, `pk_win_time`, `online_time`, `online_time_update`, `pet_level`, `pet_level_time`, `pet_attack`, `pet_attack_time`, `equip_score`, `equip_score_time`, `total_score`, `total_score_time`) VALUES
(1, '洪荒至尊', 1, 100, 99999999, NOW(), 88888888, NOW(), 9999, NOW(), 8888, NOW(), 7777, NOW(), 5000, NOW(), 500, NOW(), 10000, NOW(), 80, NOW(), 3000, NOW(), 9500, NOW(), 95000, NOW()),
(2, '妖族霸主', 2, 95, 88888888, NOW(), 77777777, NOW(), 9500, NOW(), 8500, NOW(), 7500, NOW(), 4800, NOW(), 480, NOW(), 9500, NOW(), 75, NOW(), 2800, NOW(), 9200, NOW(), 92000, NOW()),
(3, '巫族战神', 1, 90, 77777777, NOW(), 66666666, NOW(), 9200, NOW(), 8200, NOW(), 7200, NOW(), 4600, NOW(), 460, NOW(), 9000, NOW(), 70, NOW(), 2600, NOW(), 8900, NOW(), 89000, NOW()),
(4, '修仙大能', 2, 88, 66666666, NOW(), 55555555, NOW(), 8900, NOW(), 7900, NOW(), 6900, NOW(), 4400, NOW(), 440, NOW(), 8800, NOW(), 68, NOW(), 2400, NOW(), 8600, NOW(), 86000, NOW()),
(5, '财神爷', 1, 85, 55555555, NOW(), 99999999, NOW(), 8600, NOW(), 7600, NOW(), 6600, NOW(), 4200, NOW(), 420, NOW(), 8500, NOW(), 65, NOW(), 2200, NOW(), 8300, NOW(), 83000, NOW()),
(6, '武林盟主', 2, 82, 44444444, NOW(), 44444444, NOW(), 9800, NOW(), 7300, NOW(), 6300, NOW(), 4000, NOW(), 400, NOW(), 8200, NOW(), 62, NOW(), 2000, NOW(), 8000, NOW(), 80000, NOW()),
(7, '铁壁金刚', 1, 80, 33333333, NOW(), 33333333, NOW(), 8000, NOW(), 9600, NOW(), 6000, NOW(), 3800, NOW(), 380, NOW(), 8000, NOW(), 60, NOW(), 1800, NOW(), 7700, NOW(), 77000, NOW()),
(8, '疾风剑圣', 2, 78, 22222222, NOW(), 22222222, NOW(), 8300, NOW(), 7000, NOW(), 9400, NOW(), 3600, NOW(), 360, NOW(), 7800, NOW(), 58, NOW(), 1600, NOW(), 7400, NOW(), 74000, NOW()),
(9, '屠魔大师', 1, 75, 11111111, NOW(), 11111111, NOW(), 7700, NOW(), 6700, NOW(), 5700, NOW(), 5200, NOW(), 340, NOW(), 7500, NOW(), 55, NOW(), 1400, NOW(), 7100, NOW(), 71000, NOW()),
(10, '战神无敌', 2, 72, 10000000, NOW(), 10000000, NOW(), 7400, NOW(), 6400, NOW(), 5400, NOW(), 3400, NOW(), 520, NOW(), 7200, NOW(), 52, NOW(), 1200, NOW(), 6800, NOW(), 68000, NOW());

-- 创建排行榜更新触发器（可选）
DELIMITER $$

CREATE TRIGGER `update_total_score` 
BEFORE UPDATE ON `ranking` 
FOR EACH ROW 
BEGIN
    -- 计算综合实力评分
    SET NEW.total_score = (
        NEW.level * 100 + 
        NEW.attack * 5 + 
        NEW.defense * 3 + 
        NEW.agility * 2 + 
        NEW.kill_monster * 10 + 
        NEW.pk_win * 50 + 
        NEW.pet_level * 20 + 
        NEW.pet_attack * 3 + 
        NEW.equip_score
    );
    
    -- 更新综合实力评分时间
    IF NEW.total_score != OLD.total_score THEN
        SET NEW.total_score_time = NOW();
    END IF;
END$$

DELIMITER ;

-- 创建定时更新在线时长的存储过程（可选）
DELIMITER $$

CREATE PROCEDURE `UpdateOnlineTime`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE role_id INT;
    DECLARE current_online_time INT;
    
    -- 声明游标
    DECLARE cur CURSOR FOR 
        SELECT r.role_id, COALESCE(r.online_time, 0)
        FROM ranking r
        INNER JOIN (
            -- 这里需要根据实际的在线用户表来查询
            -- SELECT role_id FROM online_users
            SELECT 1 as role_id  -- 示例数据
        ) ou ON r.role_id = ou.role_id;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO role_id, current_online_time;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 更新在线时长（每次增加1分钟）
        UPDATE ranking 
        SET online_time = current_online_time + 1,
            online_time_update = NOW()
        WHERE role_id = role_id;
        
    END LOOP;
    
    CLOSE cur;
END$$

DELIMITER ;
