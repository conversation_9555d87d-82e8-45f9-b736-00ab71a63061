package com.honghuang.game.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 简单缓存配置类
 * 当Redis不可用时使用内存缓存
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableCaching
@ConditionalOnProperty(name = "spring.redis.host", havingValue = "false", matchIfMissing = true)
public class SimpleCacheConfig {

    /**
     * 简单的内存缓存管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(
            "userCache",
            "roleCache", 
            "gameCache",
            "configCache"
        );
    }
}
