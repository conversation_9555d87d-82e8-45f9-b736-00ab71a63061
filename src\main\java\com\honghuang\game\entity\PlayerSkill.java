package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 玩家技能实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "player_skill")
public class PlayerSkill {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 玩家角色ID
     */
    @Column(name = "role_id")
    private Integer roleId;

    /**
     * 技能ID
     */
    @Column(name = "skill_id")
    private Integer skillId;

    /**
     * 技能等级
     */
    @Column(name = "skill_level")
    private Integer skillLevel = 1;

    /**
     * 技能经验
     */
    @Column(name = "skill_exp")
    private Long skillExp = 0L;

    /**
     * 快捷键位置 0-9，0表示未设置快捷键
     */
    @Column(name = "shortcut_key")
    private Integer shortcutKey = 0;

    /**
     * 上次使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_use_time")
    private LocalDateTime lastUseTime;

    /**
     * 学习时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "learn_time")
    private LocalDateTime learnTime;

    /**
     * 技能信息（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "skill_id", insertable = false, updatable = false)
    private Skill skill;

    // 业务方法

    /**
     * 是否可以升级
     */
    public boolean canUpgrade() {
        if (skill == null) return false;
        return skillLevel < skill.getMaxLevel() && skillExp >= getNextLevelExp();
    }

    /**
     * 计算升级所需经验
     */
    public Long getNextLevelExp() {
        return (long) (skillLevel * skillLevel * 50 + skillLevel * 25);
    }

    /**
     * 增加技能经验
     */
    public void addExp(Long exp) {
        this.skillExp += exp;
    }

    /**
     * 升级技能
     */
    public boolean upgrade() {
        if (!canUpgrade()) {
            return false;
        }
        
        this.skillExp -= getNextLevelExp();
        this.skillLevel++;
        return true;
    }

    /**
     * 检查技能是否在冷却中
     */
    public boolean isOnCooldown() {
        if (skill == null || skill.getCooldown() == 0 || lastUseTime == null) {
            return false;
        }
        
        LocalDateTime cooldownEnd = lastUseTime.plusSeconds(skill.getCooldown());
        return LocalDateTime.now().isBefore(cooldownEnd);
    }

    /**
     * 获取剩余冷却时间（秒）
     */
    public long getRemainingCooldown() {
        if (!isOnCooldown()) {
            return 0;
        }
        
        LocalDateTime cooldownEnd = lastUseTime.plusSeconds(skill.getCooldown());
        return java.time.Duration.between(LocalDateTime.now(), cooldownEnd).getSeconds();
    }

    /**
     * 使用技能
     */
    public void use() {
        this.lastUseTime = LocalDateTime.now();
    }

    /**
     * 检查角色是否有足够MP使用技能
     */
    public boolean canUse(PlayerRole role) {
        if (skill == null) return false;
        
        // 检查冷却时间
        if (isOnCooldown()) {
            return false;
        }
        
        // 检查MP消耗
        int mpCost = skill.calculateMpCost(skillLevel);
        return role.getCurrentMp() >= mpCost;
    }

    /**
     * 获取当前等级的技能效果值
     */
    public Integer getCurrentValue() {
        if (skill == null) return 0;
        return skill.calculateValue(skillLevel);
    }

    /**
     * 获取当前等级的MP消耗
     */
    public Integer getCurrentMpCost() {
        if (skill == null) return 0;
        return skill.calculateMpCost(skillLevel);
    }

    /**
     * 获取升级费用
     */
    public Long getUpgradeCost() {
        if (skill == null) return 0L;
        return skill.calculateUpgradeCost(skillLevel + 1);
    }

    /**
     * 设置快捷键
     */
    public void setShortcut(Integer key) {
        if (key >= 0 && key <= 9) {
            this.shortcutKey = key;
        }
    }

    /**
     * 清除快捷键
     */
    public void clearShortcut() {
        this.shortcutKey = 0;
    }

    /**
     * 是否设置了快捷键
     */
    public boolean hasShortcut() {
        return shortcutKey > 0;
    }

    /**
     * 获取技能完整名称（包含等级）
     */
    public String getFullName() {
        if (skill == null) return "未知技能";
        return skill.getName() + " Lv." + skillLevel;
    }

    /**
     * 获取技能进度百分比
     */
    public double getExpProgress() {
        if (skillLevel >= skill.getMaxLevel()) {
            return 100.0;
        }
        
        Long nextLevelExp = getNextLevelExp();
        if (nextLevelExp == 0) {
            return 100.0;
        }
        
        return (double) skillExp / nextLevelExp * 100.0;
    }

    /**
     * 是否为被动技能
     */
    public boolean isPassive() {
        return skill != null && skill.isPassiveSkill();
    }

    /**
     * 是否为攻击技能
     */
    public boolean isAttackSkill() {
        return skill != null && skill.isAttackSkill();
    }

    /**
     * 是否为治疗技能
     */
    public boolean isHealSkill() {
        return skill != null && skill.isHealSkill();
    }

    /**
     * 是否为辅助技能
     */
    public boolean isSupportSkill() {
        return skill != null && skill.isSupportSkill();
    }

    /**
     * 计算技能伤害（对于攻击技能）
     */
    public Integer calculateDamage(PlayerRole role) {
        if (!isAttackSkill() || skill == null) {
            return 0;
        }
        
        int baseValue = getCurrentValue();
        int roleAttack = role.getTotalAttack();
        
        // 技能伤害 = 基础伤害 + 角色攻击力 * 技能系数
        double skillCoefficient = 0.5 + skillLevel * 0.1; // 技能等级越高，系数越大
        return (int) (baseValue + roleAttack * skillCoefficient);
    }

    /**
     * 计算技能治疗量（对于治疗技能）
     */
    public Integer calculateHeal(PlayerRole role) {
        if (!isHealSkill() || skill == null) {
            return 0;
        }
        
        int baseValue = getCurrentValue();
        int roleIntelligence = role.getTotalIntelligence();
        
        // 治疗量 = 基础治疗 + 角色智力 * 技能系数
        double skillCoefficient = 0.3 + skillLevel * 0.05;
        return (int) (baseValue + roleIntelligence * skillCoefficient);
    }
}
