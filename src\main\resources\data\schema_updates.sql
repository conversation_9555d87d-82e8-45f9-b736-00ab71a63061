-- 数据库表结构更新脚本
-- 为新功能添加必要的表和字段

-- 1. 战斗记录表
CREATE TABLE IF NOT EXISTS battle_record (
    id INT AUTO_INCREMENT PRIMARY KEY,
    player_role_id INT NOT NULL,
    monster_id INT NOT NULL,
    battle_type INT DEFAULT 1 COMMENT '1:PVE 2:PVP',
    battle_result INT COMMENT '1:胜利 2:失败 3:逃跑',
    battle_rounds INT DEFAULT 0,
    player_initial_hp INT,
    player_remaining_hp INT,
    monster_initial_hp INT,
    monster_remaining_hp INT,
    exp_gained BIGINT DEFAULT 0,
    copper_gained BIGINT DEFAULT 0,
    dropped_items TEXT COMMENT '掉落物品JSON',
    battle_details TEXT COMMENT '战斗详情JSON',
    battle_duration INT COMMENT '战斗时长（秒）',
    battle_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_player_role (player_role_id),
    INDEX idx_monster (monster_id),
    INDEX idx_battle_time (battle_time),
    INDEX idx_battle_result (battle_result)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='战斗记录表';

-- 2. 更新角色表，添加新字段
ALTER TABLE player_role 
ADD COLUMN IF NOT EXISTS next_level_exp BIGINT DEFAULT 100 COMMENT '升级所需经验',
ADD COLUMN IF NOT EXISTS profession VARCHAR(20) COMMENT '角色职业',
ADD COLUMN IF NOT EXISTS base_attack INT DEFAULT 10 COMMENT '基础攻击力',
ADD COLUMN IF NOT EXISTS base_defense INT DEFAULT 5 COMMENT '基础防御力',
ADD COLUMN IF NOT EXISTS base_agility INT DEFAULT 5 COMMENT '基础敏捷',
ADD COLUMN IF NOT EXISTS base_intelligence INT DEFAULT 5 COMMENT '基础智力',
ADD COLUMN IF NOT EXISTS base_constitution INT DEFAULT 5 COMMENT '基础体质',
ADD COLUMN IF NOT EXISTS extra_attack INT DEFAULT 0 COMMENT '额外攻击力（装备加成）',
ADD COLUMN IF NOT EXISTS extra_defense INT DEFAULT 0 COMMENT '额外防御力（装备加成）',
ADD COLUMN IF NOT EXISTS extra_agility INT DEFAULT 0 COMMENT '额外敏捷（装备加成）',
ADD COLUMN IF NOT EXISTS extra_intelligence INT DEFAULT 0 COMMENT '额外智力（装备加成）',
ADD COLUMN IF NOT EXISTS extra_constitution INT DEFAULT 0 COMMENT '额外体质（装备加成）',
ADD COLUMN IF NOT EXISTS attribute_points INT DEFAULT 0 COMMENT '可分配属性点',
ADD COLUMN IF NOT EXISTS map_x INT DEFAULT 0 COMMENT '当前地图坐标X',
ADD COLUMN IF NOT EXISTS map_y INT DEFAULT 0 COMMENT '当前地图坐标Y',
ADD COLUMN IF NOT EXISTS reputation INT DEFAULT 0 COMMENT '声望',
ADD COLUMN IF NOT EXISTS pk_value INT DEFAULT 0 COMMENT 'PK值';

-- 3. 更新物品表，添加新字段
ALTER TABLE goods_info 
ADD COLUMN IF NOT EXISTS goods_icon VARCHAR(100) COMMENT '物品图标',
ADD COLUMN IF NOT EXISTS is_stackable INT DEFAULT 0 COMMENT '是否可堆叠',
ADD COLUMN IF NOT EXISTS max_stack INT DEFAULT 1 COMMENT '最大堆叠数量',
ADD COLUMN IF NOT EXISTS is_tradeable INT DEFAULT 1 COMMENT '是否可交易',
ADD COLUMN IF NOT EXISTS is_droppable INT DEFAULT 1 COMMENT '是否可丢弃',
ADD COLUMN IF NOT EXISTS use_level_limit INT DEFAULT 1 COMMENT '使用等级限制',
ADD COLUMN IF NOT EXISTS sex_limit INT DEFAULT 0 COMMENT '性别限制',
ADD COLUMN IF NOT EXISTS race_limit INT DEFAULT 0 COMMENT '种族限制',
ADD COLUMN IF NOT EXISTS profession_limit VARCHAR(100) COMMENT '职业限制',
ADD COLUMN IF NOT EXISTS force_bonus INT DEFAULT 0 COMMENT '力量加成',
ADD COLUMN IF NOT EXISTS agile_bonus INT DEFAULT 0 COMMENT '敏捷加成',
ADD COLUMN IF NOT EXISTS physique_bonus INT DEFAULT 0 COMMENT '体魄加成',
ADD COLUMN IF NOT EXISTS savvy_bonus INT DEFAULT 0 COMMENT '悟性加成',
ADD COLUMN IF NOT EXISTS use_effect VARCHAR(200) COMMENT '使用效果描述',
ADD COLUMN IF NOT EXISTS cooldown INT DEFAULT 0 COMMENT '冷却时间（秒）',
ADD COLUMN IF NOT EXISTS duration INT DEFAULT 0 COMMENT '持续时间（秒）',
ADD COLUMN IF NOT EXISTS is_active INT DEFAULT 1 COMMENT '是否激活';

-- 4. 更新玩家物品表，添加新字段
ALTER TABLE u_part_goods 
ADD COLUMN IF NOT EXISTS position_index INT COMMENT '具体位置索引',
ADD COLUMN IF NOT EXISTS is_bound INT DEFAULT 0 COMMENT '是否绑定',
ADD COLUMN IF NOT EXISTS durability INT COMMENT '耐久度（当前）',
ADD COLUMN IF NOT EXISTS max_durability INT COMMENT '最大耐久度',
ADD COLUMN IF NOT EXISTS enhance_level INT DEFAULT 0 COMMENT '强化等级',
ADD COLUMN IF NOT EXISTS gem1_id INT COMMENT '镶嵌宝石1',
ADD COLUMN IF NOT EXISTS gem2_id INT COMMENT '镶嵌宝石2',
ADD COLUMN IF NOT EXISTS gem3_id INT COMMENT '镶嵌宝石3',
ADD COLUMN IF NOT EXISTS gem4_id INT COMMENT '镶嵌宝石4',
ADD COLUMN IF NOT EXISTS extra_attributes TEXT COMMENT '附加属性JSON',
ADD COLUMN IF NOT EXISTS obtain_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
ADD COLUMN IF NOT EXISTS last_use_time DATETIME COMMENT '最后使用时间',
ADD COLUMN IF NOT EXISTS expire_time DATETIME COMMENT '过期时间',
ADD COLUMN IF NOT EXISTS update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 5. 更新场景表，添加新字段
ALTER TABLE scene_info 
ADD COLUMN IF NOT EXISTS scene_type INT DEFAULT 1 COMMENT '场景类型',
ADD COLUMN IF NOT EXISTS level_limit INT DEFAULT 1 COMMENT '场景等级限制',
ADD COLUMN IF NOT EXISTS max_players INT DEFAULT 100 COMMENT '最大玩家数量',
ADD COLUMN IF NOT EXISTS is_safe_zone INT DEFAULT 0 COMMENT '是否安全区域',
ADD COLUMN IF NOT EXISTS allow_pk INT DEFAULT 1 COMMENT '是否允许PK',
ADD COLUMN IF NOT EXISTS allow_teleport INT DEFAULT 1 COMMENT '是否允许使用回城卷',
ADD COLUMN IF NOT EXISTS background_music VARCHAR(100) COMMENT '背景音乐',
ADD COLUMN IF NOT EXISTS background_image VARCHAR(100) COMMENT '背景图片',
ADD COLUMN IF NOT EXISTS map_width INT DEFAULT 100 COMMENT '地图宽度',
ADD COLUMN IF NOT EXISTS map_height INT DEFAULT 100 COMMENT '地图高度',
ADD COLUMN IF NOT EXISTS spawn_x INT DEFAULT 50 COMMENT '默认出生点X坐标',
ADD COLUMN IF NOT EXISTS spawn_y INT DEFAULT 50 COMMENT '默认出生点Y坐标',
ADD COLUMN IF NOT EXISTS north_scene VARCHAR(10) COMMENT '北方连接场景ID',
ADD COLUMN IF NOT EXISTS south_scene VARCHAR(10) COMMENT '南方连接场景ID',
ADD COLUMN IF NOT EXISTS east_scene VARCHAR(10) COMMENT '东方连接场景ID',
ADD COLUMN IF NOT EXISTS west_scene VARCHAR(10) COMMENT '西方连接场景ID',
ADD COLUMN IF NOT EXISTS up_scene VARCHAR(10) COMMENT '上层场景ID',
ADD COLUMN IF NOT EXISTS down_scene VARCHAR(10) COMMENT '下层场景ID',
ADD COLUMN IF NOT EXISTS monster_config TEXT COMMENT '场景刷新怪物配置JSON',
ADD COLUMN IF NOT EXISTS npc_config TEXT COMMENT '场景NPC配置JSON',
ADD COLUMN IF NOT EXISTS teleport_config TEXT COMMENT '场景传送点配置JSON',
ADD COLUMN IF NOT EXISTS event_config TEXT COMMENT '场景特殊事件配置JSON',
ADD COLUMN IF NOT EXISTS weather_type INT DEFAULT 1 COMMENT '天气类型',
ADD COLUMN IF NOT EXISTS time_type INT DEFAULT 1 COMMENT '时间类型',
ADD COLUMN IF NOT EXISTS is_active INT DEFAULT 1 COMMENT '是否激活',
ADD COLUMN IF NOT EXISTS create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 6. 更新NPC表，添加新字段
ALTER TABLE npc_info 
ADD COLUMN IF NOT EXISTS npc_type INT DEFAULT 1 COMMENT '怪物类型',
ADD COLUMN IF NOT EXISTS npc_level INT DEFAULT 1 COMMENT '怪物等级',
ADD COLUMN IF NOT EXISTS npc_hp INT DEFAULT 100 COMMENT '最大生命值',
ADD COLUMN IF NOT EXISTS npc_mp INT DEFAULT 50 COMMENT '最大法力值',
ADD COLUMN IF NOT EXISTS npc_attack INT DEFAULT 10 COMMENT '攻击力',
ADD COLUMN IF NOT EXISTS npc_defense INT DEFAULT 5 COMMENT '防御力',
ADD COLUMN IF NOT EXISTS npc_agility INT DEFAULT 5 COMMENT '敏捷',
ADD COLUMN IF NOT EXISTS npc_intelligence INT DEFAULT 5 COMMENT '智力',
ADD COLUMN IF NOT EXISTS npc_constitution INT DEFAULT 5 COMMENT '体质',
ADD COLUMN IF NOT EXISTS exp_reward BIGINT DEFAULT 10 COMMENT '经验值奖励',
ADD COLUMN IF NOT EXISTS copper_reward BIGINT DEFAULT 5 COMMENT '铜钱奖励',
ADD COLUMN IF NOT EXISTS drop_items TEXT COMMENT '掉落物品配置JSON',
ADD COLUMN IF NOT EXISTS skills TEXT COMMENT '技能配置JSON',
ADD COLUMN IF NOT EXISTS npc_description TEXT COMMENT '怪物描述',
ADD COLUMN IF NOT EXISTS npc_icon VARCHAR(100) COMMENT '怪物图标',
ADD COLUMN IF NOT EXISTS respawn_time INT DEFAULT 300 COMMENT '刷新时间（秒）',
ADD COLUMN IF NOT EXISTS move_speed INT DEFAULT 100 COMMENT '移动速度',
ADD COLUMN IF NOT EXISTS attack_range INT DEFAULT 1 COMMENT '攻击范围',
ADD COLUMN IF NOT EXISTS sight_range INT DEFAULT 5 COMMENT '视野范围',
ADD COLUMN IF NOT EXISTS ai_type INT DEFAULT 2 COMMENT 'AI类型',
ADD COLUMN IF NOT EXISTS is_active INT DEFAULT 1 COMMENT '是否激活',
ADD COLUMN IF NOT EXISTS create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 7. 添加索引优化查询性能
ALTER TABLE player_role ADD INDEX IF NOT EXISTS idx_grade (grade);
ALTER TABLE player_role ADD INDEX IF NOT EXISTS idx_current_scene (current_scene);
ALTER TABLE player_role ADD INDEX IF NOT EXISTS idx_is_online (is_online);

ALTER TABLE goods_info ADD INDEX IF NOT EXISTS idx_goods_type (goods_type);
ALTER TABLE goods_info ADD INDEX IF NOT EXISTS idx_goods_level (goods_level);
ALTER TABLE goods_info ADD INDEX IF NOT EXISTS idx_goods_quality (goods_quality);
ALTER TABLE goods_info ADD INDEX IF NOT EXISTS idx_is_active (is_active);

ALTER TABLE u_part_goods ADD INDEX IF NOT EXISTS idx_goods_position (goods_position);
ALTER TABLE u_part_goods ADD INDEX IF NOT EXISTS idx_position_index (position_index);

ALTER TABLE scene_info ADD INDEX IF NOT EXISTS idx_scene_type (scene_type);
ALTER TABLE scene_info ADD INDEX IF NOT EXISTS idx_level_limit (level_limit);
ALTER TABLE scene_info ADD INDEX IF NOT EXISTS idx_is_active (is_active);

ALTER TABLE npc_info ADD INDEX IF NOT EXISTS idx_npc_type (npc_type);
ALTER TABLE npc_info ADD INDEX IF NOT EXISTS idx_npc_level (npc_level);
ALTER TABLE npc_info ADD INDEX IF NOT EXISTS idx_is_active (is_active);

-- 8. 创建任务相关表（为后续任务系统准备）
CREATE TABLE IF NOT EXISTS quest_info (
    quest_id INT AUTO_INCREMENT PRIMARY KEY,
    quest_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    quest_type INT DEFAULT 1 COMMENT '任务类型',
    quest_level INT DEFAULT 1 COMMENT '任务等级',
    quest_description TEXT COMMENT '任务描述',
    quest_target TEXT COMMENT '任务目标JSON',
    quest_reward TEXT COMMENT '任务奖励JSON',
    prerequisite_quest INT COMMENT '前置任务ID',
    is_repeatable INT DEFAULT 0 COMMENT '是否可重复',
    is_active INT DEFAULT 1 COMMENT '是否激活',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_quest_type (quest_type),
    INDEX idx_quest_level (quest_level),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务信息表';

CREATE TABLE IF NOT EXISTS player_quest (
    id INT AUTO_INCREMENT PRIMARY KEY,
    player_role_id INT NOT NULL,
    quest_id INT NOT NULL,
    quest_status INT DEFAULT 1 COMMENT '1:进行中 2:已完成 3:已放弃',
    quest_progress TEXT COMMENT '任务进度JSON',
    accept_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    complete_time DATETIME,
    INDEX idx_player_role (player_role_id),
    INDEX idx_quest (quest_id),
    INDEX idx_quest_status (quest_status),
    UNIQUE KEY uk_player_quest (player_role_id, quest_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='玩家任务表';

-- 9. 创建聊天记录表
CREATE TABLE IF NOT EXISTS chat_record (
    id INT AUTO_INCREMENT PRIMARY KEY,
    player_role_id INT NOT NULL,
    channel_type INT DEFAULT 1 COMMENT '1:世界 2:私聊 3:帮派 4:系统',
    target_role_id INT COMMENT '目标角色ID（私聊时使用）',
    message_content TEXT NOT NULL,
    send_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_player_role (player_role_id),
    INDEX idx_channel_type (channel_type),
    INDEX idx_send_time (send_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天记录表';

-- 10. 创建好友关系表
CREATE TABLE IF NOT EXISTS friend_relation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    player_role_id INT NOT NULL,
    friend_role_id INT NOT NULL,
    relation_type INT DEFAULT 1 COMMENT '1:好友 2:黑名单',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_player_role (player_role_id),
    INDEX idx_friend_role (friend_role_id),
    UNIQUE KEY uk_friend_relation (player_role_id, friend_role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='好友关系表';
