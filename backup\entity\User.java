package com.honghuang.game.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户实体类
 * 对应原表 u_login_info
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"password", "roles"})
@Entity
@Table(name = "u_login_info")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "u_pk")
    private Integer id;

    /**
     * 用户登录名
     */
    @Column(name = "u_name", unique = true, length = 20)
    private String username;

    /**
     * 用户登录密码
     */
    @Column(name = "u_paw", length = 60)
    private String password;

    /**
     * 登录状态 1为登录 0为未登录
     */
    @Column(name = "login_state")
    private Integer loginState = 0;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 最后登录IP地址
     */
    @Column(name = "last_login_ip", length = 20)
    private String lastLoginIp;

    /**
     * 最后一次登录时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 元宝数量
     */
    @Column(name = "yuanbao")
    private Integer yuanbao = 0;

    /**
     * 商城积分数量
     */
    @Column(name = "jifen")
    private Integer jifen = 0;

    /**
     * 超级渠道
     */
    @Column(name = "super_qudao")
    private String superChannel;

    /**
     * 渠道来源
     */
    @Column(name = "qudao")
    private String channel;

    /**
     * 用户角色列表
     */
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PlayerRole> roles;

    // 自定义构造函数
    public User(String username, String password) {
        this.username = username;
        this.password = password;
    }
}
